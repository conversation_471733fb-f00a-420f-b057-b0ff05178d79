package com.lg.dao.core.fill;

import com.lg.dao.core.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 默认的元数据处理器
 */
@Slf4j
public class DefaultMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(Object entity) {
        if (entity == null) {
            return;
        }

        try {
            // 填充创建时间
            setFieldValByName(entity, "createTime", new Date(), AutoFill.FillStrategy.INSERT);
            setFieldValByName(entity, "createAt", LocalDateTime.now(), AutoFill.FillStrategy.INSERT);
            
            // 填充更新时间
            setFieldValByName(entity, "updateTime", new Date(), AutoFill.FillStrategy.INSERT);
            setFieldValByName(entity, "updateAt", LocalDateTime.now(), AutoFill.FillStrategy.INSERT);
            
            // 填充租户ID
            String tenantId = TenantContext.getTenantId();
            if (tenantId != null) {
                setFieldValByName(entity, "tenantId", tenantId, AutoFill.FillStrategy.INSERT);
            }
            
            // 填充创建人
            // TODO: 从当前用户上下文获取用户ID
            
            // 填充删除标记
            setFieldValByName(entity, "deleted", false, AutoFill.FillStrategy.INSERT);
            setFieldValByName(entity, "delFlag", 0, AutoFill.FillStrategy.INSERT);
        } catch (Exception e) {
            log.warn("Auto fill fields for insert failed", e);
        }
    }

    @Override
    public void updateFill(Object entity) {
        if (entity == null) {
            return;
        }

        try {
            // 填充更新时间
            setFieldValByName(entity, "updateTime", new Date(), AutoFill.FillStrategy.UPDATE);
            setFieldValByName(entity, "updateAt", LocalDateTime.now(), AutoFill.FillStrategy.UPDATE);
            
            // 填充更新人
            // TODO: 从当前用户上下文获取用户ID
        } catch (Exception e) {
            log.warn("Auto fill fields for update failed", e);
        }
    }

    private void setFieldValByName(Object entity, String fieldName, Object value, AutoFill.FillStrategy expectedStrategy) {
        Field field = ReflectionUtils.findField(entity.getClass(), fieldName);
        if (field == null) {
            return;
        }

        AutoFill autoFill = field.getAnnotation(AutoFill.class);
        if (autoFill == null) {
            return;
        }

        AutoFill.FillStrategy strategy = autoFill.strategy();
        if (strategy != expectedStrategy && strategy != AutoFill.FillStrategy.INSERT_UPDATE) {
            return;
        }

        field.setAccessible(true);
        try {
            // 只有字段为null时才填充
            if (field.get(entity) == null) {
                field.set(entity, value);
            }
        } catch (IllegalAccessException e) {
            log.warn("Set field value failed: {}", fieldName, e);
        }
    }
} 