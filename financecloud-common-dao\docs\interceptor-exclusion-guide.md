# 拦截器排除管理指南

## 概述

`InterceptorExclusionManager` 提供了统一的拦截器排除机制，可以在特定场景下排除一个或多个拦截器的执行，避免死循环和不必要的处理。

## 支持的拦截器类型

- `DATA_PERMISSION`: 数据权限拦截器
- `TENANT`: 租户拦截器  
- `SQL_PRINT`: SQL打印拦截器
- `CACHE`: 缓存拦截器
- `AUDIT`: 审计拦截器
- `PERFORMANCE`: 性能监控拦截器

## 基本用法

### 1. 检查拦截器是否被排除

```java
if (InterceptorExclusionManager.isExcluded(InterceptorType.DATA_PERMISSION)) {
    // 数据权限拦截器被排除，跳过处理
    return originalSql;
}
```

### 2. 排除单个拦截器

```java
// 排除数据权限拦截器
InterceptorExclusionManager.exclude(InterceptorType.DATA_PERMISSION);

// 执行操作...

// 取消排除
InterceptorExclusionManager.include(InterceptorType.DATA_PERMISSION);
```

### 3. 排除多个拦截器

```java
// 排除多个拦截器
InterceptorExclusionManager.exclude(
    InterceptorType.DATA_PERMISSION, 
    InterceptorType.TENANT,
    InterceptorType.AUDIT
);
```

### 4. 执行操作时临时排除拦截器

```java
// 有返回值的操作
List<User> users = InterceptorExclusionManager.executeWithExclusion(() -> {
    return userDao.selectAll();
}, InterceptorType.DATA_PERMISSION, InterceptorType.TENANT);

// 无返回值的操作
InterceptorExclusionManager.executeWithExclusion(() -> {
    userDao.updateStatus(userId, status);
}, InterceptorType.AUDIT);
```

### 5. 便捷方法

```java
// 排除数据权限拦截器
List<DataPermissionRule> rules = InterceptorExclusionManager.executeWithoutDataPermission(() -> {
    return ruleDao.selectByTableCode(tableCode);
});

// 排除租户拦截器
InterceptorExclusionManager.executeWithoutTenant(() -> {
    globalConfigDao.updateConfig(key, value);
});
```

## 在拦截器中的使用

### 数据权限拦截器示例

```java
@Override
public String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
    // 检查是否排除数据权限拦截器
    if (InterceptorExclusionManager.isExcluded(InterceptorType.DATA_PERMISSION)) {
        return sql;
    }
    
    // 执行数据权限处理...
    return processedSql;
}
```

### 租户拦截器示例

```java
@Override
public String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
    // 检查是否排除租户拦截器
    if (InterceptorExclusionManager.isExcluded(InterceptorType.TENANT)) {
        return sql;
    }
    
    // 执行租户隔离处理...
    return processedSql;
}
```

## 常见使用场景

### 1. 避免数据权限查询死循环

```java
// 在DataPermissionService中查询权限规则时，需要排除数据权限拦截器
public List<DataPermissionRule> getDataPermissionRules(String tableCode) {
    return InterceptorExclusionManager.executeWithoutDataPermission(() -> {
        return ruleDao.selectByTableCode(tableCode);
    });
}
```

### 2. 系统初始化时跳过所有拦截器

```java
@PostConstruct
public void initSystemData() {
    InterceptorExclusionManager.executeWithExclusion(() -> {
        // 初始化系统数据，不需要权限检查、租户隔离等
        systemDataDao.initDefaultData();
    }, InterceptorType.DATA_PERMISSION, InterceptorType.TENANT, InterceptorType.AUDIT);
}
```

### 3. 管理员操作跳过权限检查

```java
@PreAuthorize("hasRole('ADMIN')")
public void adminOperation() {
    InterceptorExclusionManager.executeWithoutDataPermission(() -> {
        // 管理员操作，跳过数据权限检查
        userDao.updateAllUsers();
    });
}
```

## 调试和监控

### 启用调试模式

```yaml
light:
  orm:
    debug:
      enable: true
```

### 调试接口

- `GET /dao/interceptor/exclusions` - 查看当前排除的拦截器
- `POST /dao/interceptor/exclude/{type}` - 排除指定拦截器
- `POST /dao/interceptor/include/{type}` - 取消排除指定拦截器
- `POST /dao/interceptor/clear` - 清除所有排除
- `GET /dao/interceptor/types` - 查看所有拦截器类型

### 示例调用

```bash
# 查看当前排除状态
curl http://localhost:8080/dao/interceptor/exclusions

# 排除数据权限拦截器
curl -X POST http://localhost:8080/dao/interceptor/exclude/data_permission

# 清除所有排除
curl -X POST http://localhost:8080/dao/interceptor/clear
```

## 注意事项

1. **线程安全**: 排除状态是基于线程本地变量的，不会影响其他线程
2. **自动清理**: 请求结束时会自动清理线程本地变量，防止内存泄漏
3. **嵌套调用**: 支持嵌套调用，内层的排除设置不会影响外层
4. **异常安全**: 即使发生异常，也会正确恢复排除状态
5. **性能影响**: 排除检查的性能开销很小，可以放心使用

## 最佳实践

1. **明确排除范围**: 只排除必要的拦截器，避免过度排除
2. **使用便捷方法**: 优先使用 `executeWithExclusion` 等便捷方法
3. **添加注释**: 在排除拦截器的地方添加清晰的注释说明原因
4. **测试验证**: 确保排除拦截器后的行为符合预期
5. **监控使用**: 在生产环境中监控拦截器排除的使用情况
