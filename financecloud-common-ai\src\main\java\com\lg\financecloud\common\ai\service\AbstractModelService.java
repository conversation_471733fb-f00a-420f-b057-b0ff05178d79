package com.lg.financecloud.common.ai.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.financecloud.common.ai.config.AIModelProperties;
import com.lg.financecloud.common.ai.exception.AIServiceException;
import com.lg.financecloud.common.ai.factory.AIServiceFactory;
import com.lg.financecloud.common.ai.model.*;
import com.lg.financecloud.common.ai.service.AIModelService;
import com.lg.financecloud.common.ai.service.impl.OpenAiModelService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@AllArgsConstructor
@Service
public class AbstractModelService implements AIModelService {

    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_PREFIX = "Bearer ";
    public static final String CONTENT_TYPE_HEADER = "Content-Type";
    public static final String APPLICATION_JSON = "application/json";
    public static final String ACCEPT_HEADER = "Accept";
    public static final String TEXT_EVENT_STREAM = "text/event-stream";
    public static final String X_DASHSCOPE_SSE_HEADER = "X-DashScope-SSE";
    public static final String X_REQUEST_ID_HEADER = "X-Request-ID";
    public static final String ENABLE = "enable";
    public static final String MODEL_KEY = "model";
    public static final String STREAM_KEY = "stream";
    public static final String MESSAGES_KEY = "messages";
    public static final String ROLE_KEY = "role";
    public static final String CONTENT_KEY = "content";
    public static final String DATA_PREFIX = "data: ";
    public static final String DONE_MARKER = "data: [DONE]";
    public static final int REQUEST_TIMEOUT_SECONDS = 60; // 设置请求超时时间为 30 秒

    // 新增辅助方法：构建HTTP请求
    private HttpPost buildHttpRequest(AIRequest request) throws AIServiceException {
        HttpPost httpPost = new HttpPost(getRequestEndpoint(request));
        httpPost.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + request.getApiKey());
        httpPost.setHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON);
        httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(buildRequestBody(request)), StandardCharsets.UTF_8));
        return httpPost;
    }


    public AIResponse process(AIRequest request) throws AIServiceException {
        try {
            log.info("[AI Processing] Start processing requestID: {}, Model: {}",
                    request.getRequestId(), request.getModelName());
            HttpPost httpPost = buildHttpRequest(request);
            CloseableHttpAsyncClient client = createHttpClient();
            CountDownLatch latch = new CountDownLatch(1);
            AtomicReference<String> responseJsonRef = new AtomicReference<>();
            client.execute(httpPost, new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    try {
                        responseJsonRef.set(EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
                    } catch (IOException e) {
                        log.error("Error reading response body", e);
                    } finally {
                        latch.countDown();
                    }
                }
                @Override
                public void failed(Exception ex) {
                    log.error("Request failed", ex);
                    latch.countDown();
                }
                @Override
                public void cancelled() {
                    log.warn("Request cancelled");
                    latch.countDown();
                }
            });
            // 修改后的超时处理代码
            try {
                Integer timeout = Optional.ofNullable(request.getTimeout()).orElse(REQUEST_TIMEOUT_SECONDS);
                if (timeout == -1) {
                    latch.await(); // 无限等待
                } else {
                    if (!latch.await(timeout, TimeUnit.SECONDS)) {
                        throw new AIServiceException("Request timed out after " + timeout + " seconds");
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new AIServiceException("Request interrupted", e);
            } finally {
                latch.countDown(); // 确保无论如何都释放锁
            }
            String responseJson = responseJsonRef.get();
            System.err.println(responseJson);
            if (responseJson == null) {
                throw new AIServiceException("No response received");
            }

            return parseAIResponse(JSONUtil.parseObj(responseJson));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Unexpected error during AI request processing for requestId: {}", request.getRequestId(), e);
            throw new AIServiceException("AI请求处理失败: 未知错误", e);
        }
    }

    private String getRequestEndpoint(AIRequest request) throws AIServiceException {
        if (StrUtil.isEmpty(request.getRequestUrl())) {
            throw new AIServiceException("请求URL为空");
        }
        return request.getRequestUrl();
    }

    private Map<String, Object> buildRequestBody(AIRequest request) {
        Map<String, Object> body = new LinkedHashMap<>();
        body.put(MODEL_KEY, detectModelType(request));
        body.put(STREAM_KEY, false);
        body.put(MESSAGES_KEY, buildMessageContents(request.getMessages()));
        return body;
    }

    private String detectModelType(AIRequest request) {
        boolean isMultimodal = request.getMessages().stream()
                .anyMatch(msg -> isMultimodalContent(msg.getContent()));

        return isMultimodal ?
                Optional.ofNullable(request.getModelName()).orElse("qwen-vl-plus") :
                request.getModelName();
    }

    private List<Map<String, Object>> buildMessageContents(List<ChatMessage> messages) {
        return messages.stream().map(msg -> {
            Map<String, Object> message = new HashMap<>();
            message.put(ROLE_KEY, msg.getRole());
            message.put(CONTENT_KEY, parseMessageContent(msg.getContent()));
            return message;
        }).collect(Collectors.toList());
    }

    private Object parseMessageContent(String content) {
        try {
            JSONArray array = JSONUtil.parseArray(content);
            if (array.stream().anyMatch(item ->
                    ((JSONObject) item).containsKey("type"))) {
                return array;
            }
            return wrapTextContent(content);
        } catch (Exception e) {
            log.warn("消息内容解析失败，降级为文本处理");
            return wrapTextContent(content);
        }
    }

    private JSONArray wrapTextContent(String text) {
        JSONArray array = JSONUtil.createArray();
        array.add(JSONUtil.createObj()
                .set("type", "text")
                .set("text", text));
        return array;
    }

    private boolean isMultimodalContent(String content) {
        try {
            return JSONUtil.parseArray(content).stream()
                    .anyMatch(item -> "image_url".equals(
                            ((JSONObject) item).getStr("type")));
        } catch (Exception e) {
            return false;
        }
    }

    private AIResponse parseAIResponse(JSONObject responseJson) {
        return AIResponse.builder()
                .code(200)
                .message("成功")
                .content(extractContent(responseJson))
                .modelName(responseJson.getStr(MODEL_KEY))
                .requestId(responseJson.getStr("id"))
                .usage(responseJson.getBean("usage", TokenUsage.class))
                .createdAt(responseJson.getLong("created"))
                .build();
    }

    private String extractContent(JSONObject responseJson) {
        return Optional.ofNullable(responseJson.getJSONArray("choices"))
                .filter(arr -> !arr.isEmpty())
                .map(arr -> arr.getJSONObject(0))
                .map(choice -> choice.getJSONObject("message"))
                .map(message -> message.getStr(CONTENT_KEY))
                .orElse("");
    }

    private CloseableHttpAsyncClient createHttpClient() {
        CloseableHttpAsyncClient client = HttpAsyncClients.custom()
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(20)
                .build();
        client.start();
        return client;
    }

    private String formatSSEEvent(Object data, String eventType) {
        return new StringBuilder()
                .append("id:").append(UUID.randomUUID().toString())
                .append("\nevent:").append(eventType)
                .append("\n:HTTP_STATUS/200")
                .append("\ndata:").append(JSONUtil.toJsonStr(data))
                .append("\n\n")
                .toString();
    }


    @Override
    public ResponseBodyEmitter processStream(AIRequest request) throws AIServiceException {
        ResponseBodyEmitter emitter = new ResponseBodyEmitter(60000L);
        int timeoutSeconds = Optional.ofNullable(request.getTimeout()).orElse(REQUEST_TIMEOUT_SECONDS);
        String sessionId = initOrGetSession(request);

        processStreamWithCallback(request, new StreamCallback() {
            private long eventId = 1;
            private long outputTokens = 0;

            @Override
            public void onData(String content, String sessionId) {
                try {
                    // 构造 SSE 块
                    String sseBlock = buildSSEBlock(eventId++, request.getRequestId(), sessionId, content, request.getModelName(), ++outputTokens);
                    emitter.send(sseBlock.getBytes(StandardCharsets.UTF_8)); // 使用字节数组保证编码安全
                } catch (IOException e) {
                    log.error("SSE 数据发送失败", e);
                    emitter.completeWithError(e);
                }
            }

            @Override
            public void onComplete(String sessionId, long totalTokens) {
                try {
                    String finalBlock = buildFinalSSEBlock(request.getRequestId(), sessionId, outputTokens);
                    emitter.send(finalBlock.getBytes(StandardCharsets.UTF_8));
                    emitter.complete();
                } catch (IOException e) {
                    log.error("SSE 完成事件发送失败", e);
                    emitter.completeWithError(e);
                }
            }

            @Override
            public void onError(Throwable error, String sessionId) {
                try {
                    String errorEvent = formatSSEEvent(buildErrorEvent(error), "error");
                    emitter.send(errorEvent.getBytes(StandardCharsets.UTF_8));
                    emitter.completeWithError(error);
                } catch (IOException e) {
                    log.error("SSE 错误事件发送失败", e);
                    emitter.completeWithError(e);
                }
            }
        }, timeoutSeconds);

        setupEmitterCallbacks(emitter, null); // 可选：根据需要传入 client
        return emitter;
    }


    private String buildSSEBlock(long eventId, String requestId, String sessionId,
                                 String content, String modelName, long outputTokens) {
        JSONObject data = new JSONObject()
                .set("request_id", requestId)
                .set("output", new JSONObject()
                        .set("text", content)
                        .set("finish_reason", "null")
                        .set("session_id", sessionId))
                .set("usage", new JSONObject()
                        .set("models", Collections.singletonList(
                                new JSONObject()
                                        .set("model_id", modelName)
                                        .set("input_tokens", 0)
                                        .set("output_tokens", outputTokens))));

        return new StringBuilder()
                .append("id:").append(eventId).append("\n")
                .append("event:result\n")
                .append(":HTTP_STATUS/200\n")
                .append("data:").append(data.toString()).append("\n\n")
                .toString();
    }

    private String buildFinalSSEBlock(String requestId, String sessionId, long totalTokens) {
        JSONObject data = new JSONObject()
                .set("request_id", requestId)
                .set("output", new JSONObject()
                        .set("text", "")
                        .set("finish_reason", "stop")
                        .set("session_id", sessionId))
                .set("usage", new JSONObject()
                        .set("models", Collections.singletonList(
                                new JSONObject()
                                        .set("model_id", "qwen-turbo")
                                        .set("input_tokens", 0)
                                        .set("output_tokens", totalTokens))));

        return new StringBuilder()
                .append("id:final\n")
                .append("event:result\n")
                .append(":HTTP_STATUS/200\n")
                .append("data:").append(data.toString()).append("\n\n")
                .toString();
    }


    private String extractEventType(String eventLine) {
        return eventLine.substring("event: ".length()).trim();
    }

    private String extractEventData(String dataLine) {
        return dataLine.substring("data: ".length()).trim();
    }

    private void handleError(ResponseBodyEmitter emitter, CloseableHttpAsyncClient client, Throwable error) {
        try {
            String errorEvent = formatSSEEvent(buildErrorEvent(error), "error");
            sendFormattedEvent(emitter, errorEvent);
            emitter.completeWithError(error);
        } catch (Exception e) {
            log.error("错误事件发送失败", e);
        } finally {
            closeClient(client);
        }
    }
    private void completeRequest(ResponseBodyEmitter emitter, CloseableHttpAsyncClient client) {
        try {
            String sessionId = "sess-" + UUID.randomUUID().toString().substring(0, 8);
            String completeEvent = formatSSEEvent(buildFinalEvent(sessionId), "complete");
            sendFormattedEvent(emitter, completeEvent);
            emitter.complete();
        } catch (Exception e) {
            log.error("完成事件发送失败", e);
        } finally {
            closeClient(client);
        }
    }
    private void setupEmitterCallbacks(ResponseBodyEmitter emitter, CloseableHttpAsyncClient client) {
        emitter.onCompletion(() -> {
            if (!client.isRunning()) {
                closeClient(client);
            }
        });
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时");
            closeClient(client);
            try {
                String timeoutEvent = formatSSEEvent(buildTimeoutEvent(), "error");
                sendFormattedEvent(emitter, timeoutEvent);
                emitter.complete();
            } catch (IOException e) {
                log.error("超时事件发送失败", e);
            }
        });
    }
    private void sendFormattedEvent(ResponseBodyEmitter emitter, String eventStr) throws IOException {
        String[] parts = eventStr.split("\n");
        String eventType = extractEventType(parts[0]);
        String data = extractEventData(parts[1]);
        emitter.send(data);
    }

    private HttpPost buildSSERequest(AIRequest request) throws AIServiceException {
        HttpPost httpPost = new HttpPost(getRequestEndpoint(request));

        httpPost.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + request.getApiKey());
        httpPost.setHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON);
        httpPost.setHeader(ACCEPT_HEADER, TEXT_EVENT_STREAM);
        httpPost.setHeader(X_DASHSCOPE_SSE_HEADER, ENABLE);
        httpPost.setHeader(X_REQUEST_ID_HEADER, UUID.randomUUID().toString());
        Map<String, Object> body = new LinkedHashMap<>();
        body.put(MODEL_KEY, request.getModelName());
        body.put(STREAM_KEY, true);
        List<Map<String, String>> messages = new ArrayList<>();
        for (ChatMessage msg : request.getMessages()) {
            Map<String, String> message = new HashMap<>();
            message.put(ROLE_KEY, msg.getRole().toLowerCase());
            message.put(CONTENT_KEY, msg.getContent());
            messages.add(message);
        }
        body.put(MESSAGES_KEY, messages);

        httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(body), StandardCharsets.UTF_8));
        return httpPost;
    }

    private void processResponse(HttpResponse response,
                                 FluxSink<String> emitter,
                                 AtomicReference<String> sessionId,
                                 AtomicLong messageId,
                                 AIRequest request) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(response.getEntity().getContent()))) {
            AtomicLong eventCounter = new AtomicLong(1);
            String requestId = UUID.randomUUID().toString();
            String fixedSessionId = sessionId.get();
            reader.lines()
                    .filter(line -> line.startsWith(DATA_PREFIX))
                    .forEach(line -> {
                        if (StrUtil.contains(line, DONE_MARKER)) {
                            emitter.next(buildFinalSSEBlock(requestId, fixedSessionId, eventCounter.get()));
                            return;
                        }

                        String content = parseContent(line.substring(DATA_PREFIX.length()).trim());
                        if (content != null) {
                            emitter.next(buildSSEBlock(
                                    eventCounter.getAndIncrement(),
                                    requestId,
                                    fixedSessionId,
                                    content,
                                    request.getModelName(),
                                    messageId.incrementAndGet()
                            ));
                        }
                    });
        } catch (IOException e) {
            log.error("Error reading SSE response for requestId: {}", request.getRequestId(), e);
            emitter.error(e);
        } finally {
            emitter.complete();
        }
    }

    private String parseContent(String line) {
        try {
            JSONObject json = JSONUtil.parseObj(line);
            return json.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("delta")
                    .getStr(CONTENT_KEY);
        } catch (Exception e) {
            log.error("解析内容失败", e);
            return null;
        }
    }



    private String initOrGetSession(AIRequest request) {
        return request.getRequestId() != null ?
                request.getRequestId() :
                "sess-" + UUID.randomUUID().toString().substring(0, 8);
    }

    private Map<String, Object> buildFinalEvent(String sessionId) {
        Map<String, Object> event = new HashMap<>();
        event.put("type", "stream_end");
        event.put("sessionId", sessionId);
        event.put("status", "completed");
        event.put("timestamp", System.currentTimeMillis());
        return event;
    }

    private Map<String, Object> buildErrorEvent(Throwable e) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", 500);
        error.put("message", e.getMessage());
        error.put("timestamp", System.currentTimeMillis());
        return error;
    }

    private Map<String, Object> buildTimeoutEvent() {
        Map<String, Object> timeout = new HashMap<>();
        timeout.put("code", 504);
        timeout.put("message", "Request timeout");
        timeout.put("timestamp", System.currentTimeMillis());
        return timeout;
    }





    @Override
    public FileUploadResponse uploadFile(File file, String purpose, String apiUrl,String apiKey) throws AIServiceException {
        try {
            // 参数校验增强
            validateFileUploadParams(file, purpose);
            if(apiUrl==null){
                apiUrl="https://dashscope.aliyuncs.com/compatible-mode/v1/files";
            }

            CloseableHttpClient client = HttpClients.createDefault(); // 使用同步客户端
            HttpPost httpPost = new HttpPost(apiUrl);

            // 修正后的Multipart构建方式
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addPart("file",
                    new FileBody(file, ContentType.DEFAULT_BINARY, file.getName()));
            builder.addPart("purpose",
                    new StringBody(purpose, ContentType.TEXT_PLAIN));

            httpPost.setEntity(builder.build());
            httpPost.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + apiKey);
            // 同步执行（文件上传通常建议同步）
            HttpResponse response = client.execute(httpPost);
            return parseUploadResponse(response);
        } catch (Exception e) {
            throw handleUploadException(e);
        }
    }

    private void validateFileUploadParams(File file, String purpose) throws AIServiceException {
        // 文件存在性校验
        if (!file.exists()) {
            throw new AIServiceException("文件不存在: " + file.getAbsolutePath());
        }

        // 文件大小校验（500MB限制）
        if (file.length() > 500 * 1024 * 1024) {
            throw new AIServiceException("文件大小超过500MB限制");
        }

        // 用途校验
        if (!Arrays.asList("batch", "file-extract").contains(purpose)) {
            throw new AIServiceException("Invalid purpose值，必须为 'batch' 或 'file-extract'");
        }
    }

    private FileUploadResponse parseUploadResponse(HttpResponse response) throws Exception {
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = JSONUtil.parseObj(responseBody);

        return FileUploadResponse.builder()
                .id(json.getStr("id"))
                .bytes(json.getLong("bytes"))
                .createdAt(json.getLong("created_at"))
                .filename(json.getStr("filename"))
                .object(json.getStr("object"))
                .purpose(json.getStr("purpose"))
                .status(json.getStr("status"))
                .statusDetails(json.getStr("status_details"))
                .build();
    }

    private AIServiceException handleUploadException(Exception e) {
        log.error("文件上传失败: {}", ExceptionUtil.getRootCauseMessage(e));
        return new AIServiceException("文件上传失败: " + ExceptionUtil.getRootCauseMessage(e), e);
    }



    @Override
    public BatchTaskResult createBatchTask(BatchRequest request,String apiUrl, String apiKey) throws AIServiceException {
        try {
            validateBatchRequest(request);
            if(apiUrl==null){
                apiUrl="https://dashscope.aliyuncs.com/compatible-mode/v1/batches";
            }
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(apiUrl);

            httpPost.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + apiKey);
            httpPost.setHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON);

            JSONObject requestBody = buildBatchRequestBody(request);
            httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(requestBody), StandardCharsets.UTF_8));

            HttpResponse response = client.execute(httpPost);
            return parseTaskResultResponse(response);
        } catch (Exception e) {
            log.error("批量任务创建失败: {}", ExceptionUtil.getRootCauseMessage(e));
            throw new AIServiceException("批量任务创建失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BatchTaskResult queryBatchTaskResult(String taskId, String apiUrl, String apiKey) throws AIServiceException {
        try {
            // 参数校验增强
            validateTaskId(taskId);
            if (apiUrl == null) {
                apiUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1/batches/" + taskId;
            }

            CloseableHttpClient client = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(apiUrl);

            httpGet.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + apiKey);
            httpGet.setHeader(ACCEPT_HEADER, APPLICATION_JSON);

            HttpResponse response = client.execute(httpGet);
            return parseTaskResultResponse(response);
        } catch (Exception e) {
            log.error("批量任务查询失败: {}", ExceptionUtil.getRootCauseMessage(e));
            throw new AIServiceException("批量任务查询失败: " + e.getMessage(), e);
        }
    }

    // 新增校验方法
    private void validateTaskId(String taskId) throws AIServiceException {
        if (StrUtil.isBlank(taskId)) {
            throw new AIServiceException("任务ID不能为空");
        }
        if (!taskId.startsWith("batch_")) {
            throw new AIServiceException("非法的任务ID格式，应以batch_开头");
        }
    }

    // 修改后的解析方法
    private BatchTaskResult parseTaskResultResponse(HttpResponse response) throws Exception {
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = JSONUtil.parseObj(responseBody);

        return BatchTaskResult.builder()
                .id(json.getStr("id"))
                .outputFileId(json.getStr("output_file_id"))
                .errorFileId(json.getStr("error_file_id"))
                .completionWindow(json.getStr("completion_window"))
                .status(json.getStr("status"))
                .createdAt(json.getLong("created_at"))
                // 新增时间戳字段
                .inProgressAt(json.getLong("in_progress_at", 0L))
                .expiresAt(json.getLong("expires_at", 0L))
                .finalizingAt(json.getLong("finalizing_at", 0L))
                .completedAt(json.getLong("completed_at", 0L))
                .failedAt(json.getLong("failed_at", 0L))
                .expiredAt(json.getLong("expired_at", 0L))
                .cancellingAt(json.getLong("cancelling_at", 0L))
                .cancelledAt(json.getLong("cancelled_at", 0L))
                // 请求统计
                .requestCounts(parseRequestCounts(json.getJSONObject("request_counts")))
                // 元数据解析增强
                .metadata(parseExtendedMetadata(json.getJSONObject("metadata")))
                .errors(json.getJSONObject("errors"))
                .build();
    }

    // 增强的元数据解析
    private Map<String, String> parseExtendedMetadata(JSONObject metadataJson) {
        Map<String, String> metadata = new HashMap<>();
        if (metadataJson != null) {
            metadataJson.keySet().forEach(key ->
                    metadata.put(key, metadataJson.getStr(key))
            );
        }
        return metadata;
    }

    private void validateBatchRequest(BatchRequest request) throws AIServiceException {
        // 必填字段校验
        if (StrUtil.isEmpty(request.getInputFileId())) {
            throw new AIServiceException("input_file_id不能为空");
        }
        if (StrUtil.isEmpty(request.getEndpoint())) {
            throw new AIServiceException("endpoint不能为空");
        }
        if (StrUtil.isEmpty(request.getCompletionWindow())) {
            throw new AIServiceException("completion_window不能为空");
        }

        // 时间窗口格式校验
        if (!request.getCompletionWindow().matches("^\\d+[hd]$")) {
            throw new AIServiceException("completion_window格式错误，示例：24h 或 14d");
        }

        // 元数据校验
        if (request.getMetadata() != null) {
            if (request.getMetadata().containsKey("ds_name")
                    && request.getMetadata().get("ds_name").length() > 20) {
                throw new AIServiceException("任务名称长度不能超过20个字符");
            }
            if (request.getMetadata().containsKey("ds_description")
                    && request.getMetadata().get("ds_description").length() > 200) {
                throw new AIServiceException("任务描述长度不能超过200个字符");
            }
        }
    }

    private JSONObject buildBatchRequestBody(BatchRequest request) {
        JSONObject body = JSONUtil.createObj()
                .set("input_file_id", request.getInputFileId())
                .set("endpoint", request.getEndpoint())
                .set("completion_window", request.getCompletionWindow());

        if (request.getMetadata() != null && !request.getMetadata().isEmpty()) {
            body.set("metadata", JSONUtil.parseObj(request.getMetadata()));
        }
        return body;
    }



    private Map<String, Integer> parseRequestCounts(JSONObject countsJson) {
        Map<String, Integer> counts = new HashMap<>();
        counts.put("total", countsJson.getInt("total", 0));
        counts.put("completed", countsJson.getInt("completed", 0));
        counts.put("failed", countsJson.getInt("failed", 0));
        return counts;
    }

    private Map<String, String> parseMetadata(JSONObject metadataJson) {
        Map<String, String> metadata = new HashMap<>();
        if (metadataJson != null) {
            metadata.put("ds_name", metadataJson.getStr("ds_name"));
            metadata.put("ds_description", metadataJson.getStr("ds_description"));
        }
        return metadata;
    }


    @Override
    public InputStream downloadBatchResultAsStream(String fileId, String apiUrl, String apiKey)
            throws AIServiceException {

        CloseableHttpClient client = HttpClients.createDefault();
        try {
            validateFileId(fileId);
            if (apiUrl == null) {
                apiUrl = String.format(
                        "https://dashscope.aliyuncs.com/compatible-mode/v1/files/%s/content",
                        fileId
                );
            }

            HttpGet httpGet = new HttpGet(apiUrl);
            httpGet.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + apiKey);

            HttpResponse response = client.execute(httpGet);
            validateResponse(response);
            // 返回流并保持客户端连接（调用者需负责关闭）
            return new ResponseStreamWrapper(response.getEntity().getContent(), client);
        } catch (Exception e) {
            if(client!=null){
                try {
                    client.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            throw handleDownloadException(e);
        }
    }

    @Override
    public void downloadBatchResultToFile(String fileId, String outputPath, String apiUrl, String apiKey)
            throws AIServiceException {

        try (InputStream inputStream = downloadBatchResultAsStream(fileId, apiUrl, apiKey);
             FileOutputStream outputStream = new FileOutputStream(outputPath)) {
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException e) {
            throw new AIServiceException("文件写入失败: " + e.getMessage(), e);
        }
    }

    // 文件ID校验方法
    private void validateFileId(String fileId) throws AIServiceException {
        if (StrUtil.isBlank(fileId)) {
            throw new AIServiceException("文件ID不能为空");
        }
        if (!fileId.startsWith("file-batch_output")) {
            throw new AIServiceException("非法的文件ID格式，应以file-batch_output开头");
        }
    }



    // 下载异常处理方法
    private AIServiceException handleDownloadException(Exception e) {
        String errorMsg = "文件下载失败: ";

        if (e instanceof IOException) {
            errorMsg += "网络通信异常";
        } else if (e instanceof AIServiceException) {
            return (AIServiceException) e;
        } else {
            errorMsg += "未知错误";
        }

        Throwable rootCause = ExceptionUtil.getRootCause(e);
        log.error("{} 根本原因: {}", errorMsg, rootCause.getMessage(), rootCause);

        return new AIServiceException(errorMsg + " - " + rootCause.getMessage(), e);
    }


    // 自定义流包装类（处理资源关闭）
    private static class ResponseStreamWrapper extends InputStream {
        private final InputStream wrappedStream;
        private final CloseableHttpClient client;

        ResponseStreamWrapper(InputStream stream, CloseableHttpClient client) {
            this.wrappedStream = stream;
            this.client = client;
        }

        @Override
        public int read() throws IOException {
            return wrappedStream.read();
        }

        @Override
        public void close() throws IOException {
            try {
                wrappedStream.close();
            } finally {
                if(client!=null)
              client.close();
            }
        }
    }

    // 增强响应校验
    private void validateResponse(HttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            String errorBody = EntityUtils.toString(response.getEntity());
            throw new IOException("HTTP错误码: " + statusCode + " 响应内容: " + errorBody);
        }

        Header contentType = response.getEntity().getContentType();
        if (contentType != null && !contentType.getValue().contains("application/json")) {
            throw new IOException("非预期的内容类型: " + contentType.getValue());
        }
    }

    @Override
    public void processStreamWithCallback(AIRequest request,
                                          StreamCallback callback,
                                          int timeoutSeconds) throws AIServiceException {
        CloseableHttpAsyncClient client = null;
        try {
            client = createHttpClient();
            String sessionId = initOrGetSession(request);
            AtomicLong tokenCounter = new AtomicLong(0);
            AtomicBoolean completed = new AtomicBoolean(false); // 新增原子状态标记

            HttpPost httpPost = buildSSERequest(request);
            httpPost.setHeader(X_REQUEST_ID_HEADER, sessionId);
            CloseableHttpAsyncClient finalClient = client;
            FutureCallback<HttpResponse> futureCallback = new FutureCallback<HttpResponse>() {
                private void safeComplete(Runnable action) {
                    if (completed.compareAndSet(false, true)) { // 原子操作保证只执行一次
                        action.run();
                        closeClient(finalClient);
                    }
                }

                @Override
                public void completed(HttpResponse response) {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.getEntity().getContent()))) {
                        String line = null;
                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith(DATA_PREFIX)) {
                                if (!line.contains(DONE_MARKER)) {
                                    processLine(line, sessionId, tokenCounter, callback);
                                }
                            }
                        }
                        safeComplete(() -> callback.onComplete(sessionId, tokenCounter.get()));
                    } catch (Exception e) {
                        safeComplete(() -> callback.onError(e, sessionId));
                    }
                }

                @Override
                public void failed(Exception ex) {
                    safeComplete(() -> callback.onError(ex, sessionId));
                }

                @Override
                public void cancelled() {
                    safeComplete(() -> callback.onError(new AIServiceException("请求被取消"), sessionId));
                }
            };

            // 超时控制增强
            CloseableHttpAsyncClient finalClient1 = client;
            // 修改后的超时控制部分（约第 896 行）
            if (timeoutSeconds != -1) { // 新增条件判断
                ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
                scheduler.schedule(() -> {
                    if (completed.compareAndSet(false, true)) {
                        callback.onError(new AIServiceException("请求超时 (" + timeoutSeconds + "s)"), sessionId);
                        closeClient(finalClient1);
                    }
                    scheduler.shutdown();
                }, timeoutSeconds, TimeUnit.SECONDS);
            } else {
                log.info("禁用超时控制，请求将无限等待");
            }

            client.execute(httpPost, futureCallback);
        } catch (Exception e) {
            closeClient(client);
            throw new AIServiceException("流式处理启动失败", e);
        }
    }


    // 辅助方法：处理单行数据
    private void processLine(String line,
                             String sessionId,
                             AtomicLong tokenCounter,
                             StreamCallback callback) {
        try {
            log.error("处理单行数据: {}", line);
            JSONObject data = JSONUtil.parseObj(line.substring(DATA_PREFIX.length()));
            JSONArray choices = data.getJSONArray("choices");
            if (choices != null && !choices.isEmpty()) {
                String content = choices.getJSONObject(0)
                        .getJSONObject("delta")
                        .getStr(CONTENT_KEY);
                if (content != null) {
                    // 触发数据回调
                    callback.onData(content, sessionId);
                    try {
                        // 处理最终统计信息
                        if (data.containsKey("usage")) {
                            try {
                                JSONObject usage = data.getJSONObject("usage");
                                if (usage!=null && !usage.isEmpty() && usage.containsKey("models")) {
                                    JSONArray models = usage.getJSONArray("models");
                                    if (models != null && !models.isEmpty()) {
                                        JSONObject firstModel = models.getJSONObject(0);
                                        long tokens = firstModel.getLong("output_tokens", 0L);
                                        tokenCounter.addAndGet(tokens);
                                        log.debug("[Token统计] 会话ID: {} 累计tokens: {}", sessionId, tokenCounter.get());
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("Token统计异常 | 原始数据: {} | 错误: {}", data, e.getMessage());
                            }
                        }


                    }catch(Exception e){
                        log.error("================处理单行数据: {}", line);
                    }


                }
            }
        } catch (Exception e) {
            callback.onError(
                    new AIServiceException("数据解析失败: " + e.getMessage()),
                    sessionId
            );
        }
    }
    // 辅助方法：超时调度
    private void scheduleTimeout(CloseableHttpAsyncClient client,
                                 String sessionId,
                                 StreamCallback callback,
                                 int timeoutSeconds) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.schedule(() -> {
            if (!client.isRunning()) return;

            callback.onError(
                    new AIServiceException("请求超时 (" + timeoutSeconds + "s)"),
                    sessionId
            );
            closeClient(client);
            scheduler.shutdown();
        }, timeoutSeconds, TimeUnit.SECONDS);
    }
    // 辅助方法：统一错误处理
    private void handleError(Throwable error,
                             String sessionId,
                             StreamCallback callback) {
        if (error instanceof TimeoutException) {
            callback.onError(new AIServiceException("请求超时"), sessionId);
        } else {
            callback.onError(
                    new AIServiceException("处理失败: " + error.getMessage()),
                    sessionId
            );
        }
    }
    // 辅助方法：安全关闭客户端
    private static void closeClient(CloseableHttpAsyncClient client) {
        if (client != null && client.isRunning()) {
            try {
                client.close();
            } catch (IOException e) {
                log.warn("关闭HTTP客户端异常: {}", e.getMessage());
            }
        }
    }
}