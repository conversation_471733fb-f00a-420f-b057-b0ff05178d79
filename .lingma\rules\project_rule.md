# 项目规则文件

## 技术栈规范

- JDK版本: 1.8
- 构建工具: Maven
- Spring Boot版本: 2.3.3.RELEASE
- Spring Cloud版本: Hoxton.SR8

## 编码规范

- Java源文件编码: UTF-8
- 代码风格: 遵循阿里巴巴Java开发手册
- 命名规范:
  - 类名使用UpperCamelCase风格
  - 方法名、参数名、成员变量使用lowerCamelCase风格
  - 常量命名全部大写，单词间用下划线隔开

## 依赖管理

- 使用BOM统一管理依赖版本
- 依赖版本在parent pom中统一定义
- 禁止在子模块中直接指定依赖版本号

## 数据库规范

- 数据库连接使用Druid连接池
- SQL编写优先使用MyBatis-Plus
- 禁止在代码中拼接SQL

## ORM框架使用规范

### DAO层实现
- 优先使用financecloud-common-dao框架提供的BaseDao或GenericDao
- 实体类必须使用JPA或MyBatis-Plus注解标识表名和字段映射关系
- 禁止直接使用JDBC API操作数据库

### 实体类设计
- 实体类必须使用@Table注解标识表名
- 字段必须使用@Column注解标识列名
- 主键字段必须使用@Id注解标识
- 使用Lombok的@Data注解简化getter/setter方法

### 查询构建
- 使用LambdaQuery进行类型安全的查询构建
- 复杂查询使用SQL模板引擎
- 禁止在业务代码中直接拼接SQL字符串

### 缓存使用
- 合理使用统一缓存管理器UnifiedCacheManager
- 根据数据特点选择合适的缓存类型和过期策略
- 实体信息使用ENTITY_CACHE缓存类型
- SQL模板使用SQL_TEMPLATE_CACHE缓存类型

### 性能优化
- 批量操作使用框架提供的批量处理方法
- 分页查询必须使用框架内置分页功能
- 避免N+1查询问题，合理使用JOIN查询

## 日志规范

- 使用SLF4J作为日志门面
- 日志输出应包含必要的上下文信息
- 禁止使用System.out.println()输出业务日志

## 安全规范

- 敏感信息必须加密存储
- 接口必须进行参数校验
- 用户权限必须进行服务端校验

## 提交规范

- Git提交信息使用Angular规范
- 每次提交必须包含相关测试
- 禁止提交包含敏感信息的代码