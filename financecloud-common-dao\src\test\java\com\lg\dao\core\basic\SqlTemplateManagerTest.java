package com.lg.dao.core.basic;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.lg.dao.config.LightORMAutoConfiguration;
import com.lg.dao.config.properties.BasicProperties;
import com.lg.dao.core.BaseDao;
import com.lg.dao.core.SqlTemplateManager;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.executor.LightOrmSqlExecutor;
import com.lg.dao.core.template.MybatisTemplateEngine;
import com.lg.dao.helper.DaoHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * SQL模板管理器测试类
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test")
@Sql(scripts = "classpath:data.sql")
@Import(LightORMAutoConfiguration.class)
public class SqlTemplateManagerTest {





    @Autowired
    private SqlTemplateManager sqlTemplateManager;

    private TestDao testDao;

    @Before
    public void setUp() {
//        MockitoAnnotations.initMocks(this);
        
//        // 初始化配置
//        BasicProperties properties = new BasicProperties();
//        BasicProperties.SqlTemplate sqlTemplate = new BasicProperties.SqlTemplate();
//        sqlTemplate.setLocations(Collections.singletonList("classpath:sql/*.sql"));
//        sqlTemplate.setEnableCache(true);
//        sqlTemplate.setCacheSize(100);
//        sqlTemplate.setEnableHotReload(false);
//        properties.setSqlTemplate(sqlTemplate);




        
        // 初始化测试DAO
        testDao = new TestDao();
    }



    @Test
    public void testSqlTemplate() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("userNames", ListUtil.of("张三", "张三丰"));
        JSONArray jsonArray = DaoHelper.getBaseDao().selectJsonArrayByTemplate("user.findByUserNames", params);
        System.err.println(jsonArray);
        params.put("userNames", ListUtil.of("xx", "x"));
        jsonArray = DaoHelper.getBaseDao().selectJsonArrayByTemplate("user.findByUserNames", params);
        System.err.println(jsonArray);
    }

    @Test
    public void testSqlTemplateWithoutGroupName() {
        // 测试不带组名的模板名称
        Map<String, Object> params = new HashMap<>();
        params.put("userNames", ListUtil.of("张三", "张三丰"));
        
        // 直接使用SqlTemplateManager
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams("findByUserNames", params);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getSql());
        assertTrue(result.getSql().contains("user_name in"));
        
        System.out.println("SQL: " + result.getSql());
        System.out.println("Params: " + result.getParams());
    }

    @Test
    public void testIfQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("instanceStatus", "1");
        params.put("title", "1");
        params.put("category", "1");
        params.put("startTime", "1");
        params.put("endTime", "1");
        params.put("assignee_id", "1");
        params.put("userId", "1");
        params.put("nodeType", "1");

        // 直接使用SqlTemplateManager
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams("testIfQuery", params);


        System.out.println("SQL: " + result.getSql());
        System.out.println("Params: " + result.getParams());
    }

    @Test
    public void testWorkflowMapper1() {
        // 创建包含多种类型参数的通用测试数据
        Map<String, Object> params = new HashMap<>();
        // 基本类型参数
        params.put("userId", "test_user_123");
        params.put("tenantId", 1001L);
        params.put("status", 1);
        params.put("level", 3);
        params.put("nodeType", "approval");
        params.put("organizationType", 2);
        params.put("organizationId", 2001);
        // 集合类型参数
        params.put("list", Arrays.asList("PENDING", "COMPLETED"));
        params.put("userNames", Arrays.asList("PENDING", "COMPLETED"));
        params.put("ids", Arrays.asList(101L, 102L, 103L));
        params.put("positionList", Arrays.asList(3001, 3002));
        // 日期类型参数
        params.put("startTime", new Date());
        params.put("endTime", new Date(System.currentTimeMillis() + 86400000));
        // 字符串参数
        params.put("userName", "test_user");
        params.put("realName", "测试用户");
        params.put("mobile", "13800138000");
        params.put("title", "测试标题");
        params.put("category", "finance");

        Map<String, String> templateCache = sqlTemplateManager.getTemplateCache();
        int total = templateCache.size();
        int successCount = 0;
        int errorCount = 0;

        // 新增性能统计变量
        long totalRenderTime = 0;  // 总渲染时间（纳秒）
        long maxTime = Long.MIN_VALUE; // 单次最大耗时
        long minTime = Long.MAX_VALUE; // 单次最小耗时

        for(int i=0;i<100;i++) {
            for (String key : templateCache.keySet()) {
                long startTime = System.nanoTime(); // 记录开始时间

                try {
                    MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(key, params);
                    successCount++;
                } catch (Exception e) {
                    System.err.println("模板渲染失败: " + key);
                    e.printStackTrace();
                    errorCount++;
                } finally {
                    long duration = System.nanoTime() - startTime; // 计算单次耗时
                    totalRenderTime += duration;

                    // 更新最大最小值
                    if(duration > maxTime) maxTime = duration;
                    if(duration < minTime) minTime = duration;
                }
            }
        }

        // 打印性能报告
        System.out.println("\n性能测试报告 =======================");
        System.out.println("总渲染次数: " + (successCount + errorCount));
        System.out.println("成功次数: " + successCount);
        System.out.println("失败次数: " + errorCount);
        System.out.println("总耗时: " + (totalRenderTime / 1_000_000) + " ms");
        System.out.println("平均单次耗时: " + (totalRenderTime / (successCount + errorCount)) + " ns");
        System.out.println("单次最大耗时: " + maxTime + " ns");
        System.out.println("单次最小耗时: " + minTime + " ns");
        System.out.println("===================================");
    }
    @Test
    public void testWorkflowMapper() {
        // 创建包含多种类型参数的通用测试数据
        Map<String, Object> params = new HashMap<>();
        // 基本类型参数
        params.put("userId", "test_user_123");
        params.put("tenantId", 1001L);
        params.put("status", 1);
        params.put("level", 3);
        params.put("nodeType", "approval");
        params.put("organizationType", 2);
        params.put("organizationId", 2001);
        // 集合类型参数
        params.put("list", Arrays.asList("PENDING", "COMPLETED"));
        params.put("userNames", Arrays.asList("PENDING", "COMPLETED"));
        params.put("ids", Arrays.asList(101L, 102L, 103L));
        params.put("positionList", Arrays.asList(3001, 3002));
        // 日期类型参数
        params.put("startTime", new Date());
        params.put("endTime", new Date(System.currentTimeMillis() + 86400000));
        // 字符串参数
        params.put("userName", "test_user");
        params.put("realName", "测试用户");
        params.put("mobile", "13800138000");
        params.put("title", "测试标题");
        params.put("category", "finance");
        
        Map<String, String> templateCache = sqlTemplateManager.getTemplateCache();
        int total = templateCache.size();
        int successCount = 0;
        int errorCount = 0;

        // 循环测试  打印出耗时
        for(int i=0;i<100;i++) {
            for (String key : templateCache.keySet()) {
                try {
                    MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(key, params);

                    successCount++;
                } catch (Exception e) {
                    System.err.println("模板渲染失败: " + key);
                    e.printStackTrace();
                    errorCount++;
                }
            }
        }
    }


    @Test
    public void testForeachTagHandling() {
        // 准备一个带有foreach标签的SQL模板
        String sqlTemplate = "SELECT * FROM t_user WHERE user_name in\n" +
                             "    <foreach collection=\"userNames\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
                             "        #{item}\n" +
                             "    </foreach>";
        
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("userNames", ListUtil.of("张三", "张三丰"));
        
        // 直接使用模板引擎渲染
        MybatisTemplateEngine engine = new MybatisTemplateEngine();
        MybatisTemplateEngine.RenderResult result = engine.renderWithParams(sqlTemplate, params);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getSql());
        assertTrue(result.getSql().contains("?"));
        assertEquals(2, result.getParams().size());
        
        System.out.println("渲染后的SQL: " + result.getSql());
        System.out.println("参数列表: " + result.getParams());
    }

    /**
     * 测试用的DAO类
     */
    private static class TestDao extends BaseDao {
        public TestEntity findById(Long id, String tenantId) {
            Map<String, Object> params = new HashMap<>();
            params.put("id", id);
            params.put("tenantId", tenantId);
            return selectOneByTemplate(TestEntity.class, "user.findById", params);
        }

        public List<TestEntity> findByName(String name, String tenantId) {
            Map<String, Object> params = new HashMap<>();
            params.put("userName", name);
            params.put("tenantId", tenantId);
            return selectListByTemplate(TestEntity.class, "user.findByUserName", params);
        }
    }

    /**
     * 测试用的实体类
     */
    private static class TestEntity {
        private Long id;
        private String name;
        private String tenantId;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }
    }

    /**
     * 测试模板SQL缓存功能
     */
    @Test
    public void testTemplateCacheFunction() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("instanceStatus", "1");
        params.put("title", "测试标题");
        params.put("category", "测试分类");

        // 第一次调用，应该执行解析并缓存
        MybatisTemplateEngine.RenderResult result1 = sqlTemplateManager.getSqlWithParams("testIfQuery", params);
        assertNotNull("第一次渲染结果不应为空", result1);
        assertNotNull("第一次渲染的SQL不应为空", result1.getSql());

        // 第二次调用，应该从缓存获取
        MybatisTemplateEngine.RenderResult result2 = sqlTemplateManager.getSqlWithParams("testIfQuery", params);
        assertNotNull("第二次渲染结果不应为空", result2);
        assertNotNull("第二次渲染的SQL不应为空", result2.getSql());

        // 验证结果一致性
        assertEquals("缓存的 SQL 应该一致", result1.getSql(), result2.getSql());
        assertEquals("缓存的参数数量应该一致", result1.getParams().size(), result2.getParams().size());

        System.out.println("模板缓存测试通过，SQL: " + result1.getSql());
    }

    /**
     * 测试集合参数不缓存
     */
    @Test
    public void testCollectionParamNoCache() {
        // 准备包含集合的参数
        Map<String, Object> params = new HashMap<>();
        params.put("userNames", ListUtil.of("张三", "李四", "王五"));

        // 多次调用，每次都应该重新解析（因为包含集合参数）
        MybatisTemplateEngine.RenderResult result1 = sqlTemplateManager.getSqlWithParams("findByUserNames", params);
        MybatisTemplateEngine.RenderResult result2 = sqlTemplateManager.getSqlWithParams("findByUserNames", params);

        assertNotNull("第一次渲染结果不应为空", result1);
        assertNotNull("第二次渲染结果不应为空", result2);

        // 虽然不缓存，但结果应该一致
        assertEquals("SQL 应该一致", result1.getSql(), result2.getSql());

        System.out.println("集合参数不缓存测试通过，SQL: " + result1.getSql());
    }

    /**
     * 测试模板SQL性能对比（有缓存 vs 无缓存）
     */
    @Test
    public void testTemplatePerformanceComparison() {
        // 创建不带缓存的模板管理器
        BasicProperties properties = new BasicProperties();
        BasicProperties.SqlTemplate sqlTemplate = new BasicProperties.SqlTemplate();
        sqlTemplate.setLocations(Collections.singletonList("classpath:sql/*.sql"));
        properties.setSqlTemplate(sqlTemplate);

        SqlTemplateManager noCacheManager = new SqlTemplateManager(properties);

        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("instanceStatus", "1");
        params.put("title", "测试标题");

        int iterations = 500;

        // 测试无缓存性能
        long startTime1 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            MybatisTemplateEngine.RenderResult result = noCacheManager.getSqlWithParams("testIfQuery", params);
            assertNotNull(result);
        }
        long endTime1 = System.nanoTime();
        long noCacheTime = endTime1 - startTime1;

        // 预热缓存
        sqlTemplateManager.getSqlWithParams("testIfQuery", params);

        // 测试有缓存性能
        long startTime2 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams("testIfQuery", params);
            assertNotNull(result);
        }
        long endTime2 = System.nanoTime();
        long cacheTime = endTime2 - startTime2;

        System.out.println("模板SQL性能对比测试:");
        System.out.println("无缓存总时间: " + noCacheTime / 1_000_000 + " ms");
        System.out.println("有缓存总时间: " + cacheTime / 1_000_000 + " ms");
        System.out.println("性能提升: " + (noCacheTime / (double) cacheTime) + "x");

        // 缓存应该显著提升性能
        assertTrue("缓存应该提升性能", cacheTime < noCacheTime);
    }
}