# EntityInfo 单一缓存优化

## 📋 核心理念

**保持一份缓存，避免重复解析**

## 🎯 问题分析

### 原始问题
- **重复解析**：每次调用 `EntityInfo.of()` 都会重新解析注解和反射
- **性能浪费**：相同的实体类被重复解析多次
- **内存浪费**：可能在多个地方缓存同一个 EntityInfo

### 错误的优化方向
之前尝试的多层缓存策略：
```java
// ❌ 错误：复杂的多层缓存
if (globalCacheManager != null) {
    return globalCacheManager.get(...);
}
return LOCAL_CACHE.computeIfAbsent(...);
```

这种方式反而增加了开销，违背了"保持一份缓存"的原则。

## 🔧 正确的优化方案

### 核心实现
```java
/**
 * 统一获取实体信息的方法
 * 策略：只维护一份缓存，避免重复解析
 */
public EntityInfo getEntityInfo(Class<?> entityClass) {
    if (entityClass == null) {
        throw new IllegalArgumentException("Entity class cannot be null");
    }
    
    // 使用 computeIfAbsent 确保只解析一次，线程安全
    return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
}
```

### 设计原则

#### 1. 单一缓存
- **只有一个缓存**：`ConcurrentHashMap<Class<?>, EntityInfo>`
- **全局共享**：所有组件共享同一个缓存
- **线程安全**：`ConcurrentHashMap` 保证并发安全

#### 2. 避免重复解析
- **computeIfAbsent**：确保每个实体类只解析一次
- **原子操作**：多线程环境下也只解析一次
- **高效访问**：后续访问直接从缓存获取

#### 3. 简化设计
- **移除复杂逻辑**：不再使用 UnifiedCacheManager
- **减少开销**：没有额外的缓存管理开销
- **保持兼容**：API 保持不变

## 📊 性能对比

### 测试策略
```java
@Test
public void testPerformanceComparison() {
    EntityInfoManager manager = EntityInfoManager.getInstance();
    
    // 第一次调用 - 会解析并缓存
    EntityInfo firstResult = manager.getEntityInfo(TestUser.class);
    
    // 后续调用 - 直接从缓存获取
    for (int i = 0; i < 10000; i++) {
        EntityInfo cachedResult = manager.getEntityInfo(TestUser.class);
        assertSame(firstResult, cachedResult); // 同一个对象
    }
    
    // 对比：每次都重新解析
    for (int i = 0; i < 10000; i++) {
        EntityInfo.of(TestUser.class); // 每次都重新解析
    }
}
```

### 预期效果
- **首次调用**：正常的解析时间
- **缓存调用**：极快的 HashMap 查找时间
- **重复解析**：每次都要执行反射和注解解析
- **性能提升**：10-100倍的性能提升（取决于实体复杂度）

## 🚀 优化效果

### 1. 性能提升
- **避免重复解析**：每个实体类只解析一次
- **高效缓存访问**：`ConcurrentHashMap.get()` 极快
- **内存友好**：每个 EntityInfo 只存储一份

### 2. 代码简化
```java
// 优化前：复杂的多层缓存逻辑
public EntityInfo getEntityInfo(Class<?> entityClass) {
    if (globalCacheManager != null) {
        String cacheKey = entityClass.getName();
        return globalCacheManager.get(
            UnifiedCacheManager.CacheType.ENTITY_CACHE,
            cacheKey,
            () -> EntityInfo.of(entityClass)
        );
    }
    return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
}

// 优化后：简洁的单一缓存
public EntityInfo getEntityInfo(Class<?> entityClass) {
    if (entityClass == null) {
        throw new IllegalArgumentException("Entity class cannot be null");
    }
    return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
}
```

### 3. 线程安全
- **ConcurrentHashMap**：高效的并发访问
- **computeIfAbsent**：原子操作，避免重复计算
- **无锁设计**：没有额外的同步开销

## 📝 使用示例

### 基本使用
```java
// 获取实体信息（第一次会解析，后续从缓存获取）
EntityInfoManager manager = EntityInfoManager.getInstance();
EntityInfo entityInfo = manager.getEntityInfo(User.class);

// 多次调用返回同一个对象
EntityInfo entityInfo2 = manager.getEntityInfo(User.class);
assert entityInfo == entityInfo2; // 同一个对象
```

### 缓存管理
```java
EntityInfoManager manager = EntityInfoManager.getInstance();

// 查看缓存统计
String stats = manager.getCacheStats();
System.out.println(stats);

// 清除指定实体缓存
manager.evictEntityInfo(User.class);

// 清除所有缓存
manager.evictAllEntityInfo();
```

### 多线程使用
```java
// 多线程环境下安全使用
ExecutorService executor = Executors.newFixedThreadPool(10);
for (int i = 0; i < 100; i++) {
    executor.submit(() -> {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        EntityInfo entityInfo = manager.getEntityInfo(User.class);
        // 所有线程获取到的都是同一个对象
    });
}
```

## 🎯 核心优势

### 1. 真正避免重复解析
- **一次解析，永久缓存**：每个实体类只解析一次
- **对象复用**：返回同一个 EntityInfo 对象
- **内存高效**：没有重复的 EntityInfo 实例

### 2. 极简设计
- **单一职责**：只做缓存，不做其他复杂逻辑
- **高性能**：`ConcurrentHashMap` 的原生性能
- **易维护**：代码简洁，逻辑清晰

### 3. 完全兼容
- **API 不变**：所有现有代码无需修改
- **功能完整**：保留所有缓存管理功能
- **平滑升级**：无感知的性能提升

## 📋 总结

通过采用"保持一份缓存，避免重复解析"的策略：

1. **彻底解决重复解析问题**：每个实体类只解析一次
2. **极大简化代码逻辑**：从复杂的多层缓存简化为单一缓存
3. **显著提升性能**：预期 10-100倍的性能提升
4. **保持完全兼容**：API 和功能保持不变
5. **线程安全保证**：`ConcurrentHashMap` 的高效并发

这是真正符合"避免重复解析，保持一份缓存"理念的优化方案。
