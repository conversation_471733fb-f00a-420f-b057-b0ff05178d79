package com.lg.dao.example;

import com.lg.dao.core.BaseDao;
import com.lg.dao.helper.DaoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 灵活查询示例 - 演示无实体查询返回Map的功能
 */
@Slf4j
@Component
public class FlexibleQueryExample {

    /**
     * 示例1：使用直接SQL进行无实体查询
     */
    public void example1_DirectSqlQuery() {
        log.info("=== 示例1：直接SQL查询返回Map ===");
        
        // 获取无泛型的BaseDao
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 查询用户统计信息
        String sql = "SELECT COUNT(*) as user_count, MAX(create_time) as latest_time FROM user WHERE status = ?";
        Map<String, Object> result = baseDao.selectOneMap(sql, 1);
        
        log.info("查询结果: {}", result);
        // 输出: {user_count=100, latest_time=2024-01-01 12:00:00}
    }
    
    /**
     * 示例2：查询多行数据返回Map列表
     */
    public void example2_MultiRowQuery() {
        log.info("=== 示例2：多行查询返回Map列表 ===");
        
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 查询部门统计
        String sql = "SELECT dept_name, COUNT(*) as emp_count, AVG(salary) as avg_salary " +
                    "FROM employee e JOIN department d ON e.dept_id = d.id " +
                    "WHERE e.status = ? GROUP BY dept_name";
        
        List<Map<String, Object>> results = baseDao.selectMapList(sql, "ACTIVE");
        
        log.info("查询到 {} 个部门", results.size());
        for (Map<String, Object> row : results) {
            log.info("部门: {}, 员工数: {}, 平均薪资: {}", 
                    row.get("dept_name"), row.get("emp_count"), row.get("avg_salary"));
        }
    }
    
    /**
     * 示例3：使用SQL模板进行无实体查询
     */
    public void example3_TemplateQuery() {
        log.info("=== 示例3：SQL模板查询 ===");
        
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 准备模板参数
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", "2024-01-01");
        params.put("endDate", "2024-12-31");
        params.put("status", "COMPLETED");
        
        // 使用模板查询订单统计
        List<Map<String, Object>> results = baseDao.selectMapListByTemplate("orderStatistics", params);
        
        log.info("订单统计结果: {}", results);
    }
    
    /**
     * 示例4：复杂聚合查询
     */
    public void example4_ComplexAggregation() {
        log.info("=== 示例4：复杂聚合查询 ===");
        
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 复杂的销售数据分析
        String sql = "  SELECT \n" +
                "                DATE_FORMAT(order_date, '%Y-%m') as month,\n" +
                "                COUNT(*) as order_count,\n" +
                "                SUM(total_amount) as total_sales,\n" +
                "                AVG(total_amount) as avg_order_value,\n" +
                "                COUNT(DISTINCT customer_id) as unique_customers\n" +
                "            FROM orders \n" +
                "            WHERE order_date >= ? AND order_date <= ? AND status = ?\n" +
                "            GROUP BY DATE_FORMAT(order_date, '%Y-%m')\n" +
                "            ORDER BY month";
        
        List<Map<String, Object>> monthlyStats = baseDao.selectMapList(sql, 
                "2024-01-01", "2024-12-31", "COMPLETED");
        
        log.info("月度销售统计:");
        for (Map<String, Object> stat : monthlyStats) {
            log.info("月份: {}, 订单数: {}, 总销售额: {}, 平均订单价值: {}, 独立客户数: {}",
                    stat.get("month"), stat.get("order_count"), stat.get("total_sales"),
                    stat.get("avg_order_value"), stat.get("unique_customers"));
        }
    }
    
    /**
     * 示例5：动态查询构建
     */
    public void example5_DynamicQuery() {
        log.info("=== 示例5：动态查询构建 ===");
        
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 动态构建查询条件
        StringBuilder sql = new StringBuilder("SELECT * FROM product WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        String category = "Electronics";
        Double minPrice = 100.0;
        String brand = "Apple";
        
        if (category != null) {
            sql.append(" AND category = ?");
            params.add(category);
        }
        
        if (minPrice != null) {
            sql.append(" AND price >= ?");
            params.add(minPrice);
        }
        
        if (brand != null) {
            sql.append(" AND brand = ?");
            params.add(brand);
        }
        
        sql.append(" ORDER BY price DESC LIMIT 10");
        
        List<Map<String, Object>> products = baseDao.selectMapList(sql.toString(), params.toArray());
        
        log.info("找到 {} 个符合条件的产品", products.size());
        for (Map<String, Object> product : products) {
            log.info("产品: {}, 价格: {}, 品牌: {}", 
                    product.get("name"), product.get("price"), product.get("brand"));
        }
    }
    
    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        try {
            example1_DirectSqlQuery();
            example2_MultiRowQuery();
            example3_TemplateQuery();
            example4_ComplexAggregation();
            example5_DynamicQuery();
        } catch (Exception e) {
            log.error("运行示例时出错", e);
        }
    }
}