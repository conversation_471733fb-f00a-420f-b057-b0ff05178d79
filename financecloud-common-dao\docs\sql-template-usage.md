# SQL模板使用指南

本文档介绍如何在FinanceCloud框架中使用SQL模板功能，该功能允许开发者在外部文件中定义和管理SQL语句，实现SQL与代码的分离，提高代码的可维护性。

## 功能特点

- **SQL与代码分离**：所有SQL语句统一存放在`.sql`文件中，方便维护和管理
- **支持动态SQL**：基于MyBatis的语法，支持动态SQL条件、循环等高级功能
- **热加载支持**：开发环境下支持SQL文件的热加载，修改后自动生效
- **性能优化**：内置缓存机制，提高SQL解析和执行效率
- **增强的注释支持**：支持SQL注释，不会干扰XML解析
- **XML特殊字符处理**：智能处理XML特殊字符，避免解析错误
- **高性能解析**：优化的SQL文件解析算法，提高加载和解析速度
- **批量处理支持**：支持批量解析和渲染SQL模板，显著提高性能

## 使用方法

### 1. 配置SQL模板位置

在`application.yml`中配置SQL模板文件的位置：

```yaml
finance:
  dao:
    sql-template:
      locations:
        - classpath:sql/*.sql             # 加载classpath下sql目录的所有.sql文件
        - package:com.lg.app.repository   # 加载指定包路径下的所有.sql文件
      enable-cache: true                  # 启用缓存
      cache-size: 100                     # 缓存大小
      enable-hot-reload: true             # 开发环境启用热加载
      reload-interval: 5                  # 热加载检查间隔（秒）
```

### 2. 编写SQL模板文件

SQL模板文件采用简单的格式，以`#`开头的行定义SQL标识符，之后的内容为SQL语句：

```sql
#findById
SELECT 
    id,
    user_name,
    real_name,
    mobile,
    email,
    status,
    tenant_id,
    create_time,
    update_time
FROM sys_user 
WHERE id = #{id}
    AND tenant_id = #{tenantId}
    AND deleted = 0

#findByUserName
SELECT 
    *
FROM t_user
WHERE user_name = #{userName}
    AND tenant_id = #{tenantId}
```

### 3. 在DAO中使用SQL模板

在DAO类中使用SQL模板：

```java
public class UserDao extends BaseDao {
    
    public User findById(Long id, String tenantId) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("tenantId", tenantId);
        return selectOneByTemplate(User.class, "user.findById", params);
    }
    
    public List<User> findByName(String userName, String tenantId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        params.put("tenantId", tenantId);
        return selectListByTemplate(User.class, "user.findByUserName", params);
    }
}
```

### 4. 批量处理SQL模板

对于需要一次性处理多个SQL模板的场景，可以使用批量处理API：

```java
public class BatchUserDao extends BaseDao {
    
    public Map<String, List<User>> batchFindUsers(List<Long> userIds, String tenantId) {
        // 准备模板名称列表
        List<String> templateNames = Arrays.asList("user.findById", "user.findByDeptId", "user.findByRoleId");
        
        // 准备参数映射
        Map<String, Map<String, Object>> paramMaps = new HashMap<>();
        paramMaps.put("user.findById", Collections.singletonMap("ids", userIds));
        paramMaps.put("user.findByDeptId", Collections.singletonMap("tenantId", tenantId));
        paramMaps.put("user.findByRoleId", Collections.singletonMap("tenantId", tenantId));
        
        // 批量执行SQL并获取结果
        Map<String, List<User>> results = new HashMap<>();
        Map<String, MybatisTemplateEngine.RenderResult> sqlResults = 
            sqlTemplateManager.batchGetSqlWithParams(templateNames, paramMaps);
        
        // 处理每个SQL的结果
        for (Map.Entry<String, MybatisTemplateEngine.RenderResult> entry : sqlResults.entrySet()) {
            String templateName = entry.getKey();
            MybatisTemplateEngine.RenderResult result = entry.getValue();
            
            // 执行SQL并获取结果
            List<User> users = executeQuery(result.getSql(), result.getParams(), User.class);
            results.put(templateName, users);
        }
        
        return results;
    }
}
```

## 高级特性

### 动态SQL支持

SQL模板支持MyBatis风格的动态SQL标签：

```sql
#findPage
SELECT 
    u.id,
    u.user_name,
    u.real_name
FROM sys_user u
WHERE u.deleted = 0 
    AND u.tenant_id = #{tenantId}
    <if test="userName != null and userName != ''">
    AND u.user_name LIKE CONCAT('%', #{userName}, '%')
    </if>
    <if test="status != null">
    AND u.status = #{status}
    </if>
    <if test="deptIds != null and deptIds.size() > 0">
    AND u.dept_id IN 
    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
        #{deptId}
    </foreach>
    </if>
ORDER BY u.create_time DESC
```

### 注释支持

SQL模板支持SQL风格的注释，不会干扰XML解析：

```sql
#findWithComments
SELECT 
    id,
    user_name,
    -- 这是一个行注释，不会影响SQL执行
    real_name,
    /* 
     * 这是一个块注释
     * 支持多行
     */
    status
FROM sys_user
WHERE id = #{id}
```

### 处理XML特殊字符

在SQL中使用XML特殊字符时，系统会自动处理，避免XML解析错误：

```sql
#findWithSpecialChars
SELECT 
    id,
    user_name
FROM sys_user
WHERE status < #{status} -- 使用 < 符号不会引起XML解析错误
    AND role > #{role}   -- 使用 > 符号不会引起XML解析错误
```

### 复杂的IN条件

使用`foreach`标签处理IN条件：

```sql
#findByIds
SELECT 
    id,
    user_name
FROM sys_user 
WHERE id IN 
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND tenant_id = #{tenantId}
```

## 性能优化

SQL模板系统内置了多重性能优化机制，确保高效处理大量SQL模板：

### 1. 整体文件处理

系统一次性处理整个SQL文件，而不是每个SQL语句单独解析，大幅提高了解析速度：

```java
// 旧的逐条解析方式
for (String sqlId : sqlIds) {
    String sql = parseSql(sqlId, content);  // 每个SQL都单独解析一次
    cache.put(sqlId, sql);
}

// 新的整体解析方式
String preprocessed = preprocessFile(content);  // 整个文件只预处理一次
for (String sqlId : extractSqlIds(preprocessed)) {
    cache.put(sqlId, sqlId);  // 直接存储，无需重复处理
}
```

### 2. 批量SQL解析

支持将多个SQL语句组合成一个XML文档，一次性解析，避免重复解析开销：

```java
// 单个SQL解析
String sql1 = getSql("user.findById", params1);  // 每次调用都需要创建XML文档和解析
String sql2 = getSql("user.findByName", params2);
String sql3 = getSql("user.findByRole", params3);

// 批量SQL解析
List<String> templateNames = Arrays.asList("user.findById", "user.findByName", "user.findByRole");
Map<String, String> results = batchGetSql(templateNames, paramMaps);  // 一次创建XML文档和解析
String sql1 = results.get("user.findById");
String sql2 = results.get("user.findByName");
String sql3 = results.get("user.findByRole");
```

### 3. 配置共享与缓存

使用共享配置对象和缓存，避免重复创建资源：

```java
// 共享的MyBatis配置对象
private static final Configuration SHARED_CONFIGURATION = new Configuration();

// 渲染结果缓存
private final Cache<String, String> renderedCache;
```

### 4. 性能监控

内置性能监控，自动记录各个处理阶段的耗时统计：

```
SQL Template Engine Performance: {
  "render": 3.2ms, 
  "preprocessTemplate": 0.8ms, 
  "parseSql": 2.1ms, 
  "postprocessResult": 0.3ms,
  "batchParseSql": 5.6ms
}
```

### 5. 优化资源管理

改进的资源管理确保所有资源正确关闭：

```java
InputStream inputStream = null;
try {
    // 处理逻辑
} finally {
    IoUtil.close(inputStream);  // 确保资源释放
}
```

## 最佳实践

1. **SQL文件组织**：按模块或表名组织SQL文件，如`user.sql`、`order.sql`等
2. **SQL命名规范**：使用有意义的名称，如`findById`、`updateStatus`等
3. **使用注释**：在SQL中添加注释，说明SQL的用途和参数含义
4. **缓存配置**：在生产环境启用缓存，提高性能
5. **热加载配置**：仅在开发环境启用热加载，避免影响生产性能
6. **SQL文件大小**：将相关SQL语句放在同一文件中，减少文件数量，提高解析效率
7. **定期清理**：定期清理不再使用的SQL语句，避免累积过多冗余SQL
8. **批量处理**：对于需要一次执行多个SQL的场景，使用批量API提高性能

## 常见问题

### 1. SQL模板未找到

错误信息：`SQL template not found: xxx`

解决方法：
- 检查SQL文件是否放在正确的位置
- 检查SQL标识符是否正确
- 检查配置中的`locations`是否正确

### 2. XML解析错误

错误信息：`XML parsing error`

解决方法：
- 检查SQL中是否有未处理的XML特殊字符（如`<`、`>`等）
- 使用最新版本的框架，支持自动处理XML特殊字符

### 3. 参数未找到

错误信息：`Parameter 'xxx' not found`

解决方法：
- 检查传入的参数名是否与SQL模板中的参数名一致
- 检查参数名的大小写是否一致（区分大小写）

### 4. 性能问题

问题：SQL模板解析速度慢

解决方法：
- 启用缓存
- 合并小型SQL文件为较大的文件
- 关闭开发环境中的热加载
- 升级到最新版本，使用优化后的解析算法
- 使用批量API处理多个SQL

## 参考资料

- [MyBatis动态SQL](https://mybatis.org/mybatis-3/zh/dynamic-sql.html)
- [FinanceCloud框架文档](https://financecloud.example.com/docs)

## 版本历史

- **1.3.0**：添加批量SQL解析功能
- **1.2.0**：添加性能优化和监控
- **1.1.0**：增强注释支持和XML特殊字符处理
- **1.0.0**：初始版本，基本SQL模板功能 