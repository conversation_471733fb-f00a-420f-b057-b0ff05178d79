# EntityRowMapper普通VO对象支持文档

## 概述

EntityRowMapper完全支持普通VO对象（Plain Old Java Objects）的映射，无需任何JPA或MyBatis-Plus注解。这使得它非常适合用于查询结果映射、统计报表、API响应对象等场景。

## 核心特性

### 1. 零注解支持
- ✅ 无需@Entity、@Table、@Column等JPA注解
- ✅ 无需@TableName、@TableField等MyBatis-Plus注解
- ✅ 纯POJO类即可使用

### 2. 智能字段映射
- ✅ 自动驼峰转下划线：`userName` -> `user_name`
- ✅ 布尔字段映射：`isActive` -> `is_active`
- ✅ 复合词映射：`phoneNumber` -> `phone_number`

### 3. 全类型支持
- ✅ 基本类型：int, long, boolean, double等
- ✅ 包装类型：Integer, Long, Boolean, Double等
- ✅ 字符串类型：String
- ✅ 日期时间：Date, LocalDateTime, LocalDate等
- ✅ 数值类型：BigDecimal, BigInteger等
- ✅ 枚举类型：Enum

## 使用示例

### 基本VO类定义
```java
@Data
public class UserVo {
    private Long userId;           // 映射到 user_id
    private String userName;       // 映射到 user_name
    private String email;          // 映射到 email
    private Integer age;           // 映射到 age
    private Boolean isActive;      // 映射到 is_active
    private Date createTime;       // 映射到 create_time
    private BigDecimal salary;     // 映射到 salary
}
```

### 使用EntityRowMapper
```java
// 创建行映射器
EntityRowMapper<UserVo> rowMapper = new EntityRowMapper<>(UserVo.class);

// 在查询中使用
List<UserVo> users = jdbcTemplate.query(
    "SELECT user_id, user_name, email, age, is_active, create_time, salary FROM users",
    rowMapper
);
```

### 复杂查询结果映射
```java
@Data
public class StatisticsVo {
    private String department;
    private Integer employeeCount;
    private BigDecimal totalSalary;
    private BigDecimal averageSalary;
    private Date statisticsDate;
}

// 统计查询
String sql = """
    SELECT 
        department,
        COUNT(*) as employee_count,
        SUM(salary) as total_salary,
        AVG(salary) as average_salary,
        CURRENT_DATE as statistics_date
    FROM employees 
    GROUP BY department
    """;

List<StatisticsVo> statistics = jdbcTemplate.query(sql, 
    new EntityRowMapper<>(StatisticsVo.class));
```

## 字段映射规则

### 命名转换规则
| Java字段名 | 数据库列名 | 说明 |
|-----------|-----------|------|
| `id` | `id` | 简单字段直接映射 |
| `userId` | `user_id` | 驼峰转下划线 |
| `userName` | `user_name` | 驼峰转下划线 |
| `isActive` | `is_active` | 布尔字段保持is前缀 |
| `phoneNumber` | `phone_number` | 复合词转换 |
| `createTime` | `create_time` | 时间字段转换 |

### 类型转换支持
- **自动类型转换**：使用Hutool的Convert工具进行智能类型转换
- **日期时间处理**：支持Date、LocalDateTime等多种时间类型
- **数值精度**：BigDecimal保持精度不丢失
- **布尔值处理**：支持数据库的0/1、true/false等格式

## 性能优势

### 1. 预构建映射表
- 启动时构建字段映射关系
- 运行时O(1)复杂度查找
- 避免重复反射调用

### 2. PropertyDescriptor缓存
- 延迟初始化PropertyDescriptor
- 缓存setter/getter方法
- 减少反射开销

### 3. 智能匹配策略
```java
// 支持多种列名格式的智能匹配
EntityInfo.FieldInfo field = entityInfo.findFieldInfoByColumn("user_name");  // ✅
EntityInfo.FieldInfo field = entityInfo.findFieldInfoByColumn("USER_NAME");  // ✅
EntityInfo.FieldInfo field = entityInfo.findFieldInfoByColumn("userName");   // ✅
```

## 适用场景

### 1. 查询结果映射
```java
// 复杂查询结果直接映射到VO
@Data
public class OrderDetailVo {
    private Long orderId;
    private String customerName;
    private String productName;
    private BigDecimal totalAmount;
    private Date orderDate;
}
```

### 2. 统计报表
```java
// 统计查询结果映射
@Data
public class SalesReportVo {
    private String salesPerson;
    private Integer orderCount;
    private BigDecimal totalRevenue;
    private BigDecimal averageOrderValue;
}
```

### 3. API响应对象
```java
// API返回的数据传输对象
@Data
public class UserProfileVo {
    private Long userId;
    private String userName;
    private String email;
    private String department;
    private String position;
}
```

### 4. 多表关联查询
```java
// 关联查询结果映射
@Data
public class EmployeeDetailVo {
    private Long employeeId;
    private String employeeName;
    private String departmentName;
    private String positionTitle;
    private BigDecimal currentSalary;
    private Date hireDate;
}
```

## 与注解实体的区别

| 特性 | 普通VO | 注解实体 |
|------|--------|----------|
| **注解要求** | 无需注解 | 需要JPA/MyBatis-Plus注解 |
| **主键字段** | 可选 | 必须有@Id字段 |
| **表名映射** | 类名转下划线 | 注解指定或类名转换 |
| **字段映射** | 驼峰转下划线 | 注解指定或驼峰转换 |
| **CRUD操作** | 仅支持查询映射 | 支持完整CRUD |
| **性能** | 相同的高性能 | 相同的高性能 |
| **使用场景** | 查询结果、报表、DTO | 实体操作、ORM |

## 最佳实践

### 1. VO类设计
```java
@Data
public class QueryResultVo {
    // 使用包装类型，避免null值问题
    private Long id;
    private String name;
    private Integer count;
    private BigDecimal amount;
    
    // 明确的字段命名，便于映射
    private Date createTime;
    private Boolean isActive;
}
```

### 2. 查询SQL编写
```sql
-- 列名使用下划线格式，与VO字段对应
SELECT 
    u.user_id,
    u.user_name,
    u.email,
    d.department_name,
    p.position_title,
    u.is_active,
    u.create_time
FROM users u
JOIN departments d ON u.department_id = d.department_id
JOIN positions p ON u.position_id = p.position_id
```

### 3. 性能优化
```java
// 复用EntityRowMapper实例
private static final EntityRowMapper<UserVo> USER_VO_MAPPER = 
    new EntityRowMapper<>(UserVo.class);

// 在查询中使用
List<UserVo> users = jdbcTemplate.query(sql, USER_VO_MAPPER);
```

## 总结

EntityRowMapper的普通VO支持提供了：

1. **零配置**：无需任何注解，纯POJO即可使用
2. **高性能**：与注解实体相同的优化策略
3. **智能映射**：自动处理命名转换和类型转换
4. **广泛适用**：支持各种查询场景和数据类型

这使得EntityRowMapper成为Java项目中查询结果映射的理想选择，特别适合复杂查询、统计报表和API数据传输等场景。
