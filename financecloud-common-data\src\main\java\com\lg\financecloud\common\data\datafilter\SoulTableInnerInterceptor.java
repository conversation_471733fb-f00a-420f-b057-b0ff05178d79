//package com.lg.financecloud.common.data.datafilter;
//
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.map.MapUtil;
//import cn.hutool.core.text.NamingCase;
//import cn.hutool.core.util.ArrayUtil;
//import cn.hutool.core.util.ReflectUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.core.util.URLUtil;
//import cn.hutool.extra.servlet.ServletUtil;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.ttl.TransmittableThreadLocal;
//import com.baomidou.mybatisplus.annotation.TableField;
//import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
//import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
//import com.lg.financecloud.admin.api.dto.SessionUser;
//import com.lg.financecloud.common.core.util.WebUtils;
//import com.lg.financecloud.common.data.tenant.TenantContextHolder;
//import lombok.extern.slf4j.Slf4j;
//import net.sf.jsqlparser.JSQLParserException;
//import net.sf.jsqlparser.expression.Expression;
//import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
//import net.sf.jsqlparser.parser.CCJSqlParserUtil;
//import net.sf.jsqlparser.statement.select.PlainSelect;
//import net.sf.jsqlparser.statement.select.Select;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.executor.parameter.ParameterHandler;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.ResultMapping;
//import org.apache.ibatis.plugin.Invocation;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.ibatis.session.ResultHandler;
//import org.apache.ibatis.session.RowBounds;
//
//import javax.servlet.http.HttpServletRequest;
//import java.lang.reflect.Field;
//import java.lang.reflect.Method;
//import java.sql.Connection;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
//@Slf4j
//public class SoulTableInnerInterceptor implements InnerInterceptor {
//
//    @Override
//    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
//        try {
//            this.doIntercept(executor,ms,parameter,rowBounds,resultHandler,boundSql);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    public static ThreadLocal<Boolean> getSoulTableInterceptorExclude() {
//        return THREAD_LOCAL_EXCLUDE;
//    }
//
//    public static void setThreadLocalExclude(ThreadLocal<Boolean> threadLocalExclude) {
//        THREAD_LOCAL_EXCLUDE = threadLocalExclude;
//    }
//
//    private  static    ThreadLocal<Boolean> THREAD_LOCAL_EXCLUDE = new TransmittableThreadLocal<>();
//
//
//
//
//    public static void setSoulTableInterceptorExclude(){
//        THREAD_LOCAL_EXCLUDE.set(true);
//    }
//    public static void cleanSoulTableInterceptorExclude(){
//        THREAD_LOCAL_EXCLUDE.remove();
//    }
//
//    private String dbType;
//
//    private enum DB_DIALECT {ORACLE, MYSQL};
//
//    public String getDbType() {
//        return dbType;
//    }
//    //这里的dbType设置请参考在351行
//    public void setDbType(String dbType) {
//        this.dbType = dbType;
//    }
//
//
//    private static final String CURRENT_USER_KEY = "sessionUser";
//    private static final String SO_FILTER_PARAM = "dynamicFilter";
//    private static final int NEW_ADDING_SIZE = 2;
//
//
//
//
//    private Map processParameterObject( MappedStatement ms, Object parameterObject) {
//
//        Map paramMap = null;
//        if (parameterObject == null) {
//            paramMap = new HashMap(NEW_ADDING_SIZE);
//        } else if (parameterObject instanceof Map) {
//            paramMap = new HashMap(((Map) parameterObject).size() + NEW_ADDING_SIZE);
//            paramMap.putAll((Map) parameterObject);
//        } else {
//            paramMap = new HashMap(5);
//            boolean hasTypeHandler = ms.getConfiguration().getTypeHandlerRegistry().hasTypeHandler(parameterObject.getClass());
//            MetaObject metaObject = MetaObjectUtil.forObject(parameterObject);
//            if (!hasTypeHandler) {
//                String[] getterNames = metaObject.getGetterNames();
//                int getterNamesLength = getterNames.length;
//
//                for (int i = 0; i < getterNamesLength; ++i) {
//                    String name = getterNames[i];
//                    paramMap.put(name, metaObject.getValue(name));
//                }
//            } else {
//                //注意、只有一个参数情况下只能使用'_parameter'注入了。
//                paramMap.put("_parameter", parameterObject);
//            }
//
//            return this.processParameter(paramMap);
//        }
//
//        return this.processParameter(paramMap);
//    }
//
//    private Map<String, Object> processParameter(Map<String, Object> paramMap) {
//
//        SessionUser sessionUser = TenantContextHolder.getCurrentSessionUser();
//
//        if (sessionUser!=null) {
//            paramMap.put("sessionUser", sessionUser);
//        }
//        paramMap.put(SO_FILTER_PARAM   , MapUtil.newHashMap());
//        return paramMap;
//    }
//
//
//
//    private void putParamsIfNecessary(BoundSql boundSql,String condition) {
//          boundSql.setAdditionalParameter(SO_FILTER_PARAM, condition);
//    }
//
//    private DynamicDataFilter getDynamicDataFilterAnnotation(MappedStatement mappedStatement) {
//        DynamicDataFilter annotation = null;
//        try {
//            String id = mappedStatement.getId();
//            String className = id.substring(0, id.lastIndexOf("."));
//            String methodName = id.substring(id.lastIndexOf(".") + 1);
//            final Method[] method = Class.forName(className).getMethods();
//            for (Method me : method) {
//                if (me.getName().equals(methodName) && me.isAnnotationPresent(DynamicDataFilter.class)) {
//                    return me.getAnnotation(DynamicDataFilter.class);
//                }
//            }
//        } catch (Exception ex) {
//            log.error("", ex);
//        }
//        return annotation;
//    }
//    private ExcludeDataFilter getExcludeDataFilterAnnotation(MappedStatement mappedStatement) {
//        ExcludeDataFilter annotation = null;
//        try {
//            String id = mappedStatement.getId();
//            String className = id.substring(0, id.lastIndexOf("."));
//            String methodName = id.substring(id.lastIndexOf(".") + 1);
//            final Method[] method = Class.forName(className).getMethods();
//            for (Method me : method) {
//                if (me.getName().equals(methodName) && me.isAnnotationPresent(ExcludeDataFilter.class)) {
//                    return me.getAnnotation(ExcludeDataFilter.class);
//                }
//            }
//        } catch (Exception ex) {
//            log.error("", ex);
//        }
//        return annotation;
//    }
//    /**
//     *
//     * @Title: getUrlParams
//     * @Description: 将url参数转换成map
//     * @param param aa=11&bb=22&cc=33
//     * @param @return    参数
//     * @return Map<String,Object>    返回类型
//     * @throws
//     */
//    public static Map<String, String> getUrlParams(String param) {
//        Map<String, String> map = new HashMap<>();
//        if (StrUtil.isBlank(param)) {
//            return map;
//        }
//        String[] params = param.split("&");
//        for (int i = 0; i < params.length; i++) {
//            String[] p = params[i].split("=", 2);
//            if (p.length == 2) {
//                map.put(p[0], p[1]);
//            }
//        }
//        return map;
//    }
//
//
//
//    private void doIntercept(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws Exception {
//
//        HttpServletRequest request = WebUtils.getRequest();
//        if(request==null){
//            return ;
//        }
//        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
//        Map<String, String>  parameterMap  = ServletUtil.getParamMap(request);
//
//
//
//        ExcludeDataFilter excludeDataFilterAnnotation = getExcludeDataFilterAnnotation(ms);
//        // 排除筛选
//        if(excludeDataFilterAnnotation!=null){
//            return  ;
//        }
//
//        DynamicDataFilter dynamicDataFilter = getDynamicDataFilterAnnotation(ms);
//
//
//        // 注入参数
//        parameter = processParameterObject(ms, parameter);
//
//        // 原始的SQL语句
//        String sql = mpBs.sql();
//
//
//        boolean hasSos = parameterMap.containsKey("filterSos");
//        boolean hasField = parameterMap.containsKey("field");
//        boolean hasOrder = parameterMap.containsKey("order");
//        Boolean soTable = false;
//        if(hasSos ==true|| ( hasField==true &&hasOrder==true)){
//            soTable=true;
//        }
//
//        // 带注解 但是没有入参 也直接放过
//        if(dynamicDataFilter!=null && soTable==false){
//              ((Map) parameter).put(SO_FILTER_PARAM, null);
//                boundSql = ms.getBoundSql(parameter);
//                MappedStatement newMappedStatement = MybatisPluginHelper.newMappedStatement(  ms,  MybatisPluginHelper.newBoundSql(ms, boundSql, boundSql.getSql()));
//                ms = newMappedStatement;
//            return ;
//        }
//
//        if(!soTable){
//            return ;
//        }
//
//
//        // 没有 soulPage 不需要拦截
//        if (soTable) {
//
//            SoulPage soulPage = new SoulPage();
//            String filterSos1 = URLUtil.decode( parameterMap.get("filterSos"));
//            soulPage.setFilterSos(filterSos1);
//            soulPage.setTableFilterType(parameterMap.get("tableFilterType"));
//            soulPage.setOrder(parameterMap.get("order"));
//            soulPage.setField(parameterMap.get("field"));
//            soulPage.setColumns(parameterMap.get("columns"));
//           String filedMappingStr =   URLUtil.decode(parameterMap.get("fieldMapping"));
//           Boolean toUnderlineCaseFlag =Boolean.valueOf( StrUtil.emptyToDefault(parameterMap.get("toUnderlineCase"),"true"));
//
//           if(StrUtil.isNotEmpty(filedMappingStr)){
//               soulPage.setFieldMapping(JSONUtil.toBean(filedMappingStr, Map.class));
//           }
//
//            if(StrUtil.isNotEmpty(parameterMap.get("code"))){
//                soulPage.setCode(Integer.valueOf(parameterMap.get("code")));
//            }
//            List<FilterSo> filterSos = soulPage.getFilterSos();
//                if (soulPage.isColumn()) {
//                    // 排序
//                    return ;
//                } else {
//                    Map<String, String> fieldMap = null;
//
//
//                    if(dynamicDataFilter!=null){
//                        FilterField[] filterFields = dynamicDataFilter.value();
//
//                        if(filterFields!=null && filterFields.length>0){
//                            fieldMap = new HashMap<>();
//                            for(FilterField filterField:filterFields){
//                                fieldMap.put(filterField.key(), filterField.value());
//                            }
//                        }
//                    }
//                    if(CollectionUtil.isNotEmpty(soulPage.getFieldMapping())){
//                        fieldMap = soulPage.getFieldMapping();
//                    }
//                    if(CollectionUtil.isNotEmpty(fieldMap)){
//                        StringBuffer filterSql = new StringBuffer("");
//                        StringBuffer dynamicFilterSql = buildQuery(sql, filterSql, soulPage, fieldMap, filterSos);
//                        Map paramMap = (Map) parameter;
//                        paramMap.put(SO_FILTER_PARAM, dynamicFilterSql.toString());
//                        if(log.isInfoEnabled()){
//                            log.info("filter data sql: {}",filterSql);
//                        }
//                        boundSql = ms.getBoundSql(parameter);
//                        MappedStatement newMappedStatement = MybatisPluginHelper.newMappedStatement(  ms,  MybatisPluginHelper.newBoundSql(ms, boundSql, boundSql.getSql()));
//                        ms = newMappedStatement;
//                        return ;
//                    }
//
//
//                    if(fieldMap==null){
//                        fieldMap = MapUtil.newHashMap();
//                    }
//                    // 返回类型是Map 标志
//                    Boolean mapResultClass = false;
//                    if (ms.getResultMaps().get(0).getResultMappings().size()>0) {
//                        for (ResultMapping resultMapping : ms.getResultMaps().get(0).getResultMappings()) {
//                            fieldMap.put(resultMapping.getProperty(),resultMapping.getColumn());
//                        }
//                    } else if (ms.getResultMaps().get(0).getType() != null) {
//
//                        Class<?> type = ms.getResultMaps().get(0).getType();
//
//                        if(StrUtil.equalsIgnoreCase(type.getSimpleName(),"Map")){
//                            mapResultClass = true;
//                        }else{
//                            // 兼容 tableField 的写法
//                            Field[] fields = ReflectUtil.getFields(ms.getResultMaps().get(0).getType());
//                            //TODO  判断基类是@ENTITY 需特需处理
//                            Class<?> superclass = ms.getResultMaps().get(0).getType().getSuperclass();
//                            //如果当前实体超类是Entity，则不转下划线
//                            if (superclass!=null && superclass.getName().equals("com.github.foxnic.dao.entity.Entity")){
//                                toUnderlineCaseFlag = false;
//                            }
//                            if(ArrayUtil.isNotEmpty(fields))
//                                for (Field field : fields) {
//                                    TableField tableField = field.getAnnotation(TableField.class);
//                                    if(toUnderlineCaseFlag){
//                                        if (tableField == null) {
//                                            String underlineCase = NamingCase.toUnderlineCase(field.getName());
//                                            fieldMap.put(field.getName(), underlineCase);
//                                        } else {
//                                            String underlineCase = NamingCase.toUnderlineCase(field.getName());
//                                            if(tableField.exist()){
//                                                fieldMap.put(field.getName(),underlineCase);
//                                            }else{
//                                                fieldMap.put(field.getName(),field.getName());
//                                            }
//
//                                        }
//                                    }else{
//                                        if (tableField == null) {
//                                            fieldMap.put(field.getName(), field.getName());
//                                        } else {
//                                            fieldMap.put(field.getName(),field.getName());
//                                        }
//                                    }
//
//                                }
//                        }
//
//                    }
//
//                    // 特殊字段 映射， specialFiledMapping 覆盖已有字段
//
//                   String  specialFiledMapping = parameterMap.get("specialFiledMapping");
//
//                    if(StrUtil.isNotEmpty(specialFiledMapping)){
//                        fieldMap.putAll(JSON.parseObject(specialFiledMapping,Map.class));
//                    }
//                    Boolean existsFilterField = false;
//                    if(CollectionUtil.isNotEmpty(filterSos)) {
//
//
//                        for (FilterSo filterSo : filterSos) {
//                            existsFilterField = getIgnoreCaseMapValue(fieldMap,  filterSo.getField())!=null;
//                            if (existsFilterField) {
//                                break;
//                            }
//                        }
//                    }
//
//                    if(  getIgnoreCaseMapValue(fieldMap,  soulPage.getField())!=null ){
//                        existsFilterField = true;
//                    }
//
//                    // 有排序 或者 存在筛选字段 则继续
//                    if(existsFilterField|| hasOrder || soTable){
//
//                    }else{
//                        return ;
//                    }
//
////                    // 类型属性中的字段  不存在 ，且不是Map 类型 直接返回
////                    if(!existsFilterField &&!mapResultClass){
////                        return invocation.proceed();
////                    }
//
//
//                    StringBuffer withinSql = new StringBuffer();
//
//
//                    List<FilterSo> withinSos =filterSos==null? new ArrayList<>() : filterSos.stream().filter(e -> "within".equals(e.getQueryMethod())).collect(Collectors.toList());
//                    if(CollectionUtil.isNotEmpty(withinSos)){
//                        doDuildQuery(sql, withinSql, soulPage, fieldMap, withinSos);
//                        String withinsql=withinSql.toString();
//                        int index1=withinsql.indexOf(" ");
//                        int index2=withinsql.substring(index1+1).indexOf(" ");
//                        sql = addWhereClause(sql, withinsql.substring(index2+1));
//                    }
//
//
//                    /**
//                     * 分页
//                     */
//                    filterSos= filterSos==null? new ArrayList<>() :filterSos.stream().filter(e->!"within".equals(e.getQueryMethod())).collect(Collectors.toList());
//                    StringBuffer filterSql = new StringBuffer("select * from (" + sql + ") soul WHERE");
//                    buildQuery(sql, filterSql,soulPage, fieldMap, filterSos);
//
//                    if(log.isInfoEnabled()){
//                        log.info("filter data sql: {}",filterSql);
//                    }
//
//                    mpBs.sql(filterSql.toString());
//                    return ;
//
//                }
//        }
//
//    }
//
//    private StringBuffer doDuildQuery(String orignSql,StringBuffer filterSql, SoulPage soulPage, Map<String, String> fieldMap, List<FilterSo> filterSos) {
//
//        // 获取前端指定类型
//        Map<String, Map<String, String>> typeMap = soulPage.getTypeMap();
//        if (CollectionUtil.isNotEmpty(filterSos)) {
//
//            filterSos.forEach(filterSo->{
//                handleFilterSo(filterSo, typeMap, fieldMap, filterSql);
//            });
//        }else{
//            if(StrUtil.endWith(filterSql,"WHERE" )){
//                filterSql.append(" 1=1");
//            }
//            //
//        }
//        return filterSql;
//    }
//
//    private StringBuffer buildQuery(String orignSql,StringBuffer filterSql, SoulPage soulPage, Map<String, String> fieldMap, List<FilterSo> filterSos) {
//
//        // 获取前端指定类型
//        Map<String, Map<String, String>> typeMap = soulPage.getTypeMap();
//        if (CollectionUtil.isNotEmpty(filterSos)) {
//
//            filterSos.forEach(filterSo->{
//                handleFilterSo(filterSo, typeMap, fieldMap, filterSql);
//            });
//        }else{
//            if(StrUtil.endWith(filterSql,"WHERE" )){
//                filterSql.append(" 1=1");
//            }
//            //
//        }
//        // 排序
//        if (StringUtils.isNotBlank(soulPage.getField())) {
//            String sortField = getIgnoreCaseMapValue(fieldMap, soulPage.getField());
//            filterSql.append(" order by ").append(sortField).append(" ").append(soulPage.getOrder());
//        }
//        return filterSql;
//    }
//
//    /**
//     * 处理表头筛选数据
//     *
//     * <AUTHOR>
//     * @date 2019-03-16 22:52
//     * @param filterSo
//     * @param typeMap
//     * @param fieldMap
//     * @param filterSql
//     * @return void
//     */
//    private void handleFilterSo(FilterSo filterSo, Map<String, Map<String, String>> typeMap, Map<String, String> fieldMap, StringBuffer filterSql) {
//        if (!StringUtils.endsWith(filterSql, "(") && !StringUtils.endsWith(filterSql, "WHERE")) {
//            filterSql.append(StringUtils.isBlank(filterSo.getPrefix())?" and":" "+filterSo.getPrefix());
//        }
//
//        String field = getIgnoreCaseMapValue(fieldMap,filterSo.getField() );
//        String value = filterSo.getValue();
//        switch (filterSo.getMode()) {
//            // 关键字搜索
//            case "search":
//                switch (filterSo.getType()) {
//                    case "all":
//                        String searchFields = filterSo.getField();
//                        List<String> searchFieldArray = StrUtil.split(searchFields, StrUtil.COMMA);
//                        if(CollectionUtil.isNotEmpty(searchFieldArray)){
//                            filterSql.append(" ");
//                            filterSql.append("( ");
//                            for(int i=0;i<searchFieldArray.size();i++){
//                                String ignoreCaseMapValue = getIgnoreCaseMapValue(fieldMap, searchFieldArray.get(i));
//                                String searchF = ignoreCaseMapValue==null? searchFieldArray.get(i): ignoreCaseMapValue ;
//                                filterSql.append(searchF);
//                                filterSql.append(" like '%").append(value).append("%'");
//                                if(i!=searchFieldArray.size()-1){
//                                    filterSql.append(" or ");
//                                }
//                            }
//                            filterSql.append(" )");
//                        }
//                        break;
//                }
//
//            break;
//            case "in":
//                if (filterSo.getValues()==null || filterSo.getValues().size()==0) {
//                    filterSql.append(" 1=1");
//                    break;
//                }
//                switch (typeMap.get(field)==null?"":typeMap.get(field).get("type")) {
//                    case "date":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" to_char(");
//                        } else {
//                            filterSql.append(" DATE_FORMAT(");
//                        }
//
//                        filterSql.append(field)
//                                .append(", '");
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(typeMap.get(field).get("value").replaceAll("HH", "HH24").replaceAll("mm", "mi"));
//                        } else {
//                            filterSql.append(typeMap.get(field).get("value")
//                                    .replaceAll("yyyy", "%Y")
//                                    .replaceAll("MM", "%m")
//                                    .replaceAll("dd", "%d")
//                                    .replaceAll("HH", "%H")
//                                    .replaceAll("mm", "%i")
//                                    .replaceAll("ss", "%s"));
//                        }
//
//                        filterSql.append("') in ('")
//                                .append(StringUtils.join(filterSo.getValues(), "','"))
//                                .append("')");
//                        break;
//                    default:
//                        if (StringUtils.isBlank(filterSo.getSplit())) {
//                            filterSql.append(" ")
//                                    .append(field)
//                                    .append(" in ('")
//                                    .append(StringUtils.join(filterSo.getValues(), "','"))
//                                    .append("')");
//                        } else {
//                            //todo 兼容value值内包含正则特殊字符
//                            if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                                filterSql.append(" regexp_like(")
//                                        .append(field)
//                                        .append(", '");
//                                for (String filterSoValue : filterSo.getValues()) {
//                                    filterSql.append("("+filterSo.getSplit()+"|^){1}"+filterSoValue+"("+filterSo.getSplit()+"|$){1}|");
//                                }
//                                filterSql.deleteCharAt(filterSql.length()-1);
//                                filterSql.append("')");
//                            } else {
//                                filterSql.append(" ")
//                                        .append(field)
//                                        .append(" regexp '(");
//                                for (String filterSoValue : filterSo.getValues()) {
//                                    filterSql.append("("+filterSo.getSplit()+"|^){1}"+filterSoValue+"("+filterSo.getSplit()+"|$){1}|");
//                                }
//                                filterSql.deleteCharAt(filterSql.length()-1);
//                                filterSql.append(")+'");
//                            }
//                        }
//
//                        break;
//                }
//                break;
//            case "condition":
//                if (StringUtils.isBlank(filterSo.getType()) || ((!"null".equals(filterSo.getType()) && !"notNull".equals(filterSo.getType())) && StringUtils.isBlank(filterSo.getValue()))) {
//                    filterSql.append(" 1=1");
//                    break;
//                }
//                filterSql.append(" ");
//                filterSql.append(field);
//                switch (filterSo.getType()) {
//                    case "eq":
//                        filterSql.append(" = '").append(value).append("'");
//                        break;
//                    case "ne":
//                        filterSql.append(" != '").append(value).append("'");
//                        break;
//                    case "gt":
//                        filterSql.append(" > '").append(value).append("'");
//                        break;
//                    case "ge":
//                        filterSql.append(" >= '").append(value).append("'");
//                        break;
//                    case "lt":
//                        filterSql.append(" < '").append(value).append("'");
//                        break;
//                    case "le":
//                        filterSql.append(" <= '").append(value).append("'");
//                        break;
//                    case "contain":
//                        filterSql.append(" like '%").append(value).append("%'");
//                        break;
//                    case "notContain":
//                        filterSql.append(" not like '%").append(value).append("%'");
//                        break;
//                    case "start":
//                        filterSql.append(" like '").append(value).append("%'");
//                        break;
//                    case "end":
//                        filterSql.append(" like '%").append(value).append("'");
//                        break;
//                    case "null":
//                        filterSql.append(" is null");
//                        break;
//                    case "notNull":
//                        filterSql.append(" is not null");
//                        break;
//                    default:break;
//                }
//                break;
//            case "date":
//                filterSql.append(" ");
//                filterSql.append(field);
//                switch (filterSo.getType()) {
//                    case "yesterday":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between trunc(sysdate - 1) and trunc(sysdate)-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between date_add(curdate(), interval -1 day) and date_add(curdate(),  interval -1 second) ");
//                        }
//                        break;
//                    case "thisWeek":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between trunc(sysdate - to_char(sysdate-2,'D')) and trunc(sysdate - to_char(sysdate-2,'D') + 7)-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between date_add(curdate(), interval - weekday(curdate()) day) and date_add(date_add(curdate(), interval - weekday(curdate())+7 day), interval -1 second) ");
//                        }
//                        break;
//                    case "lastWeek":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between trunc(sysdate - to_char(sysdate-2,'D') - 7) and trunc(sysdate - to_char(sysdate-2,'D'))-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between date_add(curdate(), interval - weekday(curdate())-7 day) and date_add(date_add(curdate(), interval - weekday(curdate()) day), interval -1 second) ");
//                        }
//                        break;
//                    case "thisMonth":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between trunc(sysdate, 'mm') and trunc(last_day(sysdate)+1)-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between date_add(curdate(), interval - day(curdate()) + 1 day) and DATE_ADD(last_day(curdate()), interval 24*60*60-1 second) ");
//                        }
//                        break;
//                    case "thisYear":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between trunc(sysdate, 'yyyy') and to_date(to_char(sysdate,'yyyy')||'-12-31 23:59:59', 'yyyy-mm-dd hh24:mi:ss') ");
//                        } else {
//                            filterSql.append(" between date_sub(curdate(),interval dayofyear(now())-1 day) and str_to_date(concat(year(now()),'-12-31 23:59:59'), '%Y-%m-%d %H:%i:%s') ");
//                        }
//
//                        break;
//                    case "specific":
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between to_date('").append(filterSo.getValue()).append("', 'yyyy-mm-dd') and to_date('").append(filterSo.getValue()).append("', 'yyyy-mm-dd')+1-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between str_to_date('").append(filterSo.getValue()).append("', '%Y-%m-%d') and str_to_date(concat('").append(filterSo.getValue()).append("',' 23:59:59'), '%Y-%m-%d %H:%i:%s') ");
//                        }
//                        break;
//                    case "dateRange":
//                        String filterSoValue = filterSo.getValue();
//                        String startTime = StrUtil.split(filterSoValue, StrUtil.COMMA).get(0);
//                        String endTime = StrUtil.split(filterSoValue, StrUtil.COMMA).get(1);
//
//                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
//                            filterSql.append(" between to_date('").append(startTime).append("', 'yyyy-mm-dd') and to_date('").append(endTime).append("', 'yyyy-mm-dd')+1-1/(24*60*60) ");
//                        } else {
//                            filterSql.append(" between str_to_date('").append(startTime).append("', '%Y-%m-%d %H:%i:%s') and str_to_date('").append(endTime).append("', '%Y-%m-%d %H:%i:%s') ");
//                        }
//                        break;
//
//                    case "all":
//                    default:
//                        filterSql.delete(filterSql.lastIndexOf(" "), filterSql.length());
//                        filterSql.append(" 1=1");
//                        break;
//                }
//                break;
//            case "group":
//                filterSql.append(" (");
//                if (filterSo.getChildren().size()>0) {
//                    filterSo.getChildren().forEach(f->{
//                        handleFilterSo(f, typeMap, fieldMap ,filterSql);
//                    });
//                } else {
//                    filterSql.append(" 1=1");
//                }
//                filterSql.append(" )");
//            default:break;
//        }
//
//
//    }
//
//
//
//
//
//    /**
//     * 获取当前sql查询的记录总数
//     *
//     * <AUTHOR>
//     * @date 2019-03-16 22:53
//     * @param invocation
//     * @param metaObject
//     * @param sql
//     * @return int
//     */
//    private int getTotle(Invocation invocation, MetaObject metaObject, String sql) throws SQLException {
//        Connection connection = (Connection)invocation.getArgs()[0];
//        // 查询总条数的SQL语句
//        String countSql = "select count(*) from (" + sql + ") a";
//        //执行总条数SQL语句的查询
//        PreparedStatement countStatement = connection.prepareStatement(countSql);
//       // 获取参数信息即where语句的条件信息，注意上面拿到的sql中参数还是用?代替的
//        ParameterHandler parameterHandler = (ParameterHandler) metaObject.getValue("delegate.parameterHandler");
//        parameterHandler.setParameters(countStatement);
//        ResultSet rs = countStatement.executeQuery();
//
//        if(rs.next()) {
//            return rs.getInt(1);
//        }
//        return 0;
//    }
//
//
//
//    //忽略大小写 map
//    public  String getIgnoreCaseMapValue(Map<String,String> map,String key){
//        for(String stringKey:map.keySet()){
//            if(StrUtil.equalsIgnoreCase(key, stringKey)){
//                return map.get(stringKey);
//            }
//
//        }
//        return key;
//    }
//
//
//    public static String addWhereClause(String originalSql, String newCondition) {
//        try {
//            Select selectStatement = (Select) CCJSqlParserUtil.parse(originalSql.replace("#","--"));
//            Select selectBody = selectStatement.getSelectBody();
//
//            if (selectBody instanceof PlainSelect) {
//                PlainSelect plainSelect = (PlainSelect) selectBody;
//
//                Expression where = plainSelect.getWhere();
//                plainSelect.getGroupBy();
//
//                if (where == null) {
//                    plainSelect.setWhere(CCJSqlParserUtil.parseCondExpression(newCondition));
//                } else {
//                    // 如果已有WHERE子句，添加新条件
//                    CCJSqlParserUtil.parseCondExpression(newCondition); // 确保新条件是合法的
//                    plainSelect.setWhere(new AndExpression(where, CCJSqlParserUtil.parseCondExpression(newCondition)));
//                }
//
//
//                return selectStatement.toString();
//            } else {
//                throw new UnsupportedOperationException("Only PlainSelect statements are supported for adding WHERE clause.");
//            }
//        } catch (JSQLParserException e) {
//            throw new RuntimeException("Error while parsing SQL", e);
//        }
//    }
//
//
//    public static void main(String[] args) {
//
////        String sql="SELECT\n" +
////                "        a.id,\n" +
////                "        a.default_number defaultNumber,\n" +
////                "        a.oper_time operTime,\n" +
////                "        a.oper_person_name operPersonName,\n" +
////                "        b.supplier supplierName,\n" +
////                "        a.total_price totalPrice,\n" +
////                "        FORMAT(a.discount_last_money, 2) discountLastMoney,\n" +
////                "        FORMAT((IFNULL(a.discount_money, 0) + IFNULL(a.discount_last_money, 0)), 2) taxLastMoneyPrice,\n" +
////                "        a.status state,\n" +
////                "        a.salesman,\n" +
////                "        a.invoice_type invoiceType,\n" +
////                "        a.submit_type submitType,\n" +
////                "        IFNULL(a.process_instance_id, '') processInstanceId,\n" +
////                "        a.create_time createTime,\n" +
////                "        d.name applyDept,\n" +
////                "        a.depot_id depotId,\n" +
////                "        a.location_id locationId\n" +
////                "        FROM\n" +
////                "        erp_depothead a\n" +
////                "        LEFT JOIN erp_supplier b ON a.organ_id = b.id" +
////                "        left join sys_dept d on(d.dept_id=a.dept_id)\n" +
////                "        WHERE\n" +
////                "        a.sub_type = ?\n" +
////                "        AND a.delete_flag = '0'\n" +
////                "         \n" +
////                "         \n" +
////                "        GROUP BY a.id\n" +
////                "        ORDER BY a.create_time DESC";
////
////        String newCondition=" pa.create_time between str_to_date('2024-05-01 00:00:00', '%Y-%m-%d %H:%i:%s') and str_to_date('2024-05-31 23:59:59', '%Y-%m-%d %H:%i:%s') ";
////
////        System.err.println(addWhereClause(sql,newCondition));
//    }
//
//
//}
//
