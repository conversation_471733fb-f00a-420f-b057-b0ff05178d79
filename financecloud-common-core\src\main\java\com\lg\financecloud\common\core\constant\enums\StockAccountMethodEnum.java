/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StockAccountMethodEnum {

	/**
	 *
	 */
	rate( "0","按比例核算成本"),
	detail("1","按明细核算成本");



    /**
	 * 描述
	 */
	private String code;
	private String name;


	private static final Map<String, StockAccountMethodEnum> lookup = new HashMap<String, StockAccountMethodEnum>();


    static {
        for (StockAccountMethodEnum s : EnumSet.allOf(StockAccountMethodEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

	public static StockAccountMethodEnum lookup(String code) {
		return lookup.get(code);
	}




}
