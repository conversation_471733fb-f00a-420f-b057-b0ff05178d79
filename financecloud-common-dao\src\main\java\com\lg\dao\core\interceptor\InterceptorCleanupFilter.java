package com.lg.dao.core.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 拦截器清理过滤器
 * 确保在请求结束时清理线程本地变量，防止内存泄漏
 * 通过LightORMAutoConfiguration进行Bean配置，不使用@Component注解
 *
 * <AUTHOR>
 */
@Slf4j
@Order(-1000) // 高优先级，确保在其他过滤器之前执行
public class InterceptorCleanupFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("初始化拦截器清理过滤器");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        try {
            // 继续执行过滤器链
            chain.doFilter(request, response);
        } finally {
            // 清理拦截器排除管理器的线程本地变量
            try {
                InterceptorExclusionManager.cleanup();
                log.trace("已清理拦截器线程本地变量: {}", httpRequest.getRequestURI());
            } catch (Exception e) {
                log.warn("清理拦截器线程本地变量失败: {}", httpRequest.getRequestURI(), e);
            }
        }
    }

    @Override
    public void destroy() {
        log.info("销毁拦截器清理过滤器");
    }
}
