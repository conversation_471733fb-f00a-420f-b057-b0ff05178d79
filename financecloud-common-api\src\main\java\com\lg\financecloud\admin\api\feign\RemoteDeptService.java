package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.entity.SysDept;
import com.lg.financecloud.admin.api.entity.SysLog;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(contextId = "remoteDeptService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteDeptService {

    @GetMapping("/dept/list4Oa")
    R<List<SysDept>> listAll4Oa(@RequestHeader(SecurityConstants.FROM) String from, @RequestParam("tenantId")Integer tenantId);
}
