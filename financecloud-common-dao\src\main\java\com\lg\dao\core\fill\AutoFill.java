package com.lg.dao.core.fill;

import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AutoFill {
    /**
     * 填充策略
     */
    FillStrategy strategy() default FillStrategy.INSERT;

    enum FillStrategy {
        /**
         * 插入时填充
         */
        INSERT,
        
        /**
         * 更新时填充
         */
        UPDATE,
        
        /**
         * 插入和更新时都填充
         */
        INSERT_UPDATE
    }
} 