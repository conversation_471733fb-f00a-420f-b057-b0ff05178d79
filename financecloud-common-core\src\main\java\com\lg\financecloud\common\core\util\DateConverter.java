package com.lg.financecloud.common.core.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 自定义日期反序列化器
 * 支持多种日期格式的自动转换
 *
 * <AUTHOR>
 */
@Slf4j
public class DateConverter extends JsonDeserializer<Date> {

    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd HH:mm",
        "yyyy-MM-dd",
        "yyyy/MM/dd HH:mm:ss",
        "yyyy/MM/dd HH:mm",
        "yyyy/MM/dd",
        "MM/dd/yyyy HH:mm:ss",
        "MM/dd/yyyy HH:mm",
        "MM/dd/yyyy",
        "dd/MM/yyyy HH:mm:ss",
        "dd/MM/yyyy HH:mm",
        "dd/MM/yyyy",
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
        "yyyy-MM-dd'T'HH:mm:ssXXX"
    };

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String dateString = jsonParser.getText();
        
        if (!StringUtils.hasText(dateString)) {
            return null;
        }

        // 去除前后空格
        dateString = dateString.trim();

        // 如果是时间戳（纯数字）
        if (dateString.matches("^\\d+$")) {
            try {
                long timestamp = Long.parseLong(dateString);
                // 判断是秒级还是毫秒级时间戳
                if (timestamp < 10000000000L) {
                    // 秒级时间戳，转换为毫秒
                    timestamp *= 1000;
                }
                return new Date(timestamp);
            } catch (NumberFormatException e) {
                log.warn("无法解析时间戳: {}", dateString);
            }
        }

        // 尝试各种日期格式
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setLenient(false); // 严格模式
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 如果所有格式都失败，记录错误并抛出异常
        log.error("无法解析日期字符串: {}", dateString);
        throw new IOException("无法解析日期格式: " + dateString + 
                            "，支持的格式包括: yyyy-MM-dd, yyyy-MM-dd HH:mm:ss 等");
    }
}
