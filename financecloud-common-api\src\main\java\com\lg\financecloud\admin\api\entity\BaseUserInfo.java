/*
 *    Copyright (c) 2018-2025, ebag All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息表
 *
 * <AUTHOR> code generator
 * @date 2020-04-09 10:24:52
 */
@Data


@ApiModel(value = "用户信息表")
public class BaseUserInfo extends Model<BaseUserInfo> {
private static final long serialVersionUID = 1L;

    /**
     * uid
     */

    @ApiModelProperty(value="uid")
    private Long uid;
	/**
	 * communityId
	 */

	@ApiModelProperty(value="communityId")
	private String communityId;
    /**
     * 组织id
     */
    @ApiModelProperty(value="组织id")
    private Long orgId;
    /**
     * 姓名
     */
    @ApiModelProperty(value="姓名")
    private String name;
    /**
     * 0男1女
     */
    @ApiModelProperty(value="0男1女")
    private String sex;
    /**
     * 身份证号码
     */
    @ApiModelProperty(value="身份证号码")
    private String idcard;
    /**
     * 学号
     */
    @ApiModelProperty(value="学号")
    private String studyNo;
    /**
     * 生日
     */
    @ApiModelProperty(value="生日")
    private LocalDateTime birthday;
    /**
     * 年龄
     */
    @ApiModelProperty(value="年龄")
    private Integer age;
    /**
     * 星座
     */
    @ApiModelProperty(value="星座")
    private String constellation;
    /**
     * 身高
     */
    @ApiModelProperty(value="身高")
    private String height;
    /**
     * 体重
     */
    @ApiModelProperty(value="体重")
    private String weight;
    /**
     * 二寸照片
     */
    @ApiModelProperty(value="二寸照片")
    private String idPhoto;
    /**
     * 邮箱
     */
    @ApiModelProperty(value="邮箱")
    private String email;
    /**
     * 血型
     */
    @ApiModelProperty(value="血型")
    private String bloodType;
    /**
     * 籍贯
     */
    @ApiModelProperty(value="籍贯")
    private String nativePlace;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value="政治面貌")
    private String politicalFace;
    /**
     * 民族
     */
    @ApiModelProperty(value="民族")
    private String nation;
    /**
     * 户口性质
     */
    @ApiModelProperty(value="户口性质")
    private String accountType;
    /**
     * 户口所在地
     */
    @ApiModelProperty(value="户口所在地")
    private String accountAddress;
    /**
     * 现居住地址
     */
    @ApiModelProperty(value="现居住地址")
    private String currentAddress;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value="邮政编码")
    private String postCode;
    /**
     * 婚姻
     */
    @ApiModelProperty(value="婚姻")
    private String maritalStatus;
    /**
     * 身体健康
     */
    @ApiModelProperty(value="身体健康")
    private String healthStatus;
    /**
     * 学籍
     */
    @ApiModelProperty(value="学籍")
    private String studyStatus;
    /**
     * 毕业院校
     */
    @ApiModelProperty(value="毕业院校")
    private String graduatedSchool;
    /**
     * 职业
     */
    @ApiModelProperty(value="职业")
    private String job;

	/**
	 * 职业
	 */
	@ApiModelProperty(value="")
	private String technicalTitleCode;
    /**
     * 工作单位
     */
    @ApiModelProperty(value="工作单位")
    private String workUnit;
    /**
     * 最小收入
     */
    @ApiModelProperty(value="最小收入")
    private Double minIncome;
    /**
     * 最大收入
     */
    @ApiModelProperty(value="最大收入")
    private Double maxIncome;
    /**
     * 设备信息
     */
    @ApiModelProperty(value="设备信息")
    private String deviceInfo;
    /**
     * 位置信息
     */
    @ApiModelProperty(value="位置信息")
    private String positionInfo;
    /**
     * 社交账号
     */
    @ApiModelProperty(value="社交账号")
    private String socialAccount;
    /**
     * 紧急联系人的姓名
     */
    @ApiModelProperty(value="紧急联系人的姓名")
    private String emergencyContact;
    /**
     * 紧急联系人的关系
     */
    @ApiModelProperty(value="紧急联系人的关系")
    private String emergencyContactShip;
    /**
     * 紧急联系人的电话
     */
    @ApiModelProperty(value="紧急联系人的电话")
    private String emergencyContactPhone;
    /**
     * 紧急联系人的工作单位
     */
    @ApiModelProperty(value="紧急联系人的工作单位")
    private String emergencyContactWu;
    /**
     * 专长/技能
     */
    @ApiModelProperty(value="专长/技能")
    private String speciality;
    /**
     * 是否独生子女
     */
    @ApiModelProperty(value="是否独生子女")
    private String isOnlyChild;
    /**
     * 孩子是否留守
     */
    @ApiModelProperty(value="孩子是否留守")
    private String isStayChild;
    /**
     * 个人爱好
     */
    @ApiModelProperty(value="个人爱好")
    private String hobby;
    /**
     * 注册时间
     */
    @ApiModelProperty(value="注册时间")
    private LocalDateTime registerDate;
    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateDate;
//    /**
//     * 当前班级ID
//     */
//    @ApiModelProperty(value="当前班级ID")
//    private String currClassId;
//    /**
//     *
//     */
//    @ApiModelProperty(value="")
//    private String currEduStationId;
//    /**
//     *
//     */
//    @ApiModelProperty(value="")
//    private String currSchoolId;
//    /**
//     *
//     */
//    @ApiModelProperty(value="")
//    private String currGradeId;


    /**
     * 手机
     */
    @ApiModelProperty(value="手机")
    private String phone;

	/**
	 * 省份
	 */
	@ApiModelProperty(value="省份")
	private Integer province;
	/**
	 * 城市
	 */
	@ApiModelProperty(value="城市")
	private Integer city;
	/**
	 * 区/县
	 */
	@ApiModelProperty(value="区/县")
	private Integer county;

	/**
	 * 街道
	 */
	@ApiModelProperty(value="街道")
	private Integer street;

	private String schoolName;

	private String userType;
	//头像地址
	private String avatar;
	//介绍
	private String introduce;

	@ApiModelProperty(value="年级")
	private String gradeCode;

	@ApiModelProperty(value="班级")
	private String className;

	@ApiModelProperty(value="用户真实姓名")
	private String realName;

    }
