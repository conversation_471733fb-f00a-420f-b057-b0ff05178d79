package com.lg.financecloud.common.data.intercept;

import cn.hutool.core.date.StopWatch;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.lg.financecloud.common.data.tenant.SwithDbSqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.SQLException;

@Slf4j
public class SwithDbIntercept implements InnerInterceptor {
    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        StopWatch stopWatch = new StopWatch("getSwithDbSql4SqlParse");
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        // 获取到原始sql语句
        String sql = mpBs.sql();
        // 增强sql
        // 通过反射，拦截方法上带有自定义@InterceptAnnotation注解的方法，并增强sql
        stopWatch.start();
        String mSql = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
        stopWatch.stop();
        if(log.isInfoEnabled())
            log.info("duri sql parse cost nanos:{} cost ms: {},sql:{}" ,stopWatch.getTotalTimeNanos(),stopWatch.getTotalTimeMillis() , mSql);
        mpBs.sql(mSql);
    }
}
