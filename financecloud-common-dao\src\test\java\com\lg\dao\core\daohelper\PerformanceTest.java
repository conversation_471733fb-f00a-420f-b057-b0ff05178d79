package com.lg.dao.core.daohelper;

import com.lg.dao.DaoMode;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.basic.TestApplication;
import com.lg.dao.core.basic.User;
import com.lg.dao.helper.DaoHelper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 性能测试
 */
@SpringBootTest(classes = TestApplication.class)
@ActiveProfiles("test")
public class PerformanceTest {

    @Test
    @DisplayName("测试DAO实例创建性能")
    void testDaoInstanceCreationPerformance() {
        long startTime = System.currentTimeMillis();
        
        // 创建1000个DAO实例（应该被缓存）
        List<GenericDao<User, Long>> daos = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            daos.add(DaoHelper.dao(User.class));
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("创建1000个DAO实例耗时: " + duration + "ms");
        
        // 验证缓存效果 - 所有实例应该是同一个
        GenericDao<User, Long> firstDao = daos.get(0);
        for (GenericDao<User, Long> dao : daos) {
            assert dao == firstDao : "DAO实例应该被缓存";
        }
        
        System.out.println("✅ DAO实例创建性能测试通过");
    }

    @Test
    @DisplayName("测试模式切换性能")
    void testModeSwitchPerformance() {
        long startTime = System.currentTimeMillis();
        
        // 频繁切换模式
        for (int i = 0; i < 1000; i++) {
            DaoHelper.setMode(DaoMode.MEMORY);
            DaoHelper.setMode(DaoMode.DATABASE);
            DaoHelper.setMode(DaoMode.AUTO);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("1000次模式切换耗时: " + duration + "ms");
        System.out.println("✅ 模式切换性能测试通过");
    }

    @Test
    @DisplayName("测试批量操作性能")
    void testBatchOperationPerformance() {
        GenericDao<User, Long> dao = DaoHelper.dao(User.class);
        
        // 准备测试数据
        List<User> users = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            User user = new User();
            user.setId((long) i);
            user.setUserName("性能测试用户" + i);
            user.setAge(20 + (i % 50));
            user.setTenantId("perf_test");
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            users.add(user);
        }
        
        long startTime = System.currentTimeMillis();
        
        // 批量插入
        for (User user : users) {
            dao.insert(user);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("插入100条记录耗时: " + duration + "ms");
        System.out.println("✅ 批量操作性能测试通过");
    }
}