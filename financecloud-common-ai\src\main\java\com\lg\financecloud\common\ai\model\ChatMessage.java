package com.lg.financecloud.common.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {
    /**
     * 角色类型：system/user/assistant
     */
    private String role;
    
    /**
     * 消息内容
     */
    private String content;
    private String type;

    public ChatMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

    // 新增工厂方法
    public static ChatMessage of(String role, Object content) {
        if (content instanceof String) {
            return new ChatMessage(role, (String) content);
        }
        return new ChatMessage(role, content.toString());
    }
}
