/*
 *    Copyright (c) 2018-2025, ebag All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2019-05-28 23:58:27
 */
@Data

@ApiModel(description = "商品")
public class PayGoodsOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 商品订单ID
	 */
	private String goodsOrderId;
	/**
	 * 商品ID
	 */
	private String goodsId;
	/**
	 * 商品名称
	 */
	private String goodsName;
	/**
	 * 金额,单位分
	 */
	private String amount;
	/**
	 * 用户ID
	 */
	private String userId;

	private String openId;
	/**
	 * 订单状态,订单生成(0),支付成功(1),处理完成(2),处理失败(-1)
	 */
	private String status;
	/**
	 * 支付订单号
	 */
	private String payOrderId;
	/**
	 * 0-正常,1-删除
	 */
	@TableLogic
	private String delFlag;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;
	/**
	 * 租户ID
	 */
	private Integer tenantId;

	/***
	 * 订单来源0是爱服务 1是书香校园
	 * 
	 */
	private String source;
	/***
	 * 支付通道 不传入  则使用ua 信息自动识别


	 	 ALIPAY_WAP 付宝手机支付
         ALIPAY_APP 支付宝手机APP支付
        WEIXIN_WAP 微信H5支付

		 WEIXIN_MP 微信公众号支付
		 WEIXIN_APP 微信app支付
	 */
	private String channel;
	/***
	 * 支付appid 跟支付通道配套的 如支付通道是微信  则是微信的appid
	 */
	private String appId;


	/***
	 *  h5 支付 返回地址
	 */
	private String returnUrl;


}
