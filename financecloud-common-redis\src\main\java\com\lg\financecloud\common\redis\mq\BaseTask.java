package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.net.NetUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.io.Serializable;

/**
 // 举例：
 表达式
 意义
 "0 0 12 * * ?"     每天中午12点触发
 "0 15 10 ? * *"     每天上午10:15触发
 "0 15 10 * * ?"     每天上午10:15触发
 "0 15 10 * * ? *"     每天上午10:15触发
 "0 15 10 * * ? 2005"     2005年的每天上午10:15触发
 "0 * 14 * * ?"     在每天下午2点到下午2:59期间的每1分钟触发
 "0 0/5 14 * * ?"     在每天下午2点到下午2:55期间的每5分钟触发
 "0 0/5 14,18 * * ?"     在每天下午2点到2:55期间和下午6点到6:55期间的每5分钟触发
 "0 0-5 14 * * ?"     在每天下午2点到下午2:05期间的每1分钟触发
 "0 10,44 14 ? 3 WED"     每年三月的星期三的下午2:10和2:44触发
 "0 15 10 ? * MON-FRI"     周一至周五的上午10:15触发
 "0 15 10 15 * ?"     每月15日上午10:15触发
 "0 15 10 L * ?"     每月最后一日的上午10:15触发
 "0 15 10 ? * 6L"     每月的最后一个星期五上午10:15触发
 "0 15 10 ? * 6L 2002-2005"     2002年至2005年的每月的最后一个星期五上午10:15触发
 "0 15 10 ? * 6#3"     每月的第三个星期五上午10:15触发


***/

@Data
public    class BaseTask implements Serializable {

    /**
     * 等待处理
     */
    public static final String TASK_STATE_WAIT = "1";

    /**
     * 处理中
     */
    public static final String TASK_STATE_PROCESSING = "2";

    /**
     * 执行失败
     */
    public static final String TASK_STATE_FAIL = "3";

    /**
     * 执行成功
     */
    public static final String TASK_STATE_SUCCESS = "4";

    /**
     * 部分成功
     */
    public static final String TASK_STATE_PARTIAL_SUCCESS = "5";

   // 1.我的输出  2.我的发送  3.我的获取 4. 后端任务 不需要给用户端呈现的
    public static final Short TASK_TYPE_OUT_PUT=1;

    public static final Short TASK_TYPE_SEND=2;

    public static final Short TASK_TYPE_GET=3;
    // 系统类任务 不需要呈现
    public static final Short TASK_TYPE_SYS=4;

    /**
     * 任务执行错误次数
     */
    private int errorTimes = 1;

    /**
     * 最大重试次数
     */
    private Integer maxRetry = 3;

    /**
     * 模块名称
     */
    private String moduleName;


    private String createTime;

    private String hostname;


    /**
     * 任务唯一标识
     * @return 任务唯一标识
     */
    protected   String taskIdentity;
     // 队列名称
    protected   String queueCode;

    protected  String jobDesc="";


    protected String state;


    protected String createBy;

    protected Integer tenantId;


    protected JSONObject taskData;

    //关联业务key
    protected String bizKey;



    protected Short type = Short.valueOf(TASK_TYPE_SYS);

    public String getQueueCode() {
        return queueCode();
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }


    public  String queueCode(){
        return "baseTask" ;
    }

    public String hostname(){

        return  NetUtil.getLocalhostStr();
    }

    public BaseTask(String taskIdentity, JSONObject taskData) {
        this.taskIdentity = taskIdentity;
        this.taskData = taskData;
        this.hostname = hostname();
    }

    public BaseTask() {
    }
}
