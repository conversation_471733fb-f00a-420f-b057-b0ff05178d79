package com.lg.dao.core.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * SQL拦截器链
 */
@Component
public class LightOrmSqlInterceptorChain {
    
    private final List<LightOrmSqlInterceptor> interceptors = new CopyOnWriteArrayList<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    @Autowired(required = false)
    @Lazy
    public void setLightOrmInterceptors(List<LightOrmSqlInterceptor> interceptorList) {
        if (interceptorList != null) {
            try {
                lock.writeLock().lock();
                interceptors.clear();
                interceptors.addAll(interceptorList);
                // 按优先级排序
                List<LightOrmSqlInterceptor> sortedList = new ArrayList<>(interceptors);
                sortedList.sort(Comparator.comparingInt(LightOrmSqlInterceptor::getOrder));
                interceptors.clear();
                interceptors.addAll(sortedList);
            } finally {
                lock.writeLock().unlock();
            }
        }
    }

    /**
     * 执行拦截器链
     */
    public String execute(String sql, Object params, SqlInterceptorContext context) {
        String result = sql;
        try {
            lock.readLock().lock();
            for (LightOrmSqlInterceptor interceptor : interceptors) {
                if (params instanceof List) {
                    result = interceptor.beforeExecute(result, (List<Object>) params, context);
                }
            }
        } finally {
            lock.readLock().unlock();
        }
        return result;
    }

    /**
     * 执行拦截器链的后置处理
     */
    public void executeAfter(String sql, Object params, Object result, SqlInterceptorContext context) {
        try {
            lock.readLock().lock();
            for (LightOrmSqlInterceptor interceptor : interceptors) {
                if (params instanceof List) {
                    interceptor.afterExecute(sql, (List<Object>) params, result, context);
                }
            }
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取所有注册的拦截器
     */
    public List<LightOrmSqlInterceptor> getInterceptors() {
        try {
            lock.readLock().lock();
            return new ArrayList<>(interceptors);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 清除所有拦截器
     */
    public void clear() {
        try {
            lock.writeLock().lock();
            interceptors.clear();
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 添加拦截器
     */
    public void addInterceptor(LightOrmSqlInterceptor interceptor) {
        if (interceptor == null) {
            return;
        }
        try {
            lock.writeLock().lock();
            interceptors.add(interceptor);
            // 重新排序
            List<LightOrmSqlInterceptor> sortedList = new ArrayList<>(interceptors);
            sortedList.sort(Comparator.comparingInt(LightOrmSqlInterceptor::getOrder));
            interceptors.clear();
            interceptors.addAll(sortedList);
        } finally {
            lock.writeLock().unlock();
        }
    }
}