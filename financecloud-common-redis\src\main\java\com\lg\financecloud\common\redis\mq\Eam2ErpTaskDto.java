package com.lg.financecloud.common.redis.mq;



/****
 *  eam 往erp 发送的通用消息队列
 */
public class Eam2ErpTaskDto extends BaseTask {

    public final static String MSG_TYPE_ADD_ASSET = "addAsset";
    public final static String MSG_TYPE_UPD_ASSET = "updAsset";
    public final static String MSG_TYPE_DEL_ASSET = "delAsset";

    @Override
    public String queueCode() {
        return "eam2Erp";
    }




    // 消息类型
    private String messageType;

    // json 自定义消息体
    private String messageBody;




    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }


}
