/**
 * RestApi数据源配置对话框
 * Created by UReport Team on 2024-01-10.
 */
import {alert} from '../MsgBox.js';

export default class RestApiDialog {
    constructor(datasources) {
        this.datasources = datasources;
        this.dialog = $(`
            <div class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                            <h4 class="modal-title">RestApi数据源配置</h4>
                        </div>
                        <div class="modal-body"></div>
                        <div class="modal-footer"></div>
                    </div>
                </div>
            </div>
        `);
        $('body').append(this.dialog);
        this.initBody();
        this.initFooter();
    }
    
    initBody() {
        const body = this.dialog.find('.modal-body');
        
        // 基本配置
        body.append('<h5>基本配置</h5>');
        
        // 数据源名称
        const nameRow = this.createFormGroup("数据源名称", this.nameEditor = $('<input type="text" class="form-control" placeholder="请输入数据源名称">'));
        body.append(nameRow);
        
        // 基础URL
        const baseUrlRow = this.createFormGroup("基础URL", this.baseUrlEditor = $('<input type="text" class="form-control" placeholder="https://api.example.com">'));
        body.append(baseUrlRow);
        
        // 认证配置
        body.append('<h5 style="margin-top: 20px;">认证配置</h5>');
        
        // 认证类型
        this.authTypeSelect = $(`
            <select class="form-control">
                <option value="none">无认证</option>
                <option value="bearer">Bearer Token</option>
                <option value="basic">Basic Auth</option>
                <option value="apikey">API Key</option>
            </select>
        `);
        const authTypeRow = this.createFormGroup("认证类型", this.authTypeSelect);
        body.append(authTypeRow);
        
        // 认证令牌
        const authTokenRow = this.createFormGroup("认证令牌", this.authTokenEditor = $('<input type="text" class="form-control" placeholder="请输入认证令牌">'));
        body.append(authTokenRow);
        
        // 高级配置
        body.append('<h5 style="margin-top: 20px;">高级配置</h5>');
        
        // 超时时间
        const timeoutRow = this.createFormGroup("超时时间(ms)", this.timeoutEditor = $('<input type="number" class="form-control" value="30000" min="1000" max="300000">'));
        body.append(timeoutRow);
        
        // 请求头配置
        body.append('<h6 style="margin-top: 15px;">自定义请求头</h6>');
        const headersContainer = $('<div class="headers-container"></div>');
        body.append(headersContainer);
        
        this.headersContainer = headersContainer;
        this.headers = [];
        
        // 添加请求头按钮
        const addHeaderBtn = $('<button type="button" class="btn btn-sm btn-default">添加请求头</button>');
        addHeaderBtn.click(() => this.addHeader());
        body.append(addHeaderBtn);
        
        // 认证类型变化事件
        this.authTypeSelect.change(() => {
            const authType = this.authTypeSelect.val();
            if (authType === 'none') {
                this.authTokenEditor.prop('disabled', true);
                this.authTokenEditor.attr('placeholder', '无需认证');
            } else {
                this.authTokenEditor.prop('disabled', false);
                switch(authType) {
                    case 'bearer':
                        this.authTokenEditor.attr('placeholder', '请输入Bearer Token');
                        break;
                    case 'basic':
                        this.authTokenEditor.attr('placeholder', '请输入Base64编码的用户名:密码');
                        break;
                    case 'apikey':
                        this.authTokenEditor.attr('placeholder', '请输入API Key');
                        break;
                }
            }
        });
    }
    
    createFormGroup(label, input) {
        const row = $(`<div class="row" style="margin-bottom: 10px;"></div>`);
        const labelCol = $(`<div class="col-md-3" style="text-align:right;margin-top:5px">${label}：</div>`);
        const inputCol = $(`<div class="col-md-9"></div>`);
        inputCol.append(input);
        row.append(labelCol);
        row.append(inputCol);
        return row;
    }
    
    addHeader(name = '', value = '') {
        const headerRow = $(`
            <div class="row header-row" style="margin-bottom: 5px;">
                <div class="col-md-4">
                    <input type="text" class="form-control header-name" placeholder="请求头名称" value="${name}">
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control header-value" placeholder="请求头值" value="${value}">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-danger remove-header">删除</button>
                </div>
            </div>
        `);
        
        headerRow.find('.remove-header').click(() => {
            headerRow.remove();
        });
        
        this.headersContainer.append(headerRow);
    }
    
    initFooter() {
        const footer = this.dialog.find('.modal-footer');
        
        // 测试连接按钮
        const testButton = $('<button type="button" class="btn btn-info">测试连接</button>');
        footer.append(testButton);
        testButton.click(() => this.testConnection());
        
        // 取消按钮
        const cancelButton = $('<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>');
        footer.append(cancelButton);
        
        // 保存按钮
        const saveButton = $('<button type="button" class="btn btn-primary">保存</button>');
        footer.append(saveButton);
        saveButton.click(() => this.save());
    }
    
    testConnection() {
        const name = this.nameEditor.val().trim();
        const baseUrl = this.baseUrlEditor.val().trim();
        const authType = this.authTypeSelect.val();
        const authToken = this.authTokenEditor.val().trim();
        const timeout = parseInt(this.timeoutEditor.val()) || 30000;
        
        if (!name) {
            alert('请输入数据源名称');
            return;
        }
        
        if (!baseUrl) {
            alert('请输入基础URL');
            return;
        }
        
        if (authType !== 'none' && !authToken) {
            alert('请输入认证令牌');
            return;
        }
        
        // 构建请求头
        const headers = this.getHeaders();
        
        // 发送测试请求
        $.ajax({
            url: window._server + "/datasource/testRestApiConnection",
            type: 'POST',
            data: {
                name: name,
                baseUrl: baseUrl,
                authType: authType,
                authToken: authToken,
                timeout: timeout,
                headers: JSON.stringify(headers)
            },
            success: (result) => {
                if (result.success) {
                    alert('连接测试成功！');
                } else {
                    alert('连接测试失败：' + (result.message || '未知错误'));
                }
            },
            error: () => {
                alert('连接测试失败：网络错误');
            }
        });
    }
    
    getHeaders() {
        const headers = {};
        this.headersContainer.find('.header-row').each(function() {
            const name = $(this).find('.header-name').val().trim();
            const value = $(this).find('.header-value').val().trim();
            if (name && value) {
                headers[name] = value;
            }
        });
        return headers;
    }
    
    save() {
        const name = this.nameEditor.val().trim();
        const baseUrl = this.baseUrlEditor.val().trim();
        const authType = this.authTypeSelect.val();
        const authToken = this.authTokenEditor.val().trim();
        const timeout = parseInt(this.timeoutEditor.val()) || 30000;
        
        if (!name) {
            alert('请输入数据源名称');
            return;
        }
        
        if (!baseUrl) {
            alert('请输入基础URL');
            return;
        }
        
        if (authType !== 'none' && !authToken) {
            alert('请输入认证令牌');
            return;
        }
        
        // 检查数据源名称是否重复
        for (let ds of this.datasources) {
            if (ds.name === name && (!this.oldName || this.oldName !== name)) {
                alert(`数据源[${name}]已存在！`);
                return;
            }
        }
        
        const headers = this.getHeaders();
        
        if (this.onSave) {
            this.onSave.call(this, name, baseUrl, authType, authToken, timeout, headers);
        }
        
        this.dialog.modal('hide');
    }
    
    show(onSave, ds) {
        this.onSave = onSave;
        
        if (ds) {
            // 编辑模式
            this.oldName = ds.name;
            this.nameEditor.val(ds.name);
            this.baseUrlEditor.val(ds.baseUrl);
            this.authTypeSelect.val(ds.authType || 'none');
            this.authTokenEditor.val(ds.authToken || '');
            this.timeoutEditor.val(ds.timeout || 30000);
            
            // 清空现有请求头
            this.headersContainer.empty();
            
            // 加载请求头
            if (ds.headers) {
                for (let name in ds.headers) {
                    this.addHeader(name, ds.headers[name]);
                }
            }
        } else {
            // 新增模式
            this.oldName = null;
            this.nameEditor.val('');
            this.baseUrlEditor.val('https://api.example.com');
            this.authTypeSelect.val('none');
            this.authTokenEditor.val('');
            this.timeoutEditor.val(30000);
            this.headersContainer.empty();
        }
        
        // 触发认证类型变化事件
        this.authTypeSelect.trigger('change');
        
        this.dialog.modal('show');
    }
}
