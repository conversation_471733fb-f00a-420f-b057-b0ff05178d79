package com.lg.financecloud.common.data.tenant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 多租户配置
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "financecloud.tenant")
public class CloudxTenantConfigProperties {

	private boolean enabled = true;
	private boolean swithDb = true;

	/**
	 * 维护租户列名称
	 */
	private String column = "tenant_id";

	/**
	 * 多租户的数据表集合
	 */
	private List<String> tenantTables = new ArrayList<>();

	private List<String> ignoreTableNamePrefix = new ArrayList<>();

	private List<String> ignoreMatchTableAlias = new ArrayList<>();
	private List<String> globTables = new ArrayList<>();

}
