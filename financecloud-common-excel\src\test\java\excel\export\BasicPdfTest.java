package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.pdf.PdfExportConfig;

import java.util.*;

/**
 * 基本PDF导出测试
 */
public class BasicPdfTest {

    public static void main(String[] args) {
        System.out.println("开始基本PDF导出测试...");
        
        try {
            // 检查PDF依赖
            try {
                Class.forName("com.itextpdf.text.Document");
                System.out.println("✓ PDF依赖检查通过");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠ 警告: 未找到iText依赖");
                System.out.println("请在pom.xml中添加以下依赖:");
                System.out.println("<dependency>");
                System.out.println("    <groupId>com.itextpdf</groupId>");
                System.out.println("    <artifactId>itextpdf</artifactId>");
                System.out.println("    <version>5.5.13.3</version>");
                System.out.println("</dependency>");
                return;
            }
            
            // 创建测试数据
            List<Map<String, Object>> data = new ArrayList<>();
            
            Map<String, Object> row1 = new HashMap<>();
            row1.put("name", "张三");
            row1.put("department", "技术部");
            row1.put("position", "工程师");
            data.add(row1);
            
            Map<String, Object> row2 = new HashMap<>();
            row2.put("name", "李四");
            row2.put("department", "销售部");
            row2.put("position", "销售经理");
            data.add(row2);
            
            // 确保目录存在
            java.io.File dir = new java.io.File("src/test/java/excel/export/excel");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 配置PDF导出参数
            PdfExportConfig pdfConfig = new PdfExportConfig()
                    .setOrientation(PdfExportConfig.PageOrientation.PORTRAIT)
                    .setPageSize(PdfExportConfig.PageSize.A4)
                    .setFontSize(12f)
                    .setMargins(20f)
                    .setShowGridLines(true)
                    .setEnableChineseFont(true);
            
            // 导出PDF
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(data)
                    .opsHeader()
                        .simple()
                        .texts("姓名", "部门", "职位")
                        .done()
                    .opsColumn()
                        .fields("name", "department", "position")
                        .done()
                    .done()
                    .exportToPdf("src/test/java/excel/export/excel/basic_test.pdf", pdfConfig);
            
            // 验证文件是否生成
            java.io.File resultFile = new java.io.File("src/test/java/excel/export/excel/basic_test.pdf");
            if (resultFile.exists() && resultFile.length() > 0) {
                System.out.println("✓ 基本PDF导出成功，文件大小: " + resultFile.length() + " 字节");
            } else {
                System.out.println("⚠ PDF文件未生成或为空");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 基本PDF导出测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
