# 低代码平台集成部署指南

## 🏗️ 部署架构

### 系统组件
```
┌─────────────────────────────────────────────────────────────┐
│                      Nginx (反向代理)                        │
├─────────────────────────────────────────────────────────────┤
│                      API Gateway                           │
├─────────────────────────────────────────────────────────────┤
│  用户中心  │  财务管理  │  OA系统  │  低代码平台  │  其他服务  │
│   服务    │   系统    │        │   服务      │          │
├─────────────────────────────────────────────────────────────┤
│                    前端系统                                  │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   cloudx-ui     │  │      Workflow-ui               │   │
│  │ (业务系统前端)    │  │   (低代码平台前端)              │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 部署步骤

### 1. 环境准备

#### 服务器要求
- **操作系统**: CentOS 7+ / Ubuntu 18+
- **内存**: 8GB+
- **CPU**: 4核+
- **磁盘**: 100GB+

#### 软件依赖
```bash
# Java 环境
java -version  # JDK 1.8+

# Node.js 环境
node -v        # Node.js 14+
npm -v         # npm 6+

# 数据库
mysql -V       # MySQL 5.7+

# Redis
redis-server -v # Redis 5.0+

# Nginx
nginx -v       # Nginx 1.16+
```

### 2. 后端服务部署

#### 2.1 用户中心服务
```bash
# 部署用户中心服务
cd user-center-service
mvn clean package -DskipTests
java -jar target/user-center-service.jar --server.port=8080

# 配置文件 application.yml
server:
  port: 8080
spring:
  datasource:
    url: ***************************************
    username: root
    password: password
```

#### 2.2 低代码平台服务
```bash
# 部署低代码平台服务
cd financecloud-lowcode-platform
mvn clean package -DskipTests
java -jar target/financecloud-lowcode-platform.jar --server.port=8082

# 配置文件 application.yml
server:
  port: 8082
spring:
  datasource:
    url: ********************************************
    username: root
    password: password
```

#### 2.3 其他业务服务
```bash
# 财务管理服务
cd finance-service
mvn clean package -DskipTests
java -jar target/finance-service.jar --server.port=8083

# OA系统服务
cd oa-service
mvn clean package -DskipTests
java -jar target/oa-service.jar --server.port=8084
```

### 3. 前端系统部署

#### 3.1 cloudx-ui (主业务系统前端)
```bash
# 构建 cloudx-ui
cd cloudx-ui
npm install
npm run build

# 部署到 Nginx
cp -r dist/* /var/www/cloudx-ui/
```

#### 3.2 Workflow-ui (低代码平台前端)
```bash
# 构建 Workflow-ui
cd Workflow-ui
npm install
npm run build

# 部署到 Nginx
cp -r dist/* /var/www/workflow-ui/
```

### 4. Nginx 配置

#### 4.1 主配置文件
```nginx
# /etc/nginx/nginx.conf
upstream api_gateway {
    server 127.0.0.1:8080;  # API Gateway
}

upstream lowcode_service {
    server 127.0.0.1:8082;  # 低代码平台服务
}

server {
    listen 80;
    server_name your-domain.com;

    # 主业务系统前端
    location / {
        root /var/www/cloudx-ui;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 低代码平台前端
    location /lowcode {
        alias /var/www/workflow-ui;
        index index.html;
        try_files $uri $uri/ /lowcode/index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 低代码平台 API 代理
    location /api/lc/ {
        proxy_pass http://lowcode_service/api/lc/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 4.2 SSL 配置 (可选)
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 其他配置同上...
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 5. 数据库初始化

#### 5.1 用户中心数据库
```sql
-- 创建数据库
CREATE DATABASE user_center DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入表结构和初始数据
USE user_center;
SOURCE /path/to/user_center_schema.sql;
SOURCE /path/to/user_center_data.sql;
```

#### 5.2 低代码平台数据库
```sql
-- 创建数据库
CREATE DATABASE lowcode_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入表结构
USE lowcode_platform;
SOURCE /path/to/lowcode_schema.sql;
```

### 6. 环境变量配置

#### 6.1 cloudx-ui 环境变量
```bash
# .env.production
VUE_APP_API_BASE_URL=https://your-domain.com/api
VUE_APP_LOWCODE_DOMAIN=https://your-domain.com/lowcode
VUE_APP_LOWCODE_API=https://your-domain.com/api/lc
```

#### 6.2 Workflow-ui 环境变量
```bash
# .env.production
VUE_APP_API_BASE_URL=https://your-domain.com/api/lc
VUE_APP_MAIN_SYSTEM_DOMAIN=https://your-domain.com
```

### 7. 系统集成配置

#### 7.1 菜单集成
```bash
# 在 cloudx-ui 中配置低代码平台菜单
# 通过菜单管理界面添加以下菜单项：

低代码平台 (分组)
├── 页面设计器 (新窗口打开: /lowcode/pages/designer)
├── 页面管理 (iframe: /lowcode/pages)
├── 数据模型 (iframe: /lowcode/models)
├── 组件管理 (iframe: /lowcode/components)
└── 模板市场 (iframe: /lowcode/templates)
```

#### 7.2 权限配置
```sql
-- 在用户中心添加低代码平台权限
INSERT INTO sys_permission (code, name, description, category) VALUES
('lowcode:access', '低代码平台访问', '访问低代码平台的基础权限', 'lowcode'),
('lowcode:page:design', '页面设计', '使用页面设计器创建和编辑页面', 'lowcode'),
('lowcode:page:publish', '页面发布', '发布低代码页面到生产环境', 'lowcode'),
('lowcode:model:design', '数据模型设计', '设计和修改数据模型', 'lowcode'),
('lowcode:admin', '平台管理', '低代码平台管理员权限', 'lowcode');
```

### 8. 启动服务

#### 8.1 启动脚本
```bash
#!/bin/bash
# start_services.sh

echo "启动用户中心服务..."
cd /opt/user-center-service
nohup java -jar user-center-service.jar > logs/app.log 2>&1 &

echo "启动低代码平台服务..."
cd /opt/lowcode-platform
nohup java -jar financecloud-lowcode-platform.jar > logs/app.log 2>&1 &

echo "启动财务管理服务..."
cd /opt/finance-service
nohup java -jar finance-service.jar > logs/app.log 2>&1 &

echo "启动OA系统服务..."
cd /opt/oa-service
nohup java -jar oa-service.jar > logs/app.log 2>&1 &

echo "重启 Nginx..."
systemctl restart nginx

echo "所有服务启动完成！"
```

#### 8.2 服务检查
```bash
#!/bin/bash
# check_services.sh

echo "检查服务状态..."

# 检查端口
netstat -tlnp | grep :8080  # 用户中心
netstat -tlnp | grep :8082  # 低代码平台
netstat -tlnp | grep :8083  # 财务管理
netstat -tlnp | grep :8084  # OA系统

# 检查健康状态
curl -f http://localhost:8080/actuator/health
curl -f http://localhost:8082/actuator/health
curl -f http://localhost:8083/actuator/health
curl -f http://localhost:8084/actuator/health

echo "服务检查完成！"
```

### 9. 监控和日志

#### 9.1 日志配置
```yaml
# logback-spring.xml
logging:
  level:
    com.lg.lc: DEBUG
    org.springframework: INFO
  file:
    name: logs/application.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

#### 9.2 监控配置
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 10. 故障排查

#### 10.1 常见问题
1. **跨域问题**: 检查 Nginx 配置和前端代理设置
2. **认证失败**: 检查 token 传递和用户中心服务状态
3. **菜单不显示**: 检查权限配置和菜单同步状态
4. **iframe 加载失败**: 检查 X-Frame-Options 设置

#### 10.2 日志查看
```bash
# 查看应用日志
tail -f /opt/lowcode-platform/logs/application.log

# 查看 Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 查看系统资源
top
df -h
free -m
```

### 11. 备份和恢复

#### 11.1 数据库备份
```bash
#!/bin/bash
# backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/database"

# 备份用户中心数据库
mysqldump -u root -p user_center > $BACKUP_DIR/user_center_$DATE.sql

# 备份低代码平台数据库
mysqldump -u root -p lowcode_platform > $BACKUP_DIR/lowcode_platform_$DATE.sql

echo "数据库备份完成: $DATE"
```

#### 11.2 应用备份
```bash
#!/bin/bash
# backup_applications.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/applications"

# 备份应用文件
tar -czf $BACKUP_DIR/cloudx-ui_$DATE.tar.gz /var/www/cloudx-ui
tar -czf $BACKUP_DIR/workflow-ui_$DATE.tar.gz /var/www/workflow-ui

echo "应用备份完成: $DATE"
```

## 📞 技术支持

如遇到部署问题，请联系技术团队：
- 技术负责人: [技术负责人联系方式]
- 运维负责人: [运维负责人联系方式]
- 紧急联系: [紧急联系方式]
