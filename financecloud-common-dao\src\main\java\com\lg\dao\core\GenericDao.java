package com.lg.dao.core;

import cn.hutool.core.bean.BeanUtil;
import com.lg.dao.core.builder.DeleteBuilder;
import com.lg.dao.core.builder.UpdateBuilder;
import com.lg.dao.core.query.JoinQueryBuilder;
import com.lg.dao.core.query.LambdaJoinQuery;
import com.lg.dao.core.query.LambdaQuery;
import com.lg.dao.core.query.QueryBuilder;
import com.lg.dao.core.sql.SqlBuilder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.function.Consumer;

/**
 * 优化后的通用单表CRUD基类
 * @param <T> 实体类型
 * @param <K> 主键类型
 */
@Slf4j
public class GenericDao<T, K> extends BaseDao {
    @Setter
    @Getter
    private  Class<T> entityClass;
    @Setter
    @Getter
    private final EntityInfo entityInfo;
    
    @SuppressWarnings("unchecked")
    public GenericDao() {
        // 获取泛型类型
        Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            Type[] actualTypeArguments = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
            if (actualTypeArguments[0] instanceof Class) {
                this.entityClass = (Class<T>) actualTypeArguments[0];
            } else {
                throw new IllegalStateException("无法解析实体类型，请使用带参数的构造函数");
            }
        } else {
            throw new IllegalStateException("无法解析实体类型，请使用带参数的构造函数");
        }
        // 获取实体信息
        this.entityInfo = getEntityInfo(entityClass);
    }


    /**
     * 带实体类参数的构造函数
     * @param entityClass 实体类
     */
    public GenericDao(Class<T> entityClass) {
        this.entityClass = entityClass;
        this.entityInfo = getEntityInfo(entityClass);
    }

    /**
     * 根据ID更新
     */
    public int updateById(T entity) {
        return super.update(entity);
    }

    /**
     * 根据ID删除
     */
    public int deleteById(K id) {
        SqlBuilder builder = buildDeleteByIdSql(id);
        return  lightOrmSqlExecutor.executeUpdate(builder.getSql(), builder.getParams());
    }

    /**
     * 根据ID查询
     */
    public T getById(K id) {
        SqlBuilder builder = buildSelectByIdSql(id);
        return super.selectOne(entityClass, builder);
    }

    /**
     * 创建查询构建器
     */
    public QueryBuilder<T> query() {
        return new QueryBuilder<>(this, entityClass, entityInfo);
    }

    /**
     * 使用预构建的LambdaQuery执行查询
     * @param lambdaQuery 预构建的Lambda查询条件
     * @return 查询结果列表
     */
    public List<T> query(LambdaQuery<T> lambdaQuery) {
        return lambdaQuery.list();
    }

    /**
     * 使用Lambda表达式构建查询条件并执行查询
     * @param consumer Lambda查询条件构建器
     * @return 查询结果列表
     */
    public List<T> query(Consumer<LambdaQuery<T>> consumer) {
        LambdaQuery<T> query = lambdaQuery();
        consumer.accept(query);
        return query.list();
    }

    /**
     * 使用预构建的LambdaQuery执行分页查询
     * @param lambdaQuery 预构建的Lambda查询条件
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页查询结果
     */
    public Page<T> queryPage(LambdaQuery<T> lambdaQuery, int pageNum, int pageSize) {
        return lambdaQuery.page(pageNum, pageSize);
    }

    /**
     * 使用Lambda表达式构建查询条件并执行分页查询
     * @param consumer Lambda查询条件构建器
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页查询结果
     */
    public Page<T> queryPage(Consumer<LambdaQuery<T>> consumer, int pageNum, int pageSize) {
        LambdaQuery<T> query = lambdaQuery();
        consumer.accept(query);
        return query.page(pageNum, pageSize);
    }

    /**
     * 使用预构建的LambdaQuery执行单条记录查询
     * @param lambdaQuery 预构建的Lambda查询条件
     * @return 单条记录查询结果
     */
    public T queryOne(LambdaQuery<T> lambdaQuery) {
        return lambdaQuery.one();
    }

    /**
     * 使用Lambda表达式构建查询条件并执行单条记录查询
     * @param consumer Lambda查询条件构建器
     * @return 单条记录查询结果
     */
    public T queryOne(Consumer<LambdaQuery<T>> consumer) {
        LambdaQuery<T> query = lambdaQuery();
        consumer.accept(query);
        return query.one();
    }

    /**
     * 使用预构建的LambdaQuery执行统计查询
     * @param lambdaQuery 预构建的Lambda查询条件
     * @return 统计结果
     */
    public long count(LambdaQuery<T> lambdaQuery) {
        return lambdaQuery.count();
    }

    /**
     * 使用Lambda表达式构建查询条件并执行统计查询
     * @param consumer Lambda查询条件构建器
     * @return 统计结果
     */
    public long count(Consumer<LambdaQuery<T>> consumer) {
        LambdaQuery<T> query = lambdaQuery();
        consumer.accept(query);
        return query.count();
    }

    public LambdaQuery<T> lambdaQuery() {
        return new LambdaQuery<>(this, entityClass);
    }
    
    /**
     * 创建多表连接查询构建器
     * @return 多表连接查询构建器
     */
    public JoinQueryBuilder joinQuery() {
        return new JoinQueryBuilder(this);
    }
    
    /**
     * 创建Lambda风格的JOIN查询构建器
     * @param <E> 实体类型
     * @return Lambda风格的JOIN查询构建器
     */
    public <E> LambdaJoinQuery<E> lambdaJoinQuery(Class<E> entityClass) {
        return new LambdaJoinQuery(this, entityClass);
    }
    
    /**
     * 创建更新构建器
     */
    public UpdateBuilder<T> lambdaUpdate() {
        return new UpdateBuilder<>(entityClass, entityInfo,this);
    }

    /**
     * 使用Lambda表达式构建更新条件并执行更新
     * @param consumer Lambda更新条件构建器
     * @return 更新影响的记录数
     */
    public int update(Consumer<UpdateBuilder<T>> consumer) {
        UpdateBuilder<T> updateBuilder = lambdaUpdate();
        consumer.accept(updateBuilder);
        return updateBuilder.update();
    }

    /**
     * 创建删除构建器
     */
    public DeleteBuilder<T> lambdaDelete() {
        return new DeleteBuilder<>(entityClass, entityInfo,this);
    }

    /**
     * 使用Lambda表达式构建删除条件并执行删除
     * @param consumer Lambda删除条件构建器
     * @return 删除影响的记录数
     */
    public int delete(Consumer<DeleteBuilder<T>> consumer) {
        DeleteBuilder<T> deleteBuilder = lambdaDelete();
        consumer.accept(deleteBuilder);
        return deleteBuilder.execute();
    }





    // ==================== 私有辅助方法 ====================

    private SqlBuilder buildSelectByIdSql(K id) {
        SqlBuilder builder = new SqlBuilder()
                .append("SELECT * FROM " + entityInfo.getTableName() + " WHERE ");

        addIdConditions(builder, id);
        return builder;
    }

    private SqlBuilder buildDeleteByIdSql(K id) {
        SqlBuilder builder = new SqlBuilder()
                .append("DELETE FROM " + entityInfo.getTableName() + " WHERE ");

        addIdConditions(builder, id);
        return builder;
    }

    private void addIdConditions(SqlBuilder builder, K id) {
        // 优先处理复合主键情况
        if (!entityInfo.getIdFields().isEmpty()) {
            boolean firstId = true;
            for (EntityInfo.FieldInfo idField : entityInfo.getIdFields()) {
                if (!firstId) {
                    builder.append(" AND ");
                }
                    builder.append(idField.getColumn() + " = ?", id);
                    firstId = false;
            }
        } else {
            // 回退处理单主键情况
            EntityInfo.FieldInfo idField = entityInfo.getIdField();
                builder.append(idField.getColumn() + " = ?", id);

        }
    }
}

