package com.lg.dao.core;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.persistence.*;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * 特殊字段处理测试
 * 测试@Transient、@TableField(exist=false)、insertable/updatable等特殊场景
 */
@Slf4j
public class SpecialFieldsTest {

    private UnifiedCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        cacheManager = mock(UnifiedCacheManager.class);
        
        // 模拟缓存管理器的get方法，让它直接调用supplier
        when(cacheManager.get(any(), any(), any())).thenAnswer(invocation -> {
            java.util.function.Supplier<?> supplier = invocation.getArgument(2);
            return supplier.get();
        });
        
        // 初始化EntityInfoManager的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    @Data
    @Table(name = "special_entity")
    @TableName("special_entity")
    public static class SpecialEntity {
        @Id
        @TableId
        @Column(name = "entity_id")
        private Long entityId;
        
        @Column(name = "entity_name")
        private String entityName;
        
        // 数据库不存在的字段 - 使用@Transient
        @Transient
        private String transientField;
        
        // 数据库不存在的字段 - 使用@TableField(exist=false)
        @TableField(exist = false)
        private String nonExistField;
        
        // 组合使用两种注解
        @Transient
        @TableField(exist = false)
        private List<String> combinedField;
        
        // 只读字段 - 不可插入和更新
        @Column(name = "readonly_field", insertable = false, updatable = false)
        private String readonlyField;
        
        // 只能插入，不能更新
        @Column(name = "insert_only_field", insertable = true, updatable = false)
        private String insertOnlyField;
        
        // 只能更新，不能插入
        @Column(name = "update_only_field", insertable = false, updatable = true)
        private String updateOnlyField;
        
        // 计算字段 - 用于业务逻辑
        @TableField(exist = false)
        private String calculatedField;
        
        // 临时状态字段
        @Transient
        private boolean temporaryStatus;
    }

    @Test
    void testEntityInfoCreationWithSpecialFields() {
        log.info("=== 测试包含特殊字段的EntityInfo创建 ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SpecialEntity.class);
        assertNotNull(entityInfo);
        
        // 打印所有字段信息
        log.info("实体字段总数: {}", entityInfo.getFields().size());
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("字段: {} -> 列: {} (insertable: {}, updatable: {})", 
                field.getPropertyName(), 
                field.getColumn(),
                field.isInsertable(),
                field.isUpdatable());
        }
        
        // 验证@Transient字段被排除
        EntityInfo.FieldInfo transientField = entityInfo.findFieldInfoByColumn("transient_field");
        assertNull(transientField, "@Transient字段不应该被包含在映射中");
        
        // 验证@TableField(exist=false)字段被排除
        EntityInfo.FieldInfo nonExistField = entityInfo.findFieldInfoByColumn("non_exist_field");
        assertNull(nonExistField, "@TableField(exist=false)字段不应该被包含在映射中");
        
        // 验证正常字段存在
        EntityInfo.FieldInfo entityNameField = entityInfo.findFieldInfoByColumn("entity_name");
        assertNotNull(entityNameField, "正常字段应该被包含在映射中");
        
        // 验证insertable/updatable属性
        EntityInfo.FieldInfo readonlyField = entityInfo.findFieldInfoByColumn("readonly_field");
        assertNotNull(readonlyField);
        assertFalse(readonlyField.isInsertable(), "readonly字段不应该可插入");
        assertFalse(readonlyField.isUpdatable(), "readonly字段不应该可更新");
        
        EntityInfo.FieldInfo insertOnlyField = entityInfo.findFieldInfoByColumn("insert_only_field");
        assertNotNull(insertOnlyField);
        assertTrue(insertOnlyField.isInsertable(), "insert_only字段应该可插入");
        assertFalse(insertOnlyField.isUpdatable(), "insert_only字段不应该可更新");
        
        EntityInfo.FieldInfo updateOnlyField = entityInfo.findFieldInfoByColumn("update_only_field");
        assertNotNull(updateOnlyField);
        assertFalse(updateOnlyField.isInsertable(), "update_only字段不应该可插入");
        assertTrue(updateOnlyField.isUpdatable(), "update_only字段应该可更新");
    }

    @Test
    void testEntityRowMapperWithSpecialFields() throws SQLException {
        log.info("=== 测试EntityRowMapper处理特殊字段 ===");
        
        EntityRowMapper<SpecialEntity> rowMapper = new EntityRowMapper<>(SpecialEntity.class, cacheManager);
        
        // 创建模拟的ResultSet，包含一些不存在的字段
        ResultSet rs = createMockResultSetWithSpecialFields();
        
        // 执行映射
        SpecialEntity entity = rowMapper.mapRow(rs, 1);
        
        assertNotNull(entity);
        assertEquals(1L, entity.getEntityId());
        assertEquals("Test Entity", entity.getEntityName());
        assertEquals("Readonly Value", entity.getReadonlyField());
        assertEquals("Insert Only Value", entity.getInsertOnlyField());
        assertEquals("Update Only Value", entity.getUpdateOnlyField());
        
        // 验证@Transient和@TableField(exist=false)字段没有被设置
        assertNull(entity.getTransientField(), "@Transient字段不应该被映射");
        assertNull(entity.getNonExistField(), "@TableField(exist=false)字段不应该被映射");
        assertNull(entity.getCombinedField(), "组合注解字段不应该被映射");
        
        log.info("特殊字段映射测试通过");
    }

    @Test
    void testInsertableUpdatableFields() {
        log.info("=== 测试insertable/updatable字段属性 ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SpecialEntity.class);
        
        // 获取可插入的字段
        List<EntityInfo.FieldInfo> insertableFields = entityInfo.getFields().stream()
            .filter(EntityInfo.FieldInfo::isInsertable)
            .collect(Collectors.toList());
        
        // 获取可更新的字段
        List<EntityInfo.FieldInfo> updatableFields = entityInfo.getFields().stream()
            .filter(EntityInfo.FieldInfo::isUpdatable)
                .collect(Collectors.toList());
        
        log.info("可插入字段数量: {}", insertableFields.size());
        log.info("可更新字段数量: {}", updatableFields.size());
        
        // 验证特定字段的属性
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            String fieldName = field.getPropertyName();
            switch (fieldName) {
                case "readonlyField":
                    assertFalse(field.isInsertable(), "readonlyField不应该可插入");
                    assertFalse(field.isUpdatable(), "readonlyField不应该可更新");
                    break;
                case "insertOnlyField":
                    assertTrue(field.isInsertable(), "insertOnlyField应该可插入");
                    assertFalse(field.isUpdatable(), "insertOnlyField不应该可更新");
                    break;
                case "updateOnlyField":
                    assertFalse(field.isInsertable(), "updateOnlyField不应该可插入");
                    assertTrue(field.isUpdatable(), "updateOnlyField应该可更新");
                    break;
                case "entityName":
                    assertTrue(field.isInsertable(), "entityName应该可插入");
                    assertTrue(field.isUpdatable(), "entityName应该可更新");
                    break;
            }
        }
        
        log.info("insertable/updatable属性测试通过");
    }

    @Test
    void testExistFalseFieldMapping() {
        log.info("=== 测试exist=false字段的映射支持 ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SpecialEntity.class);
        
        // 虽然exist=false字段不在主要字段列表中，但应该支持属性映射
        // 这对于某些业务场景（如计算字段）很有用
        
        // 验证exist=false字段可以通过属性名查找
        EntityInfo.FieldInfo calculatedField = entityInfo.getFieldInfo("calculatedField");
        // 注意：根据当前实现，exist=false字段可能不在主映射表中
        // 这是正确的行为，因为它们不对应数据库列
        
        log.info("exist=false字段映射测试完成");
    }

    private ResultSet createMockResultSetWithSpecialFields() throws SQLException {
        ResultSet rs = mock(ResultSet.class);
        ResultSetMetaData rsmd = mock(ResultSetMetaData.class);
        
        // 设置列数
        when(rsmd.getColumnCount()).thenReturn(6);
        
        // 设置列名（包括一些特殊字段）
        when(rsmd.getColumnName(1)).thenReturn("entity_id");
        when(rsmd.getColumnName(2)).thenReturn("entity_name");
        when(rsmd.getColumnName(3)).thenReturn("readonly_field");
        when(rsmd.getColumnName(4)).thenReturn("insert_only_field");
        when(rsmd.getColumnName(5)).thenReturn("update_only_field");
        when(rsmd.getColumnName(6)).thenReturn("unknown_column"); // 数据库中存在但实体中没有的列
        
        // 设置列标签
        when(rsmd.getColumnLabel(1)).thenReturn("entity_id");
        when(rsmd.getColumnLabel(2)).thenReturn("entity_name");
        when(rsmd.getColumnLabel(3)).thenReturn("readonly_field");
        when(rsmd.getColumnLabel(4)).thenReturn("insert_only_field");
        when(rsmd.getColumnLabel(5)).thenReturn("update_only_field");
        when(rsmd.getColumnLabel(6)).thenReturn("unknown_column");
        
        when(rs.getMetaData()).thenReturn(rsmd);
        
        // 设置值
        when(rs.getObject(1)).thenReturn(1L);
        when(rs.getObject(2)).thenReturn("Test Entity");
        when(rs.getObject(3)).thenReturn("Readonly Value");
        when(rs.getObject(4)).thenReturn("Insert Only Value");
        when(rs.getObject(5)).thenReturn("Update Only Value");
        when(rs.getObject(6)).thenReturn("Unknown Value");
        
        return rs;
    }
}
