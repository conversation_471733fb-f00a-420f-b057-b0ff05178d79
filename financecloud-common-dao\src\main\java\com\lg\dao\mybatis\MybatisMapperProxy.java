package com.lg.dao.mybatis;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.cache.UnifiedCacheManager;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;


/**
 * MyBatis Mapper接口代理工厂
 * 用于创建Mapper接口的代理实例，将MyBatis的操作转发到LightDao
 */
public class MybatisMapperProxy<T> implements FactoryBean<T>, InvocationHandler {

    private final Class<T> mapperInterface;

    private volatile BaseDao baseDao;
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    private MybatisNativeXmlParser nativeXmlParser;

    @Autowired(required = false)
    private UnifiedCacheManager unifiedCacheManager;

    public MybatisMapperProxy(Class<T> mapperInterface) {
        this.mapperInterface = mapperInterface;
    }

    @Override
    public T getObject() {
        // 创建接口的动态代理
        return (T) Proxy.newProxyInstance(
                mapperInterface.getClassLoader(),
                new Class[]{mapperInterface},
                this
        );
    }

    @Override
    public Class<?> getObjectType() {
        return mapperInterface;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 如果是Object的方法，直接调用
        if (Object.class.equals(method.getDeclaringClass())) {
            return method.invoke(this, args);
        }

        // 延迟初始化BaseDao（双重检查锁定）
        if (baseDao == null) {
            synchronized (this) {
                if (baseDao == null) {
                    baseDao = new BaseDao();
                    // 手动注入Spring依赖
                    applicationContext.getAutowireCapableBeanFactory().autowireBean(baseDao);
                }
            }
        }

        // 获取或创建MapperMethod（使用缓存优化）
        EnhancedMapperMethod mapperMethod = getMapperMethod(method);

        // 执行方法
        return mapperMethod.execute(baseDao, args);
    }
    
    private EnhancedMapperMethod getMapperMethod(Method method) {
        // 如果启用了统一缓存，使用统一缓存管理器
        if (unifiedCacheManager != null) {
            String cacheKey = "method:" + mapperInterface.getName() + "." + method.getName();
            return unifiedCacheManager.get(
                UnifiedCacheManager.CacheType.MYBATIS_PROXY_CACHE,
                cacheKey,
                () -> new EnhancedMapperMethod(mapperInterface, method, nativeXmlParser)
            );
        } else {
            // 如果没有统一缓存管理器，直接创建（不缓存）
            return new EnhancedMapperMethod(mapperInterface, method, nativeXmlParser);
        }
    }
} 