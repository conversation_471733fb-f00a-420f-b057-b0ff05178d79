# MyBatis Mapper代理

本模块提供了MyBatis Mapper接口的代理实现，可以无缝替换MyBatis和MyBatis Plus，使用LightDao作为底层实现。

## 功能特点

1. 支持MyBatis风格的Mapper接口
2. 支持注解定义SQL (@Select, @Insert, @Update, @Delete)
3. 支持XML文件定义SQL
4. 自动处理参数和返回值类型
5. 与Spring Boot无缝集成
6. 支持延迟加载，只有在实际使用时才创建代理
7. 支持指定特定的Mapper接口进行替换，实现渐进式迁移

## 基本使用方法

### 1. 启用MyBatis Mapper代理（全局方式）

在配置类上添加`@EnableMybatisMapper`注解：

```java
@Configuration
@EnableMybatisMapper(basePackages = "com.example.mapper")
public class AppConfig {
    // ...
}
```

### 2. 使用渐进式替换和延迟加载

在配置类上添加`@EnableLightDaoMappers`注解：

```java
@Configuration
@EnableLightDaoMappers(
    // 指定特定的Mapper接口类
    mapperClasses = {UserMapper.class, OrderMapper.class},
    // 或者指定包
    basePackages = "com.example.mapper.custom",
    // 是否启用延迟加载
    lazyInit = true,
    // 是否替换已存在的Bean
    replaceExisting = false
)
public class AppConfig {
    // ...
}
```

### 3. 定义Mapper接口

```java
public interface UserMapper {
    
    @Select("SELECT * FROM t_user WHERE id = #{id}")
    User getUserById(Long id);
    
    @Insert("INSERT INTO t_user(username, email) VALUES(#{username}, #{email})")
    int insertUser(User user);
    
    @Update("UPDATE t_user SET username = #{username} WHERE id = #{id}")
    int updateUser(User user);
    
    @Delete("DELETE FROM t_user WHERE id = #{id}")
    int deleteUser(Long id);
}
```

### 4. 使用XML文件定义SQL（可选）

在`resources/mapper`目录下创建与Mapper接口同名的XML文件：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    
    <select id="getUsersByStatus" resultType="com.example.entity.User">
        SELECT * FROM t_user WHERE status = #{status}
    </select>
    
</mapper>
```

### 5. 在服务中注入Mapper接口

```java
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    public User getUser(Long id) {
        return userMapper.getUserById(id);
    }
    
    // ...
}
```

## 参数处理

1. 单个参数：直接使用`#{paramName}`引用
2. 多个参数：使用`#{param1}`, `#{param2}`等引用，或者使用`@Param`注解

## 返回值处理

1. 对象：返回单个对象
2. List：返回对象列表
3. int/Integer：返回影响行数
4. boolean/Boolean：返回是否成功（影响行数 > 0）

## 延迟加载

使用延迟加载可以显著提高应用启动速度，特别是在有大量Mapper接口时。只有在实际调用Mapper方法时，才会创建代理对象和解析SQL。

## 渐进式迁移

通过`@EnableLightDaoMappers`注解的`mapperClasses`属性，可以指定需要替换的特定Mapper接口，而不必一次性替换所有接口。这样可以实现渐进式迁移，逐步将MyBatis替换为LightDao。

## 注意事项

1. 本模块是MyBatis的轻量级替代，不支持MyBatis的所有高级特性
2. 复杂查询建议使用LambdaJoinQuery等LightDao原生API
3. XML文件中只支持基本的SQL语句，不支持动态SQL 