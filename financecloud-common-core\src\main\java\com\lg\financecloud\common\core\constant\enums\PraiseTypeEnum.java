package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @date 2019-05-16
 * <p>
 * 点赞类型
 */
@Getter
@AllArgsConstructor
public enum PraiseTypeEnum {
	ISVR_COURSE("1", "课程点赞"),
	ISVR_COURSE_COMMENT("2", "课程评论点赞"),

	ISVR_USER_SHARE("3", "用户分享点赞"),

	ISVR_USER_SHARE_COMMENT("4", "用户分享评论点赞");

	/**
	 * 类型
	 */
	private String type;
	/**
	 * 描述
	 */
	private String description;


}
