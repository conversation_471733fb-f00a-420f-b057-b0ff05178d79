package com.lg.financecloud.admin.api.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
@Data
public class RoleInfoVo implements Serializable {
    private Integer roleId;

    private String roleName;

    private String roleCode;

    private String roleDesc;

    private Integer dsType;

    /**
     * 数据权限作用范围
     */
    private String dsScope;


    private String source;
}
