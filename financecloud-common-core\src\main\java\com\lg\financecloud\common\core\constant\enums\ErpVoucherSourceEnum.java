
package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ErpVoucherSourceEnum {


  erp_zgrk("100","erp_cgrk","erp暂估入库"),
  erp_ysrk("101","erp_ysrk","erp验收入库"),
  erp_xsck("102","erp_xsck","erp销售出库"),
  erp_scll("103","erp_scll","erp生产领料"),
  erp_yfjs("104","erp_yfjs","erp应付结算"),
  erp_ysjs("105","erp_ysjs","erp应收结算"),
  erp_pddj("106","erp_pddj","erp盘点单据"),
  erp_cgch("107","erp_cgth","erp采购退货"),
  erp_xsth("108","erp_xsth","erp销售退货"),
  erp_wwck("109","erp_wwck","erp委外出库"),
  erp_wwrk("110","erp_wwrk","erp委外入库"),
  erp_fybx("111","erp_fybx","erp费用报销"),
  erp_cfd("112","erp_cfd","erp拆分单"),
  erp_zzd("113","erp_zzd","erp组装单"),
  erp_cgkp("114","erp_cgkp","erp采购开票"),
  erp_xskp("115","erp_xskp","erp销售开票"),
  erp_yfkd("116","erp_yfkd","erp预付款单"),
  erp_yskd("117","erp_yskd","erp预收款单"),
  erp_scbl("118","erp_scbl","erp生产补料"),
  erp_sctl("119","erp_sctl","erp生产退料")
  ;


  /**
   * 描述
   */
  private String num;
  private String code;
  private String name;


  private static final Map<String, ErpVoucherSourceEnum> lookup = new HashMap<String, ErpVoucherSourceEnum>();

  private static final Map<String, ErpVoucherSourceEnum> keyByCode = new HashMap<String, ErpVoucherSourceEnum>();

  static {
    for (ErpVoucherSourceEnum s : EnumSet.allOf(ErpVoucherSourceEnum.class)) {
      lookup.put(s.getNum(), s);
    }
  }

  static {
    for (ErpVoucherSourceEnum s : EnumSet.allOf(ErpVoucherSourceEnum.class)) {
      lookup.put(s.getCode(), s);
    }
  }

  public static ErpVoucherSourceEnum lookup(String num) {
    return lookup.get(num);
  }

  public static ErpVoucherSourceEnum keyByCode(String code) {
    return lookup.get(code);
  }



}
