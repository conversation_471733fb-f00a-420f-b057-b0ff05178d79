package com.lg.financecloud.common.data.intercept;

/*
 * Copyright (c) 2011-2024, baomi<PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.lg.financecloud.common.data.MybatisConstant;
import com.lg.financecloud.common.data.tenant.SwithDbSqlUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 3.4.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@SuppressWarnings({"rawtypes"})
public class TenantInterceptor  implements InnerInterceptor {

    private TenantSqlParser tenantSqlParser;

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {


        boolean ignoreTenantLine = InterceptorIgnoreHelper.willIgnoreTenantLine(ms.getId());
        boolean ignoreDb = InterceptorIgnoreHelper.willIgnoreOthersByKey(ms.getId(), MybatisConstant.SWITH_DB_INTERCEPT_NAME);
        if(ignoreTenantLine && ignoreDb){
            return;
        }
        SqlCommandType sct = ms.getSqlCommandType();
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        parseStatement(mpBs, ignoreTenantLine, ignoreDb);
    }

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {



        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();

        if (sct == SqlCommandType.INSERT || sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
            boolean ignoreTenantLine = InterceptorIgnoreHelper.willIgnoreTenantLine(ms.getId());
            boolean ignoreDb = InterceptorIgnoreHelper.willIgnoreOthersByKey(ms.getId(), "db");
            if(ignoreTenantLine && ignoreDb){
                return;
            }
            parseStatement(mpBs, ignoreTenantLine, ignoreDb);
        }




    }

    private void parseStatement(PluginUtils.MPBoundSql mpBs, boolean ignoreTenantLine, boolean ignoreDb) {
        List<SQLStatement> sqlStatements = SQLUtils.parseStatements(mpBs.sql(), DbType.mysql);

        String newSQL = null;
        if(!ignoreDb){
            if(tenantSqlParser.getTenantLineHandler().getProperties().isSwithDb())
            newSQL = SwithDbSqlUtil.getSwithDbSql4SqlParse(mpBs.sql(),sqlStatements);
        }
        if(!ignoreTenantLine){
            if(tenantSqlParser.getTenantLineHandler().getProperties().isEnabled())
            newSQL = tenantSqlParser.setTenantParameter(newSQL,sqlStatements);
        }
        mpBs.sql(newSQL);
    }


}

