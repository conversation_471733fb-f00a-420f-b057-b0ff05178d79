package com.lg.dao.core.sequence;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 序列管理器
 */
@Component
@RequiredArgsConstructor
public class SequenceManager {
    
    @Lazy
    private final SequenceDao sequenceDao;
    private final ConcurrentMap<String, Lock> locks = new ConcurrentHashMap<>();

    @Value("${light.orm.sequence.default-config.prefix:}")
    private String defaultPrefix;

    @Value("${light.orm.sequence.default-config.suffix-length:4}")
    private int defaultSuffixLength;

    @Value("${light.orm.sequence.default-config.increment-value:1}")
    private int defaultIncrementValue;

    @Value("${light.orm.sequence.default-config.strategy:NEVER}")
    private SequenceStrategy defaultStrategy;

    /**
     * 获取下一个序列值（使用默认配置自动创建）
     */
    public String nextValue(String tenantId, String sequenceName) {
        return nextValue(tenantId, sequenceName, defaultStrategy, defaultPrefix, defaultSuffixLength);
    }

    public String nextValue(String tenantId, String sequenceName, SequenceStrategy strategy, int suffixLength){
        return nextValue(tenantId, sequenceName, strategy, defaultPrefix, suffixLength);
    }
    public String nextValue(String tenantId, String sequenceName, int suffixLength){
        return nextValue(tenantId, sequenceName, SequenceStrategy.NEVER, null, suffixLength);
    }

    /**
     * 获取下一个序列值（使用指定配置自动创建）
     */
    @Transactional
    public String nextValue(String tenantId, String sequenceName, SequenceStrategy strategy, String prefix, int suffixLength) {
        String lockKey = tenantId + ":" + sequenceName;
        Lock lock = locks.computeIfAbsent(lockKey, k -> new ReentrantLock());
        lock.lock();
        try {
            // 检查序列是否存在，不存在则创建
            if (!sequenceDao.exists(tenantId, sequenceName)) {
                createSequence(tenantId, sequenceName, strategy, prefix, suffixLength);
            }

            Sequence sequence = sequenceDao.getByTenantAndName(tenantId, sequenceName);
            
            // 检查是否需要重置
            if (needReset(sequence)) {
                sequenceDao.reset(tenantId, sequenceName, 1L);
                sequence.setCurrentValue(1L);
                sequence.setLastResetTime(LocalDateTime.now());
            }

            // 获取并递增序列值
            long value = sequenceDao.getAndIncrement(tenantId, sequenceName, sequence.getIncrementValue());

            // 格式化序列值
            return formatSequenceValue(sequence, value);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 创建新序列
     */
    @Transactional
    public void createSequence(String tenantId, String sequenceName, SequenceStrategy strategy, String prefix, int suffixLength) {
        String lockKey = tenantId + ":" + sequenceName;
        Lock lock = locks.computeIfAbsent(lockKey, k -> new ReentrantLock());
        lock.lock();
        try {
            // 双重检查，确保序列不存在
            if (sequenceDao.exists(tenantId, sequenceName)) {
                return;
            }

            Sequence sequence = new Sequence();
            sequence.setTenantId(tenantId);
            sequence.setSequenceName(sequenceName);
            sequence.setCurrentValue(0L);
            sequence.setIncrementValue(defaultIncrementValue);
            sequence.setStrategy(strategy);
            sequence.setPrefix(prefix);
            sequence.setSuffixLength(suffixLength);
            sequence.setLastResetTime(LocalDateTime.now());
            sequence.setCreateTime(LocalDateTime.now());
            sequence.setUpdateTime(LocalDateTime.now());
            
            sequenceDao.insert(sequence);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查是否需要重置
     */
    private boolean needReset(Sequence sequence) {
        if (sequence.getStrategy() == SequenceStrategy.NEVER) {
            return false;
        }

        LocalDateTime lastResetTime = sequence.getLastResetTime();
        LocalDateTime now = LocalDateTime.now();

        // 如果没有重置时间，说明是新创建的序列，不需要重置
        if (lastResetTime == null) {
            return false;
        }
        
        switch (sequence.getStrategy()) {
            case DAILY:
                return !isSameDay(lastResetTime, now);
            case WEEKLY:
                return ChronoUnit.WEEKS.between(lastResetTime, now) > 0;
            case MONTHLY:
                return !isSameMonth(lastResetTime, now);
            case QUARTERLY:
                return !isSameQuarter(lastResetTime, now);
            case YEARLY:
                return !isSameYear(lastResetTime, now);
            default:
                return false;
        }
    }

    /**
     * 格式化序列值
     */
    private String formatSequenceValue(Sequence sequence, long value) {
        StringBuilder result = new StringBuilder();
        
        // 添加前缀
        if (sequence.getPrefix() != null) {
            result.append(sequence.getPrefix());
        }
        
        // 添加日期部分（根据策略）
        LocalDateTime now = LocalDateTime.now();
        switch (sequence.getStrategy()) {
            case DAILY:
                result.append(String.format("%tY%tm%td", now, now, now));
                break;
            case WEEKLY:
                result.append(String.format("%tY%02d", now, now.get(java.time.temporal.WeekFields.ISO.weekOfWeekBasedYear())));
                break;
            case MONTHLY:
                result.append(String.format("%tY%tm", now, now));
                break;
            case QUARTERLY:
                result.append(String.format("%tY%02d", now, ((now.getMonthValue() - 1) / 3) + 1));
                break;
            case YEARLY:
                result.append(String.format("%tY", now));
                break;
        }
        
        // 添加序列值
        result.append(String.format("%0" + sequence.getSuffixLength() + "d", value));
        
        return result.toString();
    }

    private boolean isSameDay(LocalDateTime dt1, LocalDateTime dt2) {
        return dt1.toLocalDate().equals(dt2.toLocalDate());
    }

    private boolean isSameMonth(LocalDateTime dt1, LocalDateTime dt2) {
        return dt1.getYear() == dt2.getYear() && dt1.getMonthValue() == dt2.getMonthValue();
    }

    private boolean isSameQuarter(LocalDateTime dt1, LocalDateTime dt2) {
        return dt1.getYear() == dt2.getYear() && 
               ((dt1.getMonthValue() - 1) / 3) == ((dt2.getMonthValue() - 1) / 3);
    }

    private boolean isSameYear(LocalDateTime dt1, LocalDateTime dt2) {
        return dt1.getYear() == dt2.getYear();
    }
}