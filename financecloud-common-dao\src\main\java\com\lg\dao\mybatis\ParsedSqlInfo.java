package com.lg.dao.mybatis;

import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;

import java.util.List;

/**
 * 解析后的 SQL 信息
 * 包含 SQL 语句、参数列表和元数据
 */
public class ParsedSqlInfo {
    
    /**
     * 解析后的 SQL 语句（已处理动态 SQL）
     */
    private final String sql;
    
    /**
     * 参数值数组（按顺序）
     */
    private final Object[] parameters;
    
    /**
     * 参数映射信息
     */
    private final List<ParameterMapping> parameterMappings;
    
    /**
     * SQL 命令类型
     */
    private final SqlCommandType sqlCommandType;
    
    public ParsedSqlInfo(String sql, Object[] parameters, List<ParameterMapping> parameterMappings, SqlCommandType sqlCommandType) {
        this.sql = sql;
        this.parameters = parameters;
        this.parameterMappings = parameterMappings;
        this.sqlCommandType = sqlCommandType;
    }
    
    public String getSql() {
        return sql;
    }
    
    public Object[] getParameters() {
        return parameters;
    }
    
    public List<ParameterMapping> getParameterMappings() {
        return parameterMappings;
    }
    
    public SqlCommandType getSqlCommandType() {
        return sqlCommandType;
    }
    
    /**
     * 获取参数数量
     */
    public int getParameterCount() {
        return parameters != null ? parameters.length : 0;
    }
    
    /**
     * 检查是否有参数
     */
    public boolean hasParameters() {
        return parameters != null && parameters.length > 0;
    }
    
    /**
     * 检查是否为查询语句
     */
    public boolean isSelect() {
        return SqlCommandType.SELECT == sqlCommandType;
    }
    
    /**
     * 检查是否为插入语句
     */
    public boolean isInsert() {
        return SqlCommandType.INSERT == sqlCommandType;
    }
    
    /**
     * 检查是否为更新语句
     */
    public boolean isUpdate() {
        return SqlCommandType.UPDATE == sqlCommandType;
    }
    
    /**
     * 检查是否为删除语句
     */
    public boolean isDelete() {
        return SqlCommandType.DELETE == sqlCommandType;
    }
    
    @Override
    public String toString() {
        return "ParsedSqlInfo{" +
                "sql='" + sql + '\'' +
                ", parameterCount=" + getParameterCount() +
                ", sqlCommandType=" + sqlCommandType +
                '}';
    }
}
