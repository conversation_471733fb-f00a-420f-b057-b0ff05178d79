/**
 * MagicApi数据源配置对话框
 * Created by UReport Team on 2024-01-10.
 */
import {alert} from '../MsgBox.js';

export default class MagicApiDialog {
    constructor(datasources) {
        this.datasources = datasources;
        this.dialog = $(`
            <div class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                            <h4 class="modal-title">MagicApi数据源配置</h4>
                        </div>
                        <div class="modal-body"></div>
                        <div class="modal-footer"></div>
                    </div>
                </div>
            </div>
        `);
        $('body').append(this.dialog);
        this.initBody();
        this.initFooter();
    }
    
    initBody() {
        const body = this.dialog.find('.modal-body');
        
        // 数据源名称
        const nameRow = this.createFormGroup("数据源名称", this.nameEditor = $('<input type="text" class="form-control" placeholder="请输入数据源名称">'));
        body.append(nameRow);
        
        // 基础URL
        const baseUrlRow = this.createFormGroup("基础URL", this.baseUrlEditor = $('<input type="text" class="form-control" placeholder="http://localhost:9999/magic/web">'));
        body.append(baseUrlRow);
        
        // API路径
        const apiPathRow = this.createFormGroup("API路径", this.apiPathEditor = $('<input type="text" class="form-control" placeholder="/api/data">'));
        body.append(apiPathRow);
        
        // 请求头配置
        body.append('<h5 style="margin-top: 20px;">请求头配置</h5>');
        const headersContainer = $('<div class="headers-container"></div>');
        body.append(headersContainer);
        
        this.headersContainer = headersContainer;
        this.headers = [];
        
        // 添加请求头按钮
        const addHeaderBtn = $('<button type="button" class="btn btn-sm btn-default">添加请求头</button>');
        addHeaderBtn.click(() => this.addHeader());
        body.append(addHeaderBtn);
    }
    
    createFormGroup(label, input) {
        const row = $(`<div class="row" style="margin-bottom: 10px;"></div>`);
        const labelCol = $(`<div class="col-md-3" style="text-align:right;margin-top:5px">${label}：</div>`);
        const inputCol = $(`<div class="col-md-9"></div>`);
        inputCol.append(input);
        row.append(labelCol);
        row.append(inputCol);
        return row;
    }
    
    addHeader(name = '', value = '') {
        const headerRow = $(`
            <div class="row header-row" style="margin-bottom: 5px;">
                <div class="col-md-4">
                    <input type="text" class="form-control header-name" placeholder="请求头名称" value="${name}">
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control header-value" placeholder="请求头值" value="${value}">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-danger remove-header">删除</button>
                </div>
            </div>
        `);
        
        headerRow.find('.remove-header').click(() => {
            headerRow.remove();
        });
        
        this.headersContainer.append(headerRow);
    }
    
    initFooter() {
        const footer = this.dialog.find('.modal-footer');
        
        // 测试连接按钮
        const testButton = $('<button type="button" class="btn btn-info">测试连接</button>');
        footer.append(testButton);
        testButton.click(() => this.testConnection());
        
        // 取消按钮
        const cancelButton = $('<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>');
        footer.append(cancelButton);
        
        // 保存按钮
        const saveButton = $('<button type="button" class="btn btn-primary">保存</button>');
        footer.append(saveButton);
        saveButton.click(() => this.save());
    }
    
    testConnection() {
        const name = this.nameEditor.val().trim();
        const baseUrl = this.baseUrlEditor.val().trim();
        const apiPath = this.apiPathEditor.val().trim();
        
        if (!name) {
            alert('请输入数据源名称');
            return;
        }
        
        if (!baseUrl) {
            alert('请输入基础URL');
            return;
        }
        
        if (!apiPath) {
            alert('请输入API路径');
            return;
        }
        
        // 构建请求头
        const headers = this.getHeaders();
        
        // 发送测试请求
        $.ajax({
            url: window._server + "/datasource/testMagicApiConnection",
            type: 'POST',
            data: {
                name: name,
                baseUrl: baseUrl,
                apiPath: apiPath,
                headers: JSON.stringify(headers)
            },
            success: (result) => {
                if (result.success) {
                    alert('连接测试成功！');
                } else {
                    alert('连接测试失败：' + (result.message || '未知错误'));
                }
            },
            error: () => {
                alert('连接测试失败：网络错误');
            }
        });
    }
    
    getHeaders() {
        const headers = {};
        this.headersContainer.find('.header-row').each(function() {
            const name = $(this).find('.header-name').val().trim();
            const value = $(this).find('.header-value').val().trim();
            if (name && value) {
                headers[name] = value;
            }
        });
        return headers;
    }
    
    save() {
        const name = this.nameEditor.val().trim();
        const baseUrl = this.baseUrlEditor.val().trim();
        const apiPath = this.apiPathEditor.val().trim();
        
        if (!name) {
            alert('请输入数据源名称');
            return;
        }
        
        if (!baseUrl) {
            alert('请输入基础URL');
            return;
        }
        
        if (!apiPath) {
            alert('请输入API路径');
            return;
        }
        
        // 检查数据源名称是否重复
        for (let ds of this.datasources) {
            if (ds.name === name && (!this.oldName || this.oldName !== name)) {
                alert(`数据源[${name}]已存在！`);
                return;
            }
        }
        
        const headers = this.getHeaders();
        
        if (this.onSave) {
            this.onSave.call(this, name, baseUrl, apiPath, headers);
        }
        
        this.dialog.modal('hide');
    }
    
    show(onSave, ds) {
        this.onSave = onSave;
        
        if (ds) {
            // 编辑模式
            this.oldName = ds.name;
            this.nameEditor.val(ds.name);
            this.baseUrlEditor.val(ds.baseUrl);
            this.apiPathEditor.val(ds.apiPath);
            
            // 清空现有请求头
            this.headersContainer.empty();
            
            // 加载请求头
            if (ds.headers) {
                for (let name in ds.headers) {
                    this.addHeader(name, ds.headers[name]);
                }
            }
        } else {
            // 新增模式
            this.oldName = null;
            this.nameEditor.val('');
            this.baseUrlEditor.val('http://localhost:9999/magic/web');
            this.apiPathEditor.val('/api/data');
            this.headersContainer.empty();
        }
        
        this.dialog.modal('show');
    }
}
