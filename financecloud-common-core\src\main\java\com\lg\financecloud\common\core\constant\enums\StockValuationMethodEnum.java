/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StockValuationMethodEnum {

	/**
	 *存货计价法：1 先进先出发 2 移动加权平均法 3 月末一次加权平均法
	 */
	xjxc( "1","先进先出法"),
	ydjq("2","移动加权平均法"),
	ymycjq("3","月末一次加权平均法");



    /**
	 * 描述
	 */
	private String code;
	private String name;


	private static final Map<String, StockValuationMethodEnum> lookup = new HashMap<String, StockValuationMethodEnum>();


    static {
        for (StockValuationMethodEnum s : EnumSet.allOf(StockValuationMethodEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

	public static StockValuationMethodEnum lookup(String code) {
		return lookup.get(code);
	}




}
