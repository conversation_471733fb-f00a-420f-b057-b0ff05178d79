package com.lg.financecloud.common.core.sensitive.annotation;


import com.lg.financecloud.common.core.sensitive.SensitiveFilterService;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {SensitiveFilterService.class})
public @interface SensitiveWord {

	String message() default "不能有敏感词";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};

	String filed() default "";
}