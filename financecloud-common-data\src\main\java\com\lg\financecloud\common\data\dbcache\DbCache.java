package com.lg.financecloud.common.data.dbcache;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.util.Date;


/**
 * 数据库缓存
*/

@Data
@TableName("sys_db_cache")
public class DbCache  {

	public static final  String OWN_TYPT_USER="1";
	public static final  String OWN_TYPT_ACCOUNT="2";
	public static final  String OWN_TYPT_TENANT="3";
	public static final  String OWN_TYPT_SYSTEM="4";

	public static final  String COMMON_CATALOG="businesssettings";
	public static final  String OWN_ID_SYSTEM="system";


	private static final long serialVersionUID = 1L;


	/**
	 * ID：ID
	*/
	@Id
	@ApiModelProperty(required = true,value="ID" , notes = "ID")
	private String id;
	
	/**
	 * 数据分类：数据分类
	*/
	@ApiModelProperty(required = false,value="数据分类" , notes = "数据分类")
	private String catalog;
	
	/**
	 * 数据分区：数据分区
	*/
	@ApiModelProperty(required = false,value="数据分区" , notes = "数据分区")
	private String area;
	
	/**
	 * 所有者类型：所有者类型
	*/
	@ApiModelProperty(required = false,value="所有者类型" , notes = "所有者类型")
	private String ownerType;
	/**
	 * 数据配置级别范围
	*/
	@ApiModelProperty(required = false,value="数据配置级别范围" , notes = "数据配置级别范围")
	private String scope;

	/**
	 * 所有者ID：所有者ID
	*/
	@ApiModelProperty(required = false,value="所有者ID" , notes = "所有者ID")
	private String ownerId;
	
	/**
	 * 数据：数据
	*/
	@ApiModelProperty(required = false,value="数据" , notes = "数据")
	private String value;
	
	/**
	 * 过期时间：过期时间
	*/
	@ApiModelProperty(required = false,value="过期时间" , notes = "过期时间")
	private Date expireTime;
	
	/**
	 * 创建人ID：创建人ID
	*/
	@ApiModelProperty(required = false,value="创建人ID" , notes = "创建人ID")
	private String createBy;
	
	/**
	 * 创建时间：创建时间
	*/
	@ApiModelProperty(required = false,value="创建时间" , notes = "创建时间")
	private Date createTime;
	
	/**
	 * 修改人ID：修改人ID
	*/
	@ApiModelProperty(required = false,value="修改人ID" , notes = "修改人ID")
	private String updateBy;
	
	/**
	 * 修改时间：修改时间
	*/
	@ApiModelProperty(required = false,value="修改时间" , notes = "修改时间")
	private Date updateTime;
	
	/**
	 * 是否已删除：是否已删除
	*/
	@ApiModelProperty(required = true,value="是否已删除" , notes = "是否已删除")
	private Integer deleted;

	
	/**
	 * 删除人ID：删除人ID
	*/
	@ApiModelProperty(required = false,value="删除人ID" , notes = "删除人ID")
	private String deleteBy;

	/**
	 * 删除人ID：删除人ID
	 */
	@ApiModelProperty(required = false,value="备注" , notes = "备注")
	private String remark;

	/**
	 *模块
	 */
	@ApiModelProperty(required = false,value="模块" , notes = "模块")
	private String moduleCode;
	
	/**
	 * 删除时间：删除时间
	*/
	@ApiModelProperty(required = false,value="删除时间" , notes = "删除时间")
	private Date deleteTime;
	
	/**
	 * 数据版本号：数据版本号
	*/
	@ApiModelProperty(required = true,value="数据版本号" , notes = "数据版本号")
	private Integer version;
	

}