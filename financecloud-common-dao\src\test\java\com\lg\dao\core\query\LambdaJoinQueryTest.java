package com.lg.dao.core.query;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.core.func.LFunction;
import com.lg.dao.core.util.LambdaUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LambdaJoinQuery单元测试
 */
public class LambdaJoinQueryTest {

    // 测试实体类
    @Entity
    @Table(name = "t_order")
    public static class Order {
        @Id
        private Long id;
        
        @Column(name = "user_id")
        private Long userId;
        
        private BigDecimal amount;
        
        @Column(name = "create_time")
        private Date createTime;
        
        @Transient
        private User user;  // 关联的用户对象

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public User getUser() {
            return user;
        }

        public void setUser(User user) {
            this.user = user;
        }
    }

    @Entity
    @Table(name = "t_user")
    public static class User {
        @Id
        private Long id;
        
        private String username;
        
        private String status;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    // 测试DTO类
    public static class OrderWithUserDTO {
        private Long id;
        private BigDecimal amount;
        private Long userId;
        private String userName;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }
    }

    // 模拟对象
    private GenericDao<Order, Long> orderDao;
    private JoinQueryBuilder joinQueryBuilder;
    private EntityInfo orderEntityInfo;
    private EntityInfo userEntityInfo;

    @SuppressWarnings("unchecked")
    @BeforeEach
    public void setUp() {
        // 创建模拟对象
        orderDao = Mockito.mock(GenericDao.class);
        joinQueryBuilder = Mockito.mock(JoinQueryBuilder.class);
        orderEntityInfo = Mockito.mock(EntityInfo.class);
        userEntityInfo = Mockito.mock(EntityInfo.class);
        
        // 设置模拟行为
        when(orderDao.getEntityInfo(Order.class)).thenReturn(orderEntityInfo);
        when(orderDao.getEntityInfo(User.class)).thenReturn(userEntityInfo);
        
        when(orderEntityInfo.getTableName()).thenReturn("t_order");
        when(userEntityInfo.getTableName()).thenReturn("t_user");
        
        // 模拟列名映射
        when(orderEntityInfo.getColumnName("id")).thenReturn("id");
        when(orderEntityInfo.getColumnName("userId")).thenReturn("user_id");
        when(orderEntityInfo.getColumnName("amount")).thenReturn("amount");
        when(orderEntityInfo.getColumnName("createTime")).thenReturn("create_time");
        
        when(userEntityInfo.getColumnName("id")).thenReturn("id");
        when(userEntityInfo.getColumnName("username")).thenReturn("username");
        when(userEntityInfo.getColumnName("status")).thenReturn("status");
        
        // 模拟JoinQueryBuilder的链式调用
        when(joinQueryBuilder.select(any(String[].class))).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.from(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.leftJoin(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.innerJoin(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.rightJoin(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.on(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.where()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.and()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.or()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.eq(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.ne(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.gt(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.ge(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.lt(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.le(anyString(), any())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.like(anyString(), anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.in(anyString(), anyList())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.orderBy(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.desc()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.asc()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.groupBy(anyString())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.having()).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.limit(anyInt())).thenReturn(joinQueryBuilder);
        when(joinQueryBuilder.offset(anyInt())).thenReturn(joinQueryBuilder);
        
        // 模拟LambdaUtils的行为
        mockLambdaUtils();
    }

    /**
     * 模拟LambdaUtils的行为
     */
    private void mockLambdaUtils() {
        // 这里我们不能直接模拟静态方法，但可以在测试中使用真实的LambdaUtils
        // 如果需要，可以使用PowerMockito来模拟静态方法
    }

    /**
     * 测试基本的JOIN查询
     */
    @Test
    public void testBasicJoinQuery() {
        // 准备测试数据
        List<Map<String, Object>> mockResults = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", 1L);
        row1.put("user_id", 101L);
        row1.put("amount", new BigDecimal("100.00"));
        row1.put("username", "user1");
        row1.put("status", "active");
        mockResults.add(row1);
        
        // 模拟JoinQueryBuilder行为
        when(joinQueryBuilder.listMap()).thenReturn(mockResults);
        
        // 创建LambdaJoinQuery实例
        LambdaJoinQuery<Order> query = new LambdaJoinQuery<>(orderDao, Order.class);
        
        // 执行查询
        List<Map<String, Object>> results = query
            .selectFields(Order.class,Order::getId, Order::getUserId, Order::getAmount)
            .select("u.username", "u.status")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .eq(User.class, User::getStatus, "active")
            .listMap();
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(1L, results.get(0).get("id"));
        assertEquals(101L, results.get(0).get("user_id"));
        assertEquals(new BigDecimal("100.00"), results.get(0).get("amount"));
        assertEquals("user1", results.get(0).get("username"));
        assertEquals("active", results.get(0).get("status"));
        
        // 验证调用
        verify(joinQueryBuilder).leftJoin("t_user u");
        verify(joinQueryBuilder).eq(contains("u.status"), eq("active"));
    }
    
    /**
     * 测试返回DTO对象
     */
    @Test
    public void testReturnDTO() {
        // 准备测试数据
        List<OrderWithUserDTO> mockResults = new ArrayList<>();
        OrderWithUserDTO dto = new OrderWithUserDTO();
        dto.setId(1L);
        dto.setAmount(new BigDecimal("100.00"));
        dto.setUserId(101L);
        dto.setUserName("user1");
        mockResults.add(dto);
        
        // 模拟JoinQueryBuilder行为
        when(joinQueryBuilder.list(eq(OrderWithUserDTO.class))).thenReturn(mockResults);
        
        // 创建LambdaJoinQuery实例
        LambdaJoinQuery<Order> query = new LambdaJoinQuery<>(orderDao, Order.class);
        
        // 执行查询 - 修复select方法调用
        List<OrderWithUserDTO> results = query
            .select("o.id")
            .select("o.amount")
            .select("u.id as userId", "u.username as userName")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .list(OrderWithUserDTO.class);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(1L, results.get(0).getId().longValue());
        assertEquals(new BigDecimal("100.00"), results.get(0).getAmount());
        assertEquals(101L, results.get(0).getUserId().longValue());
        assertEquals("user1", results.get(0).getUserName());
        
        // 验证调用
        verify(joinQueryBuilder).list(OrderWithUserDTO.class);
    }
    
    /**
     * 测试返回带嵌套对象的实体
     */
    @Test
    public void testReturnNestedEntity() {
        // 准备测试数据
        List<Map<String, Object>> mockResults = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", 1L);
        row1.put("user_id", 101L);
        row1.put("amount", new BigDecimal("100.00"));
        row1.put("create_time", new Date());
        row1.put("u_id", 101L);
        row1.put("u_username", "user1");
        row1.put("u_status", "active");
        mockResults.add(row1);
        
        // 模拟JoinQueryBuilder行为
        when(joinQueryBuilder.listMap()).thenReturn(mockResults);
        
        // 创建LambdaJoinQuery实例
        LambdaJoinQuery<Order> query = new LambdaJoinQuery<>(orderDao, Order.class);
        
        // 执行查询
        List<Map<String, Object>> results = query
            .select("o.*")
            .select("u.id as u_id", "u.username as u_username", "u.status as u_status")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .listMap();
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        // 手动转换为带嵌套对象的实体
        List<Order> orders = convertToNestedEntities(results);
        
        // 验证嵌套对象
        assertNotNull(orders);
        assertEquals(1, orders.size());
        Order order = orders.get(0);
        assertEquals(1L, order.getId().longValue());
        assertEquals(101L, order.getUserId().longValue());
        assertEquals(new BigDecimal("100.00"), order.getAmount());
        assertNotNull(order.getUser());
        assertEquals(101L, order.getUser().getId().longValue());
        assertEquals("user1", order.getUser().getUsername());
        assertEquals("active", order.getUser().getStatus());
        
        // 验证调用
        verify(joinQueryBuilder).listMap();
    }
    
    /**
     * 测试分页查询
     */
    @Test
    public void testPageQuery() {
        // 准备测试数据
        List<OrderWithUserDTO> mockResults = new ArrayList<>();
        for (int i = 1; i <= 15; i++) {
            OrderWithUserDTO dto = new OrderWithUserDTO();
            dto.setId((long) i);
            dto.setAmount(new BigDecimal(i * 100 + ".00"));
            dto.setUserId(100L + i);
            dto.setUserName("user" + i);
            mockResults.add(dto);
        }
        
        // 模拟JoinQueryBuilder行为 - 修复返回类型
        when(joinQueryBuilder.list(eq(OrderWithUserDTO.class))).thenReturn(mockResults);
        
        // 创建LambdaJoinQuery实例
        LambdaJoinQuery<Order> query = spy(new LambdaJoinQuery<>(orderDao, Order.class));
        
        // 模拟list方法返回，避免内部调用真实的分页逻辑
        doReturn(mockResults).when(query).list(eq(OrderWithUserDTO.class));
        
        // 执行分页查询
        Page<OrderWithUserDTO> page = query
            .select("o.id", "o.amount", "u.id as userId", "u.username as userName")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .page(OrderWithUserDTO.class, 2, 5);
        
        // 验证分页结果
        assertNotNull(page);
        assertEquals(15, page.getTotal());
        assertEquals(2, page.getPageNum());
        assertEquals(5, page.getPageSize());
        assertEquals(5, page.getRecords().size());
        
        // 验证第二页的数据
        assertEquals(6L, page.getRecords().get(0).getId().longValue());
        assertEquals(10L, page.getRecords().get(4).getId().longValue());
    }
    
    /**
     * 测试动态条件
     */
    @Test
    public void testDynamicConditions() {
        // 模拟JoinQueryBuilder行为
        when(joinQueryBuilder.listMap()).thenReturn(Collections.emptyList());
        
        // 创建LambdaJoinQuery实例
        LambdaJoinQuery<Order> query = new LambdaJoinQuery<>(orderDao, Order.class);
        
        // 执行查询，测试动态条件
        String username = "test";
        String status = null;
        
        query.select("o.*")
            .select("u.username", "u.status")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .likeIfNotEmpty(User.class, User::getUsername, username)
            .and()
            .eqIfNotNull(User.class, User::getStatus, status)
            .listMap();
        
        // 验证只有非空条件被添加
        verify(joinQueryBuilder).like(contains("u.username"), eq("test"));
        // 验证空条件未被添加 - 使用never()验证
        verify(joinQueryBuilder, never()).eq(contains("u.status"), isNull());
    }
    
    /**
     * 将Map转换为带嵌套对象的实体
     */
    private List<Order> convertToNestedEntities(List<Map<String, Object>> maps) {
        List<Order> result = new ArrayList<>();
        
        for (Map<String, Object> map : maps) {
            // 创建Order对象
            Order order = new Order();
            order.setId((Long) map.get("id"));
            order.setUserId((Long) map.get("user_id"));
            order.setAmount((BigDecimal) map.get("amount"));
            order.setCreateTime((Date) map.get("create_time"));
            
            // 创建User对象
            User user = new User();
            user.setId((Long) map.get("u_id"));
            user.setUsername((String) map.get("u_username"));
            user.setStatus((String) map.get("u_status"));
            
            // 设置关联
            order.setUser(user);
            
            result.add(order);
        }
        
        return result;
    }
    
    /**
     * 测试实际使用场景的示例DAO
     */
    public static class OrderDao extends GenericDao<Order, Long> {
        
        /**
         * 查询订单并关联用户信息
         */
        public List<Order> findOrdersWithUser(String username) {
            // 执行连接查询获取原始数据
            List<Map<String, Object>> resultMaps = createLambdaJoinQuery(Order.class)
                .select("o.*")
                .select("u.id as u_id", "u.username as u_username", "u.status as u_status")
                .from(Order.class, "o")
                .leftJoin(User.class, "u")
                .on(Order.class, Order::getUserId, User.class, User::getId)
                .where()
                .likeIfNotEmpty(User.class, User::getUsername, username)
                .listMap();
            
            // 手动转换结果，将用户信息填充到订单对象中
            List<Order> orders = new ArrayList<>();
            for (Map<String, Object> map : resultMaps) {
                // 创建Order对象
                Order order = new Order();
                order.setId((Long) map.get("id"));
                order.setUserId((Long) map.get("user_id"));
                order.setAmount((BigDecimal) map.get("amount"));
                order.setCreateTime((Date) map.get("create_time"));
                
                // 创建User对象
                User user = new User();
                user.setId((Long) map.get("u_id"));
                user.setUsername((String) map.get("u_username"));
                user.setStatus((String) map.get("u_status"));
                
                // 设置关联
                order.setUser(user);
                
                orders.add(order);
            }
            
            return orders;
        }
        
        /**
         * 查询订单并关联用户信息，返回自定义DTO
         */
        public List<OrderWithUserDTO> findOrdersWithUserDTO(String username) {
            return createLambdaJoinQuery(Order.class)
                .select("o.id", "o.amount")
                .select("u.id as userId", "u.username as userName")
                .from(Order.class, "o")
                .leftJoin(User.class, "u")
                .on(Order.class, Order::getUserId, User.class, User::getId)
                .where()
                .likeIfNotEmpty(User.class, User::getUsername, username)
                .list(OrderWithUserDTO.class);
        }
        
        /**
         * 创建Lambda风格JOIN查询的方法
         */
        public <E> LambdaJoinQuery<E> createLambdaJoinQuery(Class<E> entityClass) {
            return new LambdaJoinQuery<>((GenericDao<E, ?>) this, entityClass);
        }
    }
} 