/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目成员
 *
 * <AUTHOR>
 * @date 2024-09-09 16:38:54
 */
@Data

@ApiModel(value = "项目成员")
public class PmProjectMember{
private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(type= IdType.INPUT)
    @ApiModelProperty(value="")
    private String id;
    /**
     * 项目id
     */
    @ApiModelProperty(value="项目id")
    private String projectId;
    /**
     * 员工id
     */
    @ApiModelProperty(value="员工id")
    private String staffId;
    /**
     * 0 普通成员 1管理  2只读成员
     */
    @ApiModelProperty(value="0 普通成员 1管理  2只读成员")
    private String memberScope;
    /**
     * 加入时间
     */
    @ApiModelProperty(value="加入时间")
    private LocalDateTime joinTime;
    /**
     * 0正常 1移出（项目成员移出，被移出的员工，所在的任务也需要被移除）
     */
    @ApiModelProperty(value="0正常 1移出（项目成员移出，被移出的员工，所在的任务也需要被移除）")
    private String delFlag;

    //一下为vo字段
    @TableField(exist = false)
    private String staffName;
    @TableField(exist = false)
    private String staffPhone;
    @TableField(exist = false)
    private String deptName;
    @TableField(exist = false)
    private String avatar;

    }
