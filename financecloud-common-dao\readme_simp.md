# LightORM 轻量级ORM框架

## 核心特性
- 简单易用API: BaseDao和GenericDao基础类
- Lambda查询: 类型安全的查询构建器
- 动态SQL构建和模板引擎
- 批量操作优化
- 分页查询
- 实体映射
- 多租户隔离(COLUMN/SCHEMA)
- SQL拦截器链
- 事务管理增强
- 自动填充功能
- 序列号管理
- 缓存支持(Caffeine/Redis)
- SQL安全防护
- DDL支持
- JOIN查询增强
- SQL模板引擎: 支持复杂场景查询

## 快速开始

```xml
<dependency>
    <groupId>com.lg</groupId>
    <artifactId>financecloud-common-dao</artifactId>
    <version>${version}</version>
</dependency>
```

## 主要用法

### DaoHelper零配置
```java
// 创建DAO
GenericDao<User, Long> userDao = DaoHelper.dao(User.class);
GenericDao<Order, String> orderDao = DaoHelper.daoString(Order.class);

// 基础CRUD
User user = userDao.getById(1L);
userDao.insert(user);
userDao.update(user);
userDao.deleteById(1L);
```

### Lambda查询
```java
// 基本查询
List<User> users = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .like(User::getName, "张")
    .orderByDesc(User::getCreateTime)
    .list();

// 分页查询
Page<User> page = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .page(1, 10);

// 动态条件
userDao.lambdaQuery()
    .eqIfNotNull(User::getStatus, status)
    .likeIfNotEmpty(User::getName, name)
    .inIfNotEmpty(User::getId, idList)
    .list();

// OR条件连接符
userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .or()
    .eq(User::getStatus, "PENDING")
    .list();

// AND条件连接符
userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .and()
    .eq(User::getRole, "ADMIN")
    .list();
```

### 嵌套条件分组
```java
// AND嵌套条件
userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .and(wrapper -> wrapper
        .eq(User::getStatus, "ACTIVE")
        .or()
        .eq(User::getStatus, "PENDING")
    )
    .list();

// OR嵌套条件
userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .or(wrapper -> wrapper
        .eq(User::getRole, "ADMIN")
        .eq(User::getDepartment, "技术部")
    )
    .list();

// 多层嵌套
userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .and(wrapper -> wrapper
        .eq(User::getStatus, "ACTIVE")
        .or(subWrapper -> subWrapper
            .eq(User::getRole, "ADMIN")
            .eq(User::getDepartment, "技术部")
        )
    )
    .list();
```

### 便捷API
```java
// 使用预构建查询
LambdaQuery<User> query = userDao.lambdaQuery().eq(User::getStatus, "ACTIVE");
List<User> users = userDao.query(query);

// Lambda表达式构建查询
List<User> users = userDao.query(q -> q
    .eq(User::getStatus, "ACTIVE")
    .like(User::getName, "张")
);

// Lambda表达式构建分页查询
Page<User> page = userDao.queryPage(q -> q
    .eq(User::getStatus, "ACTIVE"),
    1, 10
);

// Lambda表达式构建更新
int updated = userDao.update(u -> u
    .set(User::getStatus, "INACTIVE")
    .eq(User::getDepartment, "销售部")
);

// Lambda表达式构建删除
int deleted = userDao.delete(d -> d
    .eq(User::getStatus, "DELETED")
);
```

### SQL模板引擎
```java
// 1. 定义SQL模板文件 (user.sql)
// SQL模板使用 #sqlId 格式定义，如：
// #findByConditions
// SELECT * FROM t_user 
// WHERE 1=1
// <if test="status != null"> 
//   AND status = #{status}
// </if>
// <if test="department != null">
//   AND department = #{department}
// </if>
//
// #findById
// SELECT * FROM t_user WHERE id = #{id}
//
// #updateStatus
// UPDATE t_user SET status = #{status} WHERE id = #{id}

// 2. 使用SQL模板查询
@Repository
public class UserDao extends GenericDao<User, Long> {
    
    // 使用SQL模板查询
    public List<User> findByTemplate(String status, String department) {
        return selectListByTemplate("user.findByConditions", 
            MapUtil.of("status", status, "department", department));
    }
    
    // 带缓存的模板查询
    public User findByIdWithCache(Long id) {
        return selectOneByTemplateWithCache(User.class, 
            "user.findById",
            MapUtil.of("id", id),
            "user", id);
    }
    
    // 模板更新操作
    public int updateStatusByTemplate(Long id, String status) {
        return executeUpdateByTemplate("user.updateStatus",
            MapUtil.of("id", id, "status", status));
    }
}

// 3. 直接使用BaseDao执行SQL模板
@Service
public class UserService {
    @Autowired
    private BaseDao baseDao;
    
    public List<User> getUsersByDept(String dept) {
        return baseDao.selectListByTemplate(User.class, 
            "user.findByDepartment", 
            MapUtil.of("department", dept));
    }
    
    public Page<User> getUsersByPage(String dept, int pageNum, int pageSize) {
        return baseDao.selectPageByTemplate(User.class,
            "user.findByDepartment", 
            MapUtil.of("department", dept),
            pageNum, pageSize);
    }
}
```

### JOIN查询
```java
// 基本JOIN查询
List<Map<String, Object>> results = userDao.joinQuery()
    .select("u.id", "u.username", "o.id as order_id")
    .from("user u")
    .leftJoin("order o")
    .on("u.id = o.user_id")
    .where()
    .eq("u.status", "ACTIVE")
    .listMap();

// Lambda风格JOIN查询
List<UserOrderDTO> results = userDao.lambdaJoinQuery(User.class)
    .select(User::getId, User::getUsername)
    .select("o.id as orderId")
    .from(User.class, "u")
    .leftJoin("order o")
    .on("u.id = o.user_id")
    .list(UserOrderDTO.class);

// 分组和聚合
userDao.joinQuery()
    .select("u.id", "COUNT(o.id) as order_count")
    .from("user u")
    .leftJoin("order o")
    .on("u.id = o.user_id")
    .groupBy("u.id")
    .having()
    .gt("COUNT(o.id)", 0)
    .listMap();
```

### 事务管理
```java
// 基本事务
transactionHelper.inTransaction(() -> {
    userDao.save(user);
    userLogDao.save(log);
});

// 嵌套事务
transactionHelper.inTransaction(() -> {
    userDao.updateStatus(1L, "PROCESSING");
    transactionHelper.inNestedTransaction(() -> {
        userDao.updateBalance(1L, 100.00);
    });
    userDao.updateStatus(1L, "COMPLETED");
});

// 批量事务
transactionHelper.batchInTransaction(users, user -> {
    userDao.save(user);
}, 100);
``` 