package com.lg.dao.core.exception;

/**
 * DAO层基础异常
 */
public class DaoException extends RuntimeException {
    private final String errorCode;
    
    public DaoException(String message) {
        super(message);
        this.errorCode = "DAO_ERROR";
    }
    
    public DaoException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public DaoException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "DAO_ERROR";
    }
    
    public DaoException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}