package com.lg.financecloud.common.ai.factory;

import com.lg.financecloud.common.ai.service.AIModelService;
import com.lg.financecloud.common.ai.exception.AIServiceException;
import com.lg.financecloud.common.ai.service.impl.OpenAiModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI服务工厂
 * 负责创建和管理AI服务实例
 */
public class AIServiceFactory {
    

    /**
     * 获取指定类型的AI服务
     *
     * @param modelType 模型类型
     * @return 指定类型的AI服务实例
     * @throws AIServiceException 如果指定服务不可用
     */
    public static AIModelService getService(String modelType) throws AIServiceException {
         if(modelType == null){
             return new OpenAiModelService();
         }
         switch (modelType) {
             case "openai":
                 return new OpenAiModelService();
             default:
                 throw new AIServiceException("AI服务不可用");
         }
    }
    public static AIModelService getService( ) throws AIServiceException {
       return getService(null);
    }
    

}