package com.lg.dao.core;

import com.lg.dao.config.properties.BasicProperties;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.template.MybatisTemplateEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL模板管理器
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlTemplateManager {
    private final Map<String, String> templateCache = new ConcurrentHashMap<>();
    private final PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    private final BasicProperties.SqlTemplate config;
    private final MybatisTemplateEngine templateEngine;
    private final UnifiedCacheManager unifiedCacheManager;

    // SQL定义的正则表达式，匹配 #sqlId 格式
    private static final Pattern SQL_PATTERN = Pattern.compile("^\\s*#(\\w+)\\s*([\\s\\S]*?)(?=^\\s*#\\w+|\\Z)", Pattern.MULTILINE);

    public SqlTemplateManager(BasicProperties properties) {
        this.config = properties.getSqlTemplate();
        this.unifiedCacheManager = null;
        this.templateEngine = new MybatisTemplateEngine();
        loadAllTemplates();
    }

    public Map<String, String> getTemplateCache() {
        return templateCache;
    }

    public SqlTemplateManager(BasicProperties properties, UnifiedCacheManager unifiedCacheManager) {
        this.config = properties.getSqlTemplate();
        this.unifiedCacheManager = unifiedCacheManager;
        this.templateEngine = new MybatisTemplateEngine(unifiedCacheManager);
        loadAllTemplates();
    }

    /**
     * 加载所有SQL模板
     */
    private void loadAllTemplates() {
        try {
            for (String location : config.getLocations()) {
                Resource[] resources = resolver.getResources(location);
                for (Resource resource : resources) {
                    loadResource(resource);
                }
            }
        } catch (Exception e) {
            log.error("Failed to load SQL templates", e);
        }
    }

    /**
     * 加载单个资源文件
     */
    private void loadResource(Resource resource) throws Exception {
        try (InputStream inputStream = resource.getInputStream()) {
            String content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            String filename = resource.getFilename();
            if (filename != null) {
                parseSqlTemplate(filename, content);
            }
        }
    }

    /**
     * 解析SQL模板内容
     */
    private void parseSqlTemplate(String filename, String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        String groupName = filename.replace(".sql", "");
        Matcher matcher = SQL_PATTERN.matcher(content);
        while (matcher.find()) {
            String sqlId = matcher.group(1);
            String sqlContent = matcher.group(2).trim();
            templateCache.put(groupName + "." + sqlId, sqlContent);
        }
    }

    /**
     * 获取SQL模板并渲染
     */
    public MybatisTemplateEngine.RenderResult getSqlWithParams(String templateName, Map<String, Object> params) {
        String template = getSqlTemplate(templateName);
        if (params == null || params.isEmpty()) {
            return new MybatisTemplateEngine.RenderResult(template, new ArrayList<>());
        }
        // 直接渲染（不缓存），传递模板名称用于生成有意义的namespace
//        log.debug("跳过缓存，直接渲染SQL模板: {} (原因: {})",
//                 templateName,
//                 unifiedCacheManager == null ? "未启用缓存" : "参数包含集合类型");
        return templateEngine.renderWithParams(templateName, template, params);
    }

    /**
     * 检测参数中是否包含集合类型
     * 避免缓存陷阱：集合参数变化频繁，缓存意义不大且可能导致内存溢出
     */
    private boolean containsCollectionParam(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return false;
        }

        for (Object value : params.values()) {
            if (value instanceof Collection || value instanceof Object[] || value instanceof Map) {
                return true;
            }
        }
        return false;
    }

    /**
     * 构建缓存键
     * 格式：templateName:parameterSignature
     * 使用参数的类型和关键值构建签名，避免 hashCode 冲突
     */
    private String buildCacheKey(String templateName, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return templateName + ":null";
        }

        StringBuilder keyBuilder = new StringBuilder(templateName).append(":");

        // 根据参数数量和类型构建更稳定的缓存键
        if (params.size() == 1) {
            Object singleValue = params.values().iterator().next();
            if (singleValue instanceof String ||
                singleValue instanceof Number ||
                singleValue instanceof Boolean) {
                // 基本类型直接使用值
                keyBuilder.append("primitive:").append(singleValue);
            } else {
                // 复杂对象使用类名和 hashCode
                keyBuilder.append("object:")
                         .append(singleValue.getClass().getSimpleName())
                         .append(":")
                         .append(singleValue.hashCode());
            }
        } else {
            // 多参数使用键集合的哈希和参数数量
            keyBuilder.append("map:")
                     .append(params.size())
                     .append(":");
            // 对于 Map 参数，只使用键的集合作为签名（避免值变化导致缓存失效）
            keyBuilder.append(params.keySet().hashCode());
        }

        return keyBuilder.toString();
    }

    /**
     * 获取SQL模板
     */
    private String getSqlTemplate(String templateName) {
        if (templateName == null || templateName.trim().isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be empty");
        }

        // 直接查找完整模板名
        String sql = templateCache.get(templateName);
        if (sql != null) {
            return sql;
        }

        // 支持不带组名的模板名称
        if (!templateName.contains(".")) {
            for (String key : templateCache.keySet()) {
                if (key.endsWith("." + templateName)) {
                    return templateCache.get(key);
                }
            }
        }
        throw new IllegalArgumentException("Template not found: " + templateName);
    }
} 