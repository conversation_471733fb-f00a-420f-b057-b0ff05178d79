package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理员 API
 * 提供内部管理、监控和调试功能
 * 注意：此接口包含敏感信息，应限制访问权限
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/mq/admin")
public class TaskAdminController {
    
    @Autowired(required = false)
    private TaskRecoveryService taskRecoveryService;
    
    /**
     * 获取任务完整详情（包含敏感信息）
     */
    @GetMapping("/task/{taskId}/detail")
    public Map<String, Object> getTaskFullDetail(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto task = mqDbDao.getQueueLogById(taskId);
            
            if (task != null) {
                result.put("success", true);
                result.put("data", getFullTaskInfo(task));
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            log.error("查询任务完整详情失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 提取任务的监听器信息
     */
    @GetMapping("/task/{taskId}/listener")
    public Map<String, Object> getTaskListenerInfo(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto task = mqDbDao.getQueueLogById(taskId);
            
            if (task != null) {
                Map<String, Object> listenerInfo = extractListenerInfo(task);
                result.put("success", true);
                result.put("taskId", taskId);
                result.put("listenerInfo", listenerInfo);
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            log.error("提取任务监听器信息失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "提取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 手动重试指定任务
     */
    @PostMapping("/task/{taskId}/force-retry")
    public Map<String, Object> forceRetryTask(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto taskDto = mqDbDao.getQueueLogById(taskId);
            
            if (taskDto == null) {
                result.put("success", false);
                result.put("message", "任务不存在");
                return result;
            }
            
            // 提取监听器信息
            Map<String, Object> listenerInfo = extractListenerInfo(taskDto);
            String listenerClass = (String) listenerInfo.get("extractedClass");
            
            if (StrUtil.isNotBlank(listenerClass)) {
                // 尝试创建监听器并重新提交任务
                try {
                    RedisAmqListener listener = createListenerInstance(listenerClass);
                    if (listener != null) {
                        // 解析任务数据
                        BaseTask task = JSONUtil.toBean(taskDto.getRequestBody(), BaseTask.class);
                        task.setTaskIdentity(taskId);
                        
                        // 重新提交任务
                        String newTaskId = RedisJob.submitJob(task, listener);
                        
                        result.put("success", true);
                        result.put("message", "任务重新提交成功");
                        result.put("newTaskId", newTaskId);
                        result.put("listenerClass", listenerClass);
                    } else {
                        result.put("success", false);
                        result.put("message", "无法创建监听器实例: " + listenerClass);
                    }
                } catch (Exception e) {
                    result.put("success", false);
                    result.put("message", "重新提交任务失败: " + e.getMessage());
                }
            } else {
                result.put("success", false);
                result.put("message", "无法提取监听器信息");
                result.put("listenerInfo", listenerInfo);
            }
            
        } catch (Exception e) {
            log.error("强制重试任务失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "重试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 批量重试失败任务
     */
    @PostMapping("/task/batch-retry")
    public Map<String, Object> batchRetryFailedTasks(
            @RequestParam(defaultValue = "10") int limit) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            
            // 查找失败的任务
            List<JobMateMationDto> failedTasks = dao.lambdaQuery()
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL)
                .orderByDesc(JobMateMationDto::getCreateTime)
                .limit(limit)
                .list();
            
            int successCount = 0;
            int failCount = 0;
            
            for (JobMateMationDto taskDto : failedTasks) {
                try {
                    Map<String, Object> retryResult = forceRetryTask(taskDto.getJobId());
                    if ((Boolean) retryResult.get("success")) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("批量重试任务失败: {}", taskDto.getJobId(), e);
                    failCount++;
                }
            }
            
            result.put("success", true);
            result.put("totalTasks", failedTasks.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
        } catch (Exception e) {
            log.error("批量重试失败任务失败", e);
            result.put("success", false);
            result.put("message", "批量重试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取系统监控信息
     */
    @GetMapping("/monitor")
    public Map<String, Object> getSystemMonitor() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> monitor = new HashMap<>();
            
            // 恢复服务状态
            if (taskRecoveryService != null) {
                TaskRecoveryService.RecoveryStats stats = taskRecoveryService.getRecoveryStats();

                Map<String, Object> recoveryServiceInfo = new HashMap<>();
                recoveryServiceInfo.put("enabled", true);
                recoveryServiceInfo.put("stats", stats);
                recoveryServiceInfo.put("config", taskRecoveryService.getConfigInfo());
                monitor.put("recoveryService", recoveryServiceInfo);
            } else {
                Map<String, Object> recoveryServiceInfo = new HashMap<>();
                recoveryServiceInfo.put("enabled", false);
                monitor.put("recoveryService", recoveryServiceInfo);
            }
            
            // 任务统计
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            Map<String, Object> taskStats = new HashMap<>();
            taskStats.put("waiting", dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT).count());
            taskStats.put("processing", dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING).count());
            taskStats.put("success", dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_SUCCESS).count());
            taskStats.put("failed", dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL).count());
            monitor.put("taskStats", taskStats);
            
            result.put("success", true);
            result.put("data", monitor);
            
        } catch (Exception e) {
            log.error("获取系统监控信息失败", e);
            result.put("success", false);
            result.put("message", "获取监控信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取任务完整信息（包含敏感数据）
     */
    private Map<String, Object> getFullTaskInfo(JobMateMationDto task) {
        Map<String, Object> info = new HashMap<>();
        
        // 基本信息
        info.put("taskId", task.getJobId());
        info.put("title", task.getTitle());
        info.put("status", task.getStatus());
        info.put("queueCode", task.getJobType());
        info.put("createTime", task.getCreateTime());
        info.put("completeTime", task.getComplateTime());
        info.put("retryCount", task.getRetrys());
        info.put("responseBody", task.getResponseBody());
        
        // 完整的请求体（包含敏感信息）
        if (StrUtil.isNotBlank(task.getRequestBody())) {
            try {
                JSONObject requestBody = JSONUtil.parseObj(task.getRequestBody());
                info.put("requestBody", requestBody);
                
                // 提取监听器信息
                info.put("listenerInfo", extractListenerInfo(task));
                
            } catch (Exception e) {
                log.debug("解析任务请求体失败: {}", task.getJobId(), e);
                info.put("requestBody", task.getRequestBody());
            }
        }
        
        return info;
    }
    
    /**
     * 提取监听器信息
     */
    private Map<String, Object> extractListenerInfo(JobMateMationDto task) {
        Map<String, Object> info = new HashMap<>();
        
        try {
            if (StrUtil.isNotBlank(task.getRequestBody())) {
                JSONObject requestBody = JSONUtil.parseObj(task.getRequestBody());
                
                // 从 listenerClass 字段提取
                String listenerClass = requestBody.getStr("listenerClass");
                if (StrUtil.isNotBlank(listenerClass)) {
                    info.put("fromListenerField", listenerClass);
                }
                
                // 从 taskData 内部字段提取
                JSONObject taskData = requestBody.getJSONObject("taskData");
                if (taskData != null) {
                    String internalListener = taskData.getStr("_internal_listenerClass");
                    if (StrUtil.isNotBlank(internalListener)) {
                        info.put("fromInternalField", internalListener);
                    }
                    
                    String simpleName = taskData.getStr("_internal_listenerSimpleName");
                    if (StrUtil.isNotBlank(simpleName)) {
                        info.put("simpleName", simpleName);
                    }
                }
                
                // 推断的监听器类名
                String inferredClass = inferListenerClass(task);
                if (StrUtil.isNotBlank(inferredClass)) {
                    info.put("inferred", inferredClass);
                }
                
                // 确定最终使用的监听器类名
                String finalClass = StrUtil.isNotBlank(listenerClass) ? listenerClass :
                                   StrUtil.isNotBlank((String) info.get("fromInternalField")) ? 
                                   (String) info.get("fromInternalField") : inferredClass;
                info.put("extractedClass", finalClass);
            }
            
        } catch (Exception e) {
            log.debug("提取监听器信息失败: {}", task.getJobId(), e);
            info.put("error", e.getMessage());
        }
        
        return info;
    }
    
    /**
     * 推断监听器类名
     */
    private String inferListenerClass(JobMateMationDto task) {
        try {
            if (StrUtil.isBlank(task.getJobType())) {
                return null;
            }
            
            String queueCode = task.getJobType();
            String requestBody = task.getRequestBody();
            BaseTask baseTask = JSONUtil.toBean(requestBody, BaseTask.class);
            String moduleName = baseTask.getModuleName();
            
            if (queueCode.endsWith("Queue")) {
                String baseName = queueCode.substring(0, queueCode.length() - 5);
                String listenerName = baseName + "Listener";
                
                if (StrUtil.isNotBlank(moduleName)) {
                    return "com.lg.financecloud." + moduleName + ".listener." + listenerName;
                } else {
                    return "com.lg.financecloud.admin.listener." + listenerName;
                }
            }
            
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 创建监听器实例
     */
    private RedisAmqListener createListenerInstance(String className) {
        try {
            Class<?> clazz = Class.forName(className);
            if (RedisAmqListener.class.isAssignableFrom(clazz)) {
                return (RedisAmqListener) clazz.newInstance();
            }
            return null;
        } catch (Exception e) {
            log.error("创建监听器实例失败: {}", className, e);
            return null;
        }
    }
}
