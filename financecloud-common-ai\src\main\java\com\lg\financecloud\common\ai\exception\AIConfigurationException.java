package com.lg.financecloud.common.ai.exception;

/**
 * AI配置异常
 * 当AI服务配置出现错误时抛出此异常
 */
public class AIConfigurationException extends AIServiceException {
    private static final String ERROR_CODE = "CONFIG_ERROR";

    /**
     * 构造函数
     * @param message 错误信息
     */
    public AIConfigurationException(String message) {
        super(ERROR_CODE, String.format("AI configuration error: %s", message));
    }

    /**
     * 构造函数
     * @param message 错误信息
     * @param cause 原始异常
     */
    public AIConfigurationException(String message, Throwable cause) {
        super(ERROR_CODE, String.format("AI configuration error: %s", message), cause);
    }

    /**
     * 快速创建API密钥缺失异常
     * @param modelType 模型类型
     * @return AIConfigurationException实例
     */
    public static AIConfigurationException missingApiKey(String modelType) {
        return new AIConfigurationException(String.format("Missing API key for model type '%s'", modelType));
    }

    /**
     * 快速创建API地址缺失异常
     * @param modelType 模型类型
     * @return AIConfigurationException实例
     */
    public static AIConfigurationException missingEndpoint(String modelType) {
        return new AIConfigurationException(String.format("Missing API endpoint for model type '%s'", modelType));
    }

    /**
     * 快速创建参数配置错误异常
     * @param paramName 参数名称
     * @param value 错误的参数值
     * @return AIConfigurationException实例
     */
    public static AIConfigurationException invalidParameter(String paramName, Object value) {
        return new AIConfigurationException(String.format("Invalid parameter '%s' with value: %s", paramName, value));
    }
}