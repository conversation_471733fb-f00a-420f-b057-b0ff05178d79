package com.lg.dao.core.exception;

/**
 * SQL执行异常
 */
public class SqlExecutionException extends RuntimeException {
    
    private final String sql;
    
    public SqlExecutionException(String sql, String message) {
        super(message);
        this.sql = sql;
    }
    
    public SqlExecutionException(String sql, String message, Throwable cause) {
        super(message, cause);
        this.sql = sql;
    }
    
    public String getSql() {
        return sql;
    }
    
    @Override
    public String getMessage() {
        return super.getMessage() + "\nSQL: " + sql;
    }
}