package com.lg.dao.core.basic;

import com.lg.dao.core.GenericDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserDao extends GenericDao<User, Long> {

    public List<User> findActiveUsers() {
        return lambdaQuery()
                .eq(User::getUserName, "ACTIVE")
                .orderBy(User::getCreateTime)
                .list();
    }

    public void deleteUser(Long id){
         lambdaDelete().eq(User::getId, id).execute();
    }

    public void updateUser(User user){
        lambdaUpdate().eq(User::getId, user.getId()).update();
    }


}