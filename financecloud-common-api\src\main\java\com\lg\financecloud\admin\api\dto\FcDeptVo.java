/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 部门
 *
 * <AUTHOR>
 * @date 2021-07-16 13:45:39
 */
@Data
@ApiModel(value = "部门")
public class FcDeptVo {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */

    @ApiModelProperty(value = "id")
    private Long id;
    /**
     *
     */
    @ApiModelProperty(value = "", hidden = true)
    private Integer tenantId;
    /**
     * 账套
     */
    @ApiModelProperty(value = "账套")
    private Long accountId;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     *
     */
    @ApiModelProperty(value = "是否启用默认0,1是启用")
    private String enableFlag;
    /**
     *
     */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "")
    private LocalDateTime createTime;
    /**
     *
     */
    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "")
    private LocalDateTime updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "部门下员工工资计提科目id")
    private Long salaryAccrualSubjectId;
    @ApiModelProperty(value = "部门领料费用科目id")
    private Long depotLldFeeSubjectId;
}
