package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;

import java.util.*;

/**
 * 简单PDF模板测试
 */
public class SimplePdfTemplateTest {

    public static void main(String[] args) {
        System.out.println("开始PDF模板导出测试...");
        
        try {
            // 1. 创建简单的Excel模板
            createSimpleTemplate();
            System.out.println("✓ 模板创建成功");
            
            // 2. 测试PDF模板导出
            testPdfTemplateExport();
            System.out.println("✓ PDF模板导出测试完成");
            
        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createSimpleTemplate() {
        try {
            // 创建模板数据
            List<Map<String, Object>> templateData = new ArrayList<>();
            
            Map<String, Object> row1 = new HashMap<>();
            row1.put("col1", "${title}");
            row1.put("col2", "${date}");
            templateData.add(row1);
            
            Map<String, Object> row2 = new HashMap<>();
            row2.put("col1", "姓名");
            row2.put("col2", "部门");
            templateData.add(row2);
            
            Map<String, Object> row3 = new HashMap<>();
            row3.put("col1", "${#foreach employees}");
            row3.put("col2", "");
            templateData.add(row3);
            
            Map<String, Object> row4 = new HashMap<>();
            row4.put("col1", "${name}");
            row4.put("col2", "${department}");
            templateData.add(row4);
            
            Map<String, Object> row5 = new HashMap<>();
            row5.put("col1", "${/foreach}");
            row5.put("col2", "");
            templateData.add(row5);
            
            // 确保目录存在
            java.io.File dir = new java.io.File("src/test/java/excel/export/excel");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("列1", "列2")
                        .done()
                    .opsColumn()
                        .fields("col1", "col2")
                        .done()
                    .done()
                    .export("src/test/java/excel/export/excel/simple_template.xlsx");
            
        } catch (Exception e) {
            throw new RuntimeException("创建模板失败", e);
        }
    }

    private static void testPdfTemplateExport() {
        try {
            // 检查PDF依赖
            try {
                Class.forName("com.itextpdf.text.Document");
                System.out.println("✓ PDF依赖检查通过");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠ 警告: 未找到iText依赖，跳过PDF导出测试");
                return;
            }
            
            // 准备数据
            List<Map<String, Object>> employees = new ArrayList<>();
            
            Map<String, Object> emp1 = new HashMap<>();
            emp1.put("name", "张三");
            emp1.put("department", "技术部");
            employees.add(emp1);
            
            Map<String, Object> emp2 = new HashMap<>();
            emp2.put("name", "李四");
            emp2.put("department", "销售部");
            employees.add(emp2);
            
            // 执行PDF模板导出
            ExcelHelper.opsPdfTemplate("src/test/java/excel/export/excel/simple_template.xlsx")
                    .var("title", "员工列表")
                    .var("date", "2025-08-21")
                    .loop("employees", employees)
                    .enableChineseFont(true)
                    .fontSize(12f)
                    .exportTo("src/test/java/excel/export/excel/simple_result.pdf");
            
            // 验证文件是否生成
            java.io.File resultFile = new java.io.File("src/test/java/excel/export/excel/simple_result.pdf");
            if (resultFile.exists() && resultFile.length() > 0) {
                System.out.println("✓ PDF文件生成成功，大小: " + resultFile.length() + " 字节");
            } else {
                System.out.println("⚠ PDF文件未生成或为空");
            }
            
        } catch (Exception e) {
            System.err.println("PDF导出测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
