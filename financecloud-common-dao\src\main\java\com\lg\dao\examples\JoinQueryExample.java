package com.lg.dao.examples;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.query.JoinQueryBuilder;
import com.lg.dao.core.query.LambdaJoinQuery;
import com.lg.dao.core.sql.JoinType;
import com.lg.dao.core.sql.SqlBuilder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JOIN查询示例类
 * 展示如何使用多表连接查询功能
 */
public class JoinQueryExample {

    // 假设有一个用户DAO
    private GenericDao<User, Long> userDao;
    
    // 假设有一个订单DAO
    private GenericDao<Order, Long> orderDao;
    
    // 基础DAO，用于执行原生SQL
    private BaseDao baseDao;
    
    /**
     * 示例1：使用JoinQueryBuilder进行多表连接查询
     * 查询用户及其订单信息
     */
    public void example1_JoinQueryBuilder() {
        // 创建JOIN查询构建器
        JoinQueryBuilder joinQuery = userDao.joinQuery();
        
        // 构建查询
        List<Map<String, Object>> results = joinQuery
                .select("u.id", "u.username", "u.email", "o.id as order_id", "o.amount", "o.create_time")
                .from("user u")
                .leftJoin("order o")
                .on("u.id = o.user_id")
                .where()
                .and()
                .eq("u.status", "ACTIVE")
                .orderBy("o.create_time")
                .desc()
                .listMap();
        
        // 处理结果
        for (Map<String, Object> row : results) {
            System.out.println("用户ID: " + row.get("id"));
            System.out.println("用户名: " + row.get("username"));
            System.out.println("订单ID: " + row.get("order_id"));
            System.out.println("订单金额: " + row.get("amount"));
            System.out.println("订单时间: " + row.get("create_time"));
            System.out.println("------------------------");
        }
    }
    
    /**
     * 示例2：使用JoinQueryBuilder进行多表连接查询并映射到DTO对象
     * 查询用户及其订单信息，并映射到UserOrderDTO对象
     */
    public void example2_JoinQueryWithDTO() {
        // 创建JOIN查询构建器
        JoinQueryBuilder joinQuery = userDao.joinQuery();
        
        // 构建查询并映射到DTO
        List<UserOrderDTO> results = joinQuery
                .select("u.id", "u.username", "u.email", "o.id as orderId", "o.amount", "o.create_time as orderTime")
                .from("user u")
                .leftJoin("order o")
                .on("u.id = o.user_id")
                .where()
                .and()
                .eq("u.status", "ACTIVE")
                .orderBy("o.create_time")
                .desc()
                .list(UserOrderDTO.class);
        
        // 处理结果
        for (UserOrderDTO dto : results) {
            System.out.println("用户ID: " + dto.getId());
            System.out.println("用户名: " + dto.getUsername());
            System.out.println("订单ID: " + dto.getOrderId());
            System.out.println("订单金额: " + dto.getAmount());
            System.out.println("订单时间: " + dto.getOrderTime());
            System.out.println("------------------------");
        }
    }
    
    /**
     * 示例3：使用JoinQueryBuilder进行多表连接查询，包含多个JOIN
     * 查询用户、订单及订单详情
     */
    public void example3_MultiJoin() {
        // 创建JOIN查询构建器
        JoinQueryBuilder joinQuery = userDao.joinQuery();
        
        // 构建查询
        List<Map<String, Object>> results = joinQuery
                .select("u.id", "u.username", "o.id as order_id", "o.amount", "od.product_name", "od.quantity")
                .from("user u")
                .leftJoin("order o")
                .on("u.id = o.user_id")
                .leftJoin("order_detail od")
                .on("o.id = od.order_id")
                .where()
                .and()
                .eq("u.status", "ACTIVE")
                .and()
                .gt("o.amount", 100)
                .orderBy("o.create_time")
                .desc()
                .listMap();
        
        // 处理结果
        for (Map<String, Object> row : results) {
            System.out.println("用户ID: " + row.get("id"));
            System.out.println("用户名: " + row.get("username"));
            System.out.println("订单ID: " + row.get("order_id"));
            System.out.println("订单金额: " + row.get("amount"));
            System.out.println("产品名称: " + row.get("product_name"));
            System.out.println("产品数量: " + row.get("quantity"));
            System.out.println("------------------------");
        }
    }
    
    /**
     * 示例4：使用JoinQueryBuilder进行分组和聚合查询
     * 查询每个用户的订单总数和总金额
     */
    public void example4_GroupByAndAggregate() {
        // 创建JOIN查询构建器
        JoinQueryBuilder joinQuery = userDao.joinQuery();
        
        // 构建查询
        List<Map<String, Object>> results = joinQuery
                .select("u.id", "u.username", "COUNT(o.id) as order_count", "SUM(o.amount) as total_amount")
                .from("user u")
                .leftJoin("order o")
                .on("u.id = o.user_id")
                .where()
                .and()
                .eq("u.status", "ACTIVE")
                .groupBy("u.id", "u.username")
                .having()
                .and()
                .gt("COUNT(o.id)", 0)
                .orderBy("total_amount")
                .desc()
                .listMap();
        
        // 处理结果
        for (Map<String, Object> row : results) {
            System.out.println("用户ID: " + row.get("id"));
            System.out.println("用户名: " + row.get("username"));
            System.out.println("订单数量: " + row.get("order_count"));
            System.out.println("订单总金额: " + row.get("total_amount"));
            System.out.println("------------------------");
        }
    }
    
    /**
     * 示例5：直接使用SqlBuilder构建JOIN查询
     * 适用于更复杂的查询场景
     */
    public void example5_DirectSqlBuilder() {
        // 创建SQL构建器
        SqlBuilder sqlBuilder = new SqlBuilder();
        
        // 构建JOIN查询SQL
        sqlBuilder.SELECT("u.id", "u.username", "o.id as order_id", "o.amount")
                .FROM("user u")
                .LEFT_JOIN("order o")
                .ON("u.id = o.user_id")
                .WHERE()
                .AND()
                .EQ("u.status", "ACTIVE")
                .AND()
                .GT("o.amount", 100);
        
        // 获取SQL和参数
        String sql = sqlBuilder.getSql();
        Object[] params = {"ACTIVE", 100};
        
        // 执行查询
        List<Map<String, Object>> results = baseDao.selectMapList(sql, params);
        
        // 处理结果...
    }
    

    
    /**
     * 用户订单DTO类
     */
    public static class UserOrderDTO {
        private Long id;
        private String username;
        private String email;
        private Long orderId;
        private Double amount;
        private String orderTime;
        private String createTime;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Long getOrderId() { return orderId; }
        public void setOrderId(Long orderId) { this.orderId = orderId; }
        
        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        
        public String getOrderTime() { return orderTime; }
        public void setOrderTime(String orderTime) { this.orderTime = orderTime; }
    }
    
    /**
     * 用户实体类
     */
    @Data
    public static class User {
        private Long id;
        private String username;
        private String email;
        private String status;
        private String createTime;
        
        // Getters and Setters
    }
    
    /**
     * 订单实体类
     */
    public static class Order {
        private Long id;
        private Long userId;
        private Double amount;
        private String createTime;
        
        // Getters and Setters
    }
}