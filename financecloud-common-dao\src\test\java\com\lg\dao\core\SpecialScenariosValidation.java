package com.lg.dao.core;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.util.List;

/**
 * 特殊场景验证类
 * 验证@Transient、@TableField(exist=false)、insertable/updatable等特殊场景的处理
 */
@Slf4j
public class SpecialScenariosValidation {

    @Data
    @Table(name = "validation_entity")
    @TableName("validation_entity")
    public static class ValidationEntity {
        @Id
        @TableId
        @Column(name = "entity_id")
        private Long entityId;
        
        @Column(name = "entity_name")
        private String entityName;
        
        // 场景1: @Transient字段 - 数据库不存在
        @Transient
        private String transientField;
        
        // 场景2: @TableField(exist=false)字段 - 数据库不存在
        @TableField(exist = false)
        private String nonExistField;
        
        // 场景3: 组合注解字段
        @Transient
        @TableField(exist = false)
        private List<String> combinedField;
        
        // 场景4: 只读字段 - 不可插入和更新
        @Column(name = "readonly_field", insertable = false, updatable = false)
        private String readonlyField;
        
        // 场景5: 只能插入，不能更新
        @Column(name = "insert_only_field", insertable = true, updatable = false)
        private String insertOnlyField;
        
        // 场景6: 只能更新，不能插入
        @Column(name = "update_only_field", insertable = false, updatable = true)
        private String updateOnlyField;
        
        // 场景7: 计算字段 - 用于业务逻辑
        @TableField(exist = false)
        private String calculatedField;
        
        // 场景8: 临时状态字段
        @Transient
        private boolean temporaryStatus;
    }

    public static void main(String[] args) {
        try {
            log.info("=== 开始特殊场景验证 ===");
            
            // 验证EntityInfo创建
            EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(ValidationEntity.class);
            log.info("EntityInfo创建成功，字段数量: {}", entityInfo.getFields().size());
            
            // 验证场景1: @Transient字段应该被排除
            EntityInfo.FieldInfo transientField = entityInfo.findFieldInfoByColumn("transient_field");
            log.info("场景1 - @Transient字段查找结果: {}", transientField != null ? "存在(错误)" : "不存在(正确)");
            
            // 验证场景2: @TableField(exist=false)字段应该被排除
            EntityInfo.FieldInfo nonExistField = entityInfo.findFieldInfoByColumn("non_exist_field");
            log.info("场景2 - @TableField(exist=false)字段查找结果: {}", nonExistField != null ? "存在(错误)" : "不存在(正确)");
            
            // 验证场景3: 组合注解字段应该被排除
            EntityInfo.FieldInfo combinedField = entityInfo.findFieldInfoByColumn("combined_field");
            log.info("场景3 - 组合注解字段查找结果: {}", combinedField != null ? "存在(错误)" : "不存在(正确)");
            
            // 验证场景4-6: insertable/updatable属性
            log.info("\n=== insertable/updatable属性验证 ===");
            for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
                String fieldName = field.getPropertyName();
                switch (fieldName) {
                    case "readonlyField":
                        log.info("场景4 - readonly字段: insertable={}, updatable={}", 
                            field.isInsertable(), field.isUpdatable());
                        break;
                    case "insertOnlyField":
                        log.info("场景5 - insert_only字段: insertable={}, updatable={}", 
                            field.isInsertable(), field.isUpdatable());
                        break;
                    case "updateOnlyField":
                        log.info("场景6 - update_only字段: insertable={}, updatable={}", 
                            field.isInsertable(), field.isUpdatable());
                        break;
                    case "entityName":
                        log.info("正常字段 - entity_name: insertable={}, updatable={}", 
                            field.isInsertable(), field.isUpdatable());
                        break;
                }
            }
            
            // 验证场景7: 计算字段的属性映射
            EntityInfo.FieldInfo calculatedField = entityInfo.getFieldInfo("calculatedField");
            log.info("\n场景7 - 计算字段属性映射: {}", calculatedField != null ? "支持" : "不支持");
            
            // 验证insert/update操作的字段过滤
            log.info("\n=== insert/update操作字段过滤验证 ===");
            
            // 统计可插入字段
            long insertableCount = entityInfo.getFields().stream()
                .filter(EntityInfo.FieldInfo::isInsertable)
                .count();
            log.info("可插入字段数量: {}", insertableCount);
            
            // 统计可更新字段
            long updatableCount = entityInfo.getFields().stream()
                .filter(EntityInfo.FieldInfo::isUpdatable)
                .count();
            log.info("可更新字段数量: {}", updatableCount);
            
            // 验证字段映射表构建
            log.info("\n=== 字段映射表验证 ===");
            log.info("列映射表大小: {}", entityInfo.getColumnToFieldMap().size());
            log.info("属性映射表大小: {}", entityInfo.getPropertyToFieldMap().size());
            
            // 测试一些特殊列名的查找
            String[] testColumns = {"entity_id", "entity_name", "readonly_field", "insert_only_field", "update_only_field"};
            for (String column : testColumns) {
                EntityInfo.FieldInfo field = entityInfo.findFieldInfoByColumn(column);
                log.info("列 '{}' 查找结果: {}", column, field != null ? field.getPropertyName() : "未找到");
            }
            
            log.info("\n=== 特殊场景验证完成 ===");
            
        } catch (Exception e) {
            log.error("验证失败", e);
        }
    }
}
