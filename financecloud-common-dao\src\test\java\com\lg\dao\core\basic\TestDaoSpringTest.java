package com.lg.dao.core.basic;

import com.lg.dao.config.LightORMAutoConfiguration;
import com.lg.dao.core.BaseDao;
import com.lg.dao.core.sql.SqlBuilder;
import com.lg.dao.core.tenant.TenantContext;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

@SpringBootTest
@ActiveProfiles("test")  // 激活test配置文件
@Transactional
@Sql(scripts = "classpath:data.sql")
public class TestDaoSpringTest {

    @TestConfiguration
    @Import(LightORMAutoConfiguration.class)
    static class TestConfig {
        @Bean
        public BaseDao baseDao() {
            return new BaseDao();
        }
    }

    @Autowired
    private BaseDao baseDao;

    // 测试实体类
    @Table(name = "t_user")
    @Data
    public static class User {
        @Id
        private Long id;
        @Column(name = "user_name")
        private String userName;
        private Integer age;
        @Column(name = "tenant_id")
        private String tenantId;
        @Column(name = "create_time")
        private java.sql.Timestamp createTime;
        @Column(name = "update_time")
        private java.sql.Timestamp updateTime;
    }

    @Test
    public void testInsert() {
        User user = new User();
        user.setUserName("testUser");
        user.setAge(25);
        user.setTenantId("1001");
        user.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        user.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        
        int result = baseDao.insert(user);
        System.out.println("Insert result: " + result);
        assert result > 0;
    }

    @Test
    public void testSelect() {
        SqlBuilder builder = new SqlBuilder()
            .append("SELECT * FROM t_user WHERE user_name = ?")
            .addParam("张三");
        TenantContext.setTenantId("1001");
        User user = baseDao.selectOne(User.class, builder);
        System.out.println("Selected user: " + user);
        assert user != null;
        assert "张三".equals(user.getUserName());
    }

    @Test
    public void testUpdate() {
        // 先查询一个用户
        SqlBuilder selectBuilder = new SqlBuilder()
            .append("SELECT * FROM t_user WHERE user_name = ?")
            .addParam("张三");
        TenantContext.setTenantId("1001");
        User user = baseDao.selectOne(User.class, selectBuilder);
        
        if (user != null) {
            user.setAge(26);
            user.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
            int result = baseDao.update(user);
            System.out.println("Update result: " + result);
            assert result > 0;
        }
    }

    @Test
    public void testDelete() {
        // 先插入一个测试用户
        User user = new User();
        user.setUserName("toDelete");
        user.setAge(30);
        user.setTenantId("1001");
        user.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        user.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        
        baseDao.insert(user);
        
        // 查询刚插入的用户
        SqlBuilder selectBuilder = new SqlBuilder()
            .append("SELECT * FROM t_user WHERE user_name = ?")
            .addParam("toDelete");
        TenantContext.setTenantId("1001");
        User insertedUser = baseDao.selectOne(User.class, selectBuilder);
        
        if (insertedUser != null) {
            int result = baseDao.delete(insertedUser);
            System.out.println("Delete result: " + result);
            assert result > 0;
        }
    }

    @Test
    public void testSelectList() {
        SqlBuilder builder = new SqlBuilder()
            .append("SELECT * FROM t_user WHERE tenant_id = ?")
            .addParam("1001");
        List<User> users = baseDao.selectList(User.class, builder);
        System.out.println("Selected users count: " + users.size());
        users.forEach(System.out::println);
        assert users.size() >= 2; // 根据data.sql，应该有至少2个用户
    }

    @Test
    public void testCache() {


    }
}