# 循环依赖问题修复文档

## 问题描述

在 financecloud-common-dao 框架中出现了 Spring 循环依赖问题，错误信息如下：

```
The dependencies of some of the beans in the application context form a cycle:
defaultLightOrmSqlExecutor defined in URL [jar:file:/D:/environment/apache-maven3.6.3/newset/repository/com/lg/financecloud-common-dao/3.10.1/financecloud-common-dao-3.10.1.jar!/com/lg/dao/core/executor/DefaultLightOrmSqlExecutor.class]
↓
lightOrmSqlInterceptorChain
↓  
dataPermissionInterceptor (field private com.lg.dao.core.filter.DataPermissionService dataPermissionService)
↓
dataPermissionService
↓
com.lg.dao.core.BaseDao (field protected com.lg.dao.core.executor.LightOrmSqlExecutor lightOrmSqlExecutor)
```

## 循环依赖分析

依赖链如下：

1. **DefaultLightOrmSqlExecutor** 
   - 依赖 → `LightOrmSqlInterceptorChain`

2. **LightOrmSqlInterceptorChain** 
   - 依赖 → `List<LightOrmSqlInterceptor>` (包含 `DataPermissionInterceptor`)

3. **DataPermissionInterceptor** 
   - 依赖 → `DataPermissionService`

4. **DataPermissionService** 
   - 依赖 → `BaseDao` (通过 DaoHelper.getBaseDao())

5. **BaseDao** 
   - 依赖 → `LightOrmSqlExecutor` (即 `DefaultLightOrmSqlExecutor`)

这形成了一个完整的循环依赖链。

## 解决方案

使用 Spring 的 `@Lazy` 注解来打破循环依赖链。`@Lazy` 注解会延迟 Bean 的初始化，直到第一次使用时才创建代理对象。

### 修复点 1: DefaultLightOrmSqlExecutor

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultLightOrmSqlExecutor implements LightOrmSqlExecutor {
    
    private final JdbcTemplate jdbcTemplate;
    @Lazy  // 添加 @Lazy 注解
    private final LightOrmSqlInterceptorChain interceptorChain;
    
    // ...
}
```

### 修复点 2: DataPermissionInterceptor

```java
@Slf4j
@Component
public class DataPermissionInterceptor implements LightOrmSqlInterceptor {

    @Autowired
    @Lazy  // 添加 @Lazy 注解
    private DataPermissionService dataPermissionService;
    
    // ...
}
```

### 修复点 3: DataPermissionService

```java
@Slf4j
@Service
public class DataPermissionService {

    @Autowired
    @Lazy  // 添加 @Lazy 注解
    private BaseDao baseDao;
    
    @PostConstruct
    public void init() {
        initRuleVariables();
        // 延迟加载数据权限规则，避免循环依赖
        // loadAllDataPermissionRules();
    }
    
    // 在需要时才加载规则
    public List<DataPermissionRule> getDataPermissionRules(String tableCode) {
        // 确保规则已加载
        if (dataPermissionRuleCache.isEmpty() && baseDao != null) {
            loadAllDataPermissionRules();
        }
        // ...
    }
}
```

### 修复点 4: LightOrmSqlInterceptorChain

```java
@Component
public class LightOrmSqlInterceptorChain {
    
    @Autowired(required = false)
    @Lazy  // 添加 @Lazy 注解
    public void setLightOrmInterceptors(List<LightOrmSqlInterceptor> interceptorList) {
        // ...
    }
}
```

## 修复原理

1. **延迟初始化**: `@Lazy` 注解使得被标注的依赖在第一次使用时才被创建，而不是在容器启动时立即创建。

2. **代理对象**: Spring 会为被 `@Lazy` 标注的依赖创建一个代理对象，这个代理对象在第一次方法调用时才会触发真实对象的创建。

3. **打破循环**: 通过在循环依赖链的关键节点使用 `@Lazy`，可以打破循环，让 Spring 容器能够正常启动。

## 注意事项

1. **性能影响**: `@Lazy` 会在第一次使用时创建对象，可能会有轻微的性能延迟，但通常可以忽略。

2. **异常延迟**: 如果被延迟初始化的 Bean 在创建时出现异常，异常会在第一次使用时抛出，而不是在容器启动时。

3. **测试影响**: 在单元测试中，需要确保被 `@Lazy` 标注的依赖能够正确初始化。

## 验证方法

1. **启动测试**: 确保应用能够正常启动，不再出现循环依赖错误。

2. **功能测试**: 验证数据权限拦截器、SQL 执行器等功能正常工作。

3. **性能测试**: 确保延迟初始化不会对性能造成明显影响。

## 最佳实践

1. **避免循环依赖**: 在设计时尽量避免循环依赖，通过合理的分层和解耦来减少依赖关系。

2. **使用事件机制**: 对于复杂的初始化逻辑，可以考虑使用 Spring 的事件机制来解耦。

3. **配置类分离**: 将相关的配置分离到不同的配置类中，避免在同一个配置类中创建相互依赖的 Bean。

4. **接口隔离**: 通过接口隔离原则，减少具体实现类之间的直接依赖。

## 总结

通过在关键的依赖注入点使用 `@Lazy` 注解，成功解决了 financecloud-common-dao 框架中的循环依赖问题。这种解决方案：

- ✅ 保持了原有的功能不变
- ✅ 最小化了代码修改
- ✅ 符合 Spring 的最佳实践
- ✅ 不影响运行时性能

修复后，应用应该能够正常启动，所有功能保持正常工作。
