/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.dto;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.InputStream;

@Data
public class AttachFileVo  {
private static final long serialVersionUID = 1L;



    private Integer attachType;



    private String base64File;
    private InputStream file;

    private Long fileSize;

    private String orgFileName;


    public AttachFileVo(Integer attachType, InputStream file, Long fileSize, String orgFileName) {
        this.attachType = attachType;
        this.file = file;
        this.fileSize = fileSize;
        this.orgFileName = orgFileName;
    }

    public AttachFileVo(Integer attachType, String base64File, Long fileSize, String orgFileName) {
        this.attachType = attachType;
        this.base64File = base64File;
        this.fileSize = fileSize;
        this.orgFileName = orgFileName;
    }

    public AttachFileVo() {
    }
}
