package com.lg.dao.core.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.lg.dao.config.properties.CacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 统一缓存管理器
 * 整合框架中所有缓存，提供统一的缓存管理接口
 * 通过LightORMAutoConfiguration进行Bean配置，不使用@Component注解
 *
 * <AUTHOR>
 */
@Slf4j
public class UnifiedCacheManager implements InitializingBean, DisposableBean {
    
    /**
     * 缓存实例注册表
     */
    private final Map<String, Cache<String, Object>> cacheRegistry = new ConcurrentHashMap<>();

    /**
     * 缓存配置属性
     */
    private final CacheProperties cacheProperties;

    /**
     * 默认构造函数
     */
    public UnifiedCacheManager() {
        this.cacheProperties = new CacheProperties();
    }

    /**
     * 带配置的构造函数
     */
    public UnifiedCacheManager(CacheProperties properties) {
        this.cacheProperties = properties != null ? properties : new CacheProperties();
    }
    
    /**
     * 缓存类型
     */
    public enum CacheType {
        DAO_CACHE("dao"),
        ENTITY_CACHE("entity"),
        PERMISSION_CACHE("permission"),
        SQL_TEMPLATE_CACHE("sql_template"),
        METADATA_CACHE("metadata"),
        ENTITY_FIELD_MAPPING_CACHE("entity_field_mapping"),
        MYBATIS_PROXY_CACHE("mybatis_proxy"),
        MYBATIS_SQL_PARSE_CACHE("mybatis_sql_parse"),
        QUERY_RESULT_CACHE("query_result");

        private final String name;

        CacheType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("初始化统一缓存管理器，缓存启用状态: {}", cacheProperties.isEnable());

        if (!cacheProperties.isEnable()) {
            log.info("缓存功能已禁用，跳过缓存实例创建");
            return;
        }

        // 初始化预定义缓存
        for (CacheType cacheType : CacheType.values()) {
            createCache(cacheType);
        }

        log.info("统一缓存管理器初始化完成，已创建 {} 个缓存实例", cacheRegistry.size());
        logCacheConfiguration();
    }
    
    /**
     * 创建缓存实例
     */
    private Cache<String, Object> createCache(CacheType cacheType) {
        // 获取该缓存类型的配置
        CacheProperties.CacheConfig config = cacheProperties.getConfig(cacheType.getName());

        Cache<String, Object> cache = Caffeine.newBuilder()
                .maximumSize(config.getMaxSize())
                .expireAfterWrite(config.getExpireSeconds(), TimeUnit.SECONDS)
                .recordStats()
                .build();

        cacheRegistry.put(cacheType.getName(), cache);
        log.info("创建缓存: {} | 容量: {} | 过期时间: {}s | 描述: {}",
                cacheType.getName(),
                config.getMaxSize(),
                config.getExpireSeconds(),
                config.getDescription() != null ? config.getDescription() : "无描述");

        return cache;
    }

    /**
     * 记录缓存配置信息
     */
    private void logCacheConfiguration() {
        log.info("=== 缓存配置详情 ===");
        for (CacheType cacheType : CacheType.values()) {
            CacheProperties.CacheConfig config = cacheProperties.getConfig(cacheType.getName());
            boolean isCustomConfig = cacheProperties.getTypes().containsKey(cacheType.getName());
            log.info("缓存类型: {} | 配置来源: {} | 容量: {} | 过期: {}s | 说明: {}",
                    cacheType.getName(),
                    isCustomConfig ? "自定义配置" : "默认配置",
                    config.getMaxSize(),
                    config.getExpireSeconds(),
                    config.getDescription() != null ? config.getDescription() : "无描述");
        }
        log.info("=== 缓存配置详情结束 ===");
    }


    
    /**
     * 获取缓存实例
     */
    public Cache<String, Object> getCache(CacheType cacheType) {
        return cacheRegistry.get(cacheType.getName());
    }
    
    /**
     * 获取缓存实例（按名称）
     */
    public Cache<String, Object> getCache(String cacheName) {
        return cacheRegistry.get(cacheName);
    }
    
    /**
     * 获取缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(CacheType cacheType, String key, Supplier<T> loader) {
        // 如果缓存被禁用，直接执行加载器
        if (!cacheProperties.isEnable()) {
            log.debug("缓存已禁用，直接执行加载器: {}", cacheType.getName());
            return loader.get();
        }

        Cache<String, Object> cache = getCache(cacheType);
        if (cache == null) {
            log.warn("缓存实例不存在: {}, 直接执行加载器", cacheType.getName());
            return loader.get();
        }

        try {
            return (T) cache.get(key, k -> loader.get());
        } catch (Exception e) {
            log.error("缓存获取失败: cacheType={}, key={}", cacheType.getName(), key, e);
            return loader.get();
        }
    }
    
    /**
     * 设置缓存值
     */
    public void put(CacheType cacheType, String key, Object value) {
        Cache<String, Object> cache = getCache(cacheType);
        if (cache != null) {
            cache.put(key, value);
        } else {
            log.warn("缓存实例不存在: {}, 无法设置缓存", cacheType.getName());
        }
    }
    
    /**
     * 删除缓存值
     */
    public void remove(CacheType cacheType, String key) {
        Cache<String, Object> cache = getCache(cacheType);
        if (cache != null) {
            cache.invalidate(key);
        }
    }
    
    /**
     * 清空指定缓存
     */
    public void clear(CacheType cacheType) {
        Cache<String, Object> cache = getCache(cacheType);
        if (cache != null) {
            cache.invalidateAll();
            log.info("已清空缓存: {}", cacheType.getName());
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAll() {
        cacheRegistry.values().forEach(Cache::invalidateAll);
        log.info("已清空所有缓存");
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, CacheStats> getStats() {
        Map<String, CacheStats> stats = new ConcurrentHashMap<>();
        cacheRegistry.forEach((name, cache) -> stats.put(name, cache.stats()));
        return stats;
    }
    
    /**
     * 打印缓存统计信息
     */
    public void printStats() {
        log.info("=== 缓存统计信息 ===");
        cacheRegistry.forEach((name, cache) -> {
            CacheStats stats = cache.stats();
            log.info("缓存: {}, 大小: {}, 命中率: {:.2f}%, 请求数: {}, 命中数: {}, 未命中数: {}", 
                    name, cache.estimatedSize(), stats.hitRate() * 100,
                    stats.requestCount(), stats.hitCount(), stats.missCount());
        });
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanup() {
        cacheRegistry.values().forEach(Cache::cleanUp);
        log.debug("已执行缓存清理");
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, CacheStats> getCacheStats() {
        Map<String, CacheStats> statsMap = new HashMap<>();
        for (Map.Entry<String, Cache<String, Object>> entry : cacheRegistry.entrySet()) {
            statsMap.put(entry.getKey(), entry.getValue().stats());
        }
        return statsMap;
    }

    /**
     * 打印缓存统计信息
     */
    public void logCacheStats() {
        if (!cacheProperties.isEnable()) {
            log.info("缓存已禁用，无统计信息");
            return;
        }

        log.info("=== 缓存统计信息 ===");
        for (Map.Entry<String, Cache<String, Object>> entry : cacheRegistry.entrySet()) {
            CacheStats stats = entry.getValue().stats();
            log.info("缓存: {} | 命中率: {:.2f}% | 请求数: {} | 命中数: {} | 未命中数: {} | 驱逐数: {}",
                    entry.getKey(),
                    stats.hitRate() * 100,
                    stats.requestCount(),
                    stats.hitCount(),
                    stats.missCount(),
                    stats.evictionCount());
        }
        log.info("=== 缓存统计信息结束 ===");
    }

    /**
     * 获取缓存配置信息
     */
    public Map<String, CacheProperties.CacheConfig> getCacheConfigs() {
        Map<String, CacheProperties.CacheConfig> configMap = new HashMap<>();
        for (CacheType cacheType : CacheType.values()) {
            configMap.put(cacheType.getName(), cacheProperties.getConfig(cacheType.getName()));
        }
        return configMap;
    }

    @Override
    public void destroy() throws Exception {
        log.info("销毁统一缓存管理器");
        logCacheStats(); // 销毁前记录最终统计信息
        clearAll();
        cacheRegistry.clear();
    }
}
