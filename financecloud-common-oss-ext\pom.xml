<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lg</groupId>
		<artifactId>financecloud-common</artifactId>
		<version>3.10.1</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>financecloud-common-oss-ext</artifactId>
	<packaging>jar</packaging>

	<description>financecloud oss扩展</description>



	<dependencies>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-sts</artifactId>
			<version>${aliyun-java-sdk-sts.version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>${aliyun-java-sdk-core.version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>${aliyun-sdk-oss.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>

		</dependency>
		<dependency>
			<groupId>com.lg</groupId>
			<artifactId>financecloud-common-core</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>
</project>