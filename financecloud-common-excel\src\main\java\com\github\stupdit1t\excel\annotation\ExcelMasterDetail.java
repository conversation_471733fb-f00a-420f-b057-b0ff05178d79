package com.github.stupdit1t.excel.annotation;

import java.lang.annotation.*;

/**
 * 主从表导出注解
 * 用于标记主从表关系的导出配置
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelMasterDetail {

    /**
     * Sheet名称
     */
    String sheetName() default "";

    /**
     * 主从表之间的间隔行数
     */
    int spacingRows() default 1;

    /**
     * 是否显示明细表头
     */
    boolean showDetailHeader() default true;

    /**
     * 是否为每个主表记录重复明细表头
     */
    boolean repeatDetailHeaderForEachMaster() default false;

    /**
     * 明细数据字段名
     * 指定主表对象中包含明细数据的字段名
     */
    String detailField() default "details";
}
