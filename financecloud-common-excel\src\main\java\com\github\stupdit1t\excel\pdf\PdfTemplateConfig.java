package com.github.stupdit1t.excel.pdf;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PDF模板配置类
 * 支持基于模板的PDF生成，包括变量替换和循环数据处理
 */
public class PdfTemplateConfig extends PdfExportConfig {

    /**
     * 模板文件路径
     */
    private String templatePath;

    /**
     * 模板输入流
     */
    private InputStream templateInputStream;

    /**
     * 变量替换映射
     */
    private Map<String, Object> variables = new HashMap<>();

    /**
     * 循环数据映射
     */
    private Map<String, List<?>> loops = new HashMap<>();

    /**
     * 是否保留模板格式
     */
    private boolean preserveTemplateFormat = true;

    /**
     * 模板类型
     */
    public enum TemplateType {
        EXCEL_TO_PDF,  // Excel模板转PDF
        HTML_TO_PDF,   // HTML模板转PDF
        DIRECT_PDF     // 直接PDF模板
    }

    /**
     * 模板类型，默认为Excel转PDF
     */
    private TemplateType templateType = TemplateType.EXCEL_TO_PDF;

    /**
     * 构造函数
     */
    public PdfTemplateConfig() {
        super();
    }

    /**
     * 构造函数
     * @param templatePath 模板文件路径
     */
    public PdfTemplateConfig(String templatePath) {
        super();
        this.templatePath = templatePath;
    }

    /**
     * 构造函数
     * @param templateInputStream 模板输入流
     */
    public PdfTemplateConfig(InputStream templateInputStream) {
        super();
        this.templateInputStream = templateInputStream;
    }

    // Getter和Setter方法
    public String getTemplatePath() {
        return templatePath;
    }

    public PdfTemplateConfig setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
        return this;
    }

    public InputStream getTemplateInputStream() {
        return templateInputStream;
    }

    public PdfTemplateConfig setTemplateInputStream(InputStream templateInputStream) {
        this.templateInputStream = templateInputStream;
        return this;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public PdfTemplateConfig setVariables(Map<String, Object> variables) {
        this.variables = variables;
        return this;
    }

    /**
     * 添加变量
     * @param key 变量名
     * @param value 变量值
     * @return 配置对象
     */
    public PdfTemplateConfig addVariable(String key, Object value) {
        this.variables.put(key, value);
        return this;
    }

    /**
     * 批量添加变量
     * @param variables 变量映射
     * @return 配置对象
     */
    public PdfTemplateConfig addVariables(Map<String, Object> variables) {
        this.variables.putAll(variables);
        return this;
    }

    public Map<String, List<?>> getLoops() {
        return loops;
    }

    public PdfTemplateConfig setLoops(Map<String, List<?>> loops) {
        this.loops = loops;
        return this;
    }

    /**
     * 添加循环数据
     * @param key 循环变量名
     * @param data 循环数据列表
     * @return 配置对象
     */
    public PdfTemplateConfig addLoop(String key, List<?> data) {
        this.loops.put(key, data);
        return this;
    }

    public boolean isPreserveTemplateFormat() {
        return preserveTemplateFormat;
    }

    public PdfTemplateConfig setPreserveTemplateFormat(boolean preserveTemplateFormat) {
        this.preserveTemplateFormat = preserveTemplateFormat;
        return this;
    }

    public TemplateType getTemplateType() {
        return templateType;
    }

    public PdfTemplateConfig setTemplateType(TemplateType templateType) {
        this.templateType = templateType;
        return this;
    }

    /**
     * 链式调用重写父类方法
     */
    @Override
    public PdfTemplateConfig setFontSize(float fontSize) {
        super.setFontSize(fontSize);
        return this;
    }

    @Override
    public PdfTemplateConfig setOrientation(PageOrientation orientation) {
        super.setOrientation(orientation);
        return this;
    }

    @Override
    public PdfTemplateConfig setPageSize(PageSize pageSize) {
        super.setPageSize(pageSize);
        return this;
    }

    @Override
    public PdfTemplateConfig setMarginTop(float marginTop) {
        super.setMarginTop(marginTop);
        return this;
    }

    @Override
    public PdfTemplateConfig setMarginBottom(float marginBottom) {
        super.setMarginBottom(marginBottom);
        return this;
    }

    @Override
    public PdfTemplateConfig setMarginLeft(float marginLeft) {
        super.setMarginLeft(marginLeft);
        return this;
    }

    @Override
    public PdfTemplateConfig setMarginRight(float marginRight) {
        super.setMarginRight(marginRight);
        return this;
    }

    @Override
    public PdfTemplateConfig setShowGridLines(boolean showGridLines) {
        super.setShowGridLines(showGridLines);
        return this;
    }

    @Override
    public PdfTemplateConfig setHeaderBackgroundColor(int headerBackgroundColor) {
        super.setHeaderBackgroundColor(headerBackgroundColor);
        return this;
    }

    @Override
    public PdfTemplateConfig setHeaderFontColor(int headerFontColor) {
        super.setHeaderFontColor(headerFontColor);
        return this;
    }

    @Override
    public PdfTemplateConfig setDataFontColor(int dataFontColor) {
        super.setDataFontColor(dataFontColor);
        return this;
    }

    @Override
    public PdfTemplateConfig setEnableChineseFont(boolean enableChineseFont) {
        super.setEnableChineseFont(enableChineseFont);
        return this;
    }

    @Override
    public PdfTemplateConfig setCustomFontPath(String customFontPath) {
        super.setCustomFontPath(customFontPath);
        return this;
    }

    @Override
    public PdfTemplateConfig setMargins(float margin) {
        super.setMargins(margin);
        return this;
    }

    @Override
    public PdfTemplateConfig setMargins(float top, float right, float bottom, float left) {
        super.setMargins(top, right, bottom, left);
        return this;
    }
}
