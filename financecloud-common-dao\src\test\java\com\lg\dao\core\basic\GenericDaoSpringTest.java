package com.lg.dao.core.basic;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.dao.config.LightORMAutoConfiguration;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.template.MybatisTemplateEngine;
import com.lg.dao.core.tenant.TenantContext;
import com.lg.dao.mybatis.EnableLightDaoMappers;
import com.lg.dao.mybatis.example.User;
import com.lg.dao.mybatis.example.UserMapper;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Sql(scripts = "classpath:data.sql")
public class GenericDaoSpringTest {

    @TestConfiguration
    @Import(LightORMAutoConfiguration.class)
    /**
     * 测试配置类
     * 使用特定Mapper接口替换和延迟加载
     */
    @EnableLightDaoMappers(
            mapperClasses = {UserMapper.class},
            lazyInit = true
    )
    static class TestConfig {
        @Bean
        public UserTestDao userTestDao() {
            return new UserTestDao();
        }
        @Bean
        public UserTestDao1 userTestDao1() {
            return new UserTestDao1();
        }
    }

    @Autowired
    private UserTestDao userTestDao;
    @Autowired
    private UserTestDao1 userTestDao1;


//    @Autowired
//    private UserMapper userMapper;
//
//    @Test
//    public void testLazyMapper() {
//        // 测试查询，这时才会创建代理
//        List<com.lg.dao.mybatis.example.User> allUsers = userMapper.getAllUsers();
//
//        System.err.println(JSONUtil.toJsonStr(allUsers));
//    }

    // 测试实体类
    @Table(name = "t_user")
    @Data
    public static class User {
        @Id
        private Long id;
        @Column(name = "user_name")
        private String userName;
        private Integer age;
        @Column(name = "tenant_id")
        private String tenantId;
        @Column(name = "create_time")
        private java.sql.Timestamp createTime;
        @Column(name = "update_time")
        private java.sql.Timestamp updateTime;
    }

    // GenericDao实现类
    public static class UserTestDao extends GenericDao<User, Long> {
    }
    public static class UserTestDao1 extends GenericDao<User, Long> {
    }

    @Test
    public void testInsertEntity() {
        User user = new User();
       // user.setId(1L);
        user.setUserName("genericTestUser");
        user.setAge(28);
        user.setTenantId("1001");
        user.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        user.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        
        int result = userTestDao.insert(user);
        System.out.println("Insert result: " + result);
        List<User> list = userTestDao.lambdaQuery().list();
        System.err.println(JSONUtil.toJsonStr(list));

        int result1 = userTestDao1.insert(user);
        System.out.println("Insert result: " + result1);
        assert result > 0;
    }

    @Test
    public void testGetById() {
        TenantContext.setTenantId("1001");
        User user = userTestDao.getById(1L);
        System.out.println("Selected user: " + user);
        assert user != null;
        assert "张三".equals(user.getUserName());
    }

    @Test
    public void testUpdateById() {
        TenantContext.setTenantId("1001");
        User user = userTestDao.getById(1L);
        
        if (user != null) {
            user.setAge(30);
            user.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
            int result = userTestDao.updateById(user);
            System.out.println("Update result: " + result);
            assert result > 0;
        }
    }

    @Test
    public void testDeleteById() {
        TenantContext.setTenantId("1001");
        int result = userTestDao.deleteById(2L);
        System.out.println("Delete result: " + result);
        assert result > 0;
    }

    @Test
    public void testLambdaQuery() {
        TenantContext.setTenantId("1001");
        List<User> users = userTestDao.lambdaQuery()
                .eq(User::getTenantId, "1001")
                .ge(User::getAge, 20).in(User::getUserName, "张三", "李四")
                .list();
        
        System.out.println("Lambda query users count: " + users.size());
        users.forEach(System.out::println);
        System.err.println(JSONUtil.toJsonStr(users));

        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("userNames", ListUtil.of("张三", "张三丰"));

        // 执行测试
        JSONArray jsonArray = userTestDao.selectJsonArrayByTemplate("user.findByUserNames", params);


        System.err.println(JSONUtil.toJsonStr(jsonArray));


    }

    @Test
    public void testQueryBuilder() {
        TenantContext.setTenantId("1001");
        List<User> users = userTestDao.query()
                .eq("tenant_id", "1001")
                .like("user_name", "%三%").list();
        
        System.out.println("Query builder users count: " + users.size());
        users.forEach(System.out::println);
        assert users.size() > 0;
    }

    @Test
    public void testCount() {
        TenantContext.setTenantId("1001");
        long count =   userTestDao.query()
                .eq("tenant_id", "1001").count();
        
        System.out.println("Total count: " + count);
        assert count >= 2;
    }

    @Test
    public void testLambdaUpdate() {
        TenantContext.setTenantId("1001");
        int result =
            userTestDao.lambdaUpdate()
                .set(User::getAge, 35)
                .eq(User::getUserName, "张三").update();

        
        System.out.println("Lambda update result: " + result);
        assert result > 0;
    }

    @Test
    public void testLambdaDelete() {
        TenantContext.setTenantId("1001");
        int result =
            userTestDao.lambdaDelete()
                .eq(User::getUserName, "李四").execute();

        
        System.out.println("Lambda delete result: " + result);
        assert result > 0;
    }
}