/*
 *    Copyright (c) 2018-2025, ebag All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.oss_ext;



import com.lg.financecloud.common.oss_ext.service.OSSTemplateExt;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * minio 自动配置类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@EnableConfigurationProperties({OssExtProperties.class})
public class OssAutoConfigurationExt {
	private final OssExtProperties ossExtProperties;

	@Bean
	@ConditionalOnMissingBean(OSSTemplateExt.class)
	@ConditionalOnProperty(name = "oss.secret")
	OSSTemplateExt ossTemplateExt() {
		return new OSSTemplateExt(ossExtProperties);
	}



}
