package com.lg.dao.core.executor;

import com.lg.dao.core.EntityRowMapper;
import com.lg.dao.core.MapRowMapper;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.exception.SqlExecutionException;
import com.lg.dao.core.interceptor.LightOrmSqlInterceptorChain;
import com.lg.dao.core.interceptor.SqlInterceptorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 默认SQL执行器实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultLightOrmSqlExecutor implements LightOrmSqlExecutor {

    private final JdbcTemplate jdbcTemplate;
    @Lazy
    private final LightOrmSqlInterceptorChain interceptorChain;

    private final UnifiedCacheManager unifiedCacheManager;


    
    @Override
    public int executeUpdate(String sql, List<Object> params) {
        try {
            SqlInterceptorContext context = createContext(sql);
            sql = interceptorChain.execute(sql, params, context);
            int result = jdbcTemplate.update(sql, params.toArray());
            interceptorChain.executeAfter(sql, params, result, context);
            return result;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute update SQL", e);
        }
    }
    
    @Override
    public <T> List<T> executeQuery(String sql, List<Object> params, Class<T> resultType) {
        try {
            SqlInterceptorContext context = createContext(sql);
            context.setSqlType(SqlInterceptorContext.SqlType.SELECT);
            
            sql = interceptorChain.execute(sql, params, context);
            
            RowMapper<T> rowMapper;
            if (isPrimitiveOrWrapper(resultType)) {
                rowMapper = new SingleColumnRowMapper<>(resultType);
            } else if (Map.class.isAssignableFrom(resultType)) {
                // 对于Map类型，使用专门的MapRowMapper
                @SuppressWarnings("unchecked")
                RowMapper<T> mapRowMapper = (RowMapper<T>) new MapRowMapper();
                rowMapper = mapRowMapper;
            } else {
                rowMapper = new EntityRowMapper<>(resultType,unifiedCacheManager);
            }
            
            List<T> result = jdbcTemplate.query(sql, params.toArray(), rowMapper);
            interceptorChain.executeAfter(sql, params, result, context);
            
            return result;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute query SQL", e);
        }
    }
    
    @Override
    public <T> List<T> executeQuery(String sql, List<Object> params, RowMapper<T> rowMapper) {
        try {
            SqlInterceptorContext context = createContext(sql);
            context.setSqlType(SqlInterceptorContext.SqlType.SELECT);
            
            sql = interceptorChain.execute(sql, params, context);
            
            List<T> result = jdbcTemplate.query(sql, params.toArray(), rowMapper);
            interceptorChain.executeAfter(sql, params, result, context);
            
            return result;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute query SQL", e);
        }
    }
    
    @Override
    public int[] executeBatch(String sql, List<List<Object>> batchParams) {
        if (batchParams == null || batchParams.isEmpty()) {
            return new int[0];
        }
        
        try {
            SqlInterceptorContext context = createContext(sql);
            context.setSqlType(SqlInterceptorContext.SqlType.UPDATE);
            
            sql = interceptorChain.execute(sql, batchParams, context);
            
            List<Object[]> batchArgs = new ArrayList<>(batchParams.size());
            for (List<Object> params : batchParams) {
                if (params == null) {
                    // 对于空参数，使用空数组
                    batchArgs.add(new Object[0]);
                } else {
                    batchArgs.add(params.toArray());
                }
            }
            
            int[] result = jdbcTemplate.batchUpdate(sql, batchArgs);
            interceptorChain.executeAfter(sql, batchParams, result, context);
            
            return result;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute batch update SQL", e);
        }
    }
    
    @Override
    public List<Map<String, Object>> executeQueryForMap(String sql, List<Object> params) {
        try {
            SqlInterceptorContext context = createContext(sql);
            context.setSqlType(SqlInterceptorContext.SqlType.SELECT);
            
            sql = interceptorChain.execute(sql, params, context);
            
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, params.toArray());
            interceptorChain.executeAfter(sql, params, result, context);
            
            return result;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute query for map SQL", e);
        }
    }
    
    @Override
    public JSONArray executeQueryForJson(String sql, List<Object> params, boolean toCamelCase) {
        try {
            SqlInterceptorContext context = createContext(sql);
            context.setSqlType(SqlInterceptorContext.SqlType.SELECT);
            
            sql = interceptorChain.execute(sql, params, context);
            
            List<Map<String, Object>> mapResult = jdbcTemplate.queryForList(sql, params.toArray());
            interceptorChain.executeAfter(sql, params, mapResult, context);
            
            // 转换为JSONArray
            JSONArray jsonArray = new JSONArray();
            for (Map<String, Object> row : mapResult) {
                JSONObject jsonObject = new JSONObject();
                for (Map.Entry<String, Object> entry : row.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    
                    // 如果需要转换为驼峰命名
                    if (toCamelCase) {
                        key = toCamelCase(key);
                    }
                    
                    jsonObject.put(key, value);
                }
                jsonArray.add(jsonObject);
            }
            
            return jsonArray;
        } catch (Exception e) {
            throw new SqlExecutionException(sql, "Failed to execute query for json SQL", e);
        }
    }
    
    @Override
    public JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }
    
    private SqlInterceptorContext createContext(String sql) {
        SqlInterceptorContext context = new SqlInterceptorContext();
        context.setSqlType(determineSqlType(sql));
        context.setStopWatch(new StopWatch());
        return context;
    }
    
    private SqlInterceptorContext.SqlType determineSqlType(String sql) {
        sql = sql.trim().toUpperCase();
        if (sql.startsWith("SELECT")) return SqlInterceptorContext.SqlType.SELECT;
        if (sql.startsWith("INSERT")) return SqlInterceptorContext.SqlType.INSERT;
        if (sql.startsWith("UPDATE")) return SqlInterceptorContext.SqlType.UPDATE;
        if (sql.startsWith("DELETE")) return SqlInterceptorContext.SqlType.DELETE;
        return SqlInterceptorContext.SqlType.OTHER;
    }

    /**
     * 判断是否为基本类型或其包装类
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == String.class;
    }
    
    /**
     * 将下划线命名转换为驼峰命名
     * 例如：user_name -> userName, USER_ID -> userId
     */
    private String toCamelCase(String columnName) {
        if (StrUtil.isBlank(columnName)) {
            return columnName;
        }
        
        // 转换为小写并按下划线分割
        String[] parts = columnName.toLowerCase().split("_");
        if (parts.length <= 1) {
            return columnName.toLowerCase();
        }
        
        StringBuilder camelCase = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            if (StrUtil.isNotBlank(parts[i])) {
                camelCase.append(StrUtil.upperFirst(parts[i]));
            }
        }
        
        return camelCase.toString();
    }
}