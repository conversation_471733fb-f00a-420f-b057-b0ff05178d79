/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.entity.RobotTask;
import com.lg.financecloud.admin.api.entity.SysLog;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/6/28
 */
@FeignClient(contextId = "remoteRobotTaskService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteRobotTaskService {

	/**
	 * 创建任务
	 * @param from 是否内部调用
	 * @return succes、false
	 */
	@PostMapping("/robotTask/createTask")
	R createTask(@RequestBody RobotTask robotTask, @RequestHeader(SecurityConstants.FROM) String from);


	/**
	 * 新增机器人
	 * @param map
	 * @param from
	 * @return
	 */
	@PostMapping("/robotRegister/insertRobot")
	R insertRobot(@RequestBody Map<String,Object> map, @RequestHeader(SecurityConstants.FROM) String from);



}
