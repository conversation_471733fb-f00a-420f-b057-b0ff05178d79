---
description: 
globs: 
alwaysApply: false
---
# FinanceCloud 通用框架开发规范

## 1. 项目结构规范

### 1.1 模块命名规范
- 统一使用 `financecloud-common-{module}` 格式
- 模块名使用小写字母，单词间用横线(-)分隔
- 模块名应当能清晰表达模块的功能职责

### 1.2 核心模块划分
```
financecloud-common/
├── financecloud-common-base/    # 基础功能模块
├── financecloud-common-core/    # 核心功能模块
├── financecloud-common-data/    # 数据处理模块
├── financecloud-common-redis/   # Redis缓存模块
├── financecloud-common-security/# 安全认证模块
├── financecloud-common-log/     # 日志处理模块
└── ... 其他功能模块
```

### 1.3 模块内部结构
```
src/main/java/com/lg/
├── config/      # 配置类
├── constant/    # 常量定义
├── controller/  # 控制器
├── service/     # 服务接口与实现
├── model/       # 数据模型
└── util/        # 工具类
```

## 2. 技术栈规范

### 2.1 基础框架
- Spring Boot 版本: 2.7.5
- Spring Cloud 版本: 2021.0.5
- Spring Cloud Alibaba 版本: 2021.0.4.0
- JDK 版本: 1.8

### 2.2 核心依赖版本
- MyBatis-Plus: 3.5.2
- Hutool: 5.8.11
- Sa-Token: 1.34.0
- Redisson: 3.17.0
- Swagger/Knife4j: 2.9.2/2.0.9

## 3. 编码规范

### 3.1 命名规范
- 类名：使用PascalCase，如 `UserService`
- 方法名：使用camelCase，如 `getUserInfo`
- 常量：使用UPPER_SNAKE_CASE，如 `MAX_COUNT`
- 包名：全小写，如 `com.lg.common.core`
- 配置文件：使用kebab-case，如 `application-dev.yml`

### 3.2 代码格式
- 缩进：4个空格
- 换行：大括号单独成行
- 最大行长：120字符
- 文件编码：UTF-8

### 3.3 注释规范
- 类注释：包含功能描述、作者、日期
- 方法注释：包含功能描述、参数说明、返回值说明
- 关键代码注释：解释复杂逻辑
```java
/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
```

## 4. 配置管理规范

### 4.1 配置文件命名
- application.yml：主配置文件
- application-{env}.yml：环境配置文件
- bootstrap.yml：启动配置文件

### 4.2 配置项命名
- 使用kebab-case
- 遵循spring boot标准配置格式
```yaml
spring:
  datasource:
    url: **********************************
```

## 5. API设计规范

### 5.1 RESTful API规范
- URL使用名词而非动词
- 使用复数形式表示资源集合
- HTTP方法语义：
  - GET：查询
  - POST：创建
  - PUT：更新
  - DELETE：删除

### 5.2 响应格式
```json
{
    "code": 200,
    "msg": "success",
    "data": {}
}
```

## 6. 安全规范

### 6.1 认证授权
- 统一使用Sa-Token框架
- 遵循OAuth2.0规范
- 使用JWT令牌

### 6.2 密码安全
- 密码必须加密存储
- 使用BCrypt等安全算法
- 禁止明文传输敏感信息

## 7. 日志规范

### 7.1 日志配置
- 使用SLF4J + Logback
- 区分info和error日志
- 配置日志轮转策略

### 7.2 日志记录
- 记录关键业务操作
- 记录异常信息
- 包含必要的上下文信息

## 8. 版本管理规范

### 8.1 版本号规则
- 主版本号.次版本号.修订号
- 如：3.10.0

### 8.2 分支管理
- main：主分支
- develop：开发分支
- feature/*：功能分支
- hotfix/*：紧急修复分支

## 9. 异常处理规范

### 9.1 异常分类
- BusinessException：业务异常
- ValidationException：参数校验异常
- SystemException：系统异常

### 9.2 异常处理原则
- 统一异常处理
- 详细的异常信息
- 合适的异常粒度

## 10. 测试规范

### 10.1 单元测试
- 测试类命名：*Test
- 测试方法命名：test*
- 覆盖核心业务逻辑

### 10.2 测试原则
- 保持测试独立性
- 测试用例完整性
- 合理使用Mock


### 
jdk 版本 是1.8的所以避免用很新的api
## 

工具类 尽量使用hutool 提供的工具类

# 前端ui 框架规范
1. element-ui
2. 是基于vue 2.0 ,避免使用很新的 语法
3. 避免使用很新的api


