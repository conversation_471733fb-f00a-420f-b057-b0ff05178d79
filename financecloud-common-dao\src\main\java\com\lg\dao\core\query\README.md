# LambdaJoinQuery 使用指南

LambdaJoinQuery 是一个类型安全的多表JOIN查询构建器，支持使用Lambda表达式进行查询条件和字段的指定。

## 基本用法

```java
// 创建Lambda风格的JOIN查询
List<OrderDTO> orders = orderDao.createLambdaJoinQuery(Order.class)
    .select("o.id as orderId", "o.amount", "o.status as orderStatus")
    .select("u.username", "u.email")
    .from(Order.class, "o")
    .leftJoin(User.class, "u")
    .on(Order.class, Order::getUserId, User.class, User::getId)
    .where()
    .eq(Order.class, Order::getStatus, "PAID")
    .list(OrderDTO.class);
```

## 跨实体查询的限制

**注意**：当实体类是静态内部类时，由于Java的限制，无法直接使用`select(Order::getId, User::getUsername)`这样的Lambda表达式进行跨实体查询。

如果遇到以下错误：
```
无法从static上下文引用非static方法
```

可以选择以下解决方案：

### 解决方案1：使用selectFields方法

```java
// 对每个实体使用专门的selectFields方法
// 注意：必须先设置表别名，再调用selectFields方法
List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
    .from(Order.class, "o")
    .leftJoin(User.class, "u")
    .selectFields(Order.class, Order::getId, Order::getAmount, Order::getStatus)
    .selectFields(User.class, User::getUsername, User::getEmail)
    .on(Order.class, Order::getUserId, User.class, User::getId)
    .where()
    .eq(Order.class, Order::getStatus, "PAID")
    .listMap();
```

### 解决方案2：使用字符串方式

```java
List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
    .select("o.id", "o.amount", "u.username", "i.product_name")
    .from(Order.class, "o")
    .leftJoin(User.class, "u")
    .on("o.user_id = u.id")
    .leftJoin(OrderItem.class, "i")
    .on("i.order_id = o.id")
    .where()
    .eq("o.id", 1L)
    .listMap();
```

### 解决方案3：使用独立的实体类

将实体类移动到单独的文件中：

```java
// OrderEntity.java
@Table(name = "t_order")
@Data
public class OrderEntity {
    @Id
    private Long id;
    @Column(name = "user_id")
    private Long userId;
    private BigDecimal amount;
    private String status;
}

// UserEntity.java
@Table(name = "t_user")
@Data
public class UserEntity {
    @Id
    private Long id;
    private String username;
    private String email;
}
```

然后使用Lambda表达式：

```java
// 对于非静态内部类，可以使用原始的select方法
List<OrderDTO> results = orderDao.createLambdaJoinQuery(OrderEntity.class)
    .select(OrderEntity::getId, OrderEntity::getAmount, UserEntity::getUsername)
    .from(OrderEntity.class, "o")
    .leftJoin(UserEntity.class, "u")
    .on(OrderEntity.class, OrderEntity::getUserId, UserEntity.class, UserEntity::getId)
    .list(OrderDTO.class);
```

## 高级用法

### 分页查询

```java
Page<OrderDTO> page = orderDao.createLambdaJoinQuery(Order.class)
    .select("o.id as orderId", "o.amount", "o.status as orderStatus", "u.username")
    .from(Order.class, "o")
    .leftJoin(User.class, "u")
    .on(Order.class, Order::getUserId, User.class, User::getId)
    .orderBy(Order.class, Order::getCreateTime)
    .desc()
    .page(OrderDTO.class, 1, 10);
```

### 动态条件

```java
String username = request.getUsername(); // 可能为null
String status = request.getStatus(); // 可能为空字符串

List<OrderDTO> orders = orderDao.createLambdaJoinQuery(Order.class)
    .select("o.id as orderId", "o.amount", "u.username")
    .from(Order.class, "o")
    .leftJoin(User.class, "u")
    .on(Order.class, Order::getUserId, User.class, User::getId)
    .where()
    .likeIfNotEmpty(User.class, User::getUsername, username)
    .and()
    .eqIfNotNull(Order.class, Order::getStatus, status)
    .list(OrderDTO.class);
```

## LightORM 查询构建器

LightORM 提供了强大的查询构建器功能，支持链式调用和 Lambda 表达式，使查询代码更加简洁、类型安全。

### 基本用法

```java
// 基本查询
List<User> users = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .like(User::getName, "张")
    .orderByDesc(User::getCreateTime)
    .list();

// 分页查询
Page<User> page = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .page(1, 10);

// 统计查询
long count = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .count();
```

### 条件构建

```java
// 基本条件
userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")       // 等于
    .ne(User::getStatus, "DELETED")      // 不等于
    .gt(User::getAge, 18)                // 大于
    .ge(User::getAge, 18)                // 大于等于
    .lt(User::getAge, 60)                // 小于
    .le(User::getAge, 60)                // 小于等于
    .like(User::getName, "张")           // 包含
    .in(User::getId, 1, 2, 3)            // IN
    .between(User::getAge, 18, 60)       // BETWEEN
    .isNull(User::getDeleteTime)         // IS NULL
    .isNotNull(User::getCreateTime)      // IS NOT NULL
    .list();

// 动态条件
userDao.lambdaQuery()
    .eqIfNotNull(User::getStatus, status)             // 非null时添加条件
    .likeIfNotEmpty(User::getName, name)              // 非空字符串时添加条件
    .inIfNotEmpty(User::getDepartmentId, deptIds)     // 非空集合时添加条件
    .list();

// 实体对象条件
User queryUser = new User();
queryUser.setStatus("ACTIVE");
queryUser.setDepartment("技术部");

userDao.lambdaQuery()
    .allEq(queryUser)  // 根据实体非空属性构建等值条件
    .list();
```

### 嵌套条件分组

```java
// AND 嵌套条件分组
List<User> users = userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .and(wrapper -> wrapper
        .eq(User::getStatus, "ACTIVE")
        .or()
        .eq(User::getStatus, "PENDING")
    )
    .list();

// 生成的SQL:
// SELECT * FROM t_user 
// WHERE tenant_id = '1001' 
// AND (status = 'ACTIVE' OR status = 'PENDING')

// OR 嵌套条件分组
List<User> users = userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .or(wrapper -> wrapper
        .eq(User::getRole, "ADMIN")
        .eq(User::getDepartment, "技术部")
    )
    .list();

// 生成的SQL:
// SELECT * FROM t_user 
// WHERE tenant_id = '1001' 
// OR (role = 'ADMIN' AND department = '技术部')

// 多层嵌套条件
List<User> users = userDao.lambdaQuery()
    .eq(User::getTenantId, "1001")
    .and(wrapper -> wrapper
        .eq(User::getStatus, "ACTIVE")
        .or(subWrapper -> subWrapper
            .eq(User::getRole, "ADMIN")
            .eq(User::getDepartment, "技术部")
        )
    )
    .list();

// 生成的SQL:
// SELECT * FROM t_user 
// WHERE tenant_id = '1001' 
// AND (status = 'ACTIVE' OR (role = 'ADMIN' AND department = '技术部'))
```

### 更新和删除

```java
// 条件更新
int updated = userDao.lambdaUpdate()
    .set(User::getStatus, "INACTIVE")
    .eq(User::getDepartment, "销售部")
    .update();

// 条件删除
int deleted = userDao.lambdaDelete()
    .eq(User::getStatus, "DELETED")
    .execute();
```

### 排序和分页

```java
// 排序
List<User> users = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .orderByDesc(User::getCreateTime)
    .orderBy(User::getName)
    .list();

// 分页
Page<User> page = userDao.lambdaQuery()
    .eq(User::getStatus, "ACTIVE")
    .page(1, 10);
``` 