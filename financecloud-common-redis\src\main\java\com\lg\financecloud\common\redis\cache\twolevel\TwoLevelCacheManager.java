package com.lg.financecloud.common.redis.cache.twolevel;

import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.common.core.constant.CacheConstants;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import com.lg.financecloud.common.redis.cache.memory.MemoryCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.convert.DurationStyle;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 二级缓存管理器实现，结合了内存(Caffeine)和Redis缓存
 * 
 * <AUTHOR>
 */
@Slf4j
public class TwoLevelCacheManager implements CacheManager {
    
    private final Map<String, Cache> cacheMap = new ConcurrentHashMap<>();
    
    private final RedisCacheManager redisCacheManager;
    private final RedisCacheConfiguration defaultCacheConfig;
    private final RedisCacheWriter cacheWriter;
    
    private static final String SPLIT_FLAG = "#";
    private static final int CACHE_LENGTH = 2;
    
    private final int maxSize;
    private final long timeToLiveSeconds;
    private final long timeToIdleSeconds;
    
    /**
     * 创建二级缓存管理器
     *
     * @param redisCacheManager Redis缓存管理器
     * @param cacheWriter Redis缓存写入器
     * @param defaultCacheConfig 默认Redis缓存配置
     * @param maxSize 每个内存缓存的最大条目数
     * @param timeToLiveSeconds 默认存活时间（秒）
     * @param timeToIdleSeconds 默认空闲时间（秒）
     */
    public TwoLevelCacheManager(
            RedisCacheManager redisCacheManager, 
            RedisCacheWriter cacheWriter,
            RedisCacheConfiguration defaultCacheConfig,
            int maxSize, 
            long timeToLiveSeconds, 
            long timeToIdleSeconds) {
        this.redisCacheManager = redisCacheManager;
        this.cacheWriter = cacheWriter;
        this.defaultCacheConfig = defaultCacheConfig;
        this.maxSize = maxSize;
        this.timeToLiveSeconds = timeToLiveSeconds;
        this.timeToIdleSeconds = timeToIdleSeconds;
        
        log.info("初始化二级缓存管理器: L1=Caffeine(最大条目数={}, 存活时间={}秒, 空闲时间={}秒), L2=Redis", 
                maxSize, timeToLiveSeconds, timeToIdleSeconds);
    }
    
    @Override
    @Nullable
    public Cache getCache(String name) {
        // 处理全局缓存名称（不特定于租户）
        if (name.startsWith(CacheConstants.GLOBALLY)) {
            return getOrCreateCache(name);
        }
        
        // 为租户隔离添加租户前缀
        return getOrCreateCache(TenantContextHolder.getTenantId() + StrUtil.COLON + name);
    }
    
    @Override
    public Collection<String> getCacheNames() {
        return Collections.unmodifiableCollection(cacheMap.keySet());
    }
    
    private Cache getOrCreateCache(String name) {
        return cacheMap.computeIfAbsent(name, this::createTwoLevelCache);
    }
    
    private Cache createTwoLevelCache(String name) {
        // 解析缓存名称中的TTL（格式：cacheName#ttl）
        long ttl = timeToLiveSeconds;
        String cacheName = name;
        String redisCacheName = name; // 保留原始名称用于Redis缓存
        
        if (StrUtil.isNotBlank(name) && name.contains(SPLIT_FLAG)) {
            String[] cacheArray = name.split(SPLIT_FLAG);
            if (cacheArray.length >= CACHE_LENGTH) {
                cacheName = cacheArray[0];
                redisCacheName = cacheName; // Redis缓存名也使用不带TTL的名称
                try {
                    ttl = Long.parseLong(cacheArray[1]);
                    log.debug("缓存[{}]配置了自定义TTL: {}秒", cacheName, ttl);
                    
                    // 不直接修改defaultCacheConfig，而是使用redisCacheManager的方式获取缓存
                    // 我们依赖RedisAutoCacheManager内部处理TTL
                } catch (NumberFormatException e) {
                    // 解析失败时使用默认TTL
                    log.warn("解析缓存[{}]的TTL失败，使用默认TTL", name);
                }
            }
        }
        
        // 创建内存缓存(L1)
        final long finalTtl = ttl;
        MemoryCache memoryCache = new MemoryCache(cacheName, maxSize, finalTtl, timeToIdleSeconds);
        
        // 获取或创建Redis缓存(L2)
        // 如果有TTL后缀，我们使用原始的名称，让RedisAutoCacheManager处理TTL
        RedisCache redisCache = null;
        if (cacheName.equals(redisCacheName)) {
            // 没有TTL后缀，直接使用名称
            redisCache = (RedisCache) redisCacheManager.getCache(redisCacheName);
        } else {
            // 有TTL后缀，使用原始名称（包含TTL后缀）
            redisCache = (RedisCache) redisCacheManager.getCache(name);
        }
        
        if (redisCache == null) {
            log.error("无法创建Redis缓存: {}", name);
            throw new IllegalStateException("无法创建Redis缓存: " + name);
        }
        
        // 返回二级缓存
        return new TwoLevelCache(cacheName, memoryCache, redisCache);
    }
    
    /**
     * 清除此缓存管理器中的所有缓存
     */
    public void clearAll() {
        log.info("清除所有二级缓存，缓存数: {}", cacheMap.size());
        cacheMap.values().forEach(Cache::clear);
    }
    
    /**
     * 获取所有二级缓存的统计信息
     *
     * @return 所有二级缓存的统计信息列表
     */
    public Map<String, TwoLevelCache.CacheStats> getStats() {
        return cacheMap.entrySet().stream()
                .filter(entry -> entry.getValue() instanceof TwoLevelCache)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> ((TwoLevelCache) entry.getValue()).stats()
                ));
    }
} 