package com.lg.dao.core.transaction;

import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.AbstractPlatformTransactionManager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事务管理器工厂
 */
public class TransactionManagerFactory {
    
    private static final Map<String, PlatformTransactionManager> TRANSACTION_MANAGERS = new ConcurrentHashMap<>();
    
    /**
     * 注册事务管理器
     */
    public static void registerTransactionManager(String name, PlatformTransactionManager transactionManager) {
        TRANSACTION_MANAGERS.put(name, transactionManager);
    }
    
    /**
     * 获取事务管理器
     */
    public static PlatformTransactionManager getTransactionManager(String name) {
        return TRANSACTION_MANAGERS.get(name);
    }
    
    /**
     * 获取默认事务管理器
     */
    public static PlatformTransactionManager getDefaultTransactionManager() {
        return TRANSACTION_MANAGERS.values().iterator().next();
    }
    
    /**
     * 创建事务辅助类
     */
    public static TransactionHelper createTransactionHelper(String transactionManagerName) {
        PlatformTransactionManager manager = getTransactionManager(transactionManagerName);
        if (manager == null) {
            throw new IllegalArgumentException("Transaction manager not found: " + transactionManagerName);
        }
        return new TransactionHelper(manager);
    }
}