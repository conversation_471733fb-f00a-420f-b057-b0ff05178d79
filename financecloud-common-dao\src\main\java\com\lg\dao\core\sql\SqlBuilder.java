package com.lg.dao.core.sql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Supplier;

/**
 * 动态SQL构建器
 */
@Slf4j
public class SqlBuilder {
    private final StringBuilder sql = new StringBuilder();
    private final List<Object> params = new ArrayList<>();
    private final Stack<Boolean> conditions = new Stack<>();
    private final Stack<Integer> marks = new Stack<>();
    private boolean hasSelectClause = false;

    public SqlBuilder SELECT() {
        if (!hasSelectClause) {
            sql.append("SELECT ");
            hasSelectClause = true;
        } else {
            // 如果已经有SELECT，确保用逗号分隔
            if (!sql.toString().endsWith(" ")) {
                sql.append(" ");
            }
        }
        return this;
    }

    public SqlBuilder SELECT(String... columns) {
        if (!hasSelectClause) {
            sql.append("SELECT ");
            hasSelectClause = true;
        } else {
            // 如果已经有SELECT子句，确保用逗号分隔
            if (!sql.toString().trim().endsWith(",")) {
                sql.append(", ");
            }
        }
        
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append(columns[i]);
        }
        return this;
    }

    public SqlBuilder DISTINCT() {
        sql.append("DISTINCT ");
        return this;
    }

    public SqlBuilder FROM(String table) {
        sql.append(" FROM ").append(table).append(" ");
        return this;
    }
    
    /**
     * JOIN子句
     * @param table 要连接的表名
     * @param joinType 连接类型
     * @return SqlBuilder实例
     */
    public SqlBuilder JOIN(String table, JoinType joinType) {
        sql.append(joinType.getSql()).append(" ").append(table).append(" ");
        return this;
    }
    
    /**
     * INNER JOIN子句
     * @param table 要连接的表名
     * @return SqlBuilder实例
     */
    public SqlBuilder INNER_JOIN(String table) {
        return JOIN(table, JoinType.INNER);
    }
    
    /**
     * LEFT JOIN子句
     * @param table 要连接的表名
     * @return SqlBuilder实例
     */
    public SqlBuilder LEFT_JOIN(String table) {
        return JOIN(table, JoinType.LEFT);
    }
    
    /**
     * RIGHT JOIN子句
     * @param table 要连接的表名
     * @return SqlBuilder实例
     */
    public SqlBuilder RIGHT_JOIN(String table) {
        return JOIN(table, JoinType.RIGHT);
    }
    
    /**
     * FULL JOIN子句
     * @param table 要连接的表名
     * @return SqlBuilder实例
     */
    public SqlBuilder FULL_JOIN(String table) {
        return JOIN(table, JoinType.FULL);
    }
    
    /**
     * ON子句，用于指定JOIN条件
     * @param condition JOIN条件
     * @return SqlBuilder实例
     */
    public SqlBuilder ON(String condition) {
        sql.append("ON ").append(condition).append(" ");
        return this;
    }

    public SqlBuilder WHERE() {
        sql.append("WHERE 1=1 ");
        return this;
    }

    public SqlBuilder AND() {
        sql.append("AND ");
        return this;
    }

    public SqlBuilder OR() {
        sql.append("OR ");
        return this;
    }

    public SqlBuilder GROUP_BY() {
        sql.append("GROUP BY ");
        return this;
    }

    public SqlBuilder GROUP_BY(String... columns) {
        sql.append("GROUP BY ");
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) {
                sql.append(",");
            }
            sql.append(columns[i]);
        }
        return this;
    }


    public SqlBuilder HAVING() {
        sql.append("HAVING ");
        return this;
    }


    public SqlBuilder ORDER_BY(String field) {
        sql.append("ORDER BY ").append(field).append(" ");
        return this;
    }


    public SqlBuilder ORDER_BY(String... columns) {
        sql.append("ORDER BY ");
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) {
                sql.append(",");
            }
            sql.append(columns[i]);
        }
        return this;
    }

    public SqlBuilder ASC() {
        sql.append("ASC ");
        return this;
    }

    public SqlBuilder DESC() {
        sql.append("DESC ");
        return this;
    }

    public SqlBuilder EQ(String field, Object value) {
        sql.append(field).append(" = ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder NE(String field, Object value) {
        sql.append(field).append(" != ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder GT(String field, Object value) {
        sql.append(field).append(" > ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder GE(String field, Object value) {
        sql.append(field).append(" >= ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder LT(String field, Object value) {
        sql.append(field).append(" < ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder LE(String field, Object value) {
        sql.append(field).append(" <= ? ");
        params.add(value);
        return this;
    }

    public SqlBuilder LIKE(String field, String value) {
        sql.append(field).append(" LIKE ? ");
        params.add("%" + value + "%");
        return this;
    }

    public SqlBuilder LEFT_LIKE(String field, String value) {
        sql.append(field).append(" LIKE ? ");
        params.add("%" + value);
        return this;
    }

    public SqlBuilder RIGHT_LIKE(String field, String value) {
        sql.append(field).append(" LIKE ? ");
        params.add(value + "%");
        return this;
    }

    public SqlBuilder IN(String field, Collection<?> values) {
        if (values != null && !values.isEmpty()) {
            sql.append(field).append(" IN (");
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
            }
            sql.append(") ");
            params.addAll(values);
        }
        return this;
    }

    public SqlBuilder NOT_IN(String field, Collection<?> values) {
        if (values != null && !values.isEmpty()) {
            sql.append(field).append(" NOT IN (");
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
            }
            sql.append(") ");
            params.addAll(values);
        }
        return this;
    }

    public SqlBuilder BETWEEN(String field, Object start, Object end) {
        sql.append(field).append(" BETWEEN ? AND ? ");
        params.add(start);
        params.add(end);
        return this;
    }

    public SqlBuilder IS_NULL(String field) {
        sql.append(field).append(" IS NULL ");
        return this;
    }

    public SqlBuilder IS_NOT_NULL(String field) {
        sql.append(field).append(" IS NOT NULL ");
        return this;
    }

    public SqlBuilder append(String sqlPart) {
        sql.append(sqlPart).append(" ");
        return this;
    }

    public SqlBuilder append(String sqlPart, Object... params) {
        sql.append(sqlPart).append(" ");
        if (params != null) {
            for (Object param : params) {
                this.params.add(param);
            }
        }
        return this;
    }

    public SqlBuilder addParam(Object param) {
        params.add(param);
        return this;
    }

    public SqlBuilder IF(boolean condition) {
        conditions.push(condition);
        marks.push(sql.length());
        return this;
    }

    public SqlBuilder END() {
        if (!conditions.isEmpty()) {
            boolean condition = conditions.pop();
            int mark = marks.pop();
            if (!condition) {
                sql.setLength(mark);
            }
        }
        return this;
    }

    public SqlBuilder LIMIT(int limit) {
        sql.append("LIMIT ? ");
        params.add(limit);
        return this;
    }

    public SqlBuilder OFFSET(int offset) {
        sql.append("OFFSET ? ");
        params.add(offset);
        return this;
    }

    public String getSql() {
        return sql.toString().trim();
    }

    public List<Object> getParams() {
        return params;
    }

    public void clear() {
        sql.setLength(0);
        params.clear();
    }

    @Override
    public String toString() {
        return getSql();
    }

    /**
     * 使用示例：
     * SqlBuilder builder = new SqlBuilder()
     *     .SELECT("id", "name", "age")
     *     .FROM("user")
     *     .WHERE()
     *     .IF(name != null)
     *         .AND().LIKE("name", name)
     *     .END()
     *     .IF(age != null)
     *         .AND().EQ("age", age)
     *     .END()
     *     .ORDER_BY("id").DESC();
     *     
     * // JOIN查询示例
     * SqlBuilder joinBuilder = new SqlBuilder()
     *     .SELECT("u.id", "u.name", "o.order_id", "o.amount")
     *     .FROM("user u")
     *     .LEFT_JOIN("orders o")
     *     .ON("u.id = o.user_id")
     *     .WHERE()
     *     .AND().EQ("u.status", "ACTIVE")
     *     .ORDER_BY("o.create_time").DESC();
     */
    
    /**
     * 获取批量参数（用于批量操作）
     */
    public List<Object> getBatchParams() {
        // 对于批量插入，通常每行数据的参数数量相同
        // 这里简化处理，返回单行参数数组
        return params;
    }
}