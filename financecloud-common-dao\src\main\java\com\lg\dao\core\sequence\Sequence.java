package com.lg.dao.core.sequence;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;
import java.time.LocalDateTime;

/**
 * 序列实体
 */
@Data
@Table(name = "sys_seq")
public class Sequence {

    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    
    @Id
    @Column(name = "sequence_name")
    private String sequenceName;
    
    @Column(name = "current_value")
    private Long currentValue;
    
    @Column(name = "increment_value")
    private Integer incrementValue;
    
    @Column(name = "strategy")
    @Enumerated(EnumType.STRING)
    private SequenceStrategy strategy;
    
    @Column(name = "prefix")
    private String prefix;
    
    @Column(name = "suffix_length")
    private Integer suffixLength;
    
    @Column(name = "last_reset_time")
    private LocalDateTime lastResetTime;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}