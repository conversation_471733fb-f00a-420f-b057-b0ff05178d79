package com.lg.dao.helper;

import cn.hutool.extra.spring.SpringUtil;
import com.lg.dao.DaoMode;
import com.lg.dao.core.BaseDao;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.factory.DaoFactory;
import com.lg.dao.factory.EnhancedDaoFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * DAO助手类 - 提供静态方法便捷访问
 */
@Component
public class DaoHelper {

    private static DaoFactory daoFactory;
    private static EnhancedDaoFactory enhancedDaoFactory;
    private static UnifiedCacheManager unifiedCacheManager;

    // 当前使用模式
    private static volatile DaoMode currentMode = DaoMode.AUTO;

    private static BaseDao localBaseDao;

    @Autowired
    public void setDaoFactory(DaoFactory daoFactory) {
        DaoHelper.daoFactory = daoFactory;
    }

    @Autowired(required = false)
    public void setEnhancedDaoFactory(EnhancedDaoFactory enhancedDaoFactory) {
        DaoHelper.enhancedDaoFactory = enhancedDaoFactory;
    }

    @Autowired(required = false)
    public void setUnifiedCacheManager(UnifiedCacheManager unifiedCacheManager) {
        DaoHelper.unifiedCacheManager = unifiedCacheManager;
    }

    /**
     * 编程式设置DAO使用模式
     * @param mode 使用模式
     */
    public static void setMode(DaoMode mode) {
        if (mode != currentMode) {
            currentMode = mode;
            // 模式变更时清空本地缓存
            clearCache();
        }
    }

    /**
     * 获取当前使用模式
     * @return 当前模式
     */
    public static DaoMode getCurrentMode() {
        return currentMode;
    }

    /**
     * 重置为自动模式
     */
    public static void resetToAuto() {
        setMode(DaoMode.AUTO);
    }

    /**
     * 获取指定实体的GenericDao实例
     * @param entityClass 实体类
     * @param keyClass 主键类
     * @return GenericDao实例
     */
    @SuppressWarnings("unchecked")
    public static <T, K> GenericDao<T, K> dao(Class<T> entityClass, Class<K> keyClass) {
        String cacheKey = entityClass.getName() + "_" + keyClass.getName() + "_" + currentMode;

        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey,
                () -> getFactory().getDao(entityClass, keyClass));
        } else {
            // 降级到工厂方法
            return getFactory().getDao(entityClass, keyClass);
        }
    }

    /**
     * 获取指定实体的GenericDao实例（主键默认为Long）
     * @param entityClass 实体类
     * @return GenericDao实例
     */
    @SuppressWarnings("unchecked")
    public static <T> GenericDao<T, Long> dao(Class<T> entityClass) {
        String cacheKey = entityClass.getName() + "_Long";

        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.ENTITY_CACHE, cacheKey,
                () -> dao(entityClass, Long.class));
        } else {
            return dao(entityClass, Long.class);
        }
    }

    /**
     * 获取指定实体的GenericDao实例（主键为String类型）
     * @param entityClass 实体类
     * @return GenericDao实例
     */
    public static <T> GenericDao<T, String> daoString(Class<T> entityClass) {
        return dao(entityClass, String.class);
    }

    /**
     * 强制使用内存数据库模式
     * @param entityClass 实体类
     * @param keyClass 主键类
     * @return GenericDao实例
     */
    @SuppressWarnings("unchecked")
    public static <T, K> GenericDao<T, K> memoryDao(Class<T> entityClass, Class<K> keyClass) {
        if (enhancedDaoFactory == null) {
            throw new IllegalStateException("EnhancedDaoFactory not available. Memory mode not supported.");
        }

        String cacheKey = "memory_" + entityClass.getName() + "_" + keyClass.getName();

        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey,
                () -> enhancedDaoFactory.getMemoryDao(entityClass, keyClass));
        } else {
            return enhancedDaoFactory.getMemoryDao(entityClass, keyClass);
        }
    }

    /**
     * 强制使用内存数据库模式（主键默认为Long）
     * @param entityClass 实体类
     * @return GenericDao实例
     */
    public static <T> GenericDao<T, Long> memoryDao(Class<T> entityClass) {
        return memoryDao(entityClass, Long.class);
    }

    /**
     * 强制使用真实数据库模式
     * @param entityClass 实体类
     * @param keyClass 主键类
     * @return GenericDao实例
     */
    @SuppressWarnings("unchecked")
    public static <T, K> GenericDao<T, K> databaseDao(Class<T> entityClass, Class<K> keyClass) {
        String cacheKey = "database_" + entityClass.getName() + "_" + keyClass.getName();

        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey, () -> {
                if (enhancedDaoFactory == null) {
                    return daoFactory.getDao(entityClass, keyClass);
                }
                return enhancedDaoFactory.getDatabaseDao(entityClass, keyClass);
            });
        } else {
            if (enhancedDaoFactory == null) {
                return daoFactory.getDao(entityClass, keyClass);
            }
            return enhancedDaoFactory.getDatabaseDao(entityClass, keyClass);
        }
    }

    /**
     * 强制使用真实数据库模式（主键默认为Long）
     * @param entityClass 实体类
     * @return GenericDao实例
     */
    public static <T> GenericDao<T, Long> databaseDao(Class<T> entityClass) {
        return databaseDao(entityClass, Long.class);
    }

    /**
     * 根据当前模式选择合适的工厂
     */
    private static DaoFactory getFactory() {
        DaoFactory factory = null;
        if (currentMode == DaoMode.AUTO) {
            // 优先使用 EnhancedDaoFactory，它包含自动建表逻辑
            factory = enhancedDaoFactory != null ? enhancedDaoFactory : daoFactory;
            if (factory == null) {
                try {
                    factory = SpringUtil.getBean(EnhancedDaoFactory.class);
                    if (factory == null) {
                        factory = SpringUtil.getBean(DaoFactory.class);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    
        if (enhancedDaoFactory != null) {
            enhancedDaoFactory.setMode(currentMode);
            factory = enhancedDaoFactory;
        }
    
        if (factory == null) {
            throw new IllegalStateException("DaoHelper not initialized. Make sure Spring context is loaded.");
        }
        return factory;
    }

    /**
     * 通过EnhancedDaoFactory获取自定义DAO（简化版本）
     * 自动从DAO类的泛型参数中推断实体类和主键类型
     * @param daoClass 自定义DAO类
     * @return 自定义DAO实例
     */
    @SuppressWarnings("unchecked")
    public static <T, K, D extends GenericDao<T, K>> D getCustomDao(Class<D> daoClass) {
        if (enhancedDaoFactory == null) {
            throw new IllegalStateException("EnhancedDaoFactory not available");
        }

        String cacheKey = "custom_" + daoClass.getName();

        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey, () -> {
                try {
                    // 通过反射获取泛型参数
                    Type[] genericTypes = getGenericTypes(daoClass);
                    if (genericTypes.length < 2) {
                        throw new IllegalArgumentException("Cannot determine entity and key types from DAO class: " + daoClass.getName());
                    }

                    Class<T> entityClass = resolveClassType(genericTypes[0]);
                    Class<K> keyClass = resolveClassType(genericTypes[1]);

                    return enhancedDaoFactory.getCustomDao(daoClass, entityClass, keyClass);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to create custom DAO instance: " + daoClass.getName(), e);
                }
            });
        } else {
            try {
                // 通过反射获取泛型参数
                Type[] genericTypes = getGenericTypes(daoClass);
                if (genericTypes.length < 2) {
                    throw new IllegalArgumentException("Cannot determine entity and key types from DAO class: " + daoClass.getName());
                }

                Class<T> entityClass = resolveClassType(genericTypes[0]);
                Class<K> keyClass = resolveClassType(genericTypes[1]);

                return enhancedDaoFactory.getCustomDao(daoClass, entityClass, keyClass);
            } catch (Exception e) {
                throw new RuntimeException("Failed to create custom DAO instance: " + daoClass.getName(), e);
            }
        }
    }
    
    /**
     * 安全地解析Type为Class类型
     * 处理各种泛型类型情况
     */
    @SuppressWarnings("unchecked")
    private static <T> Class<T> resolveClassType(Type type) {
        if (type instanceof Class) {
            return (Class<T>) type;
        } else if (type instanceof ParameterizedType) {
            // 对于嵌套泛型，获取原始类型
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Type rawType = parameterizedType.getRawType();
            if (rawType instanceof Class) {
                return (Class<T>) rawType;
            }
        } else if (type instanceof java.lang.reflect.GenericArrayType) {
            // 处理泛型数组类型
            java.lang.reflect.GenericArrayType arrayType = (java.lang.reflect.GenericArrayType) type;
            Type componentType = arrayType.getGenericComponentType();
            if (componentType instanceof Class) {
                return (Class<T>) componentType;
            }
        }
        
        // 如果无法解析，抛出异常
        throw new IllegalArgumentException("Cannot resolve Class type from: " + type);
    }

    /**
     * 获取泛型类型参数
     */
    private static Type[] getGenericTypes(Class<?> clazz) {
        Type genericSuperclass = clazz.getGenericSuperclass();
        
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            
            // 如果直接继承自GenericDao，返回类型参数
            if (actualTypeArguments.length >= 2) {
                return actualTypeArguments;
            }
        }
        
        // 如果没有直接的泛型参数，尝试从接口中获取
        Type[] genericInterfaces = clazz.getGenericInterfaces();
        for (Type genericInterface : genericInterfaces) {
            if (genericInterface instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                if (actualTypeArguments.length >= 2) {
                    return actualTypeArguments;
                }
            }
        }
        
        // 递归检查父类
        Class<?> superclass = clazz.getSuperclass();
        if (superclass != null && !superclass.equals(Object.class)) {
            return getGenericTypes(superclass);
        }
        
        throw new IllegalArgumentException("Cannot determine generic types for class: " + clazz.getName());
    }
    
    /**
     * 获取无泛型的BaseDao实例，用于无实体查询
     * @return BaseDao实例
     */
    public static BaseDao getBaseDao() {
        if (localBaseDao == null) {
            synchronized (DaoHelper.class) {
                if (localBaseDao == null) {
                    localBaseDao = getFactory().getBaseDao();
                }
            }
        }
        return localBaseDao;
    }
    
    /**
     * 清除所有本地缓存
     * 在需要重新加载DAO实例时调用
     */
    public static void clearCache() {
        if (unifiedCacheManager != null) {
            unifiedCacheManager.clear(UnifiedCacheManager.CacheType.DAO_CACHE);
            unifiedCacheManager.clear(UnifiedCacheManager.CacheType.ENTITY_CACHE);
        }
        localBaseDao = null;
    }
}