package com.lg.financecloud.common.ai.exception;

/**
 * AI API调用异常
 * 当调用外部AI服务API出现错误时抛出此异常
 */
public class AIApiException extends AIServiceException {
    private static final String ERROR_CODE = "API_ERROR";

    /**
     * API响应状态码
     */
    private final int statusCode;

    /**
     * 构造函数
     * @param message 错误信息
     * @param statusCode HTTP状态码
     */
    public AIApiException(String message, int statusCode) {
        super(ERROR_CODE, String.format("AI API call failed: %s (status code: %d)", message, statusCode));
        this.statusCode = statusCode;
    }

    /**
     * 构造函数
     * @param message 错误信息
     * @param statusCode HTTP状态码
     * @param cause 原始异常
     */
    public AIApiException(String message, int statusCode, Throwable cause) {
        super(ERROR_CODE, String.format("AI API call failed: %s (status code: %d)", message, statusCode), cause);
        this.statusCode = statusCode;
    }

    /**
     * 获取API响应状态码
     * @return HTTP状态码
     */
    public int getStatusCode() {
        return statusCode;
    }
}