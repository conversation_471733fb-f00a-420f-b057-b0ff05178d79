package com.lg.dao.core.annotation;

import java.lang.annotation.*;

/**
 * 查询条件注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Condition {
    /**
     * 数据库字段名，默认使用属性名的下划线形式
     */
    String field() default "";

    /**
     * 条件类型
     */
    Type type() default Type.EQ;

    /**
     * 是否使用isEmpty判断（默认true）
     * true: 使用isEmpty判断（字符串空，集合空，数组空都算空）
     * false: 使用isNull判断（只有null才算空）
     */
    boolean isEmpty() default true;

    /**
     * 条件类型枚举
     */
    enum Type {
        EQ,             // 等于
        NE,             // 不等于
        GT,             // 大于
        GE,             // 大于等于
        LT,             // 小于
        LE,             // 小于等于
        LIKE,           // 模糊匹配（两边都有%）
        LEFT_LIKE,      // 左模糊匹配（左边有%）
        RIGHT_LIKE,     // 右模糊匹配（右边有%）
        IN,             // IN查询
        NOT_IN,         // NOT IN查询
        IS_NULL,        // IS NULL
        IS_NOT_NULL,    // IS NOT NULL
        BETWEEN         // BETWEEN查询
    }
} 