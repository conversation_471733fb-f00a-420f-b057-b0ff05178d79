package com.lg.mrp.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import com.lg.financecloud.common.base.util.AuditUtils;
import com.lg.mrp.dto.MrpRequirementQueryRequest;
import com.lg.mrp.dto.MrpRequirementUpdateRequest;
import com.lg.mrp.dto.GenerateOrderRequest;
import com.lg.mrp.entity.MrpPlan;
import com.lg.mrp.entity.MrpRequirement;
import com.lg.mrp.enums.MrpPriorityEnum;
import com.lg.mrp.mapper.MrpPlanMapper;
import com.lg.mrp.mapper.MrpRequirementMapper;
import com.lg.mrp.service.MrpRequirementService;
import com.lg.mrp.vo.MrpRequirementVO;
import com.lg.erp.v1.service.PurchaseApplyV1Service;
import com.lg.erp.entity.PurchaseApply;
import com.lg.erp.entity.PurchaseApplyItem;
import com.lg.erp.v1.entity.RestResult;
import com.lg.proplan.service.ProductionPlanService;
import com.lg.proplan.dto.ProductionPlanCreateRequest;
import com.lg.proplan.dto.ProductionPlanItemCreateRequest;
import com.lg.proplan.entity.ProductionPlan;
import com.lg.proplan.mapper.ProductionPlanMapper;
import com.lg.financecloud.common.security.util.SecurityUtils;
import com.lg.financecloud.common.base.util.SequenceUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.common.PoiWorkbookType;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * MRP需求服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class MrpRequirementServiceImpl implements MrpRequirementService {

    private final MrpRequirementMapper mrpRequirementMapper;
    private final MrpPlanMapper mrpPlanMapper;
    private final AuditUtils auditUtils;
    private final PurchaseApplyV1Service purchaseApplyV1Service;
    private final ProductionPlanService productionPlanService;
    private final ProductionPlanMapper productionPlanMapper;
    @Override
    public Page<MrpRequirementVO> getRequirementPageList(MrpRequirementQueryRequest request) {
        log.info("获取需求记录分页列表, request={}", request);

        // 创建分页对象
        Page<MrpRequirementVO> page = new Page<>(request.getCurrent(), request.getSize());
        
        // 使用MyBatis Plus分页插件进行分页查询
        return mrpRequirementMapper.selectRequirementPageList(page, TenantContextHolder.getTenantId(), request);
    }

    @Override
    public List<MrpRequirementVO> getRequirementByPlan(String planId, String materialId) {
        log.info("根据计划ID获取需求列表, planId={}, materialId={}", planId, materialId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        // 添加租户ID条件
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);

        if (StringUtils.hasText(materialId)) {
            queryWrapper.eq(MrpRequirement::getMaterialId, materialId);
        }

        queryWrapper.orderByAsc(MrpRequirement::getBomLevel)
                   .orderByAsc(MrpRequirement::getRequirementDate);

        // 查询数据库获取需求列表
        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<MrpRequirementVO> getRequirementList(String materialId, String requirementType, String sourceType) {
        log.info("获取需求列表, materialId={}, requirementType={}, sourceType={}", materialId, requirementType, sourceType);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        // 添加租户ID条件
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());

        if (StringUtils.hasText(materialId)) {
            queryWrapper.eq(MrpRequirement::getMaterialId, materialId);
        }

        if (StringUtils.hasText(sourceType)) {
            queryWrapper.eq(MrpRequirement::getSourceType, sourceType);
        }

        queryWrapper.orderByAsc(MrpRequirement::getRequirementDate);

        // 查询数据库获取需求列表
        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public MrpRequirementVO getRequirementDetail(String id) {
        log.info("获取需求详情, id={}", id);
        
        MrpRequirement requirement = mrpRequirementMapper.selectById(id);
        if (requirement == null) {
            throw new RuntimeException("需求记录不存在");
        }
        
        // 检查租户ID是否匹配
        if (!TenantContextHolder.getTenantId().equals(requirement.getTenantId())) {
            throw new RuntimeException("无权访问此需求记录");
        }
        
        return convertToVO(requirement);
    }

    @Override
    public List<MrpRequirementVO> getRequirementsByPlan(String planId) {
        log.info("根据计划ID获取需求列表, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);
        queryWrapper.orderByAsc(MrpRequirement::getBomLevel)
                   .orderByAsc(MrpRequirement::getRequirementDate);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<MrpRequirementVO> getRequirementsByMaterial(String materialId, Date startDate, Date endDate) {
        log.info("根据物料ID获取需求列表, materialId={}, startDate={}, endDate={}", materialId, startDate, endDate);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getMaterialId, materialId);

        if (startDate != null) {
            queryWrapper.ge(MrpRequirement::getRequirementDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.le(MrpRequirement::getRequirementDate, endDate);
        }

        queryWrapper.orderByAsc(MrpRequirement::getRequirementDate);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }



    @Override
    public Map<String, BigDecimal> getRequirementSummaryByMaterial(String planId) {
        log.info("按物料汇总需求, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);

        Map<String, BigDecimal> summary = new HashMap<>();
        for (MrpRequirement requirement : requirements) {
            String materialId = requirement.getMaterialId();
            BigDecimal quantity = requirement.getGrossRequirement();
            summary.merge(materialId, quantity, BigDecimal::add);
        }

        return summary;
    }

    @Override
    public Map<Date, BigDecimal> getRequirementSummaryByDate(String planId) {
        log.info("按日期汇总需求, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);

        Map<Date, BigDecimal> summary = new HashMap<>();
        for (MrpRequirement requirement : requirements) {
            Date date = requirement.getRequirementDate();
            BigDecimal quantity = requirement.getGrossRequirement();
            summary.merge(date, quantity, BigDecimal::add);
        }

        return summary;
    }

    @Override
    public Map<String, BigDecimal> getRequirementSummaryBySourceType(String planId) {
        log.info("按来源类型汇总需求, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);

        Map<String, BigDecimal> summary = new HashMap<>();
        for (MrpRequirement requirement : requirements) {
            String sourceType = requirement.getSourceType();
            BigDecimal quantity = requirement.getGrossRequirement();
            summary.merge(sourceType, quantity, BigDecimal::add);
        }

        return summary;
    }

    @Override
    public List<MrpRequirementVO> getBomRequirementTree(String planId, String materialId) {
        log.info("获取BOM层级需求树, planId={}, materialId={}", planId, materialId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);
        queryWrapper.eq(MrpRequirement::getParentMaterialId, materialId);
        queryWrapper.orderByAsc(MrpRequirement::getBomLevel);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<MrpRequirementVO> getRequirementsByBomLevel(String planId, int bomLevel) {
        log.info("获取指定层级的需求, planId={}, bomLevel={}", planId, bomLevel);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);
        queryWrapper.eq(MrpRequirement::getBomLevel, bomLevel);
        queryWrapper.orderByAsc(MrpRequirement::getRequirementDate);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public int getMaxBomLevel(String planId) {
        log.info("获取最大BOM层级, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);
        queryWrapper.orderByDesc(MrpRequirement::getBomLevel);
        queryWrapper.last("LIMIT 1");

        MrpRequirement requirement = mrpRequirementMapper.selectOne(queryWrapper);
        return requirement != null ? requirement.getBomLevel() : 0;
    }

    @Override
    public List<String> validateRequirements(String planId) {
        log.info("验证需求数据完整性, planId={}", planId);

        List<String> errors = new ArrayList<>();

        try {
            // 1. 检查计划是否存在
            MrpPlan plan = mrpPlanMapper.selectById(planId);
            if (plan == null) {
                errors.add("MRP计划不存在: " + planId);
                return errors;
            }

            // 2. 获取所有需求记录
            LambdaQueryWrapper<MrpRequirement> wrapper = Wrappers.<MrpRequirement>lambdaQuery();
            wrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
            wrapper.eq(MrpRequirement::getPlanId, planId);
            List<MrpRequirement> requirements = mrpRequirementMapper.selectList(wrapper);

            if (CollUtil.isEmpty(requirements)) {
                errors.add("计划中没有需求记录");
                return errors;
            }

            // 3. 验证每个需求记录
            for (MrpRequirement requirement : requirements) {
                validateSingleRequirement(requirement, errors);
            }

            // 4. 验证数据一致性
            validateRequirementConsistency(requirements, errors);

        } catch (Exception e) {
            log.error("验证需求数据时发生异常", e);
            errors.add("验证过程中发生异常: " + e.getMessage());
        }

        return errors;
    }

    /**
     * 验证单个需求记录
     */
    private void validateSingleRequirement(MrpRequirement requirement, List<String> errors) {
        String prefix = "需求记录[" + requirement.getId() + "]: ";

        // 检查必填字段
        if (StrUtil.isBlank(requirement.getMaterialId())) {
            errors.add(prefix + "物料ID不能为空");
        }
        if (requirement.getRequirementDate() == null) {
            errors.add(prefix + "需求日期不能为空");
        }
        if (requirement.getGrossRequirement() == null) {
            errors.add(prefix + "毛需求数量不能为空");
        }
        if (requirement.getNetRequirement() == null) {
            errors.add(prefix + "净需求数量不能为空");
        }

        // 检查数量逻辑
        if (requirement.getGrossRequirement() != null &&
            requirement.getGrossRequirement().compareTo(BigDecimal.ZERO) < 0) {
            errors.add(prefix + "毛需求数量不能为负数");
        }
        if (requirement.getNetRequirement() != null &&
            requirement.getNetRequirement().compareTo(BigDecimal.ZERO) < 0) {
            errors.add(prefix + "净需求数量不能为负数");
        }

        // 检查净需求不能大于毛需求
        if (requirement.getGrossRequirement() != null && requirement.getNetRequirement() != null &&
            requirement.getNetRequirement().compareTo(requirement.getGrossRequirement()) > 0) {
            errors.add(prefix + "净需求数量不能大于毛需求数量");
        }

        // 检查BOM层级
        if (requirement.getBomLevel() != null && requirement.getBomLevel() < 0) {
            errors.add(prefix + "BOM层级不能为负数");
        }

        // 检查需求日期不能早于计划开始日期
        // TODO: 添加计划日期范围验证
    }

    /**
     * 验证需求数据一致性
     */
    private void validateRequirementConsistency(List<MrpRequirement> requirements, List<String> errors) {
        // 按物料分组检查
        Map<String, List<MrpRequirement>> materialGroups = requirements.stream()
            .collect(Collectors.groupingBy(MrpRequirement::getMaterialId));

        for (Map.Entry<String, List<MrpRequirement>> entry : materialGroups.entrySet()) {
            String materialId = entry.getKey();
            List<MrpRequirement> materialRequirements = entry.getValue();

            // 检查同一物料的需求日期是否有重复
            Set<Date> dates = new HashSet<>();
            for (MrpRequirement req : materialRequirements) {
                if (req.getRequirementDate() != null) {
                    if (!dates.add(req.getRequirementDate())) {
                        errors.add("物料[" + materialId + "]在同一日期有多个需求记录: " +
                                 req.getRequirementDate());
                    }
                }
            }
        }
    }

    @Override
    public List<MrpRequirementVO> checkRequirementExceptions(String planId) {
        log.info("检查需求异常, planId={}", planId);

        // TODO: 实现需求异常检查逻辑
        return new ArrayList<>();
    }

    @Override
    public List<MrpRequirementVO> getZeroRequirements(String planId) {
        log.info("获取零需求记录, planId={}", planId);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        queryWrapper.eq(MrpRequirement::getPlanId, planId);
        queryWrapper.eq(MrpRequirement::getGrossRequirement, BigDecimal.ZERO);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 将实体转换为VO
     */
    private MrpRequirementVO convertToVO(MrpRequirement requirement) {
        MrpRequirementVO vo = new MrpRequirementVO();
        BeanUtils.copyProperties(requirement, vo);
        
        // 关联查询MRP计划信息
        if (StringUtils.hasText(requirement.getPlanId())) {
            MrpPlan plan = mrpPlanMapper.selectById(requirement.getPlanId());
            if (plan != null) {
                vo.setPlanCode(plan.getPlanCode());
                vo.setPlanName(plan.getPlanName());
            }
        }
        
        return vo;
    }

    // =====================================================
    // 需求调整管理功能实现
    // =====================================================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRequirement(String id, MrpRequirementUpdateRequest request) {
        log.info("更新需求信息, id={}, request={}", id, request);

        try {
            // 1. 获取原需求记录
            MrpRequirement originalRequirement = mrpRequirementMapper.selectById(id);
            if (originalRequirement == null) {
                throw new RuntimeException("需求记录不存在: " + id);
            }

            // 2. 验证调整权限和业务规则
            validateRequirementUpdate(originalRequirement, request);

            // 3. 记录调整历史
            recordRequirementAdjustment(originalRequirement, request);

            // 4. 更新需求记录
            MrpRequirement updatedRequirement = buildUpdatedRequirement(originalRequirement, request);
            int result = mrpRequirementMapper.updateById(updatedRequirement);

            if (result > 0) {
                // 6. 发送调整通知
                sendRequirementAdjustmentNotification(originalRequirement, updatedRequirement, request);

                log.info("需求调整成功, id={}", id);
                return true;
            } else {
                throw new RuntimeException("需求更新失败");
            }

        } catch (Exception e) {
            log.error("更新需求信息失败, id={}", id, e);
            throw new RuntimeException("更新需求信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateRequirements(List<MrpRequirementUpdateRequest> requests) {
        log.info("批量更新需求, 数量={}", requests.size());

        if (CollUtil.isEmpty(requests)) {
            throw new RuntimeException("批量更新请求不能为空");
        }

        try {
            int successCount = 0;
            List<String> errors = new ArrayList<>();

            for (MrpRequirementUpdateRequest request : requests) {
                try {
                    boolean success = updateRequirement(request.getId(), request);
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("批量更新中单个需求失败, id={}", request.getId(), e);
                    errors.add("需求[" + request.getId() + "]更新失败: " + e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                log.warn("批量更新部分失败, 成功数量={}, 失败数量={}", successCount, errors.size());
                // 可以选择抛出异常或返回部分成功结果
                if (successCount == 0) {
                    throw new RuntimeException("批量更新全部失败: " + String.join("; ", errors));
                }
            }

            log.info("批量更新需求完成, 成功数量={}, 总数量={}", successCount, requests.size());
            return successCount > 0;

        } catch (Exception e) {
            log.error("批量更新需求失败", e);
            throw new RuntimeException("批量更新需求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证需求调整的业务规则
     */
    private void validateRequirementUpdate(MrpRequirement original, MrpRequirementUpdateRequest request) {
        // 1. 检查计划状态
        MrpPlan plan = mrpPlanMapper.selectById(original.getPlanId());
        if (plan == null) {
            throw new RuntimeException("关联的MRP计划不存在");
        }

        // 只有已完成的计划才允许调整需求
        if (!"COMPLETED".equals(plan.getStatus())) {
            throw new RuntimeException("只有已完成的MRP计划才允许调整需求");
        }

        // 2. 验证调整数据的合理性
        if (request.getGrossRequirement() != null &&
            request.getGrossRequirement().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("毛需求数量不能为负数");
        }

        if (request.getNetRequirement() != null &&
            request.getNetRequirement().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("净需求数量不能为负数");
        }

        // 3. 检查净需求不能大于毛需求
        BigDecimal grossReq = request.getGrossRequirement() != null ?
                              request.getGrossRequirement() : original.getGrossRequirement();
        BigDecimal netReq = request.getNetRequirement() != null ?
                            request.getNetRequirement() : original.getNetRequirement();

        if (grossReq != null && netReq != null && netReq.compareTo(grossReq) > 0) {
            throw new RuntimeException("净需求数量不能大于毛需求数量");
        }

        // 4. 检查需求日期的合理性
        if (request.getRequirementDate() != null) {
//            Date planStartDate = plan.getPlanStartDate();
//            Date planEndDate = plan.getPlanEndDate();
//
//            if (planStartDate != null && request.getRequirementDate().before(planStartDate)) {
//                throw new RuntimeException("需求日期不能早于计划开始日期");
//            }
//
//            if (planEndDate != null && request.getRequirementDate().after(planEndDate)) {
//                throw new RuntimeException("需求日期不能晚于计划结束日期");
//            }
        }

        // 5. 检查调整幅度限制（可配置）
        validateAdjustmentLimits(original, request);
    }

    /**
     * 验证调整幅度限制
     */
    private void validateAdjustmentLimits(MrpRequirement original, MrpRequirementUpdateRequest request) {
        // 可以配置最大调整幅度，比如不能超过原值的50%
        final BigDecimal MAX_ADJUSTMENT_RATIO = new BigDecimal("0.5"); // 50%

        if (request.getGrossRequirement() != null && original.getGrossRequirement() != null) {
            BigDecimal originalGross = original.getGrossRequirement();
            BigDecimal newGross = request.getGrossRequirement();

            if (originalGross.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal changeRatio = newGross.subtract(originalGross)
                                               .abs()
                                               .divide(originalGross, 4, RoundingMode.HALF_UP);

                if (changeRatio.compareTo(MAX_ADJUSTMENT_RATIO) > 0) {
                    throw new RuntimeException("毛需求调整幅度不能超过原值的50%");
                }
            }
        }

        if (request.getNetRequirement() != null && original.getNetRequirement() != null) {
            BigDecimal originalNet = original.getNetRequirement();
            BigDecimal newNet = request.getNetRequirement();

            if (originalNet.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal changeRatio = newNet.subtract(originalNet)
                                              .abs()
                                              .divide(originalNet, 4, RoundingMode.HALF_UP);

                if (changeRatio.compareTo(MAX_ADJUSTMENT_RATIO) > 0) {
                    throw new RuntimeException("净需求调整幅度不能超过原值的50%");
                }
            }
        }
    }

    /**
     * 记录需求调整历史
     */
    private void recordRequirementAdjustment(MrpRequirement original, MrpRequirementUpdateRequest request) {
        try {
            // 使用依赖注入的审计工具记录调整历史
            auditUtils.recordMrpRequirementAdjustment(
                    original.getId(), 
                    original.getPlanId(), 
                    original.getMaterialCode(),
                    request.getAdjustReason(),
                    original,
                    null // 这里可以传入更新后的数据，如果有的话
            );
            log.info("记录需求调整历史成功, 需求ID={}, 调整原因={}", original.getId(), request.getAdjustReason());
        } catch (Exception e) {
            log.error("记录需求调整历史失败, 需求ID={}", original.getId(), e);
            // 不抛出异常，避免影响主要业务逻辑
        }
    }

    /**
     * 构建更新后的需求记录
     */
    private MrpRequirement buildUpdatedRequirement(MrpRequirement original, MrpRequirementUpdateRequest request) {
        MrpRequirement updated = new MrpRequirement();
        BeanUtils.copyProperties(original, updated);

        // 更新字段
        if (request.getRequirementDate() != null) {
            updated.setRequirementDate(request.getRequirementDate());
        }
        if (request.getGrossRequirement() != null) {
            updated.setGrossRequirement(request.getGrossRequirement());
        }
        if (request.getNetRequirement() != null) {
            updated.setNetRequirement(request.getNetRequirement());
        }
//        if (StrUtil.isNotBlank(request.getPriority())) {
//            updated.setPriority(request.getPriority());
//        }
//        if (StrUtil.isNotBlank(request.getRemarks())) {
//            updated.setRemarks(request.getRemarks());
//        }

        // 设置更新信息
        updated.setUpdateTime(LocalDateTime.now());
        // TODO: 设置更新人
        // updated.setUpdateBy(getCurrentUserId());

        return updated;
    }

    /**
     * 发送需求调整通知
     */
    private void sendRequirementAdjustmentNotification(MrpRequirement original,
                                                      MrpRequirement updated,
                                                      MrpRequirementUpdateRequest request) {
        try {
            log.info("发送需求调整通知, 需求ID={}", original.getId());

            // TODO: 实现通知功能
            // 可以发送邮件、系统消息等通知相关人员
            // notificationService.sendRequirementAdjustmentNotification(original, updated, request);

        } catch (Exception e) {
            log.error("发送需求调整通知失败", e);
            // 不抛出异常，避免影响需求调整操作
        }
    }

    // =====================================================
    // 导出功能实现
    // =====================================================

    /**
     * 获取用于导出的需求列表
     */
    private List<MrpRequirementVO> getRequirementListForExport(MrpRequirementQueryRequest request) {
        log.info("获取用于导出的需求列表, request={}", request);

        LambdaQueryWrapper<MrpRequirement> queryWrapper = Wrappers.<MrpRequirement>lambdaQuery();
        // 添加租户ID条件
        queryWrapper.eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());

        // 构建查询条件
        if (StrUtil.isNotBlank(request.getPlanId())) {
            queryWrapper.eq(MrpRequirement::getPlanId, request.getPlanId());
        }
        if (StrUtil.isNotBlank(request.getMaterialId())) {
            queryWrapper.eq(MrpRequirement::getMaterialId, request.getMaterialId());
        }
        if (StrUtil.isNotBlank(request.getMaterialCode())) {
            queryWrapper.like(MrpRequirement::getMaterialCode, request.getMaterialCode());
        }

        if (StrUtil.isNotBlank(request.getSourceType())) {
            queryWrapper.eq(MrpRequirement::getSourceType, request.getSourceType());
        }
        if (request.getRequirementDateStart() != null) {
            queryWrapper.ge(MrpRequirement::getRequirementDate, request.getRequirementDateStart());
        }
        if (request.getRequirementDateEnd() != null) {
            queryWrapper.le(MrpRequirement::getRequirementDate, request.getRequirementDateEnd());
        }
        if (request.getBomLevel() != null) {
            queryWrapper.eq(MrpRequirement::getBomLevel, request.getBomLevel());
        }

        // 排序
        queryWrapper.orderByAsc(MrpRequirement::getRequirementDate)
                   .orderByAsc(MrpRequirement::getBomLevel)
                   .orderByAsc(MrpRequirement::getMaterialCode);

        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(queryWrapper);
        return requirements.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public void exportRequirements(MrpRequirementQueryRequest request, HttpServletResponse response) {
        log.info("开始导出需求数据, request={}", request);

        try {
            // 获取需求数据
            List<MrpRequirementVO> requirements = getRequirementListForExport(request);

            // 设置响应头
            String fileName = "MRP需求数据_" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setCharacterEncoding("UTF-8");

            // 使用 financecloud-common-excel 框架进行导出
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(requirements)
                        .sheetName("MRP需求数据")
                        .opsHeader()
                            .simple()
                                .title("MRP需求数据导出")
                                .texts("需求ID", "MRP计划ID", "物料编码", "物料名称", "需求日期", "需求类型",
                                       "毛需求数量", "净需求数量", "BOM层级", "需求来源", "来源单据",
                                       "优先级", "备注", "创建时间")
                                .done()
                        .opsColumn()
                            .fields("id", "planId", "materialCode", "materialName", "requirementDate", 
                                   "requirementTypeName", "grossRequirement", "netRequirement", "bomLevel", 
                                   "sourceTypeName", "sourceNo", "priority", "remarks", "createTime")
                            .done()

                        .done()
                    .export(response, fileName);

            log.info("需求数据导出成功，共导出{}条记录", requirements.size());

        } catch (Exception e) {
            log.error("导出需求数据失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void exportRequirementTemplate(HttpServletResponse response) {
        log.info("开始导出需求模板");

        try {
            // 设置响应头
            String fileName = "MRP需求导入模板.xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setCharacterEncoding("UTF-8");

            // 使用 financecloud-common-excel 框架导出模板
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(Collections.emptyList())
                        .sheetName("需求导入模板")
                        .opsHeader()
                            .simple()
                                .title("MRP需求导入模板")
                                .texts("物料编码*", "物料名称", "需求日期*", "需求类型*",
                                       "毛需求数量*", "净需求数量*", "优先级", "备注")
                                .done()
                        .opsColumn()
                            .field("物料编码")
                                .verifyText("1~50", "物料编码长度必须在1-50个字符之间")
                                .done()
                            .field("物料名称")
                                .done()
                            .field("需求日期")
                                .verifyDate("2000-01-01~3000-12-31", "需求日期格式不正确")
                                .pattern("yyyy-MM-dd")
                                .done()
                            .field("需求类型")
                                .dropdown("INDEPENDENT", "DEPENDENT")
                                .done()
                            .field("毛需求数量")
                                .verifyCustom("VALUE(E3:E6000)", "毛需求数量必须是数字格式")
                                .pattern("0.00")
                                .done()
                            .field("净需求数量")
                                .verifyCustom("VALUE(F3:F6000)", "净需求数量必须是数字格式")
                                .pattern("0.00")
                                .done()
                            .field("优先级")
                                .dropdown(MrpPriorityEnum.HIGH.getCode(), MrpPriorityEnum.MEDIUM.getCode(), MrpPriorityEnum.LOW.getCode())
                                .done()
                            .field("备注")
                                .done()
                            .done()
                        .opsFooter()
                            .text("说明：", "A1:H1")
                            .text("1. 带*的字段为必填项", "A2:H2")
                            .text("2. 需求类型：INDEPENDENT(独立需求)、DEPENDENT(相关需求)", "A3:H3")
                            .text("3. 日期格式：yyyy-MM-dd", "A4:H4")
                            .text("4. 数量格式：支持小数，最多2位小数", "A5:H5")
                            .text("5. 优先级：" + MrpPriorityEnum.HIGH.getCode() + "(高)、" + MrpPriorityEnum.MEDIUM.getCode() + "(中)、" + MrpPriorityEnum.LOW.getCode() + "(低)", "A6:H6")
                            .done()
                        .done()
                    .export(response, fileName);

            log.info("需求模板导出成功");

        } catch (Exception e) {
            log.error("导出需求模板失败", e);
            throw new RuntimeException("模板导出失败: " + e.getMessage(), e);
        }
    }

    // =====================================================
    // 订单生成实现
    // =====================================================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generatePurchaseOrders(GenerateOrderRequest request) {
        log.info("开始生成采购订单, planId={}, requirementIds={}", request.getPlanId(), request.getRequirementIds());
        
        try {
            // 1. 验证请求参数
            validateGenerateOrderRequest(request, "2"); // 2表示外购物料
            
            // 2. 获取需求记录
            List<MrpRequirement> requirements = getRequirementsByIds(request.getRequirementIds());
            
            // 3. 过滤外购物料需求
            List<MrpRequirement> purchaseRequirements = requirements.stream()
                .filter(req -> req.getMaterialType() != null && req.getMaterialType().equals("2"))
                .collect(Collectors.toList());
            
            if (purchaseRequirements.isEmpty()) {
                throw new RuntimeException("没有找到外购物料需求记录");
            }
            
            // 4. 生成采购订单（这里是模拟实现，实际应该调用采购模块的服务）
            String orderResult = createPurchaseOrdersFromRequirements(purchaseRequirements, request);
            
            log.info("采购订单生成成功, 结果={}", orderResult);
            return orderResult;
            
        } catch (Exception e) {
            log.error("生成采购订单失败", e);
            throw new RuntimeException("生成采购订单失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateProductionOrders(GenerateOrderRequest request) {
        log.info("开始生成生产订单, planId={}, requirementIds={}", request.getPlanId(), request.getRequirementIds());
        
        try {
            // 1. 验证请求参数
            validateGenerateOrderRequest(request, "1"); // 1表示自产物料
            
            // 2. 获取需求记录
            List<MrpRequirement> requirements = getRequirementsByIds(request.getRequirementIds());
            
            // 3. 过滤自产物料需求
            List<MrpRequirement> productionRequirements = requirements.stream()
                .filter(req -> req.getMaterialType() != null && req.getMaterialType().equals("1"))
                .collect(Collectors.toList());
            
            if (productionRequirements.isEmpty()) {
                throw new RuntimeException("没有找到自产物料需求记录");
            }
            
            // 4. 生成生产订单（这里是模拟实现，实际应该调用生产模块的服务）
            String orderResult = createProductionOrdersFromRequirements(productionRequirements, request);
            
            log.info("生产订单生成成功, 结果={}", orderResult);
            return orderResult;
            
        } catch (Exception e) {
            log.error("生成生产订单失败", e);
            throw new RuntimeException("生成生产订单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证生成订单请求
     */
    private void validateGenerateOrderRequest(GenerateOrderRequest request, String expectedMaterialType) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (StrUtil.isBlank(request.getPlanId())) {
            throw new IllegalArgumentException("MRP计划ID不能为空");
        }
        if (CollUtil.isEmpty(request.getRequirementIds())) {
            throw new IllegalArgumentException("需求ID列表不能为空");
        }
        if (!expectedMaterialType.equals(request.getMaterialType())) {
            throw new IllegalArgumentException("物料类型不匹配");
        }
    }

    /**
     * 根据ID列表获取需求记录
     */
    private List<MrpRequirement> getRequirementsByIds(List<String> requirementIds) {
        LambdaQueryWrapper<MrpRequirement> wrapper = Wrappers.<MrpRequirement>lambdaQuery()
            .in(MrpRequirement::getId, requirementIds)
            .eq(MrpRequirement::getTenantId, TenantContextHolder.getTenantId());
        
        List<MrpRequirement> requirements = mrpRequirementMapper.selectList(wrapper);
        
        if (requirements.size() != requirementIds.size()) {
            throw new RuntimeException("部分需求记录不存在或无权限访问");
        }
        
        return requirements;
    }

    /**
     * 从 MRP 需求记录构建采购申请单对象
     */
    private PurchaseApply buildPurchaseApplyFromRequirements(List<MrpRequirement> requirements, GenerateOrderRequest request) {
        Date now = new Date();
        
        // 构建主表对象
        PurchaseApply purchaseApply = new PurchaseApply();
        purchaseApply.setId(String.valueOf(SequenceUtils.nextId()));
        purchaseApply.setSubmitType(2); // 直接提交
        purchaseApply.setSource("0"); // 默认来源
        purchaseApply.setModuleSource("0"); // MRP模块来源
        purchaseApply.setStatus(0); // 未审核
        purchaseApply.setDelFlag("0"); // 正常状态
        purchaseApply.setCreateBy(Long.valueOf(SecurityUtils.getUser().getStaffInfo().getId()));
        purchaseApply.setCreateTime(now);
        purchaseApply.setUpdateTime(now);
        purchaseApply.setOrderDate(now);
        
        // 设置申请人和申请部门
        purchaseApply.setApplicant(SecurityUtils.getUser().getStaffInfo().getId());
        purchaseApply.setApplyDept(SecurityUtils.getDeptId()+"");
        
        // 设置采购期限为最早的需求日期
        Date earliestRequirementDate = requirements.stream()
            .map(MrpRequirement::getRequirementDate)
            .filter(Objects::nonNull)
            .min(Date::compareTo)
            .orElse(now);
        purchaseApply.setPurchaseTimeLimit(earliestRequirementDate);
        
        // 设置申购事由
        purchaseApply.setReason("MRP计划生成的采购需求，计划ID：" + request.getPlanId());
        
        // 构建子表对象列表
        List<PurchaseApplyItem> itemList = new ArrayList<>();
        for (MrpRequirement requirement : requirements) {
            PurchaseApplyItem item = new PurchaseApplyItem();
            item.setId(String.valueOf(SequenceUtils.nextId()));
            item.setApplyId(purchaseApply.getId());
            item.setMaterialId(requirement.getMaterialId());
            item.setMaterialName(requirement.getMaterialName());
            item.setMaterialCode(requirement.getMaterialCode());
            item.setOperNumber(requirement.getGrossRequirement());
            item.setDemandDate(requirement.getRequirementDate());
            item.setRemark("MRP需求ID：" + requirement.getId());
            item.setNormsId(requirement.getNormsId());
            item.setType("0");
            item.setCreateTime(now);
            item.setUpdateTime(now);
            itemList.add(item);
        }
        
        // 设置子表数据
        purchaseApply.setPurchaseApplyItemList(itemList);
        
        return purchaseApply;
    }

    /**
     * 从需求记录创建采购订单
     */
    private String createPurchaseOrdersFromRequirements(List<MrpRequirement> requirements, GenerateOrderRequest request) {
        try {
            log.info("开始创建采购申请单，需求记录数量：{}", requirements.size());
            
            // 1. 构建采购申请单对象
            PurchaseApply purchaseApply = buildPurchaseApplyFromRequirements(requirements, request);
            
            // 2. 调用采购模块的保存接口
            RestResult saveResult = purchaseApplyV1Service.save(purchaseApply);
            
            // 3. 处理保存结果
            if (saveResult.isSuccess()) {
                log.info("采购申请单创建成功，申请单ID：{}", purchaseApply.getId());
                
                // 4. 回写 generated_order_no 字段
                updateRequirementsGeneratedOrderNo(requirements, purchaseApply.getOrderNumber());
                
                StringBuilder result = new StringBuilder();
                result.append("成功创建采购申请单，申请单编号：").append(purchaseApply.getOrderNumber())
                      .append("，包含 ").append(requirements.size()).append(" 个需求项：\n");
                
                for (MrpRequirement requirement : requirements) {
                    result.append("- 物料: ").append(requirement.getMaterialName())
                          .append("，数量: ").append(requirement.getGrossRequirement())
                          .append("，需求日期: ").append(requirement.getRequirementDate())
                          .append("\n");
                }
                
                return result.toString();
            } else {
                log.error("采购申请单创建失败：{}", saveResult.getReturnMessage());
                throw new RuntimeException("创建采购申请单失败：" + saveResult.getReturnMessage());
            }
            
        } catch (Exception e) {
            log.error("创建采购申请单过程中发生异常", e);
            throw new RuntimeException("创建采购申请单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从需求记录创建生产订单
     */
    private String createProductionOrdersFromRequirements(List<MrpRequirement> requirements, GenerateOrderRequest request) {
        try {
            log.info("开始创建生产计划单，需求记录数量：{}", requirements.size());
            
            // 1. 构建生产计划单对象
            ProductionPlanCreateRequest planRequest = buildProductionPlanFromRequirements(requirements, request);
            
            // 2. 调用生产计划模块的创建接口
            String planId = productionPlanService.createPlan(planRequest);
            
            // 3. 查询生产计划单获取实际的计划单号
            String planNo = getProductionPlanNo(planId);
            
            // 4. 回写 generated_order_no 字段
            updateRequirementsGeneratedOrderNo(requirements, planNo);
            
            StringBuilder result = new StringBuilder();
            result.append("成功创建生产计划单，计划单ID：").append(planId)
                  .append("，计划单号：").append(planNo)
                  .append("，包含 ").append(requirements.size()).append(" 个需求项：\n");
            
            for (MrpRequirement requirement : requirements) {
                result.append("- 物料: ").append(requirement.getMaterialName())
                      .append("，数量: ").append(requirement.getGrossRequirement())
                      .append("，需求日期: ").append(requirement.getRequirementDate())
                      .append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("创建生产计划单过程中发生异常", e);
            throw new RuntimeException("创建生产计划单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从需求记录构建生产计划单请求对象
     */
    private ProductionPlanCreateRequest buildProductionPlanFromRequirements(List<MrpRequirement> requirements, GenerateOrderRequest request) {
        ProductionPlanCreateRequest planRequest = new ProductionPlanCreateRequest();
        
        // 设置计划单基本信息
        planRequest.setPlanName("MRP需求生成的生产计划-" + LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));
        planRequest.setPlanDate(LocalDateTime.now().toLocalDate());
        
        // 获取第一个需求的MRP计划ID
        if (!requirements.isEmpty()) {
            planRequest.setMrpPlanId(requirements.get(0).getPlanId());
        }

        // 计算计划开始和结束日期
        LocalDateTime earliestDate = requirements.stream()
                .map(MrpRequirement::getRequirementDate)
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                .min(LocalDateTime::compareTo)
                .orElse(LocalDateTime.now());

        LocalDateTime latestDate = requirements.stream()
                .map(MrpRequirement::getRequirementDate)
                .filter(Objects::nonNull)
                .map(date -> date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                .max(LocalDateTime::compareTo)
                .orElse(LocalDateTime.now().plusDays(30));
        
        planRequest.setStartDate(earliestDate.toLocalDate());
        planRequest.setEndDate(latestDate.toLocalDate());
        
        // 设置部门信息（如果有的话）
        if (request.getDepartmentId() != null) {
            planRequest.setDepartmentId(request.getDepartmentId());
        } else {
            // 从最近的生产计划单中获取部门ID
            String recentDepartmentId = getRecentDepartmentId();
            if (recentDepartmentId != null) {
                planRequest.setDepartmentId(recentDepartmentId);
                log.info("从最近生产计划单获取到部门ID: {}", recentDepartmentId);
            }
        }
        
        // 设置备注
        planRequest.setRemark("由MRP需求自动生成，包含" + requirements.size() + "个需求项");
        
        // 构建计划明细
        List<ProductionPlanItemCreateRequest> items = requirements.stream()
                .map(this::convertRequirementToPlanItem)
                .collect(Collectors.toList());
        
        planRequest.setItems(items);
        
        return planRequest;
    }
    
    /**
     * 将MRP需求转换为生产计划明细
     */
    private ProductionPlanItemCreateRequest convertRequirementToPlanItem(MrpRequirement requirement) {
        ProductionPlanItemCreateRequest item = new ProductionPlanItemCreateRequest();
        item.setId(IdUtil.getSnowflakeNextIdStr());
        item.setMaterialId(requirement.getMaterialId());
        item.setMaterialCode(requirement.getMaterialCode());
        item.setMaterialName(requirement.getMaterialName());
        item.setRequirementQty(requirement.getGrossRequirement());
        item.setPlannedQty(requirement.getGrossRequirement()); // 计划数量等于需求数量
        item.setNormsId(requirement.getNormsId());
        item.setMaterialSpec(requirement.getMaterialSpec());
        item.setUnit(requirement.getUnit());
        item.setUnitId(requirement.getUnitId());
        item.setWorkCenterId(requirement.getWorkCenterId());
        item.setWorkCenterName(requirement.getWorkCenterName());
        item.setEngineeringSolutionId(requirement.getEngineeringSolutionId());
        item.setEngineeringSolutionName(requirement.getEngineeringSolutionName());
        DateTime dateTime = DateUtil.offsetSecond(requirement.getRequirementDate(), -7);

        item.setPlanStartDate(dateTime.toLocalDateTime().toLocalDate()); // 提前7天开始生产
        LocalDate localDate = DateUtil.toLocalDateTime(requirement.getRequirementDate()).toLocalDate();

        item.setPlanEndDate(localDate);

        item.setPriority(MrpPriorityEnum.MEDIUM.getCode()); // 默认优先级
        item.setRemark("由MRP需求ID:" + requirement.getId() + "生成");
        
        return item;
    }

    /**
     * 查询生产计划单的计划单号
     */
    private String getProductionPlanNo(String planId) {
        try {
            ProductionPlan plan = productionPlanMapper.selectById(planId);
            if (plan != null && plan.getPlanNo() != null) {
                return plan.getPlanNo();
            } else {
                log.warn("未找到计划单或计划单号为空，planId: {}", planId);
                // 如果查询失败，生成一个临时计划单号
                return "PP" + System.currentTimeMillis();
            }
        } catch (Exception e) {
            log.error("查询生产计划单号失败，planId: {}", planId, e);
            // 如果查询失败，生成一个临时计划单号
            return "PP" + System.currentTimeMillis();
        }
    }

    /**
     * 更新需求记录的生成订单号
     */
    private void updateRequirementsGeneratedOrderNo(List<MrpRequirement> requirements, String orderNo) {
        try {
            log.info("开始回写需求记录的生成订单号，订单号：{}，需求数量：{}", orderNo, requirements.size());
            
            for (MrpRequirement requirement : requirements) {
                requirement.setGeneratedOrderNo(orderNo);
                requirement.setUpdateTime(LocalDateTime.now());
                mrpRequirementMapper.updateById(requirement);
            }
            
            log.info("成功回写需求记录的生成订单号");
        } catch (Exception e) {
            log.error("回写需求记录的生成订单号失败", e);
            throw new RuntimeException("回写生成订单号失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从最近的生产计划单中获取部门ID
     */
    private String getRecentDepartmentId() {
        try {
            log.info("开始从最近的生产计划单中获取部门ID");
            
            // 查询最近的生产计划单（按创建时间倒序）
            LambdaQueryWrapper<ProductionPlan> wrapper = Wrappers.<ProductionPlan>lambdaQuery()
                   .orderByDesc(ProductionPlan::getCreatedTime)
                   .last("LIMIT 1");
            
            ProductionPlan recentPlan = productionPlanMapper.selectOne(wrapper);
            
            if (recentPlan != null && StringUtils.hasText(recentPlan.getDepartmentId())) {
                log.info("成功获取到最近生产计划单的部门ID：{}", recentPlan.getDepartmentId());
                return recentPlan.getDepartmentId();
            }
            
            log.warn("未找到最近的生产计划单或部门ID为空");
            return null;
        } catch (Exception e) {
            log.error("从最近生产计划单获取部门ID失败", e);
            return null;
        }
    }
}