package com.lg.dao.core.tenant;

import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import org.springframework.util.StringUtils;

/**
 * 租户上下文
 * @deprecated 请使用 {@link com.lg.financecloud.common.data.tenant.TenantContextHolder} 代替
 */
@Deprecated
public class TenantContext {
    private static final ThreadLocal<Boolean> IGNORE_TENANT = new ThreadLocal<>();

    /**
     * 设置租户ID
     * @deprecated 请使用 {@link TenantContextHolder#setTenantId(Integer)} 代替
     */
    @Deprecated
    public static void setTenantId(String tenantId) {
        if (StringUtils.hasText(tenantId)) {
            try {
                TenantContextHolder.setTenantId(Integer.parseInt(tenantId));
            } catch (NumberFormatException e) {
                // 如果无法解析为整数，则设置为默认值1
                TenantContextHolder.setTenantId(1);
            }
        }
    }

    /**
     * 获取租户ID
     * @deprecated 请使用 {@link TenantContextHolder#getTenantId()} 代替
     */
    @Deprecated
    public static String getTenantId() {
        Integer tenantId = TenantContextHolder.getTenantId();
        return tenantId != null ? tenantId.toString() : null;
    }

    /**
     * 清除租户ID
     * @deprecated 请使用 {@link TenantContextHolder#clear()} 代替
     */
    @Deprecated
    public static void clearTenantId() {
        TenantContextHolder.clear();
    }

    /**
     * 设置忽略租户
     */
    public static void setIgnoreTenant(boolean ignore) {
        IGNORE_TENANT.set(ignore);
    }

    /**
     * 是否忽略租户
     */
    public static boolean isIgnoreTenant() {
        return Boolean.TRUE.equals(IGNORE_TENANT.get());
    }

    /**
     * 清除忽略租户标记
     */
    public static void clearIgnoreTenant() {
        IGNORE_TENANT.remove();
    }

    /**
     * 清除所有上下文
     * @deprecated 请使用 {@link TenantContextHolder#clear()} 代替
     */
    @Deprecated
    public static void clear() {
        TenantContextHolder.clear();
        clearIgnoreTenant();
    }
} 