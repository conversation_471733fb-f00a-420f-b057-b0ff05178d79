# 主从表模式和PDF导出功能使用说明

## 概述

financecloud-common-excel 组件现已支持主从表模式导出和PDF导出功能，可以轻松处理一对多关系的数据结构，如订单头信息对应多个订单明细行，并支持将Excel数据直接转换为PDF格式。

## 主从表模式支持

### 1. 基本概念

主从表模式支持头表（主表）和行表（明细表）的循环导出功能：
- **主表**：包含主要信息的表，如订单头信息
- **明细表**：包含详细信息的表，如订单明细行
- **一对多关系**：一个主表记录对应多个明细表记录

### 2. 使用方式

#### 2.1 编程式API

```java
// 准备主表数据
List<OrderMaster> orders = getOrderData();

// 使用主从表导出
ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsMasterDetailSheet(orders, OrderMaster::getOrderDetails)
    .sheetName("订单主从表")
    .spacingRows(2)  // 主从表之间间隔2行
    .showDetailHeader(true)  // 显示明细表头
    .repeatDetailHeaderForEachMaster(false)  // 不为每个主表重复明细表头
    // 配置主表表头
    .opsMasterHeader()
        .simple()
        .texts("订单号", "客户名称", "订单日期", "订单金额", "订单状态")
        .done()
    // 配置主表列
    .opsMasterColumn()
        .fields("orderNo", "customerName", "orderDate", "totalAmount", "status")
        .done()
    // 配置明细表表头
    .opsDetailHeader()
        .simple()
        .texts("商品编码", "商品名称", "数量", "单价", "金额")
        .done()
    // 配置明细表列
    .opsDetailColumn()
        .fields("productCode", "productName", "quantity", "unitPrice", "amount")
        .done()
    .done()
    .export("masterDetailExport.xlsx");
```

#### 2.2 注解式配置

```java
@ExcelMasterDetail(
    sheetName = "订单信息",
    spacingRows = 1,
    showDetailHeader = true,
    detailField = "orderDetails"
)
public class OrderMaster {
    @ExcelMasterField(title = "订单号", order = 1, width = 4000)
    private String orderNo;
    
    @ExcelMasterField(title = "客户名称", order = 2, width = 3000)
    private String customerName;
    
    @ExcelMasterField(title = "订单日期", order = 3, width = 3000, pattern = "yyyy-MM-dd")
    private Date orderDate;
    
    @ExcelMasterField(title = "订单金额", order = 4, width = 3000, pattern = "#,##0.00")
    private BigDecimal totalAmount;
    
    private List<OrderDetail> orderDetails;
    
    // getter/setter...
}

public class OrderDetail {
    @ExcelDetailField(title = "商品编码", order = 1, width = 3000)
    private String productCode;
    
    @ExcelDetailField(title = "商品名称", order = 2, width = 4000)
    private String productName;
    
    @ExcelDetailField(title = "数量", order = 3, width = 2000)
    private Integer quantity;
    
    @ExcelDetailField(title = "单价", order = 4, width = 3000, pattern = "#,##0.00")
    private BigDecimal unitPrice;
    
    @ExcelDetailField(title = "金额", order = 5, width = 3000, pattern = "#,##0.00", showSum = true)
    private BigDecimal amount;
    
    // getter/setter...
}
```

### 3. 配置选项

#### 3.1 主从表配置

- `sheetName`: Sheet名称
- `spacingRows`: 主从表之间的间隔行数
- `showDetailHeader`: 是否显示明细表头
- `repeatDetailHeaderForEachMaster`: 是否为每个主表记录重复明细表头
- `detailField`: 明细数据字段名

#### 3.2 字段配置

**主表字段注解 @ExcelMasterField**
- `title`: 列标题
- `order`: 列顺序
- `width`: 列宽度
- `pattern`: 数据格式
- `mergeRepeat`: 是否合并相同值的单元格
- `align`: 水平对齐方式
- `valign`: 垂直对齐方式

**明细表字段注解 @ExcelDetailField**
- 包含主表字段的所有配置
- `showSum`: 是否显示合计
- `sumLabel`: 合计标签

## PDF导出功能

### 1. 基本使用

```java
// 直接从Excel导出PDF
ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsSheet(data)
    .opsHeader().simple().texts("列1", "列2", "列3").done()
    .opsColumn().fields("field1", "field2", "field3").done()
    .done()
    .exportToPdf("output.pdf");
```

### 2. 高级配置

```java
// 配置PDF导出参数
PdfExportConfig pdfConfig = new PdfExportConfig()
    .setOrientation(PdfExportConfig.PageOrientation.LANDSCAPE)  // 横向
    .setPageSize(PdfExportConfig.PageSize.A4)  // A4纸张
    .setFontSize(12f)  // 字体大小
    .setMargins(20f)  // 页边距
    .setShowGridLines(true)  // 显示网格线
    .setEnableChineseFont(true)  // 启用中文字体
    .setHeaderBackgroundColor(0xF0F0F0);  // 表头背景色

// 使用配置导出PDF
ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsSheet(data)
    .opsHeader().simple().texts("列1", "列2", "列3").done()
    .opsColumn().fields("field1", "field2", "field3").done()
    .done()
    .exportToPdf("output.pdf", pdfConfig);
```

### 3. 注解式PDF配置

```java
@ExcelToPdf(
    orientation = "LANDSCAPE",
    pageSize = "A4",
    fontSize = 10f,
    enableChineseFont = true,
    showGridLines = true,
    headerBackgroundColor = 0xF0F0F0
)
public class DataModel {
    // 字段定义...
}
```

### 4. 多种导出方式

```java
// 导出到文件
.exportToPdf("output.pdf");

// 导出到输出流
.exportToPdf(outputStream);

// 导出到HTTP响应
.exportToPdf(response, "filename.pdf");

// 带配置的导出
.exportToPdf("output.pdf", pdfConfig);
.exportToPdf(outputStream, pdfConfig);
.exportToPdf(response, "filename.pdf", pdfConfig);
```

## 完整示例

### Spring Boot Controller示例

```java
@RestController
@RequestMapping("/api/export")
public class ExportController {

    @GetMapping("/master-detail")
    public void exportMasterDetail(HttpServletResponse response) {
        // 获取数据
        List<OrderMaster> orders = orderService.getOrders();
        
        // 导出Excel
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
            .opsMasterDetailSheet(orders, OrderMaster::getOrderDetails)
            .sheetName("订单明细表")
            .spacingRows(1)
            .showDetailHeader(true)
            .opsMasterHeader()
                .simple()
                .texts("订单号", "客户名称", "订单日期", "订单金额")
                .done()
            .opsMasterColumn()
                .fields("orderNo", "customerName", "orderDate", "totalAmount")
                .done()
            .opsDetailHeader()
                .simple()
                .texts("商品编码", "商品名称", "数量", "单价", "金额")
                .done()
            .opsDetailColumn()
                .fields("productCode", "productName", "quantity", "unitPrice", "amount")
                .done()
            .done()
            .export(response, "订单明细表.xlsx");
    }
    
    @GetMapping("/pdf")
    public void exportPdf(HttpServletResponse response) {
        // 获取数据
        List<OrderMaster> orders = orderService.getOrders();
        
        // 配置PDF参数
        PdfExportConfig pdfConfig = new PdfExportConfig()
            .setOrientation(PdfExportConfig.PageOrientation.LANDSCAPE)
            .setEnableChineseFont(true)
            .setFontSize(10f);
        
        // 导出PDF
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
            .opsSheet(orders)
            .opsHeader().simple().texts("订单号", "客户名称", "订单日期", "订单金额").done()
            .opsColumn().fields("orderNo", "customerName", "orderDate", "totalAmount").done()
            .done()
            .exportToPdf(response, "订单列表.pdf", pdfConfig);
    }
}
```

## 注意事项

1. **依赖要求**：PDF导出功能需要添加iText相关依赖
2. **中文字体**：启用中文字体支持可能需要额外的字体文件
3. **性能考虑**：大数据量时建议使用分页或流式处理
4. **内存管理**：主从表导出时注意内存使用，避免OOM
5. **样式保持**：PDF导出会尽量保持Excel的格式，但可能有细微差异

## 扩展性

组件设计时充分考虑了扩展性：
- 支持自定义PDF渲染器
- 支持自定义主从表处理逻辑
- 支持插件式的格式转换器
- 支持自定义注解处理器
