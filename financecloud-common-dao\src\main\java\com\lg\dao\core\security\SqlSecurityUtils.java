package com.lg.dao.core.security;

import lombok.extern.slf4j.Slf4j;
import java.util.regex.Pattern;

/**
 * SQL安全工具类
 */
@Slf4j
public class SqlSecurityUtils {
    
    // 危险SQL关键字模式
    private static final Pattern DANGEROUS_PATTERN = Pattern.compile(
        "(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript).*"
    );
    
    // 字段名和表名验证模式
    private static final Pattern FIELD_NAME_PATTERN = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*$");
    
    /**
     * 验证字段名安全性
     */
    public static void validateFieldName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            throw new SecurityException("Field name cannot be null or empty");
        }
        
        if (!FIELD_NAME_PATTERN.matcher(fieldName).matches()) {
            throw new SecurityException("Invalid field name: " + fieldName);
        }
        
        if (DANGEROUS_PATTERN.matcher(fieldName).matches()) {
            throw new SecurityException("Dangerous field name detected: " + fieldName);
        }
    }
    
    /**
     * 验证表名安全性
     */
    public static void validateTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            throw new SecurityException("Table name cannot be null or empty");
        }
        
        if (!FIELD_NAME_PATTERN.matcher(tableName).matches()) {
            throw new SecurityException("Invalid table name: " + tableName);
        }
        
        if (DANGEROUS_PATTERN.matcher(tableName).matches()) {
            throw new SecurityException("Dangerous table name detected: " + tableName);
        }
    }
    
    /**
     * 清理排序字段
     */
    public static String sanitizeOrderBy(String orderBy) {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "";
        }
        
        // 移除危险字符，只保留字段名、逗号、空格和ASC/DESC
        String cleaned = orderBy.replaceAll("[^a-zA-Z0-9_,\\s]", "")
                                .replaceAll("(?i)\\b(?!asc|desc)\\w*(?:union|select|insert|update|delete|drop|create|alter|exec|execute)\\w*\\b", "");
        
        return cleaned.trim();
    }
}