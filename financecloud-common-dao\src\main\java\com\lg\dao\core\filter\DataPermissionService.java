package com.lg.dao.core.filter;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.interceptor.InterceptorExclusionManager;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.admin.api.dto.SessionUser;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import com.lg.dao.core.BaseDao;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * 数据权限服务
 * 用于加载和管理数据权限规则
 *
 * <AUTHOR>
 */
@Slf4j
public class DataPermissionService {

    @Autowired
    @Lazy
    private BaseDao baseDao;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired(required = false)
    private UnifiedCacheManager unifiedCacheManager;
    
    // 兼容老版本的RuleVariable常量定义
    public static final RuleVariable DP_RULE_VAR_CURRENT_USER_ID = new RuleVariable("{currentUserId}", "当前用户ID", "selectUser");
    public static final RuleVariable DP_RULE_VAR_CURRENT_JOB_ID = new RuleVariable("{currentJobId}", "当前职位ID", "selectJob");
    public static final RuleVariable DP_RULE_VAR_CURRENT_STAFF_ID = new RuleVariable("{currentStaffId}", "当前员工ID", "selectStaff");
    public static final RuleVariable DP_RULE_VAR_CURRENT_ROLE_ID = new RuleVariable("{currentRoleId}", "当前角色ID", "selectRole");
    public static final RuleVariable DP_RULE_VAR_CURRENT_DEPT_ID = new RuleVariable("{currentDeptId}", "当前部门ID", "selectDept");
    public static final RuleVariable DP_RULE_VAR_CURRENT_COMPANY_ID = new RuleVariable("{currentCompanyId}", "当前公司ID", "selectCompany");
    public static final RuleVariable DP_RULE_VAR_CURRENT_DEPT_AND_SUB = new RuleVariable("{currentDeptAndSub}", "本部门以及子部门");
    public static final RuleVariable DP_RULE_VAR_CURRENT_COMPANY_AND_SUB = new RuleVariable("{currentCompanyAndSub}", "本公司以及子公司");
    
    /**
     * 规则变量定义
     */
    private static final Map<String, RuleVariable> RULE_VARIABLES = new HashMap<String, RuleVariable>() {{
        put("{currentUserId}", DP_RULE_VAR_CURRENT_USER_ID);
        put("{currentJobId}", DP_RULE_VAR_CURRENT_JOB_ID);
        put("{currentStaffId}", DP_RULE_VAR_CURRENT_STAFF_ID);
        put("{currentRoleId}", DP_RULE_VAR_CURRENT_ROLE_ID);
        put("{currentDeptId}", DP_RULE_VAR_CURRENT_DEPT_ID);
        put("{currentCompanyId}", DP_RULE_VAR_CURRENT_COMPANY_ID);
        put("{currentDeptAndSub}", DP_RULE_VAR_CURRENT_DEPT_AND_SUB);
        put("{currentCompanyAndSub}", DP_RULE_VAR_CURRENT_COMPANY_AND_SUB);
    }};
    
    /**
     * 变量匹配模式
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{([^{}]+)\\}");
    
    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        initRuleVariables();
        // 延迟加载数据权限规则，避免循环依赖
        // loadAllDataPermissionRules();
    }
    
    /**
     * 初始化规则变量
     */
    private void initRuleVariables() {
        // 规则变量已在静态初始化块中定义，此方法保留用于兼容性
    }
    

    
    /**
     * 获取数据权限规则
     *
     * @param tableCode 表编码
     * @return 数据权限规则列表
     */
    public List<DataPermissionRule> getDataPermissionRules(String tableCode) {
        if (StringUtils.isEmpty(tableCode)) {
            return Collections.emptyList();
        }

        // 使用统一缓存管理器
        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.PERMISSION_CACHE, tableCode,
                () -> loadDataPermissionRulesFromDb(tableCode));
        }

        // 降级到直接数据库查询
        return loadDataPermissionRulesFromDb(tableCode);
    }

    /**
     * 从数据库加载数据权限规则
     */
    private List<DataPermissionRule> loadDataPermissionRulesFromDb(String tableCode) {
        if (baseDao == null) {
            return Collections.emptyList();
        }

        try {
            // 使用排除数据权限的方式查询，避免死循环
            return InterceptorExclusionManager.executeWithoutDataPermission(() -> {
                GenericDao<DataPermissionRule, Long> dao = DaoHelper.dao(DataPermissionRule.class);
                List<DataPermissionRule> queryRules = dao.lambdaQuery()
                    .eq(DataPermissionRule::getTableCode, tableCode)
                    .eq(DataPermissionRule::getDelFlag, 0)
                    .list();

                return queryRules != null ? queryRules : Collections.emptyList();
            });
        } catch (Exception e) {
            log.error("获取数据权限规则失败: {}", tableCode, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 刷新数据权限规则
     *
     * @param tableCode 表编码
     */
    public void refreshDataPermissionRules(String tableCode) {
        if (unifiedCacheManager != null) {
            unifiedCacheManager.remove(UnifiedCacheManager.CacheType.PERMISSION_CACHE, tableCode);
        }
        // 重新加载
        getDataPermissionRules(tableCode);
    }
    
    /**
     * 获取数据权限条件
     *
     * @param tableCode 表编码
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 数据权限条件
     */
    public String getDataPermissionCondition(String tableCode, String userId, List<String> roleIds) {
        List<DataPermissionRule> rules = getDataPermissionRules(tableCode);
        if (rules == null || rules.isEmpty()) {
            return null;
        }
        
        List<String> conditions = new ArrayList<>();
        
        for (DataPermissionRule rule : rules) {
            // 检查是否是用户ID规则
            if (userId.equals(rule.getRoleId())) {
                String condition = buildCondition(rule);
                if (!StringUtils.isEmpty(condition)) {
                    conditions.add(condition);
                }
                continue;
            }
            
            // 检查是否是角色ID规则
            if (roleIds != null && roleIds.contains(rule.getRoleId())) {
                String condition = buildCondition(rule);
                if (!StringUtils.isEmpty(condition)) {
                    conditions.add(condition);
                }
            }
        }
        
        if (conditions.isEmpty()) {
            return null;
        }
        
        return String.join(" OR ", conditions);
    }
    
    /**
     * 构建条件
     *
     * @param rule 数据权限规则
     * @return 条件
     */
    private String buildCondition(DataPermissionRule rule) {
        String conditionJson = rule.getConditionJson();
        if (StringUtils.isEmpty(conditionJson)) {
            return null;
        }
        
        try {
            JsonNode conditionNode = objectMapper.readTree(conditionJson);
            
            String field = conditionNode.path("field").asText();
            String operator = conditionNode.path("operator").asText();
            String value = conditionNode.path("value").asText();
            
            // 替换变量
            value = replaceVariables(value);
            
            // 根据操作符构建条件
            return buildSqlCondition(field, operator, value);
        } catch (JsonProcessingException e) {
            log.error("解析数据权限规则条件失败: {}", rule.getId(), e);
            return null;
        }
    }
    
    /**
     * 替换变量
     *
     * @param value 值
     * @return 替换后的值
     */
    private String replaceVariables(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }
        
        // 获取当前用户信息
        SessionUser sessionUser = TenantContextHolder.getCurrentSessionUser();
        if (sessionUser == null) {
            return value;
        }
        
        // 使用正则表达式查找所有变量
        Matcher matcher = VARIABLE_PATTERN.matcher(value);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variable = matcher.group(1);
            String replacement = getVariableValue(variable, sessionUser);
            matcher.appendReplacement(result, replacement);
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
    
    /**
     * 获取变量值
     *
     * @param variable 变量名
     * @param sessionUser 当前用户
     * @return 变量值
     */
    private String getVariableValue(String variable, SessionUser sessionUser) {
        if ("currentUserId".equals(variable)) {
            return sessionUser.getId() != null ? sessionUser.getId().toString() : "";
        } else if ("currentJobId".equals(variable)) {
            return sessionUser.getStaffInfo() != null && sessionUser.getStaffInfo().getJobId() != null 
                ? sessionUser.getStaffInfo().getJobId().toString() : "";
        } else if ("currentStaffId".equals(variable)) {
            return sessionUser.getStaffInfo() != null && sessionUser.getStaffInfo().getId() != null 
                ? sessionUser.getStaffInfo().getId().toString() : "";
        } else if ("currentRoleId".equals(variable)) {
            Integer[] roleIds = sessionUser.getRoleIds();
            if (roleIds != null && roleIds.length > 0) {
                Arrays.sort(roleIds);
                return Arrays.stream(roleIds)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            }
            return "";
        } else if ("currentDeptId".equals(variable)) {
            return sessionUser.getDeptId() != null ? sessionUser.getDeptId()+"" : "";
        } else if ("currentCompanyId".equals(variable)) {
            return sessionUser.getStaffInfo() != null && sessionUser.getStaffInfo().getCompanyId() != null 
                ? sessionUser.getStaffInfo().getCompanyId().toString() : "";
        } else if ("currentDeptAndSub".equals(variable)) {
            return buildDeptAndSubCondition(sessionUser.getDeptId()+"");
        } else if ("currentCompanyAndSub".equals(variable)) {
            String companyId = sessionUser.getStaffInfo() != null && sessionUser.getStaffInfo().getCompanyId() != null 
                ? sessionUser.getStaffInfo().getCompanyId()+"" : null;
            return buildCompanyAndSubCondition(companyId);
        }
        
        return "";
    }
    
    /**
     * 构建部门及子部门条件
     *
     * @param deptId 部门ID
     * @return 条件
     */
    private String buildDeptAndSubCondition(String deptId) {
        if (StringUtils.isEmpty(deptId)) {
            return "";
        }
        
        // 获取子部门列表
        List<String> subDeptIds = getSubDeptIds(deptId);
        
        if (subDeptIds.isEmpty()) {
            return deptId;
        }
        
        subDeptIds.add(deptId);
        return String.join(",", subDeptIds);
    }
    
    /**
     * 构建公司及子公司条件
     *
     * @param companyId 公司ID
     * @return 条件
     */
    private String buildCompanyAndSubCondition(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            return "";
        }
        
        // 获取子公司列表
        List<String> subCompanyIds = getSubCompanyIds(companyId);
        
        if (subCompanyIds.isEmpty()) {
            return companyId;
        }
        
        subCompanyIds.add(companyId);
        return String.join(",", subCompanyIds);
    }
    
    /**
     * 获取子部门ID列表
     *
     * @param deptId 部门ID
     * @return 子部门ID列表
     */
    private List<String> getSubDeptIds(String deptId) {
        BaseDao baseDao1 = DaoHelper.getBaseDao();
        try {
            List<JSONObject> jsonObjects = baseDao1.selectJsonList("SELECT id FROM sys_dept WHERE parent_id = ? AND del_flag = 0", deptId);
            if (jsonObjects == null || jsonObjects.isEmpty()) {
                return Collections.emptyList();
            }
            return jsonObjects.stream()
                .map(jsonObject -> jsonObject.get("id").toString())
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取子部门ID列表失败: {}", deptId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取子公司ID列表
     *
     * @param companyId 公司ID
     * @return 子公司ID列表
     */
    private List<String> getSubCompanyIds(String companyId) {
        try {
            BaseDao baseDao1 = DaoHelper.getBaseDao();
            List<JSONObject> jsonObjects = baseDao1.selectJsonList("SELECT id FROM sys_company WHERE parent_id = ? AND del_flag = 0", companyId);
           if(jsonObjects == null || jsonObjects.isEmpty()){
               return Collections.emptyList();
           }
            return jsonObjects.stream()
                .map(jsonObject -> jsonObject.get("id").toString())
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取子公司ID列表失败: {}", companyId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 构建SQL条件
     *
     * @param field 字段名
     * @param operator 操作符
     * @param value 值
     * @return SQL条件
     */
    private String buildSqlCondition(String field, String operator, String value) {
        if (StringUtils.isEmpty(field) || StringUtils.isEmpty(operator)) {
            return null;
        }
        
        switch (operator.toLowerCase()) {
            case "eq":
                return field + " = '" + value + "'";
            case "neq":
                return field + " != '" + value + "'";
            case "gt":
                return field + " > '" + value + "'";
            case "gte":
                return field + " >= '" + value + "'";
            case "lt":
                return field + " < '" + value + "'";
            case "lte":
                return field + " <= '" + value + "'";
            case "like":
                return field + " LIKE '%" + value + "%'";
            case "notlike":
                return field + " NOT LIKE '%" + value + "%'";
            case "in":
                return field + " IN (" + value + ")";
            case "notin":
                return field + " NOT IN (" + value + ")";
            case "isnull":
                return field + " IS NULL";
            case "isnotnull":
                return field + " IS NOT NULL";
            default:
                log.warn("不支持的操作符: {}", operator);
                return null;
        }
    }
    

    
    /**
     * 翻译数据权限规则变量
     * 兼容老版本的translateDpRuleVariables方法
     * 
     * @return 变量映射表
     */
    public static Map<String, String> translateDpRuleVariables() {
        SessionUser currentSessionUser = TenantContextHolder.getCurrentSessionUser();
        if (currentSessionUser == null) {
            return new HashMap<>();
        }
        
        Map<String, String> variables = new HashMap<>();
        
        // 当前用户ID
        if (currentSessionUser.getId() != null) {
            variables.put("{currentUserId}", currentSessionUser.getId().toString());
        }
        
        // 当前部门ID
        if (currentSessionUser.getDeptId() != null) {
            variables.put("{currentDeptId}", currentSessionUser.getDeptId()+"");
        }
        
        // 当前角色ID列表
        Integer[] roleIds = currentSessionUser.getRoleIds();
        if (roleIds != null && roleIds.length > 0) {
            Arrays.sort(roleIds);
            String roleIdStr = Arrays.stream(roleIds)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
            variables.put("{currentRoleId}", roleIdStr);
        }
        
        // 员工相关信息
        if (currentSessionUser.getStaffInfo() != null) {
            // 当前员工ID
            if (currentSessionUser.getStaffInfo().getId() != null) {
                variables.put("{currentStaffId}", currentSessionUser.getStaffInfo().getId().toString());
            }
            
            // 当前公司ID
            if (currentSessionUser.getStaffInfo().getCompanyId() != null) {
                variables.put("{currentCompanyId}", currentSessionUser.getStaffInfo().getCompanyId().toString());
            }
            
            // 当前职位ID
            if (currentSessionUser.getStaffInfo().getJobId() != null) {
                variables.put("{currentJobId}", currentSessionUser.getStaffInfo().getJobId().toString());
            }
        }
        
        return variables;
    }
    
    /**
     * 规则变量类
     */
    public static class RuleVariable {
        private final String name;
        private final String description;
        private final String method;
        
        public RuleVariable(String name, String description) {
            this(name, description, null);
        }
        
        public RuleVariable(String name, String description, String method) {
            this.name = name;
            this.description = description;
            this.method = method;
        }
        
        public String getName() {
            return name;
        }
        
        /**
         * 兼容老版本的getVariable方法
         * @return 变量名称
         */
        public String getVariable() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getMethod() {
            return method;
        }
    }
}