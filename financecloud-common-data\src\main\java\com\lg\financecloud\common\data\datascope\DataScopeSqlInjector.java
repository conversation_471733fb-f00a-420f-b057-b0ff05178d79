package com.lg.financecloud.common.data.datascope;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.AlwaysUpdateSomeColumnById;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;

import java.util.List;

/**
 * 支持自定义数据权限方法注入
 *
 * <AUTHOR>
 * @date 2020-06-17
 */
public class DataScopeSqlInjector extends DefaultSqlInjector {

	@Override
	public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
		List<AbstractMethod> methodList = super.getMethodList(mapperClass,tableInfo);
//		methodList.add(new SelectListByScope());
//		methodList.add(new SelectPageByScope());
//		methodList.add(new SelectCountByScope());
		methodList.add(new InsertBatchSomeColumn());
		methodList.add(new InsertBatchFast());
		methodList.add(new DeletePhysical());
		methodList.add(new DeleteByIdPhysical());
		return methodList;
	}


}
