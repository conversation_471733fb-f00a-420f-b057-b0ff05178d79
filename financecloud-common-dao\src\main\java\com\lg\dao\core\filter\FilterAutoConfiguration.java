package com.lg.dao.core.filter;

import com.lg.dao.config.properties.FilterProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 过滤器自动配置类
 * 用于注册数据权限拦截器和其他相关组件
 * 通过 light.orm.filter.enable 配置开关控制，默认关闭
 *
 * <AUTHOR>
 */
@Configuration
@EnableAspectJAutoProxy
@EnableConfigurationProperties(FilterProperties.class)
@ConditionalOnProperty(prefix = "light.orm.filter", name = "enable", havingValue = "true", matchIfMissing = false)
public class FilterAutoConfiguration {

    /**
     * 注册默认SQL过滤器
     */
    @Bean
    @ConditionalOnMissingBean(SqlFilter.class)
    public SqlFilter sqlFilter(DataPermissionService dataPermissionService) {
        return new DefaultSqlFilter(dataPermissionService);
    }

    /**
     * 注册JSON过滤器解析器
     */
    @Bean
    @ConditionalOnMissingBean(JsonFilterParser.class)
    public JsonFilterParser jsonFilterParser() {
        return new JsonFilterParser();
    }

    /**
     * 注册元数据服务
     */
    @Bean
    @ConditionalOnMissingBean(MetadataService.class)
    public MetadataService metadataService() {
        return new MetadataService();
    }

    /**
     * 注册数据权限服务
     */
    @Bean
    @ConditionalOnMissingBean(DataPermissionService.class)
    public DataPermissionService dataPermissionService() {
        return new DataPermissionService();
    }

    /**
     * 注册数据权限拦截器
     */
    @Bean
    @ConditionalOnMissingBean(DataPermissionInterceptor.class)
    @ConditionalOnProperty(prefix = "light.orm.filter", name = "enableDataPermission", havingValue = "true", matchIfMissing = true)
    public DataPermissionInterceptor dataPermissionInterceptor() {
        return new DataPermissionInterceptor();
    }
    
    /**
     * 注册数据过滤切面
     */
    @Bean
    @ConditionalOnMissingBean(DataFilterAspect.class)
    @ConditionalOnProperty(prefix = "light.orm.filter", name = "enableDataFilter", havingValue = "true", matchIfMissing = true)
    public DataFilterAspect dataFilterAspect() {
        return new DataFilterAspect();
    }
}