# 数据过滤和权限控制系统

## 概述

数据过滤和权限控制系统是一个基于数据库表的配置系统，用于动态控制数据的查询条件和权限。系统支持通过JSON配置过滤条件和数据权限规则，无需修改代码即可实现数据的精细化控制。

## 核心组件

1. **TableMetadata**：表元数据，存储表和字段的元数据信息
2. **DataPermissionRule**：数据权限规则，存储角色的数据权限规则
3. **MetadataService**：元数据服务，管理表元数据
4. **DataPermissionService**：数据权限服务，管理数据权限规则
5. **SqlFilter**：SQL过滤器，应用过滤条件和数据权限规则
6. **JsonFilterParser**：JSON过滤器解析器，解析JSON格式的过滤条件
7. **DataPermissionInterceptor**：数据权限拦截器，自动拦截SQL查询，应用数据权限规则
8. **DataFilterAspect**：数据过滤切面，处理方法上的注解

## 特点

1. **基于数据库表的配置**：所有配置存储在数据库表中，无需修改代码
2. **动态过滤条件**：支持多种过滤条件，如等于、大于、小于、包含等
3. **数据权限控制**：根据用户角色和配置的权限规则，控制用户可访问的数据范围
4. **自动拦截SQL查询**：通过拦截器自动应用数据权限规则，无需手动调用
5. **与自研DAO框架集成**：通过连接器链机制与自研DAO框架集成
6. **支持SoulTable格式**：兼容前端SoulTable组件的筛选条件格式
7. **注解支持**：通过注解配置数据过滤和权限控制
8. **元数据缓存**：缓存表元数据，提高性能
9. **内置变量支持**：支持多种内置变量，方便配置数据权限规则

## 数据库表结构

### 表元数据表（sys_table_metadata）

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | bigint | 主键 |
| table_code | varchar(100) | 表编码 |
| name | varchar(200) | 表名称 |
| sort_info | text | 排序信息（JSON格式） |
| field_info | text | 字段信息（JSON格式） |
| resizable_info | text | 列宽调整信息（JSON格式） |
| visible_info | text | 列隐藏显示信息（JSON格式） |
| module_name | varchar(100) | 模块名称 |
| tenant_id | varchar(32) | 租户ID |
| create_by | varchar(32) | 创建人ID |
| create_time | datetime | 创建时间 |
| update_by | varchar(32) | 修改人ID |
| update_time | datetime | 修改时间 |
| del_flag | tinyint(1) | 是否已删除 |
| delete_by | varchar(32) | 删除人ID |
| delete_time | datetime | 删除时间 |
| version | int | 数据版本号 |

### 数据权限规则表（sys_dp_rule）

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | bigint | 主键 |
| rule_name | varchar(100) | 规则名称 |
| table_code | varchar(100) | 表编码 |
| role_id | varchar(32) | 角色ID |
| condition_json | text | 条件JSON |
| create_by | varchar(32) | 创建人 |
| create_time | datetime | 创建时间 |
| update_by | varchar(32) | 更新人 |
| update_time | datetime | 更新时间 |
| del_flag | tinyint(1) | 是否删除 |
| delete_by | varchar(32) | 删除人 |
| delete_time | datetime | 删除时间 |
| version | int | 版本号 |

## 使用方式

### 1. 配置表元数据

```json
{
  "tableCode": "sys_user",
  "tableName": "用户表",
  "fields": [
    {
      "fieldName": "id",
      "fieldCode": "id",
      "fieldType": "string",
      "isKey": true
    },
    {
      "fieldName": "用户名",
      "fieldCode": "username",
      "fieldType": "string",
      "searchable": true
    },
    {
      "fieldName": "部门ID",
      "fieldCode": "dept_id",
      "fieldType": "string",
      "searchable": true
    }
  ]
}
```

### 2. 配置数据权限规则

```json
{
  "ruleName": "部门数据权限",
  "tableCode": "sys_user",
  "roleId": "ROLE_DEPT_MANAGER",
  "condition": {
    "field": "dept_id",
    "operator": "eq",
    "value": "{currentDeptId}"
  }
}
```

### 3. 内置变量

系统支持多种内置变量，可以在数据权限规则的条件中使用：

| 变量名 | 说明 | 常量定义 |
| --- | --- | --- |
| {currentUserId} | 当前用户ID | DP_RULE_VAR_CURRENT_USER_ID |
| {currentJobId} | 当前职位ID | DP_RULE_VAR_CURRENT_JOB_ID |
| {currentStaffId} | 当前员工ID | DP_RULE_VAR_CURRENT_STAFF_ID |
| {currentRoleId} | 当前角色ID | DP_RULE_VAR_CURRENT_ROLE_ID |
| {currentDeptId} | 当前部门ID | DP_RULE_VAR_CURRENT_DEPT_ID |
| {currentCompanyId} | 当前公司ID | DP_RULE_VAR_CURRENT_COMPANY_ID |
| {currentDeptAndSub} | 本部门以及子部门ID列表 | DP_RULE_VAR_CURRENT_DEPT_AND_SUB |
| {currentCompanyAndSub} | 本公司以及子公司ID列表 | DP_RULE_VAR_CURRENT_COMPANY_AND_SUB |

#### 变量翻译方法

系统提供了静态方法来获取当前用户的变量映射：

```java
// 获取当前用户的所有变量映射
Map<String, String> variables = DataPermissionService.translateDpRuleVariables();

// 使用常量获取变量名
String userIdVar = DataPermissionService.DP_RULE_VAR_CURRENT_USER_ID.getVariable();
String userId = variables.get(userIdVar);
```

#### RuleVariable常量使用

```java
// 直接使用预定义的常量
RuleVariable userIdVar = DataPermissionService.DP_RULE_VAR_CURRENT_USER_ID;
System.out.println("变量名: " + userIdVar.getName());
System.out.println("描述: " + userIdVar.getDescription());
System.out.println("方法: " + userIdVar.getMethod());
```

例如，配置只能查看本部门数据的规则：

```json
{
  "ruleName": "本部门数据",
  "tableCode": "sys_user",
  "roleId": "ROLE_DEPT_USER",
  "condition": {
    "field": "dept_id",
    "operator": "eq",
    "value": "{currentDeptId}"
  }
}
```

例如，配置能查看本部门及子部门数据的规则：

```json
{
  "ruleName": "本部门及子部门数据",
  "tableCode": "sys_user",
  "roleId": "ROLE_DEPT_MANAGER",
  "condition": {
    "field": "dept_id",
    "operator": "in",
    "value": "{currentDeptAndSub}"
  }
}
```

### 4. 在代码中使用

#### 使用SQL过滤器

```java
@Autowired
private SqlFilter sqlFilter;

public List<User> getUserList(FilterConfig filterConfig) {
    String sql = "SELECT * FROM sys_user";
    
    // 应用过滤条件
    String filteredSql = sqlFilter.apply(sql, filterConfig);
    
    // 执行查询
    return jdbcTemplate.query(filteredSql, new BeanPropertyRowMapper<>(User.class));
}
```

#### 使用数据权限服务

```java
@Autowired
private DataPermissionService dataPermissionService;

public String getDataPermissionCondition(String tableCode, String userId, List<String> roleIds) {
    return dataPermissionService.getDataPermissionCondition(tableCode, userId, roleIds);
}
```

#### 使用JSON过滤器解析器

```java
@Autowired
private JsonFilterParser jsonFilterParser;

public FilterConfig parseFilterConfig(String jsonFilter) {
    return jsonFilterParser.parse(jsonFilter);
}
```

#### 使用注解配置数据过滤

```java
@DynamicDataFilter({
    @FilterField(key = "dept_id", value = "1001"),
    @FilterField(key = "status", value = "active")
})
public List<User> getUserList() {
    String sql = "SELECT * FROM sys_user";
    return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(User.class));
}
```

#### 使用注解排除数据权限

```java
@ExcludeDataFilter
public List<User> getAllUsers() {
    String sql = "SELECT * FROM sys_user";
    return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(User.class));
}
```

#### 在线程中设置筛选条件

```java
// 设置筛选条件
FilterConfig filterConfig = new FilterConfig();
// 配置筛选条件...
DataPermissionInterceptor.setFilterConfig(filterConfig);

try {
    // 执行查询
    List<User> users = userDao.getUserList();
    return users;
} finally {
    // 清除筛选条件
    DataPermissionInterceptor.clearFilterConfig();
}
```

#### 在线程中禁用数据权限

```java
// 设置禁用数据权限
DataPermissionInterceptor.setDataPermissionExclude();

try {
    // 执行查询
    List<User> users = userDao.getUserList();
    return users;
} finally {
    // 清除禁用数据权限
    DataPermissionInterceptor.clearDataPermissionExclude();
}
```

### 5. 支持SoulTable格式

系统支持前端SoulTable组件的筛选条件格式：

```json
{
  "filterSos": "[{\"id\":1,\"prefix\":\"and\",\"mode\":\"condition\",\"field\":\"username\",\"type\":\"like\",\"value\":\"admin\"}]",
  "field": "create_time",
  "order": "desc"
}
```

## 自动配置

在Spring Boot应用程序中，只需要引入依赖，系统会自动配置所有组件：

```xml
<dependency>
    <groupId>com.lg</groupId>
    <artifactId>financecloud-common-dao</artifactId>
    <version>${version}</version>
</dependency>
```

## 兼容性说明

### 老版本兼容性

新版本完全兼容老版本的数据权限框架，包括：

1. **RuleVariable常量**：保留了所有老版本的常量定义
   ```java
   // 老版本代码可以正常使用
   DataPermissionService.DP_RULE_VAR_CURRENT_USER_ID
   DataPermissionService.DP_RULE_VAR_CURRENT_DEPT_ID
   // 等等...
   ```

2. **变量翻译方法**：提供了与老版本完全一致的方法
   ```java
   // 老版本调用方式仍然有效
   Map<String, String> variables = DataPermissionService.translateDpRuleVariables();
   ```

3. **RuleVariable对象**：支持老版本的所有方法
   ```java
   RuleVariable var = DataPermissionService.DP_RULE_VAR_CURRENT_USER_ID;
   String name = var.getName();        // 新方法
   String variable = var.getVariable(); // 兼容老版本的方法
   String desc = var.getDescription();
   String method = var.getMethod();
   ```

### 数据库兼容性

系统支持老版本的数据库表结构，包括：

- `sys_dp_rule` 表的 `own_type`、`own_id`、`dp_rule`、`enabled_flag` 等字段
- 复杂的嵌套JSON权限规则格式
- `notContain` 等老版本操作符

## 注意事项

1. 数据权限规则中的变量（如`{currentDeptId}`）会在运行时替换为当前用户的实际值
2. 系统会自动拦截所有SQL查询，应用数据权限规则
3. 可以通过配置禁用特定表的数据权限控制
4. 元数据服务会缓存表元数据，提高性能
5. 支持多种方式配置筛选条件：注解、线程变量、请求参数
6. 内置变量支持从 TenantContextHolder.getCurrentSessionUser() 获取当前用户信息
7. 新版本完全向后兼容，老代码无需修改即可正常运行

## 示例

参考 `FilterExample` 类中的示例代码，了解如何使用数据过滤和权限控制系统。