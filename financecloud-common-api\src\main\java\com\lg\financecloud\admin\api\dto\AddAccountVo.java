package com.lg.financecloud.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class AddAccountVo implements Serializable {
    /**
     * 账套名称
     */
    @ApiModelProperty(value = "账套名称")
    private String accountName;
    /**
     * 账套Id
     */
    @ApiModelProperty(value = "账套Id")
    private Long accountId;
    /**
     * 报税地区
     */
    @ApiModelProperty(value = "报税地区")
    private String taxArea;
    /**
     * 报税地区名称
     */
    @ApiModelProperty(value = "报税地区名称")
    private String taxAreaName;
    /**
     * 会计制度ID
     */
    @ApiModelProperty(value = "会计制度ID")
    private String accountSystemCode;
    /**
     * 财务启用时间
     */
    @ApiModelProperty(value = "财务启用时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern = "yyyy-MM-dd")
    private String financeTimeStr;

    /**
     * 行业类型
     */
    @ApiModelProperty(value = "行业类型")
    private String industryType;
    /**
     * 行业类型Id
     */
    @ApiModelProperty(value = "行业类型ID")
    private String industryId;
    /**
     * 状态(1：启用 2：未启用)
     */
    @ApiModelProperty(value = "状态(1：启用 2：未启用)")
    private Boolean status;
    /**
     * 是否启用模板
     */
    @ApiModelProperty(value = "是否启用模板")
    private Boolean isModel;
    /**
     * 税务局
     */
    @ApiModelProperty(value = "税务局")
    private String taxBureauName;
    /**
     * 科目编码规则
     */
    @ApiModelProperty(value = "科目编码规则")
    private String subjectCodeRule;

    private String oldRule;
    /**
     * 凭证是否需要审核 默认0 不需要 1需要
     */
    @ApiModelProperty(value = "凭证是否需要审核 默认0 不需要 1需要")
    private String needReview;
    /**
     * 凭证审核是否可以自己审核 默认0 可以 1不可以
     */
    @ApiModelProperty(value = "凭证审核是否可以自己审核 默认0 可以 1不可以")
    private String selfReview;
    /**
     * 0无外币,1有外币
     */
    @ApiModelProperty(value = "0无外币,1有外币")
    private String type;
    /**
     * 账簿余额与科目方向相同 默认0否 1是
     */
    @ApiModelProperty(value = "账簿余额与科目方向相同 默认0否 1是")
    private String subjectDirectSameFlag;
    /**
     * 是否需要过账 默认0 不需要 1需要
     */
    @ApiModelProperty(value = "是否需要过账 默认0 不需要 1需要")
    private String needPostingAccount;

    /**
     * 是否需要核准 默认0 不需要 1需要
     */
    @ApiModelProperty(value = "是否需要核准 默认0 不需要 1需要")
    private String needApproval;
    /**
     * 纳税人
     */
    private String taxpayer;
    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationCode;

    private String taxBureauId;
    /**
     * 成本核算方式 ：0按比例 1按明细（按比例将不开启存货相关，按明细核算将开启存货相关）
     * */
    private String accountMethod;

    private String taxpayerType;

    private Integer tenantId;
    private Long userId;

    private boolean isErp;
    /**
     * erp租户id
     */
    private Integer erpTenantId;
}
