/**
 * Created for toolbar button configuration
 */
import Property from './Property.js';

export default class ToolbarButtonsProperty extends Property {
    constructor() {
        super();
        this.init();
    }
    
    init() {
        // 添加标题
        this.col.append($("<h4 style='margin-top: 20px;'>工具栏按钮显示设置</h4>"));
        
        // 添加工具栏显示开关
        this.buildToolbarSwitch();
        
        // 添加按钮配置组
        this.buildButtonGroups();
    }
    
    buildToolbarSwitch() {
        const showToolbarGroup = $("<div class='form-group'>");
        showToolbarGroup.append($("<label>显示工具栏</label>"));
        
        const showToolbarCheckbox = $("<input type='checkbox' class='form-control-checkbox'>");
        const checkboxWrapper = $("<div class='checkbox'><label></label></div>");
        checkboxWrapper.find("label").append(showToolbarCheckbox);
        showToolbarGroup.append(checkboxWrapper);
        
        this.showToolbarCheckbox = showToolbarCheckbox;
        
        showToolbarCheckbox.change(() => {
            const checked = showToolbarCheckbox.prop('checked');
            window.formBuilder.reportDef.tools = window.formBuilder.reportDef.tools || {};
            window.formBuilder.reportDef.tools.show = checked;
            this.toggleButtonSettings(checked);
        });
        
        this.col.append(showToolbarGroup);
    }
    
    buildButtonGroups() {
        this.buttonSettingsContainer = $("<div id='toolbar-button-settings'></div>");
        this.col.append(this.buttonSettingsContainer);
        
        // 添加按钮配置
        this.buildButtonCheckbox("print", "打印按钮");
        this.buildButtonCheckbox("pdfPrint", "PDF在线打印按钮");
        this.buildButtonCheckbox("pdfPreviewPrint", "PDF预览打印按钮");
        this.buildButtonCheckbox("pdf", "导出PDF按钮");
        this.buildButtonCheckbox("word", "导出WORD按钮");
        this.buildButtonCheckbox("excel", "导出EXCEL按钮");
        this.buildButtonCheckbox("pagingExcel", "分页导出EXCEL按钮");
        this.buildButtonCheckbox("sheetPagingExcel", "分页分Sheet导出EXCEL按钮");
        this.buildButtonCheckbox("paging", "预览下拉菜单");
        
        // 添加预览菜单下的子按钮配置
        this.buildSubButtonsGroup();
    }
    
    buildSubButtonsGroup() {
        const subButtonGroup = $("<div class='form-group sub-buttons-group' style='margin-left: 20px; display: none;'>");
        this.buttonSettingsContainer.append(subButtonGroup);
        
        const subTitle = $("<label>预览菜单中的按钮：</label>");
        subButtonGroup.append(subTitle);
        
        // 普通预览按钮
        const normalPreviewWrapper = $("<div class='checkbox'><label></label></div>");
        const normalPreviewCheckbox = $("<input type='checkbox' class='form-control-checkbox'>");
        normalPreviewWrapper.find("label").append(normalPreviewCheckbox).append("预览按钮");
        subButtonGroup.append(normalPreviewWrapper);
        this.normalPreviewCheckbox = normalPreviewCheckbox;
        
        // 分页预览按钮
        const pagePreviewWrapper = $("<div class='checkbox'><label></label></div>");
        const pagePreviewCheckbox = $("<input type='checkbox' class='form-control-checkbox'>");
        pagePreviewWrapper.find("label").append(pagePreviewCheckbox).append("分页预览按钮");
        subButtonGroup.append(pagePreviewWrapper);
        this.pagePreviewCheckbox = pagePreviewCheckbox;
        
        // 事件绑定
        normalPreviewCheckbox.change(() => {
            window.formBuilder.reportDef.tools = window.formBuilder.reportDef.tools || {};
            window.formBuilder.reportDef.tools.normalPreview = normalPreviewCheckbox.prop('checked');
        });
        
        pagePreviewCheckbox.change(() => {
            window.formBuilder.reportDef.tools = window.formBuilder.reportDef.tools || {};
            window.formBuilder.reportDef.tools.pagePreview = pagePreviewCheckbox.prop('checked');
        });
        
        // 预览下拉菜单按钮的事件处理
        this.pagingCheckbox.change(() => {
            const checked = this.pagingCheckbox.prop('checked');
            if (checked) {
                subButtonGroup.show();
            } else {
                subButtonGroup.hide();
            }
        });
    }
    
    buildButtonCheckbox(name, label) {
        const buttonGroup = $("<div class='form-group'>");
        this.buttonSettingsContainer.append(buttonGroup);
        
        const checkboxWrapper = $("<div class='checkbox'><label></label></div>");
        const checkbox = $("<input type='checkbox' class='form-control-checkbox'>");
        checkboxWrapper.find("label").append(checkbox).append(label);
        buttonGroup.append(checkboxWrapper);
        
        this[name + 'Checkbox'] = checkbox;
        
        if (name === 'paging') {
            this.pagingCheckbox = checkbox;
        }
        
        checkbox.change(() => {
            window.formBuilder.reportDef.tools = window.formBuilder.reportDef.tools || {};
            window.formBuilder.reportDef.tools[name] = checkbox.prop('checked');
        });
    }
    
    toggleButtonSettings(show) {
        if (show) {
            this.buttonSettingsContainer.show();
        } else {
            this.buttonSettingsContainer.hide();
        }
    }
    
    refreshValue() {
        const tools = window.formBuilder.reportDef.tools || {};
        
        // 设置工具栏显示状态
        this.showToolbarCheckbox.prop('checked', tools.show !== false);
        this.toggleButtonSettings(tools.show !== false);
        
        // 设置各按钮状态
        this.printCheckbox.prop('checked', tools.print !== false);
        this.pdfPrintCheckbox.prop('checked', tools.pdfPrint !== false);
        this.pdfPreviewPrintCheckbox.prop('checked', tools.pdfPreviewPrint !== false);
        this.pdfCheckbox.prop('checked', tools.pdf !== false);
        this.wordCheckbox.prop('checked', tools.word !== false);
        this.excelCheckbox.prop('checked', tools.excel !== false);
        this.pagingExcelCheckbox.prop('checked', tools.pagingExcel !== false);
        this.sheetPagingExcelCheckbox.prop('checked', tools.sheetPagingExcel !== false);
        this.pagingCheckbox.prop('checked', tools.paging !== false);
        
        // 设置预览菜单下的子按钮状态
        this.normalPreviewCheckbox.prop('checked', tools.normalPreview !== false);
        this.pagePreviewCheckbox.prop('checked', tools.pagePreview !== false);
        
        // 控制子按钮组显示
        if (tools.paging !== false) {
            $('.sub-buttons-group').show();
        } else {
            $('.sub-buttons-group').hide();
        }
    }
} 