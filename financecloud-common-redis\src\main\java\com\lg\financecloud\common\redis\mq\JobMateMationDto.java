package com.lg.financecloud.common.redis.mq;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Id;


@TableName("job_mate_mation")
@Data
public class JobMateMationDto    {

    public static final String JOB_TYPE_BACNEND_JOB="1";
    public static final String JOB_TYPE_FRONTEND_JOB="2";
    @Id
    private String jobId;
    private String title;
    private String jobType; // 建议改为 JobType 枚举类型
    private String bigType;
    private String type;
    private String requestBody;
    private String responseBody;
    private String status;
    private String parentId;
    private String createId;
    private String createIp;
    private String createTime; // 建议改为 Long 或 LocalDateTime
    private String complateTime; // 修正拼写 + 建议改为 Long 或 LocalDateTime
    private Integer tenantId;
    private Integer retrys;

    private String bizKey;



}
