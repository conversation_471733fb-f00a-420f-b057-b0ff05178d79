package com.lg.dao.core.validation;

import com.lg.dao.core.EntityInfo;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 实体验证结果
 */
@Data
@Builder
public class ValidationResult {
    /**
     * 实体信息
     */
    private EntityInfo entityInfo;
    
    /**
     * 数据库表信息
     */
    private EntitySchemaValidator.DatabaseTableInfo databaseTableInfo;
    
    /**
     * 发现的差异列表
     */
    @Builder.Default
    private List<SchemaDifference> differences = new ArrayList<>();
    
    /**
     * 修复差异的SQL脚本
     */
    @Builder.Default
    private List<String> sqlScripts = new ArrayList<>();
    
    /**
     * 验证过程中的错误信息
     */
    private String error;
    
    /**
     * 检查是否存在差异
     */
    public boolean hasDifferences() {
        return differences != null && !differences.isEmpty();
    }
    
    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return error != null && !error.isEmpty();
    }
    
    /**
     * 检查表是否存在于数据库中
     */
    public boolean tableExists() {
        return databaseTableInfo != null;
    }
} 