package com.lg.dao.core;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lg.dao.core.func.LFunction;
import com.lg.dao.core.query.LambdaQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.beans.PropertyDescriptor;
import java.lang.annotation.Annotation;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 实体信息类
 */
@Data
@Slf4j
public class EntityInfo {
    private String tableName;
    private List<FieldInfo> fields = new ArrayList<>();
    private List<FieldInfo> idFields = new ArrayList<>();
    private FieldInfo idField;
    private List<IndexInfo> indexes = new ArrayList<>();

    // 预构建的映射表 - 性能优化核心（基于FieldInfo）
    private Map<String, FieldInfo> columnToFieldMap = new ConcurrentHashMap<>();
    private Map<String, FieldInfo> propertyToFieldMap = new ConcurrentHashMap<>();

    // 数据库方言接口
    public interface DbDialect {
        String mapJavaTypeToDbType(Class<?> javaType);
        
        /**
         * 生成索引名称
         * @param tableName 表名
         * @param columnNames 列名数组
         * @param unique 是否唯一索引
         * @return 索引名称
         */
        default String getIndexName(String tableName, String[] columnNames, boolean unique) {
            String prefix = unique ? "uk" : "idx";
            String columnsStr = String.join("_", columnNames);
            return prefix + "_" + tableName + "_" + columnsStr;
        }
    }

    // 默认MySQL方言实现
    private static final DbDialect MYSQL_DIALECT = new DbDialect() {
        @Override
        public String mapJavaTypeToDbType(Class<?> javaType) {
            if (javaType == String.class) {
                return "VARCHAR(255)";
            } else if (javaType == Integer.class || javaType == int.class) {
                return "INT";
            } else if (javaType == Long.class || javaType == long.class) {
                return "BIGINT";
            } else if (javaType == Boolean.class || javaType == boolean.class) {
                return "TINYINT(1)";
            } else if (javaType == java.util.Date.class || javaType == java.sql.Timestamp.class) {
                return "DATETIME";
            } else if (javaType.isEnum()) {
                return "VARCHAR(50)";
            } else if (javaType == Double.class || javaType == double.class || 
                       javaType == Float.class || javaType == float.class) {
                return "DECIMAL(10,2)";
            }
            return "TEXT";
        }
        
        @Override
        public String getIndexName(String tableName, String[] columnNames, boolean unique) {
            String prefix = unique ? "uk" : "idx";
            String columnsStr = String.join("_", columnNames);
            return prefix + "_" + tableName + "_" + columnsStr;
        }
    };

    // 当前使用的方言(默认为MySQL)
    private static DbDialect currentDialect = MYSQL_DIALECT;

    // 设置方言的方法(预留扩展点)
    public static void setDbDialect(DbDialect dialect) {
        currentDialect = dialect;
    }

    // 修改后的类型映射方法
    public static String mapJavaTypeToDbType(Class<?> javaType) {
        return currentDialect.mapJavaTypeToDbType(javaType);
    }

    /**
     * 根据Java字段名获取数据库列名（优化版本）
     * @param fieldName Java字段名
     * @return 对应的数据库列名，未找到返回null
     */
    public String getColumnName(String fieldName) {
        FieldInfo fieldInfo = propertyToFieldMap.get(fieldName);
        return fieldInfo != null ? fieldInfo.getColumn() : null;
    }

    /**
     * 根据数据库列名获取Java字段名（优化版本）
     * @param columnName 数据库列名
     * @return 对应的Java字段名，未找到返回null
     */
    public String getFieldName(String columnName) {
        FieldInfo fieldInfo = findFieldInfoByColumn(columnName);
        return fieldInfo != null ? fieldInfo.getPropertyName() : null;
    }

    /**
     * 高效的列名到字段信息映射查找 - EntityRowMapper性能优化核心方法
     * 支持多种列名格式的智能匹配，特别优化SQL别名处理
     * @param columnName 数据库列名或SQL别名
     * @return 字段信息，未找到返回null
     */
    public FieldInfo findFieldInfoByColumn(String columnName) {
        if (columnName == null) {
            return null;
        }

        // 1. 直接查找（最快路径）- 支持原始列名
        FieldInfo fieldInfo = columnToFieldMap.get(columnName.toLowerCase());
        if (fieldInfo != null) {
            return fieldInfo;
        }

        // 2. 尝试原始大小写
        fieldInfo = columnToFieldMap.get(columnName);
        if (fieldInfo != null) {
            return fieldInfo;
        }

        // 3. SQL别名处理：直接按属性名查找（核心优化）
        // 当SQL使用别名时，如 SELECT tenant_id AS tenantId，JdbcUtils.lookupColumnName返回tenantId
        // 这里直接按属性名查找，解决别名映射问题
        fieldInfo = propertyToFieldMap.get(columnName);
        if (fieldInfo != null) {
            return fieldInfo;
        }

        // 4. 尝试驼峰转换后查找（兼容下划线命名）
        String camelCase = toCamelCase(columnName.toLowerCase());
        fieldInfo = propertyToFieldMap.get(camelCase);
        if (fieldInfo != null) {
            return fieldInfo;
        }

        // 5. 反向查找：如果传入的是驼峰格式，尝试转换为下划线格式查找
        if (!columnName.contains("_") && !columnName.equals(columnName.toLowerCase())) {
            String underscoreFormat = camelToUnderline(columnName);
            fieldInfo = columnToFieldMap.get(underscoreFormat.toLowerCase());
            if (fieldInfo != null) {
                return fieldInfo;
            }
        }

        // 6. 去前缀匹配（如 user_id -> id）
//        if (columnName.contains("_")) {
//            String[] parts = columnName.split("_", 2);
//            if (parts.length > 1) {
//                String unprefixed = parts[1];
//                // 尝试去前缀后的直接匹配
//                fieldInfo = columnToFieldMap.get(unprefixed.toLowerCase());
//                if (fieldInfo != null) {
//                    return fieldInfo;
//                }
//                // 尝试去前缀后的驼峰匹配
//                String unprefixedCamel = toCamelCase(unprefixed.toLowerCase());
//                fieldInfo = propertyToFieldMap.get(unprefixedCamel);
//                if (fieldInfo != null) {
//                    return fieldInfo;
//                }
//            }
//        }

        return null;
    }

    /**
     * 获取字段信息（通过属性名）
     * @param propertyName 属性名
     * @return 字段信息
     */
    public FieldInfo getFieldInfo(String propertyName) {
        return propertyToFieldMap.get(propertyName);
    }

    /**
     * 字段信息 - 增强版本，包含性能优化功能
     */
    @Data
    public static class FieldInfo {
        private Field field;
        private String column;
        private boolean id;
        private IdType idType;
        private boolean insertable = true;
        private boolean updatable = true;
        private EnumType enumType;

        // 新增数据库类型字段
        private String dbType;

        // 性能优化字段 - 缓存PropertyDescriptor和WriteMethod避免重复反射调用
        private PropertyDescriptor propertyDescriptor;
        private boolean propertyDescriptorInitialized = false;
        private Method writeMethod;
        private boolean writeMethodInitialized = false;

        public FieldInfo(Field field) {
            this.field = field;
            this.field.setAccessible(true);
            
            // 修改初始化逻辑，使用新的映射方法
            this.dbType = mapJavaTypeToDbType(field.getType());
            
            processJpaAnnotations(field);
            processMybatisPlusAnnotations(field);

            // 设置默认列名：
            // - 如果column为null或空字符串，则设置默认列名
            // - 如果column为特殊标记"##EXCLUDED##"，则设置为null表示排除
            if ("##EXCLUDED##".equals(this.column)) {
                this.column = null; // 排除字段
            } else if (!StringUtils.hasText(this.column)) {
                this.column = camelToUnderline(field.getName()); // 设置默认列名
            }
        }

        private void processJpaAnnotations(Field field) {
            if (field.isAnnotationPresent(Transient.class)) {
                this.column = "##EXCLUDED##"; // 使用特殊标记表示排除字段
                return;
            }

            if (field.isAnnotationPresent(Id.class)) {
                this.id = true;
            }

            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                if (StringUtils.hasText(column.name())) {
                    this.column = column.name();
                }
                this.insertable = column.insertable();
                this.updatable = column.updatable();
            }

            Enumerated enumerated = field.getAnnotation(Enumerated.class);
            if (enumerated != null) {
                this.enumType = enumerated.value();
            }
        }

        private void processMybatisPlusAnnotations(Field field) {
            TableId tableId = field.getAnnotation(TableId.class);
            if (tableId != null) {
                this.id = true;
                if (StringUtils.hasText(tableId.value())) {
                    this.column = tableId.value();
                }
                this.idType = tableId.type();
            }

            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField != null) {
                if (StringUtils.hasText(tableField.value())) {
                    this.column = tableField.value();
                }
                if (!tableField.exist()) {
                    this.column = "##EXCLUDED##"; // 使用特殊标记表示排除字段
                }
                this.insertable = !tableField.insertStrategy().equals(com.baomidou.mybatisplus.annotation.FieldStrategy.NEVER);
                this.updatable = !tableField.updateStrategy().equals(com.baomidou.mybatisplus.annotation.FieldStrategy.NEVER);
            }
        }

        // 新增获取数据库类型的方法
        public String getDbType() {
            return dbType;
        }

        /**
         * 获取PropertyDescriptor（延迟初始化，缓存结果）
         */
        public PropertyDescriptor getPropertyDescriptor() {
            if (!propertyDescriptorInitialized) {
                synchronized (this) {
                    if (!propertyDescriptorInitialized) {
                        try {
                            this.propertyDescriptor = new PropertyDescriptor(field.getName(), field.getDeclaringClass());
                        } catch (Exception e) {
                            log.debug("Failed to create PropertyDescriptor for {}.{}: {}",
                                field.getDeclaringClass().getSimpleName(), field.getName(), e.getMessage());
                            this.propertyDescriptor = null;
                        }
                        this.propertyDescriptorInitialized = true;
                    }
                }
            }
            return propertyDescriptor;
        }

        /**
         * 高效的属性值设置方法 - 性能优化核心
         * 优先使用PropertyDescriptor的setter方法，回退到直接字段设置
         */
        public void setPropertyValue(Object entity, Object value) throws Exception {
            Method setter = getWriteMethod();
            if (setter != null) {
                // 使用缓存的setter方法（性能最优）
                setter.invoke(entity, value);
            } else {
                // 回退到直接字段设置
                field.setAccessible(true);
                field.set(entity, value);
            }
        }

        /**
         * 获取缓存的WriteMethod
         */
        private Method getWriteMethod() {
            if (!writeMethodInitialized) {
                PropertyDescriptor pd = getPropertyDescriptor();
                if (pd != null) {
                    writeMethod = pd.getWriteMethod();
                }
                writeMethodInitialized = true;
            }
            return writeMethod;
        }

        /**
         * 高效的属性值获取方法
         */
        public Object getPropertyValue(Object entity) throws Exception {
            PropertyDescriptor pd = getPropertyDescriptor();
            if (pd != null && pd.getReadMethod() != null) {
                return pd.getReadMethod().invoke(entity);
            } else {
                field.setAccessible(true);
                return field.get(entity);
            }
        }

        /**
         * 获取属性类型（直接从Field获取，避免额外反射）
         */
        public Class<?> getPropertyType() {
            return field.getType();
        }

        /**
         * 获取属性名（直接从Field获取）
         */
        public String getPropertyName() {
            return field.getName();
        }
    }

    /**
     * 创建实体信息
     */
    public static EntityInfo of(Class<?> entityClass) {
        EntityInfo entityInfo = new EntityInfo();

        TableName mpTable = entityClass.getAnnotation(TableName.class);
        if (mpTable != null) {
            entityInfo.setTableName(mpTable.value());
        } else {
            Table jpaTable = entityClass.getAnnotation(Table.class);
            if (jpaTable != null) {
                entityInfo.setTableName(jpaTable.name());
            } else {
                entityInfo.setTableName(camelToUnderline(entityClass.getSimpleName()));
            }
        }
        
        // 解析类级别的索引注解
        parseClassLevelIndexes(entityClass, entityInfo);

        for (Field field : entityClass.getDeclaredFields()) {
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                    java.lang.reflect.Modifier.isTransient(field.getModifiers())) {
                continue;
            }

            if (field.isAnnotationPresent(Transient.class)) {
                continue;
            }

            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField != null && !tableField.exist()) {
                continue;
            }

            FieldInfo fieldInfo = new FieldInfo(field);
            if (fieldInfo.getColumn() != null) {
                entityInfo.fields.add(fieldInfo);
                if (fieldInfo.isId()) {
                    entityInfo.idFields.add(fieldInfo);
                }
            }
        }

        if (entityInfo.fields.isEmpty()) {
            throw new IllegalStateException("No mapped fields found in entity: " + entityClass.getName());
        }

        if (entityInfo.idFields.size() == 1) {
            entityInfo.idField = entityInfo.idFields.get(0);
        } else if (entityInfo.idFields.isEmpty()) {
            // 对于普通VO对象，允许没有@Id字段
            // 这样可以支持查询结果映射到普通POJO类
            log.debug("No @Id field found in entity: {} (this is normal for VO classes)", entityClass.getName());
        }

        // 构建预映射表 - 性能优化关键步骤
        entityInfo.buildMappingTables(entityClass);

        return entityInfo;
    }

    /**
     * 构建预映射表 - 性能优化核心方法
     * 为每个实体类预先构建列名->字段信息的映射表，避免运行时重复查找
     * 特别优化SQL别名处理
     */
    private void buildMappingTables(Class<?> entityClass) {
        log.debug("Building mapping tables for entity: {}", entityClass.getSimpleName());

        // 清空现有映射表
        columnToFieldMap.clear();
        propertyToFieldMap.clear();

        // 为每个字段创建映射信息
        for (FieldInfo fieldInfo : fields) {
            String propertyName = fieldInfo.getPropertyName();
            String columnName = fieldInfo.getColumn();

            // 构建属性名 -> 字段信息（SQL别名支持的关键）
            propertyToFieldMap.put(propertyName, fieldInfo);

            // 构建列名 -> 字段信息（支持多种格式）
            // 1. 原始列名（小写）
            columnToFieldMap.put(columnName.toLowerCase(), fieldInfo);

            // 2. 原始列名（保持大小写）
            if (!columnName.equals(columnName.toLowerCase())) {
                columnToFieldMap.put(columnName, fieldInfo);
            }

            // 3. 驼峰格式的列名（支持SQL别名）
            String camelColumnName = toCamelCase(columnName.toLowerCase());
            if (!camelColumnName.equals(propertyName)) {
                columnToFieldMap.put(camelColumnName, fieldInfo);
            }

            // 4. SQL别名支持：将属性名也加入列名映射
            // 这样当SQL使用别名时（如 SELECT tenant_id AS tenantId），
            // JdbcUtils.lookupColumnName返回tenantId，可以直接找到对应字段
            if (!propertyName.equals(columnName.toLowerCase()) &&
                !propertyName.equals(camelColumnName)) {
                columnToFieldMap.put(propertyName, fieldInfo);
                // 同时支持首字母大写的情况
                String capitalizedProperty = propertyName.substring(0, 1).toUpperCase() +
                    propertyName.substring(1);
                columnToFieldMap.put(capitalizedProperty, fieldInfo);
            }

            // 5. 下划线转驼峰的反向映射（增强兼容性）
            // 如果属性名是驼峰格式，同时支持下划线格式的查找
            if (!propertyName.contains("_") && !propertyName.equals(propertyName.toLowerCase())) {
                String underscoreFormat = camelToUnderline(propertyName);
                if (!underscoreFormat.equals(columnName.toLowerCase())) {
                    columnToFieldMap.put(underscoreFormat.toLowerCase(), fieldInfo);
                }
            }
        }

        // 添加对exist=false字段的支持
        addExistFalseFieldMappings(entityClass);

        log.debug("Built {} column mappings and {} property mappings for entity: {}",
            columnToFieldMap.size(), propertyToFieldMap.size(), entityClass.getSimpleName());
    }

    /**
     * 添加@TableField(exist=false)字段的属性映射支持
     * 注意：exist=false字段不应该参与数据库列映射，因为它们在数据库中不存在
     * 这个方法主要是为了支持属性级别的访问，但不应该影响数据库映射
     */
    private void addExistFalseFieldMappings(Class<?> entityClass) {
        // exist=false字段已经在EntityInfo.of()方法中被正确排除
        // 这里不需要额外处理，因为它们不应该参与任何数据库相关的映射

        // 如果将来需要支持exist=false字段的属性访问，可以在这里添加
        // 但目前的设计是正确的：exist=false字段完全不参与映射
        log.debug("Exist=false fields are correctly excluded from all mappings for entity: {}",
            entityClass.getSimpleName());
    }

    /**
     * 解析类级别的索引注解
     */
    private static void parseClassLevelIndexes(Class<?> entityClass, EntityInfo entityInfo) {
        // 尝试解析JPA的@Table索引
        Table jpaTable = entityClass.getAnnotation(Table.class);
        if (jpaTable != null && jpaTable.indexes() != null && jpaTable.indexes().length > 0) {
            for (javax.persistence.Index index : jpaTable.indexes()) {
                String[] columnNames = index.columnList().split(",");
                // 转换列名为数据库列名
                String[] dbColumnNames = Arrays.stream(columnNames)
                        .map(String::trim)
                        .map(colName -> {
                            // 查找对应的FieldInfo获取数据库列名
                            for (FieldInfo fieldInfo : entityInfo.getFields()) {
                                if (fieldInfo.getField().getName().equals(colName)) {
                                    return fieldInfo.getColumn();
                                }
                            }
                            return colName; // 如果找不到对应的字段，使用原始名称
                        })
                        .toArray(String[]::new);
                
                String indexName = index.name();
                if (indexName.isEmpty()) {
                    // 生成默认索引名
                    indexName = currentDialect.getIndexName(entityInfo.getTableName(), dbColumnNames, index.unique());
                }
                
                IndexInfo indexInfo = IndexInfo.builder()
                        .name(indexName)
                        .columnNames(dbColumnNames)
                        .unique(index.unique())
                        .build();
                        
                entityInfo.getIndexes().add(indexInfo);
            }
        }
        
        // 这里可以扩展支持MyBatis-Plus的@TableIndex注解
        // 目前MyBatis-Plus官方没有提供索引注解，但可以预留扩展点
        // 如果有自定义的@TableIndex注解，可以在这里解析
        // 示例：解析自定义的@TableIndexes注解
    }

    /**
     * 驼峰转下划线
     */
    private static String camelToUnderline(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camelCase.charAt(0)));

        for (int i = 1; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_');
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }

        return result.toString();
    }

    /**
     * 下划线转驼峰
     */
    private static String toCamelCase(String underlineStr) {
        if (underlineStr == null || underlineStr.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        boolean upperCase = false;

        for (int i = 0; i < underlineStr.length(); i++) {
            char c = underlineStr.charAt(i);
            if (c == '_') {
                upperCase = true;
            } else {
                result.append(upperCase ? Character.toUpperCase(c) : c);
                upperCase = false;
            }
        }

        return result.toString();
    }

    /**
     * 根据字段名获取字段信息
     * @param fieldName Java字段名
     * @return 对应的字段信息，未找到返回null
     */
    public FieldInfo getFieldByName(String fieldName) {
        return this.fields.stream()
                .filter(f -> f.getField().getName().equals(fieldName))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 索引信息类
     */
    @Data
    @AllArgsConstructor
    public static class IndexInfo {
        private String name;        // 索引名称
        private String[] columnNames; // 列名数组
        private boolean unique;     // 是否唯一索引
        
        public static IndexInfoBuilder builder() {
            return new IndexInfoBuilder();
        }
        
        public static class IndexInfoBuilder {
            private String name;
            private String[] columnNames;
            private boolean unique;
            
            public IndexInfoBuilder name(String name) {
                this.name = name;
                return this;
            }
            
            public IndexInfoBuilder columnNames(String[] columnNames) {
                this.columnNames = columnNames;
                return this;
            }
            
            public IndexInfoBuilder unique(boolean unique) {
                this.unique = unique;
                return this;
            }
            
            public IndexInfo build() {
                return new IndexInfo(name, columnNames, unique);
            }
        }
    }
}