package com.lg.dao.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * Light ORM多租户配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.orm.tenant")
public class TenantProperties {
    
    /**
     * 是否启用多租户
     */
    private boolean enable = false;

    /**
     * 多租户模式
     */
    private TenantMode mode = TenantMode.COLUMN;

    /**
     * 租户字段名
     */
    private String tenantField = "tenant_id";

    /**
     * Schema前缀
     * 仅在 mode = SCHEMA 时生效
     */
    private String schemaPrefix = "tenant_";

    /**
     * 忽略的表名列表
     * 这些表不会进行多租户处理
     */
    private List<String> ignoreTables = Arrays.asList("sys_tenant", "sys_config","sys_sequence");

    /**
     * 多租户模式枚举
     */
    public enum TenantMode {
        /**
         * 字段模式
         * 通过在表中增加租户字段来实现多租户
         */
        COLUMN,

        /**
         * Schema模式
         * 通过不同的Schema来实现多租户
         */
        SCHEMA
    }
} 