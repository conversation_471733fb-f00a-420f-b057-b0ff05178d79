package com.github.stupdit1t.excel.pdf;

/**
 * PDF导出配置类
 */
public class PdfExportConfig {

    /**
     * 页面方向
     */
    public enum PageOrientation {
        PORTRAIT,  // 纵向
        LANDSCAPE  // 横向
    }

    /**
     * 页面大小
     */
    public enum PageSize {
        A4, A3, A5, LETTER, LEGAL
    }

    /**
     * 字体大小
     */
    private float fontSize = 10f;

    /**
     * 页面方向
     */
    private PageOrientation orientation = PageOrientation.PORTRAIT;

    /**
     * 页面大小
     */
    private PageSize pageSize = PageSize.A4;

    /**
     * 页边距 - 上
     */
    private float marginTop = 36f;

    /**
     * 页边距 - 下
     */
    private float marginBottom = 36f;

    /**
     * 页边距 - 左
     */
    private float marginLeft = 36f;

    /**
     * 页边距 - 右
     */
    private float marginRight = 36f;

    /**
     * 是否显示网格线
     */
    private boolean showGridLines = true;

    /**
     * 表头背景色 (RGB格式，如: 0xCCCCCC)
     */
    private int headerBackgroundColor = 0xF0F0F0;

    /**
     * 表头字体颜色 (RGB格式，如: 0x000000)
     */
    private int headerFontColor = 0x000000;

    /**
     * 数据行字体颜色 (RGB格式，如: 0x000000)
     */
    private int dataFontColor = 0x000000;

    /**
     * 是否启用中文字体支持
     */
    private boolean enableChineseFont = true;

    /**
     * 自定义字体路径
     */
    private String customFontPath;

    // 构造函数
    public PdfExportConfig() {
    }

    // Getter和Setter方法
    public float getFontSize() {
        return fontSize;
    }

    public PdfExportConfig setFontSize(float fontSize) {
        this.fontSize = fontSize;
        return this;
    }

    public PageOrientation getOrientation() {
        return orientation;
    }

    public PdfExportConfig setOrientation(PageOrientation orientation) {
        this.orientation = orientation;
        return this;
    }

    public PageSize getPageSize() {
        return pageSize;
    }

    public PdfExportConfig setPageSize(PageSize pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public float getMarginTop() {
        return marginTop;
    }

    public PdfExportConfig setMarginTop(float marginTop) {
        this.marginTop = marginTop;
        return this;
    }

    public float getMarginBottom() {
        return marginBottom;
    }

    public PdfExportConfig setMarginBottom(float marginBottom) {
        this.marginBottom = marginBottom;
        return this;
    }

    public float getMarginLeft() {
        return marginLeft;
    }

    public PdfExportConfig setMarginLeft(float marginLeft) {
        this.marginLeft = marginLeft;
        return this;
    }

    public float getMarginRight() {
        return marginRight;
    }

    public PdfExportConfig setMarginRight(float marginRight) {
        this.marginRight = marginRight;
        return this;
    }

    public boolean isShowGridLines() {
        return showGridLines;
    }

    public PdfExportConfig setShowGridLines(boolean showGridLines) {
        this.showGridLines = showGridLines;
        return this;
    }

    public int getHeaderBackgroundColor() {
        return headerBackgroundColor;
    }

    public PdfExportConfig setHeaderBackgroundColor(int headerBackgroundColor) {
        this.headerBackgroundColor = headerBackgroundColor;
        return this;
    }

    public int getHeaderFontColor() {
        return headerFontColor;
    }

    public PdfExportConfig setHeaderFontColor(int headerFontColor) {
        this.headerFontColor = headerFontColor;
        return this;
    }

    public int getDataFontColor() {
        return dataFontColor;
    }

    public PdfExportConfig setDataFontColor(int dataFontColor) {
        this.dataFontColor = dataFontColor;
        return this;
    }

    public boolean isEnableChineseFont() {
        return enableChineseFont;
    }

    public PdfExportConfig setEnableChineseFont(boolean enableChineseFont) {
        this.enableChineseFont = enableChineseFont;
        return this;
    }

    public String getCustomFontPath() {
        return customFontPath;
    }

    public PdfExportConfig setCustomFontPath(String customFontPath) {
        this.customFontPath = customFontPath;
        return this;
    }

    /**
     * 设置页边距（统一设置四个方向）
     */
    public PdfExportConfig setMargins(float margin) {
        this.marginTop = margin;
        this.marginBottom = margin;
        this.marginLeft = margin;
        this.marginRight = margin;
        return this;
    }

    /**
     * 设置页边距（分别设置）
     */
    public PdfExportConfig setMargins(float top, float right, float bottom, float left) {
        this.marginTop = top;
        this.marginRight = right;
        this.marginBottom = bottom;
        this.marginLeft = left;
        return this;
    }
}
