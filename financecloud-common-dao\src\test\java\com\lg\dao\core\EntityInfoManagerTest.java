package com.lg.dao.core;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.cache.UnifiedCacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EntityInfoManager 测试
 * 验证统一实体信息管理的正确性和性能
 */
public class EntityInfoManagerTest {

    private CacheProperties cacheProperties;
    private UnifiedCacheManager cacheManager;

    @BeforeEach
    public void setUp() throws Exception {
        cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);

        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();

        // 初始化 EntityInfoManager 的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    /**
     * 测试实体类
     */
    @Table(name = "test_user")
    public static class TestUser {
        @Id
        @Column(name = "user_id")
        private Long id;
        
        @Column(name = "user_name")
        private String name;
        
        @Column(name = "user_email")
        private String email;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    /**
     * 测试基本功能
     */
    @Test
    public void testBasicFunctionality() {
        EntityInfoManager manager = EntityInfoManager.getInstance();

        // 获取实体信息
        EntityInfo entityInfo = manager.getEntityInfo(TestUser.class);

        assertNotNull(entityInfo, "实体信息不应为空");
        assertEquals("test_user", entityInfo.getTableName(), "表名应该正确");
        assertTrue(entityInfo.getFields().size() > 0, "应该有字段信息");

        System.out.println("基本功能测试通过");
    }

    /**
     * 测试缓存一致性
     */
    @Test
    public void testCacheConsistency() {
        EntityInfoManager manager1 = EntityInfoManager.getInstance();
        EntityInfoManager manager2 = EntityInfoManager.getInstance();

        // 应该是同一个实例
        assertSame(manager1, manager2, "应该返回同一个单例实例");

        // 获取实体信息
        EntityInfo entityInfo1 = manager1.getEntityInfo(TestUser.class);
        EntityInfo entityInfo2 = manager2.getEntityInfo(TestUser.class);

        // 应该是同一个对象（来自缓存）
        assertSame(entityInfo1, entityInfo2, "应该返回缓存的同一个实体信息对象");

        System.out.println("缓存一致性测试通过");
    }

    /**
     * 测试无缓存管理器的情况
     */
    @Test
    public void testWithoutCacheManager() {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        EntityInfo entityInfo = manager.getEntityInfo(TestUser.class);
        
        assertNotNull(entityInfo, "无缓存管理器时实体信息不应为空");
        assertEquals("test_user", entityInfo.getTableName(), "表名应该正确");
        
        System.out.println("无缓存管理器测试通过");
    }

    /**
     * 测试预加载功能
     */
    @Test
    public void testPreloadEntityInfo() {
        EntityInfoManager manager = EntityInfoManager.getInstance();

        // 预加载实体信息
        manager.preloadEntityInfo(TestUser.class);

        // 验证是否已缓存（简化检查）
        // 注意：由于使用统一缓存，本地缓存可能为空，这是正常的
        assertDoesNotThrow(() -> manager.getEntityInfo(TestUser.class), "预加载后应该能正常获取实体信息");
        
        System.out.println("预加载功能测试通过");
    }

    /**
     * 测试缓存清除功能
     */
    @Test
    public void testCacheEviction() {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 先获取实体信息（触发缓存）
        EntityInfo entityInfo1 = manager.getEntityInfo(TestUser.class);
        assertNotNull(entityInfo1);
        
        // 清除缓存
        manager.evictEntityInfo(TestUser.class);
        
        // 再次获取（应该重新创建）
        EntityInfo entityInfo2 = manager.getEntityInfo(TestUser.class);
        assertNotNull(entityInfo2);
        
        // 内容应该相同，但可能是不同的对象实例
        assertEquals(entityInfo1.getTableName(), entityInfo2.getTableName());
        
        System.out.println("缓存清除功能测试通过");
    }

    /**
     * 测试缓存统计功能
     */
    @Test
    public void testCacheStats() {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 获取一些实体信息
        manager.getEntityInfo(TestUser.class);
        manager.getEntityInfo(TestUser.class); // 第二次应该命中缓存
        
        // 获取缓存统计
        String stats = manager.getCacheStats();
        
        assertNotNull(stats, "缓存统计不应为空");
        assertTrue(stats.contains("EntityInfo 缓存统计"), "应该包含统计标题");
        
        System.out.println("缓存统计信息:");
        System.out.println(stats);
        
        System.out.println("缓存统计功能测试通过");
    }

    /**
     * 测试性能对比 - 测试统一缓存管理器的性能
     */
    @Test
    public void testPerformanceComparison() {
        int warmupIterations = 100;
        int testIterations = 10000;

        // 预热 JVM
        for (int i = 0; i < warmupIterations; i++) {
            EntityInfo.of(TestUser.class);
        }

        EntityInfoManager manager = EntityInfoManager.getInstance();

        // 预热缓存
        for (int i = 0; i < warmupIterations; i++) {
            manager.getEntityInfo(TestUser.class);
        }

        // 测试统一缓存管理器的性能
        long cacheCallsStart = System.nanoTime();
        EntityInfo firstResult = null;
        for (int i = 0; i < testIterations; i++) {
            EntityInfo result = manager.getEntityInfo(TestUser.class);
            if (firstResult == null) {
                firstResult = result;
            } else {
                // 验证返回的是同一个对象（避免重复解析）
                assertSame(firstResult, result, "应该返回缓存的同一个对象");
            }
        }
        long cacheCallsEnd = System.nanoTime();
        long cacheCallsTime = cacheCallsEnd - cacheCallsStart;

        // 对比：每次都重新解析
        long parseCallsStart = System.nanoTime();
        for (int i = 0; i < testIterations; i++) {
            EntityInfo.of(TestUser.class); // 每次都重新解析
        }
        long parseCallsEnd = System.nanoTime();
        long parseCallsTime = parseCallsEnd - parseCallsStart;

        System.out.println("性能对比测试 (" + testIterations + " 次调用):");
        System.out.println("统一缓存调用总时间: " + cacheCallsTime / 1_000_000 + " ms");
        System.out.println("重复解析总时间: " + parseCallsTime / 1_000_000 + " ms");

        double avgCacheTime = (double) cacheCallsTime / testIterations;
        double avgParseTime = (double) parseCallsTime / testIterations;

        System.out.println("统一缓存平均时间: " + String.format("%.2f", avgCacheTime) + " ns");
        System.out.println("重复解析平均时间: " + String.format("%.2f", avgParseTime) + " ns");

        if (parseCallsTime > cacheCallsTime) {
            double improvement = (double) parseCallsTime / cacheCallsTime;
            System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
            System.out.println("✅ 统一缓存避免了重复解析，性能显著提升");
        } else {
            double overhead = (double) cacheCallsTime / parseCallsTime;
            System.out.println("性能开销: " + String.format("%.2f", overhead) + "x");
            System.out.println("⚠️ 统一缓存性能未达到预期，但避免了重复解析");
        }

        // 重要：验证避免了重复解析（返回同一个对象）
        EntityInfo result1 = manager.getEntityInfo(TestUser.class);
        EntityInfo result2 = manager.getEntityInfo(TestUser.class);
        assertSame(result1, result2, "多次调用应该返回同一个对象，避免重复解析");

        System.out.println("✅ 验证通过：避免了重复解析，返回同一个对象");
    }

    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 测试 null 参数
        assertThrows(IllegalArgumentException.class, () -> {
            manager.getEntityInfo(null);
        }, "应该抛出 IllegalArgumentException");
        
        // 测试清除 null 实体缓存（应该不抛异常）
        assertDoesNotThrow(() -> {
            manager.evictEntityInfo(null);
        }, "清除 null 实体缓存不应抛异常");
        
        System.out.println("错误处理测试通过");
    }

    /**
     * 测试全局缓存清除
     */
    @Test
    public void testClearAllCache() {
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 预加载一些实体信息
        manager.getEntityInfo(TestUser.class);
        
        // 清除所有缓存
        manager.evictAllEntityInfo();
        
        // 验证缓存已清除（这里简化验证，实际可能需要更复杂的检查）
        assertDoesNotThrow(() -> {
            manager.getEntityInfo(TestUser.class);
        }, "清除所有缓存后应该能正常获取实体信息");
        
        System.out.println("全局缓存清除测试通过");
    }
}
