/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VoucherSourceEnum {

	/**
	 * 0自定义分录 1损益结转 2计提工资 3发放工资 4计提折旧
	 */
	custom( "0","zdy","自定义"),
	syjz("1","syjz","损益结转"),
	jtgz("2","jtgz","计提工资"),
	ffgz("3","ffgz","发放工资"),
	jtzj("4","jtzj","计提折旧"),
	zcgr("5","zcgr","资产购入"),
	zccl("6","zccl","资产清理"),
	fpsb("7","fpsb","发票识别"),
	yspz("8","yspz","原始凭证"),
	qmth("9","qmth","期末调汇"),
	general("10","general","通用结转"),
	zzsjz("11","zzsjz","增值税结转"),
	fprzjz("12","fprzjz","发票认证结转"),
	jtds("13","jtds","计提地税"),
	jtsds("14","jtsds","计提所得税"),
	yclrk("15","yclrk","原材料入库"),
	lld("16","lld","领料单"),
	ccprk("17","ccprk","产成品入库"),
	ccpck("18","ccpck","产成品出库"),
	jzxscb("19","jzxscb","结转销售成本"),
	wxzctx("20","wxzctx","无形资产摊销"),
	cqtx("21","cqtx","长期摊销"),
	yhdzd("22","yhdzd","银行对账单"),
	mfsyjz("23","mfsyjz","损益结转"),
	scll("24","scll","生产领料"),
	scrk("25","scrk","生产入库"),
	jzmzzzs("26","jzmzzzs","结转免征增值税"),
	jzzzfy("27","jzzzfy","结转制造费用"),
	pzdr("28","pzdr","凭证导入"),
	rjz("29","rjz","日记账"),

	jflow("30","jflow","jflow"),
	fypj("31","fydj","费用票据"),

	;


    /**
	 * 描述
	 */
	private String num;
	private String code;
	private String name;


	private static final Map<String, VoucherSourceEnum> lookup = new HashMap<String, VoucherSourceEnum>();

    private static final Map<String, VoucherSourceEnum> keyByCode = new HashMap<String, VoucherSourceEnum>();

	static {
		for (VoucherSourceEnum s : EnumSet.allOf(VoucherSourceEnum.class)) {
			lookup.put(s.getNum(), s);
			}
		}

    static {
        for (VoucherSourceEnum s : EnumSet.allOf(VoucherSourceEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

	public static VoucherSourceEnum lookup(String num) {
		return lookup.get(num);
	}

    public static VoucherSourceEnum keyByCode(String code) {
        return lookup.get(code);
    }



}
