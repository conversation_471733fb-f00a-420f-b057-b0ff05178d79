package com.lg.dao.core.query;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.sql.JoinType;
import com.lg.dao.core.sql.SqlBuilder;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * JOIN查询构建器
 * 支持构建复杂的多表连接查询
 */
public class JoinQueryBuilder<T> {

    private static final Logger log = LoggerFactory.getLogger(JoinQueryBuilder.class);

    @Getter
    private final SqlBuilder sqlBuilder;
    private final GenericDao<T, ?> baseDao;
    private final List<Object> params = new ArrayList<>();
    
    // 用于存储查询构建的各个部分
    private final List<String> selectColumns = new ArrayList<>();
    private String fromClause;
    private final List<String> joinClauses = new ArrayList<>();
    private final List<String> whereClauses = new ArrayList<>();
    private final List<String> orderByClauses = new ArrayList<>();
    private final List<String> groupByClauses = new ArrayList<>();
    private String limitClause;
    private String offsetClause;
    private boolean hasWhereClause = false;
    private boolean hasOrderByClause = false;
    private boolean hasGroupByClause = false;

    /**
     * 构造函数
     * @param baseDao 数据访问对象
     */
    public JoinQueryBuilder(GenericDao<T, ?> baseDao) {
        this.baseDao = baseDao;
        this.sqlBuilder = new SqlBuilder();
    }

    /**
     * 构造函数
     * @param baseDao 数据访问对象
     * @param sqlBuilder SQL构建器
     */
    public JoinQueryBuilder(GenericDao<T, ?> baseDao, SqlBuilder sqlBuilder) {
        this.baseDao = baseDao;
        this.sqlBuilder = sqlBuilder;
    }

    /**
     * 添加SELECT字段
     * @param columns 列名数组
     * @return 当前查询构建器
     */
    public JoinQueryBuilder select(String... columns) {
        if (columns != null && columns.length > 0) {
            for (String column : columns) {
                selectColumns.add(column);
            }
        }
        return this;
    }

    /**
     * 设置FROM表
     * @param table 表名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder from(String table) {
        fromClause = table;
        return this;
    }

    /**
     * 添加INNER JOIN
     * @param table 表名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder innerJoin(String table) {
        joinClauses.add("INNER JOIN " + table);
        return this;
    }

    /**
     * 添加LEFT JOIN
     * @param table 表名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder leftJoin(String table) {
        joinClauses.add("LEFT JOIN " + table);
        return this;
    }

    /**
     * 添加RIGHT JOIN
     * @param table 表名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder rightJoin(String table) {
        joinClauses.add("RIGHT JOIN " + table);
        return this;
    }

    /**
     * 添加FULL JOIN
     * @param table 表名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder fullJoin(String table) {
        joinClauses.add("FULL JOIN " + table);
        return this;
    }

    /**
     * 通用JOIN方法
     * @param table 表名
     * @param joinType 连接类型
     * @return 当前查询构建器
     */
    public JoinQueryBuilder join(String table, JoinType joinType) {
        joinClauses.add(joinType.getSql() + " " + table);
        return this;
    }

    /**
     * 添加JOIN ON条件
     * @param condition ON条件
     * @return 当前查询构建器
     */
    public JoinQueryBuilder on(String condition) {
        // 添加到最后一个JOIN子句
        if (!joinClauses.isEmpty()) {
            int lastIndex = joinClauses.size() - 1;
            joinClauses.set(lastIndex, joinClauses.get(lastIndex) + " ON " + condition);
        }
        return this;
    }

    /**
     * 开始WHERE子句
     * @return 当前查询构建器
     */
    public JoinQueryBuilder where() {
        hasWhereClause = true;
        return this;
    }

    /**
     * 添加AND条件
     * @return 当前查询构建器
     */
    public JoinQueryBuilder and() {
        if (hasWhereClause && !whereClauses.isEmpty()) {
            whereClauses.add("AND");
        }
        return this;
    }

    /**
     * 添加OR条件
     * @return 当前查询构建器
     */
    public JoinQueryBuilder or() {
        if (hasWhereClause && !whereClauses.isEmpty()) {
            whereClauses.add("OR");
        }
        return this;
    }

    /**
     * 添加等于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder eq(String column, Object value) {
        whereClauses.add(column + " = ?");
        params.add(value);
        return this;
    }

    /**
     * 添加不等于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder ne(String column, Object value) {
        whereClauses.add(column + " != ?");
        params.add(value);
        return this;
    }

    /**
     * 添加大于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder gt(String column, Object value) {
        whereClauses.add(column + " > ?");
        params.add(value);
        return this;
    }

    /**
     * 添加大于等于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder ge(String column, Object value) {
        whereClauses.add(column + " >= ?");
        params.add(value);
        return this;
    }

    /**
     * 添加小于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder lt(String column, Object value) {
        whereClauses.add(column + " < ?");
        params.add(value);
        return this;
    }

    /**
     * 添加小于等于条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder le(String column, Object value) {
        whereClauses.add(column + " <= ?");
        params.add(value);
        return this;
    }

    /**
     * 添加LIKE条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder like(String column, String value) {
        whereClauses.add(column + " LIKE ?");
        params.add("%" + value + "%");
        return this;
    }

    /**
     * 添加左LIKE条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder leftLike(String column, String value) {
        whereClauses.add(column + " LIKE ?");
        params.add("%" + value);
        return this;
    }

    /**
     * 添加右LIKE条件
     * @param column 列名
     * @param value 值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder rightLike(String column, String value) {
        whereClauses.add(column + " LIKE ?");
        params.add(value + "%");
        return this;
    }

    /**
     * 添加IN条件
     * @param column 列名
     * @param values 值列表
     * @return 当前查询构建器
     */
    public JoinQueryBuilder in(String column, List<?> values) {
        whereClauses.add(column + " IN (" + String.join(", ", Collections.nCopies(values.size(), "?")) + ")");
        params.addAll(values);
        return this;
    }

    /**
     * 添加NOT IN条件
     * @param column 列名
     * @param values 值列表
     * @return 当前查询构建器
     */
    public JoinQueryBuilder notIn(String column, List<?> values) {
        whereClauses.add(column + " NOT IN (" + String.join(", ", Collections.nCopies(values.size(), "?")) + ")");
        params.addAll(values);
        return this;
    }

    /**
     * 添加BETWEEN条件
     * @param column 列名
     * @param value1 起始值
     * @param value2 结束值
     * @return 当前查询构建器
     */
    public JoinQueryBuilder between(String column, Object value1, Object value2) {
        whereClauses.add(column + " BETWEEN ? AND ?");
        params.add(value1);
        params.add(value2);
        return this;
    }

    /**
     * 添加IS NULL条件
     * @param column 列名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder isNull(String column) {
        whereClauses.add(column + " IS NULL");
        return this;
    }

    /**
     * 添加IS NOT NULL条件
     * @param column 列名
     * @return 当前查询构建器
     */
    public JoinQueryBuilder isNotNull(String column) {
        whereClauses.add(column + " IS NOT NULL");
        return this;
    }

    /**
     * 添加ORDER BY子句
     * @param columns 列名数组
     * @return 当前查询构建器
     */
    public JoinQueryBuilder orderBy(String... columns) {
        if (columns != null && columns.length > 0) {
            for (String column : columns) {
                orderByClauses.add(column);
            }
            hasOrderByClause = true;
        }
        return this;
    }

    /**
     * 添加DESC排序
     * @return 当前查询构建器
     */
    public JoinQueryBuilder desc() {
        if (hasOrderByClause && !orderByClauses.isEmpty()) {
            int lastIndex = orderByClauses.size() - 1;
            orderByClauses.set(lastIndex, orderByClauses.get(lastIndex) + " DESC");
        }
        return this;
    }

    /**
     * 添加ASC排序
     * @return 当前查询构建器
     */
    public JoinQueryBuilder asc() {
        if (hasOrderByClause && !orderByClauses.isEmpty()) {
            int lastIndex = orderByClauses.size() - 1;
            orderByClauses.set(lastIndex, orderByClauses.get(lastIndex) + " ASC");
        }
        return this;
    }

    /**
     * 添加GROUP BY子句
     * @param columns 列名数组
     * @return 当前查询构建器
     */
    public JoinQueryBuilder groupBy(String... columns) {
        if (columns != null && columns.length > 0) {
            for (String column : columns) {
                groupByClauses.add(column);
            }
            hasGroupByClause = true;
        }
        return this;
    }

    /**
     * 添加HAVING子句
     * @return 当前查询构建器
     */
    public JoinQueryBuilder having() {
        // Implementation needed
        return this;
    }

    /**
     * 添加LIMIT子句
     * @param limit 限制数量
     * @return 当前查询构建器
     */
    public JoinQueryBuilder limit(int limit) {
        limitClause = "LIMIT " + limit;
        return this;
    }

    /**
     * 添加OFFSET子句
     * @param offset 偏移量
     * @return 当前查询构建器
     */
    public JoinQueryBuilder offset(int offset) {
        offsetClause = "OFFSET " + offset;
        return this;
    }

    /**
     * 构建完整的SQL语句
     * @return SQL语句
     */
    private String buildSql() {
        StringBuilder sql = new StringBuilder();
        
        // 添加SELECT部分
        if (!selectColumns.isEmpty()) {
            sql.append("SELECT ");
            for (int i = 0; i < selectColumns.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append(selectColumns.get(i));
            }
        } else {
            sql.append("SELECT *");
        }
        
        // 添加FROM部分
        sql.append(" FROM ").append(fromClause);
        
        // 添加JOIN部分
        for (String joinClause : joinClauses) {
            sql.append(" ").append(joinClause);
        }
        
        // 添加WHERE部分
        if (hasWhereClause) {
            sql.append(" WHERE 1=1");
            for (String whereClause : whereClauses) {
                if (whereClause.equals("AND") || whereClause.equals("OR")) {
                    sql.append(" ").append(whereClause);
                } else {
                    sql.append(" AND ").append(whereClause);
                }
            }
        }
        
        // 添加GROUP BY部分
        if (hasGroupByClause && !groupByClauses.isEmpty()) {
            sql.append(" GROUP BY ");
            for (int i = 0; i < groupByClauses.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append(groupByClauses.get(i));
            }
        }
        
        // 添加ORDER BY部分
        if (hasOrderByClause && !orderByClauses.isEmpty()) {
            sql.append(" ORDER BY ");
            for (int i = 0; i < orderByClauses.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append(orderByClauses.get(i));
            }
        }
        
        // 添加LIMIT和OFFSET
        if (limitClause != null) {
            sql.append(" ").append(limitClause);
        }
        if (offsetClause != null) {
            sql.append(" ").append(offsetClause);
        }
        
        return sql.toString();
    }

    /**
     * 执行查询并返回Map列表
     * @return Map列表结果
     */
    public List<Map<String, Object>> listMap() {
        String sql = buildSql();
        // 日志记录生成的SQL，方便调试
        log.debug("Generated SQL: {}", sql);
        Object[] paramArray = params.toArray();
        return baseDao.selectMapList(sql, paramArray);
    }

    /**
     * 执行查询并返回单个Map
     * @return 单个Map结果
     */
    public Map<String, Object> oneMap() {
        String sql = buildSql();
        // 日志记录生成的SQL，方便调试
        log.debug("Generated SQL: {}", sql);
        Object[] paramArray = params.toArray();
        return baseDao.selectOneMap(sql, paramArray);
    }

    /**
     * 执行查询并返回指定类型的对象列表
     * @param clazz 目标类型
     * @param <E> 目标类型泛型
     * @return 对象列表结果
     */
    public <E> List<E> list(Class<E> clazz) {
        String sql = buildSql();
        // 日志记录生成的SQL，方便调试
        log.debug("Generated SQL: {}", sql);
        Object[] paramArray = params.toArray();
        return baseDao.selectList(sql, clazz, paramArray);
    }

    /**
     * 执行查询并返回指定类型的单个对象
     * @param clazz 目标类型
     * @param <E> 目标类型泛型
     * @return 单个对象结果
     */
    public <E> E one(Class<E> clazz) {
        String sql = buildSql();
        // 日志记录生成的SQL，方便调试
        log.debug("Generated SQL: {}", sql);
        Object[] paramArray = params.toArray();
        return baseDao.selectOne(sql, clazz, paramArray);
    }

    /**
     * 执行查询并返回记录数
     * @return 记录数
     */
    public long count() {
        // 构建原始SQL
        String originalSql = buildSql();
        
        // 构建COUNT查询
        String countSql = "SELECT COUNT(*) FROM (" + originalSql + ") t";
        
        // 执行COUNT查询
        Long count = baseDao.selectOne(countSql, Long.class, params.toArray());
        return count != null ? count : 0L;
    }
}