package com.lg.financecloud.common.data.tenant;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

import static com.lg.financecloud.common.data.tenant.TenantContextHolderFilter.ACCOUNT_ID;
import static com.lg.financecloud.common.data.tenant.TenantContextHolderFilter.TENANT_ID;

/**
 * <AUTHOR>
 * @date 2020/4/29
 * <p>
 * 传递 RestTemplate 请求的租户ID
 */
public class TenantRequestInterceptor implements ClientHttpRequestInterceptor {

	@Override
	public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
			throws IOException {

		if (TenantContextHolder.getTenantId() != null) {
			request.getHeaders().set(TENANT_ID, String.valueOf(TenantContextHolder.getTenantId()));
		}

		if (TenantContextHolder.getAccountId() != null) {
			request.getHeaders().set(ACCOUNT_ID, String.valueOf(TenantContextHolder.getAccountId()));
		}

		return execution.execute(request, body);
	}

}
