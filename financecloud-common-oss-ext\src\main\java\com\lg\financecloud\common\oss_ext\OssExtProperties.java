/*
 *    Copyright (c) 2018-2025, ebag All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.oss_ext;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * minio 配置信息
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssExtProperties {

	private String assessKey;
	private String secret;
	private String rolearn;
	private String region;
	private String bucketPub;
	private String pubCdn;
	private String privateCdn;
	private String bucketEndpoint;
	private String bucketEndpointInner;
	private String bucketPrivate;

}
