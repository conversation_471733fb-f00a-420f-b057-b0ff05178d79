/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.common.core.constant;

/**
 * <AUTHOR>
 * @date 2018年06月22日16:41:01 服务名称
 */
public interface ServiceNameConstants {

	/**
	 * 认证中心
	 */
	String AUTH_SERVICE = "financecloud-auth";

	/**
	 * UMPS模块
	 */
	String UPMS_SERVICE = "financecloud-upms-biz";

	/**
	 * 分布式事务协调服务
	 */
	String TX_MANAGER = "financecloud-tx-manager";

	/**
	 * XSVR_BIZ
	 */
	String XSVR_BIZ = "financecloud-xservice-biz";
	/**
	 * XSVR_BIZ
	 */
	String SXXY_SERVICE = "financecloud-sxxy-biz";
	/**
	 * FINANCE
	 */
	String FINANCE_SERVICE = "financecloud-finance-biz";
	/**
	 * oa
	 */
	String OA_SERVICE = "financecloud-oa-biz";

	/**
	 * jflow
	 */
	String JFLOW_SERVICE = "financecloud-jflow-biz";

	String WORKFLOW_SERVICE = "wf";
}
