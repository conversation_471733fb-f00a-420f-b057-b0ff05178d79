<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI流式输出演示</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .input-section {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .model-selection {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .output-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .controls {
            display: flex;
            gap: 10px;
        }
        .stream-toggle {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <h1>AI流式输出演示</h1>
    
    <div class="container">
        <div class="input-section">
            <div class="model-selection">
                <label for="model-type">选择模型:</label>
                <select id="model-type">
                    <option value="openai">OpenAI</option>
                    <option value="qwen">通义千问</option>
                    <option value="deepseek">DeepSeek</option>
                    <option value="baidu">百度文心一言</option>
                </select>
            </div>
            
            <textarea id="prompt" placeholder="请输入您的问题..."></textarea>
            
            <div class="controls">
                <button id="submit-btn">发送请求</button>
                <div class="stream-toggle">
                    <label class="switch">
                        <input type="checkbox" id="stream-toggle" checked>
                        <span class="slider"></span>
                    </label>
                    <span>流式输出</span>
                </div>
            </div>
        </div>
        
        <div class="output-section" id="output">响应将显示在这里...</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const submitBtn = document.getElementById('submit-btn');
            const promptInput = document.getElementById('prompt');
            const modelSelect = document.getElementById('model-type');
            const outputDiv = document.getElementById('output');
            const streamToggle = document.getElementById('stream-toggle');
            
            let eventSource = null;
            
            // 加载可用模型
            fetch('/api/ai/models')
                .then(response => response.json())
                .then(models => {
                    // 清空现有选项
                    modelSelect.innerHTML = '';
                    
                    // 添加可用模型
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model;
                        option.textContent = model;
                        modelSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载模型失败:', error);
                });
            
            submitBtn.addEventListener('click', function() {
                const prompt = promptInput.value.trim();
                if (!prompt) {
                    alert('请输入问题');
                    return;
                }
                
                // 禁用按钮，显示加载状态
                submitBtn.disabled = true;
                const loadingIndicator = document.createElement('span');
                loadingIndicator.className = 'loading';
                submitBtn.appendChild(loadingIndicator);
                
                // 清空输出区域
                outputDiv.textContent = '';
                
                const request = {
                    modelType: modelSelect.value,
                    prompt: prompt,
                    streamOutput: streamToggle.checked
                };
                
                // 关闭之前的事件源
                if (eventSource) {
                    eventSource.close();
                }
                
                if (streamToggle.checked) {
                    // 使用SSE进行流式输出
                    processStreamRequest(request);
                } else {
                    // 使用普通请求
                    processNormalRequest(request);
                }
            });
            
            function processNormalRequest(request) {
                fetch('/api/ai/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(request)
                })
                .then(response => response.json())
                .then(data => {
                    outputDiv.textContent = data.content || data.message;
                    resetSubmitButton();
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    outputDiv.textContent = '请求失败: ' + error.message;
                    resetSubmitButton();
                });
            }
            
            function processStreamRequest(request) {
                // 创建EventSource连接
                const encodedRequest = encodeURIComponent(JSON.stringify(request));
                eventSource = new EventSource(`/api/ai/process/stream?request=${encodedRequest}`);
                
                // 使用fetch和POST请求创建EventSource
                const url = '/api/ai/process/stream';
                const fetchOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(request)
                };
                
                fetch(url, fetchOptions)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        
                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        
                        function readStream() {
                            reader.read().then(({ done, value }) => {
                                if (done) {
                                    resetSubmitButton();
                                    return;
                                }
                                
                                const chunk = decoder.decode(value, { stream: true });
                                try {
                                    // 处理SSE格式的数据
                                    const lines = chunk.split('\n\n');
                                    lines.forEach(line => {
                                        if (line.startsWith('data:')) {
                                            const jsonStr = line.substring(5).trim();
                                            if (jsonStr) {
                                                const data = JSON.parse(jsonStr);
                                                if (data.content) {
                                                    outputDiv.textContent += data.content;
                                                }
                                            }
                                        }
                                    });
                                } catch (e) {
                                    console.error('解析响应失败:', e, chunk);
                                }
                                
                                readStream();
                            }).catch(error => {
                                console.error('读取流失败:', error);
                                outputDiv.textContent += '\n读取响应流失败: ' + error.message;
                                resetSubmitButton();
                            });
                        }
                        
                        readStream();
                    })
                    .catch(error => {
                        console.error('请求失败:', error);
                        outputDiv.textContent = '请求失败: ' + error.message;
                        resetSubmitButton();
                    });
            }
            
            function resetSubmitButton() {
                submitBtn.disabled = false;
                const loadingIndicator = submitBtn.querySelector('.loading');
                if (loadingIndicator) {
                    submitBtn.removeChild(loadingIndicator);
                }
            }
        });
    </script>
</body>
</html>