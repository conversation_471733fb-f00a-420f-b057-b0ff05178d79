# DAO框架缓存配置示例
# 支持为每种缓存类型配置独立的参数

# ========================================
# 1. 基础配置（使用所有默认值）
# ========================================
light:
  orm:
    cache:
      enable: true
      # 不配置 types，所有缓存类型使用默认配置

---
# ========================================
# 2. 部分自定义配置
# ========================================
spring:
  config:
    activate:
      on-profile: partial-custom

light:
  orm:
    cache:
      enable: true
      types:
        # 只调整需要的缓存类型，其他使用默认值
        entity:
          max-size: 5000
          expire-seconds: 10800  # 3小时
          description: "增强的实体缓存配置"
        
        permission:
          max-size: 2000
          expire-seconds: 900    # 15分钟
          description: "快速过期的权限缓存"

---
# ========================================
# 3. 开发环境配置
# ========================================
spring:
  config:
    activate:
      on-profile: dev

light:
  orm:
    cache:
      enable: true
      types:
        # 开发环境：小容量，短过期时间，便于调试
        dao:
          max-size: 100
          expire-seconds: 300    # 5分钟
          description: "开发环境DAO缓存"
        
        entity:
          max-size: 200
          expire-seconds: 600    # 10分钟
          description: "开发环境实体缓存"
        
        permission:
          max-size: 100
          expire-seconds: 300    # 5分钟
          description: "开发环境权限缓存"
        
        sql_template:
          max-size: 200
          expire-seconds: 300    # 5分钟
          description: "开发环境SQL模板缓存"
        
        metadata:
          max-size: 50
          expire-seconds: 600    # 10分钟
          description: "开发环境元数据缓存"
        
        mybatis_proxy:
          max-size: 50
          expire-seconds: 300    # 5分钟
          description: "开发环境MyBatis代理缓存"
        
        mybatis_sql_parse:
          max-size: 100
          expire-seconds: 300    # 5分钟
          description: "开发环境MyBatis SQL解析缓存"
        
        query_result:
          max-size: 100
          expire-seconds: 60     # 1分钟
          description: "开发环境查询结果缓存"

---
# ========================================
# 4. 生产环境配置
# ========================================
spring:
  config:
    activate:
      on-profile: prod

light:
  orm:
    cache:
      enable: true
      types:
        # 生产环境：大容量，长过期时间，优化性能
        dao:
          max-size: 2000
          expire-seconds: 7200   # 2小时
          description: "生产环境DAO缓存"
        
        entity:
          max-size: 5000
          expire-seconds: 14400  # 4小时
          description: "生产环境实体缓存"
        
        permission:
          max-size: 3000
          expire-seconds: 3600   # 1小时
          description: "生产环境权限缓存"
        
        sql_template:
          max-size: 3000
          expire-seconds: 7200   # 2小时
          description: "生产环境SQL模板缓存"
        
        metadata:
          max-size: 1000
          expire-seconds: 21600  # 6小时
          description: "生产环境元数据缓存"
        
        mybatis_proxy:
          max-size: 500
          expire-seconds: 7200   # 2小时
          description: "生产环境MyBatis代理缓存"
        
        mybatis_sql_parse:
          max-size: 2000
          expire-seconds: 3600   # 1小时
          description: "生产环境MyBatis SQL解析缓存"
        
        query_result:
          max-size: 1500
          expire-seconds: 300    # 5分钟
          description: "生产环境查询结果缓存"

---
# ========================================
# 5. 高并发环境配置
# ========================================
spring:
  config:
    activate:
      on-profile: high-concurrency

light:
  orm:
    cache:
      enable: true
      types:
        # 高并发环境：超大容量，适中过期时间
        dao:
          max-size: 5000
          expire-seconds: 3600   # 1小时
          description: "高并发环境DAO缓存"
        
        entity:
          max-size: 10000
          expire-seconds: 7200   # 2小时
          description: "高并发环境实体缓存"
        
        permission:
          max-size: 5000
          expire-seconds: 1800   # 30分钟
          description: "高并发环境权限缓存"
        
        sql_template:
          max-size: 5000
          expire-seconds: 3600   # 1小时
          description: "高并发环境SQL模板缓存"
        
        metadata:
          max-size: 2000
          expire-seconds: 14400  # 4小时
          description: "高并发环境元数据缓存"
        
        mybatis_proxy:
          max-size: 1000
          expire-seconds: 3600   # 1小时
          description: "高并发环境MyBatis代理缓存"
        
        mybatis_sql_parse:
          max-size: 3000
          expire-seconds: 1800   # 30分钟
          description: "高并发环境MyBatis SQL解析缓存"
        
        query_result:
          max-size: 3000
          expire-seconds: 600    # 10分钟
          description: "高并发环境查询结果缓存"

---
# ========================================
# 6. 测试环境配置
# ========================================
spring:
  config:
    activate:
      on-profile: test

light:
  orm:
    cache:
      enable: true
      types:
        # 测试环境：极小容量，极短过期时间
        dao:
          max-size: 50
          expire-seconds: 60     # 1分钟
          description: "测试环境DAO缓存"
        
        entity:
          max-size: 100
          expire-seconds: 120    # 2分钟
          description: "测试环境实体缓存"
        
        permission:
          max-size: 50
          expire-seconds: 60     # 1分钟
          description: "测试环境权限缓存"
        
        sql_template:
          max-size: 100
          expire-seconds: 60     # 1分钟
          description: "测试环境SQL模板缓存"
        
        metadata:
          max-size: 30
          expire-seconds: 120    # 2分钟
          description: "测试环境元数据缓存"
        
        mybatis_proxy:
          max-size: 30
          expire-seconds: 60     # 1分钟
          description: "测试环境MyBatis代理缓存"
        
        mybatis_sql_parse:
          max-size: 50
          expire-seconds: 60     # 1分钟
          description: "测试环境MyBatis SQL解析缓存"
        
        query_result:
          max-size: 50
          expire-seconds: 30     # 30秒
          description: "测试环境查询结果缓存"

---
# ========================================
# 7. 禁用缓存配置
# ========================================
spring:
  config:
    activate:
      on-profile: no-cache

light:
  orm:
    cache:
      enable: false
      # 当 enable: false 时，types 配置会被忽略

---
# ========================================
# 8. 自定义业务场景配置示例
# ========================================
spring:
  config:
    activate:
      on-profile: custom-business

light:
  orm:
    cache:
      enable: true
      types:
        # 根据具体业务需求调整
        
        # 用户权限变化频繁，使用短过期时间
        permission:
          max-size: 1000
          expire-seconds: 600    # 10分钟
          description: "频繁变化的权限缓存"
        
        # 实体元数据相对稳定，使用长过期时间
        entity:
          max-size: 3000
          expire-seconds: 21600  # 6小时
          description: "稳定的实体元数据缓存"
        
        # SQL模板解析结果可以长期缓存
        sql_template:
          max-size: 2000
          expire-seconds: 14400  # 4小时
          description: "长期缓存的SQL模板"
        
        # 查询结果需要及时更新
        query_result:
          max-size: 500
          expire-seconds: 180    # 3分钟
          description: "及时更新的查询结果缓存"
