package com.github.stupdit1t.excel.core.replace;

import com.github.stupdit1t.excel.core.ExcelHelper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 模板查看器 - 用于查看Excel模板的内容和替换后的效果
 */
public class TemplateViewer {
    
    public static void main(String[] args) {
        try {
            // 1. 生成原始模板并保存到文件
            generateTemplateToFile();
            
            // 2. 生成替换后的结果并保存到文件
            generateReplacedResultToFile();
            
            System.out.println("模板文件已生成完成！");
            System.out.println("原始模板内容: template_original.txt");
            System.out.println("替换后结果: template_replaced.txt");
            
        } catch (Exception e) {
            System.err.println("生成模板文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成原始模板内容到文件
     */
    private static void generateTemplateToFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("模板示例");
        
        // 创建完整模板
        createCompleteTemplate(sheet);
        
        // 将模板内容写入文本文件
        writeExcelToTextFile(workbook, "template_original.txt", "原始Excel模板内容");
        
        workbook.close();
    }
    
    /**
     * 生成替换后的结果到文件
     */
    private static void generateReplacedResultToFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("模板示例");
        
        // 创建完整模板
        createCompleteTemplate(sheet);
        
        // 准备测试数据
        List<Employee> employees = createTestEmployees();
        
        // 先将workbook写入到ByteArrayOutputStream，然后作为InputStream使用
        ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
        workbook.write(tempStream);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(tempStream.toByteArray());
        
        // 使用ExcelHelper进行替换
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        ExcelHelper.opsReplace()
            .from(inputStream)
            .var("companyName", "科技有限公司")
            .var("reportDate", "2024-01-15")
            .loop("employees", employees)
            .var("totalCount", employees.size())
            .replaceTo(outputStream);
        
        // 读取替换后的结果
        ByteArrayInputStream resultStream = new ByteArrayInputStream(outputStream.toByteArray());
        Workbook resultWorkbook = new XSSFWorkbook(resultStream);
        
        // 将替换后的内容写入文本文件
        writeExcelToTextFile(resultWorkbook, "template_replaced.txt", "替换后的Excel内容");
        
        workbook.close();
        resultWorkbook.close();
    }
    
    /**
     * 创建完整模板
     */
    private static void createCompleteTemplate(Sheet sheet) {
        // 第0行：公司名称
        Row companyRow = sheet.createRow(0);
        companyRow.createCell(0).setCellValue("${companyName}");
        
        // 第1行：报表日期
        Row dateRow = sheet.createRow(1);
        dateRow.createCell(0).setCellValue("报表日期: ${reportDate}");
        
        // 第2行：循环开始
        Row startRow = sheet.createRow(2);
        startRow.createCell(0).setCellValue("${#foreach employees}");
        
        // 第3行：循环体
        Row bodyRow = sheet.createRow(3);
        bodyRow.createCell(0).setCellValue("${item.empNo}");
        bodyRow.createCell(1).setCellValue("${item.name}");
        bodyRow.createCell(2).setCellValue("${item.department}");
        bodyRow.createCell(3).setCellValue("${item.salary}");
        
        // 第4行：循环结束
        Row endRow = sheet.createRow(4);
        endRow.createCell(0).setCellValue("${/foreach}");
        
        // 第5行：总计
        Row totalRow = sheet.createRow(5);
        totalRow.createCell(0).setCellValue("总计: ${totalCount} 人");
    }
    
    /**
     * 创建测试员工数据
     */
    private static List<Employee> createTestEmployees() {
        List<Employee> employees = new ArrayList<>();
        employees.add(createEmployee("E001", "张三", "开发部", 8000.00));
        employees.add(createEmployee("E002", "李四", "产品部", 7500.00));
        employees.add(createEmployee("E003", "王五", "测试部", 6500.00));
        return employees;
    }
    
    /**
     * 创建员工对象
     */
    private static Employee createEmployee(String empNo, String name, String department, Double salary) {
        Employee employee = new Employee();
        employee.setEmpNo(empNo);
        employee.setName(name);
        employee.setDepartment(department);
        employee.setSalary(salary);
        return employee;
    }
    
    /**
     * 将Excel内容写入文本文件
     */
    private static void writeExcelToTextFile(Workbook workbook, String fileName, String title) throws IOException {
        try (FileWriter writer = new FileWriter(fileName, false)) {
            writer.write("===========================================\n");
            writer.write(title + "\n");
            writer.write("===========================================\n\n");
            
            Sheet sheet = workbook.getSheetAt(0);
            
            for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    writer.write("第" + rowIndex + "行: [空行]\n");
                    continue;
                }
                
                writer.write("第" + rowIndex + "行: ");
                
                for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    if (cell == null) {
                        writer.write("[空] ");
                        continue;
                    }
                    
                    String cellValue = "";
                    switch (cell.getCellType()) {
                        case STRING:
                            cellValue = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                cellValue = cell.getDateCellValue().toString();
                            } else {
                                cellValue = String.valueOf(cell.getNumericCellValue());
                            }
                            break;
                        case BOOLEAN:
                            cellValue = String.valueOf(cell.getBooleanCellValue());
                            break;
                        case FORMULA:
                            cellValue = cell.getCellFormula();
                            break;
                        default:
                            cellValue = "[未知类型]";
                    }
                    
                    writer.write("[" + cellValue + "] ");
                }
                writer.write("\n");
            }
            
            writer.write("\n模板说明:\n");
            writer.write("- ${companyName}: 公司名称变量\n");
            writer.write("- ${reportDate}: 报表日期变量\n");
            writer.write("- ${#foreach employees}: 循环开始标记\n");
            writer.write("- ${item.empNo}: 员工编号\n");
            writer.write("- ${item.name}: 员工姓名\n");
            writer.write("- ${item.department}: 部门\n");
            writer.write("- ${item.salary}: 薪资\n");
            writer.write("- ${/foreach}: 循环结束标记\n");
            writer.write("- ${totalCount}: 总人数变量\n");
        }
    }
    
    /**
     * 员工实体类
     */
    public static class Employee {
        private String empNo;
        private String name;
        private String department;
        private Double salary;
        
        // getter和setter方法
        public String getEmpNo() { return empNo; }
        public void setEmpNo(String empNo) { this.empNo = empNo; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getDepartment() { return department; }
        public void setDepartment(String department) { this.department = department; }
        
        public Double getSalary() { return salary; }
        public void setSalary(Double salary) { this.salary = salary; }
    }
}