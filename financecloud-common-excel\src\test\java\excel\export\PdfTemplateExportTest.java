package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.pdf.PdfTemplateConfig;
import org.junit.Test;

import java.util.*;

/**
 * PDF模板导出测试类
 */
public class PdfTemplateExportTest {

    /**
     * 测试基本PDF模板导出
     */
    @Test
    public void testBasicPdfTemplateExport() {
        System.out.println("=== 测试基本PDF模板导出 ===");
        
        try {
            // 首先创建一个Excel模板
            String templatePath = "src/test/java/excel/export/excel/pdfTemplate.xlsx";
            createExcelTemplate(templatePath);
            
            // 准备变量数据
            Map<String, Object> variables = new HashMap<>();
            variables.put("companyName", "测试科技有限公司");
            variables.put("reportTitle", "员工信息报表");
            variables.put("reportDate", "2025年08月21日");
            variables.put("reportPerson", "系统管理员");
            
            // 准备循环数据
            List<Map<String, Object>> employees = createEmployeeData();
            
            // 使用PDF模板导出
            ExcelHelper.opsPdfTemplate(templatePath)
                    .vars(variables)
                    .loop("employees", employees)
                    .fontSize(12f)
                    .enableChineseFont(true)
                    .orientation(PdfTemplateConfig.PageOrientation.PORTRAIT)
                    .pageSize(PdfTemplateConfig.PageSize.A4)
                    .margins(20f)
                    .showGridLines(true)
                    .exportTo("src/test/java/excel/export/excel/pdfTemplateResult.pdf");
            
            System.out.println("PDF模板导出成功！");
            System.out.println("模板文件: " + templatePath);
            System.out.println("输出文件: src/test/java/excel/export/excel/pdfTemplateResult.pdf");
            
        } catch (Exception e) {
            System.out.println("PDF模板导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试复杂PDF模板导出
     */
    @Test
    public void testComplexPdfTemplateExport() {
        System.out.println("=== 测试复杂PDF模板导出 ===");
        
        try {
            // 创建复杂模板
            String templatePath = "src/test/java/excel/export/excel/complexPdfTemplate.xlsx";
            createComplexExcelTemplate(templatePath);
            
            // 准备数据
            Map<String, Object> variables = new HashMap<>();
            variables.put("companyName", "金融云科技有限公司");
            variables.put("reportTitle", "月度销售报表");
            variables.put("reportDate", "2025年08月");
            variables.put("reportPerson", "财务部");
            variables.put("totalAmount", "¥1,250,000.00");
            variables.put("totalOrders", "156");
            
            // 销售数据
            List<Map<String, Object>> salesData = createSalesData();
            
            // 部门数据
            List<Map<String, Object>> departments = createDepartmentData();
            
            // 导出PDF
            ExcelHelper.opsPdfTemplate(templatePath)
                    .vars(variables)
                    .loop("sales", salesData)
                    .loop("departments", departments)
                    .fontSize(10f)
                    .enableChineseFont(true)
                    .orientation(PdfTemplateConfig.PageOrientation.LANDSCAPE)
                    .pageSize(PdfTemplateConfig.PageSize.A4)
                    .margins(15f)
                    .showGridLines(true)
                    .headerBackgroundColor(0xE6F3FF)
                    .headerFontColor(0x003366)
                    .exportTo("src/test/java/excel/export/excel/complexPdfTemplateResult.pdf");
            
            System.out.println("复杂PDF模板导出成功！");
            
        } catch (Exception e) {
            System.out.println("复杂PDF模板导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试流式PDF模板导出
     */
    @Test
    public void testStreamPdfTemplateExport() {
        System.out.println("=== 测试流式PDF模板导出 ===");
        
        try {
            // 创建简单模板
            String templatePath = "src/test/java/excel/export/excel/streamPdfTemplate.xlsx";
            createSimpleExcelTemplate(templatePath);
            
            // 使用输入流读取模板
            java.io.FileInputStream templateStream = new java.io.FileInputStream(templatePath);
            
            // 准备数据
            List<Map<String, Object>> products = createProductData();
            
            // 使用输出流导出
            java.io.FileOutputStream outputStream = new java.io.FileOutputStream(
                    "src/test/java/excel/export/excel/streamPdfTemplateResult.pdf");
            
            ExcelHelper.opsPdfTemplate(templateStream)
                    .var("title", "产品清单")
                    .var("date", new Date().toString())
                    .loop("products", products)
                    .fontSize(11f)
                    .enableChineseFont(true)
                    .exportTo(outputStream);
            
            templateStream.close();
            outputStream.close();
            
            System.out.println("流式PDF模板导出成功！");
            
        } catch (Exception e) {
            System.out.println("流式PDF模板导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建Excel模板
     */
    private void createExcelTemplate(String templatePath) {
        try {
            // 创建模板数据
            List<Map<String, Object>> templateData = new ArrayList<>();
            
            // 添加标题行
            Map<String, Object> titleRow = new HashMap<>();
            titleRow.put("col1", "${companyName}");
            titleRow.put("col2", "");
            titleRow.put("col3", "");
            templateData.add(titleRow);
            
            // 添加报表标题行
            Map<String, Object> reportTitleRow = new HashMap<>();
            reportTitleRow.put("col1", "${reportTitle}");
            reportTitleRow.put("col2", "");
            reportTitleRow.put("col3", "日期: ${reportDate}");
            templateData.add(reportTitleRow);
            
            // 添加表头行
            Map<String, Object> headerRow = new HashMap<>();
            headerRow.put("col1", "员工姓名");
            headerRow.put("col2", "部门");
            headerRow.put("col3", "职位");
            templateData.add(headerRow);
            
            // 添加循环标记行
            Map<String, Object> loopStartRow = new HashMap<>();
            loopStartRow.put("col1", "${#foreach employees}");
            loopStartRow.put("col2", "");
            loopStartRow.put("col3", "");
            templateData.add(loopStartRow);
            
            // 添加数据行模板
            Map<String, Object> dataRow = new HashMap<>();
            dataRow.put("col1", "${name}");
            dataRow.put("col2", "${department}");
            dataRow.put("col3", "${position}");
            templateData.add(dataRow);
            
            // 添加循环结束标记行
            Map<String, Object> loopEndRow = new HashMap<>();
            loopEndRow.put("col1", "${/foreach}");
            loopEndRow.put("col2", "");
            loopEndRow.put("col3", "");
            templateData.add(loopEndRow);
            
            // 导出模板
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("列1", "列2", "列3")
                        .done()
                    .opsColumn()
                        .fields("col1", "col2", "col3")
                        .done()
                    .done()
                    .export(templatePath);
            
        } catch (Exception e) {
            System.out.println("创建Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 创建复杂Excel模板
     */
    private void createComplexExcelTemplate(String templatePath) {
        try {
            List<Map<String, Object>> templateData = new ArrayList<>();
            
            // 公司标题
            Map<String, Object> row1 = new HashMap<>();
            row1.put("col1", "${companyName}");
            row1.put("col2", "");
            row1.put("col3", "");
            row1.put("col4", "");
            row1.put("col5", "");
            templateData.add(row1);
            
            // 报表标题
            Map<String, Object> row2 = new HashMap<>();
            row2.put("col1", "${reportTitle}");
            row2.put("col2", "");
            row2.put("col3", "");
            row2.put("col4", "报表日期: ${reportDate}");
            row2.put("col5", "制表人: ${reportPerson}");
            templateData.add(row2);
            
            // 汇总信息
            Map<String, Object> row3 = new HashMap<>();
            row3.put("col1", "总金额: ${totalAmount}");
            row3.put("col2", "");
            row3.put("col3", "总订单数: ${totalOrders}");
            row3.put("col4", "");
            row3.put("col5", "");
            templateData.add(row3);
            
            // 销售明细表头
            Map<String, Object> row4 = new HashMap<>();
            row4.put("col1", "销售明细");
            row4.put("col2", "");
            row4.put("col3", "");
            row4.put("col4", "");
            row4.put("col5", "");
            templateData.add(row4);
            
            Map<String, Object> row5 = new HashMap<>();
            row5.put("col1", "产品名称");
            row5.put("col2", "销售数量");
            row5.put("col3", "单价");
            row5.put("col4", "金额");
            row5.put("col5", "销售员");
            templateData.add(row5);
            
            // 销售数据循环
            Map<String, Object> row6 = new HashMap<>();
            row6.put("col1", "${#foreach sales}");
            row6.put("col2", "");
            row6.put("col3", "");
            row6.put("col4", "");
            row6.put("col5", "");
            templateData.add(row6);
            
            Map<String, Object> row7 = new HashMap<>();
            row7.put("col1", "${productName}");
            row7.put("col2", "${quantity}");
            row7.put("col3", "${price}");
            row7.put("col4", "${amount}");
            row7.put("col5", "${salesperson}");
            templateData.add(row7);
            
            Map<String, Object> row8 = new HashMap<>();
            row8.put("col1", "${/foreach}");
            row8.put("col2", "");
            row8.put("col3", "");
            row8.put("col4", "");
            row8.put("col5", "");
            templateData.add(row8);
            
            // 导出模板
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("列1", "列2", "列3", "列4", "列5")
                        .done()
                    .opsColumn()
                        .fields("col1", "col2", "col3", "col4", "col5")
                        .done()
                    .done()
                    .export(templatePath);
            
        } catch (Exception e) {
            System.out.println("创建复杂Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 创建简单Excel模板
     */
    private void createSimpleExcelTemplate(String templatePath) {
        try {
            List<Map<String, Object>> templateData = new ArrayList<>();
            
            Map<String, Object> row1 = new HashMap<>();
            row1.put("col1", "${title}");
            row1.put("col2", "${date}");
            templateData.add(row1);
            
            Map<String, Object> row2 = new HashMap<>();
            row2.put("col1", "产品名称");
            row2.put("col2", "价格");
            templateData.add(row2);
            
            Map<String, Object> row3 = new HashMap<>();
            row3.put("col1", "${#foreach products}");
            row3.put("col2", "");
            templateData.add(row3);
            
            Map<String, Object> row4 = new HashMap<>();
            row4.put("col1", "${name}");
            row4.put("col2", "${price}");
            templateData.add(row4);
            
            Map<String, Object> row5 = new HashMap<>();
            row5.put("col1", "${/foreach}");
            row5.put("col2", "");
            templateData.add(row5);
            
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("列1", "列2")
                        .done()
                    .opsColumn()
                        .fields("col1", "col2")
                        .done()
                    .done()
                    .export(templatePath);
            
        } catch (Exception e) {
            System.out.println("创建简单Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 创建员工数据
     */
    private List<Map<String, Object>> createEmployeeData() {
        List<Map<String, Object>> employees = new ArrayList<>();
        
        String[] names = {"张三", "李四", "王五", "赵六", "钱七"};
        String[] departments = {"技术部", "销售部", "财务部", "人事部", "市场部"};
        String[] positions = {"工程师", "销售经理", "会计", "HR专员", "市场专员"};
        
        for (int i = 0; i < names.length; i++) {
            Map<String, Object> employee = new HashMap<>();
            employee.put("name", names[i]);
            employee.put("department", departments[i]);
            employee.put("position", positions[i]);
            employees.add(employee);
        }
        
        return employees;
    }

    /**
     * 创建销售数据
     */
    private List<Map<String, Object>> createSalesData() {
        List<Map<String, Object>> sales = new ArrayList<>();
        
        String[] products = {"笔记本电脑", "台式机", "显示器", "键盘", "鼠标"};
        int[] quantities = {50, 30, 80, 120, 150};
        double[] prices = {5000.0, 3000.0, 1200.0, 200.0, 100.0};
        String[] salespeople = {"张销售", "李销售", "王销售", "赵销售", "钱销售"};
        
        for (int i = 0; i < products.length; i++) {
            Map<String, Object> sale = new HashMap<>();
            sale.put("productName", products[i]);
            sale.put("quantity", quantities[i]);
            sale.put("price", "¥" + prices[i]);
            sale.put("amount", "¥" + (quantities[i] * prices[i]));
            sale.put("salesperson", salespeople[i]);
            sales.add(sale);
        }
        
        return sales;
    }

    /**
     * 创建部门数据
     */
    private List<Map<String, Object>> createDepartmentData() {
        List<Map<String, Object>> departments = new ArrayList<>();
        
        String[] deptNames = {"技术部", "销售部", "财务部"};
        int[] headcounts = {25, 15, 8};
        
        for (int i = 0; i < deptNames.length; i++) {
            Map<String, Object> dept = new HashMap<>();
            dept.put("name", deptNames[i]);
            dept.put("headcount", headcounts[i]);
            departments.add(dept);
        }
        
        return departments;
    }

    /**
     * 创建产品数据
     */
    private List<Map<String, Object>> createProductData() {
        List<Map<String, Object>> products = new ArrayList<>();
        
        String[] names = {"产品A", "产品B", "产品C"};
        String[] prices = {"¥100", "¥200", "¥300"};
        
        for (int i = 0; i < names.length; i++) {
            Map<String, Object> product = new HashMap<>();
            product.put("name", names[i]);
            product.put("price", prices[i]);
            products.add(product);
        }
        
        return products;
    }
}
