package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.collection.ListUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;

import java.util.List;


@Slf4j
public abstract class AbstractMqConfig implements ApplicationListener<ApplicationStartedEvent> {






    public abstract String moduleName();

    public List<String> otherModuleName() {
        return ListUtil.empty();
    }





    public void onApplicationEvent(ApplicationStartedEvent event) {
        // 注册当前模块的消息通道
        RedisJob.registerJob(moduleName());
        // 额外注册OA 消息通道 发往OA 模块
        RedisJob.registerOtherModuleJobs(otherModuleName());

        // 注册管理消息通道
        RedisJob.registerMqAdminEvent(moduleName());


    }



}
