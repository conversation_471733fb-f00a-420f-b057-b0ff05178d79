package com.lg.dao.core;

import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实体信息统一管理器
 * 解决 EntityInfo 重复创建和缓存不统一的问题
 */
@Slf4j
public class EntityInfoManager {

    private static volatile EntityInfoManager instance;
    private static volatile UnifiedCacheManager globalCacheManager;

    // 本地缓存作为后备方案（当统一缓存未启用时）
    private static final Map<Class<?>, EntityInfo> LOCAL_CACHE = new ConcurrentHashMap<>();

    private EntityInfoManager() {
        // 私有构造器
    }

    /**
     * 初始化缓存管理器（由 Spring 自动配置调用）
     */
    public static void initializeCacheManager(UnifiedCacheManager cacheManager) {
        if (globalCacheManager == null) {
            synchronized (EntityInfoManager.class) {
                if (globalCacheManager == null) {
                    globalCacheManager = cacheManager;
                    log.info("EntityInfoManager 缓存管理器初始化完成: {}",
                            cacheManager != null ? "统一缓存已启用" : "使用本地缓存");
                }
            }
        }
    }

    /**
     * 获取单例实例
     */
    public static EntityInfoManager getInstance() {
        if (instance == null) {
            synchronized (EntityInfoManager.class) {
                if (instance == null) {
                    instance = new EntityInfoManager();
                }
            }
        }
        return instance;
    }

    /**
     * 统一获取实体信息的方法
     * 所有组件都应该通过这个方法获取 EntityInfo
     *
     * 策略：优先使用统一缓存管理器，确保避免重复解析
     */
    public EntityInfo getEntityInfo(Class<?> entityClass) {
        if (entityClass == null) {
            throw new IllegalArgumentException("Entity class cannot be null");
        }

        // 如果启用了统一缓存，使用统一缓存管理器
        if (globalCacheManager != null) {
            String cacheKey = entityClass.getName();
            return globalCacheManager.get(
                UnifiedCacheManager.CacheType.ENTITY_CACHE,
                cacheKey,
                () -> EntityInfo.of(entityClass)
            );
        }

        // 回退到本地缓存（确保避免重复解析）
        return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
    }
    
    /**
     * 预加载实体信息
     * 用于应用启动时预热缓存
     */
    public void preloadEntityInfo(Class<?>... entityClasses) {
        if (entityClasses == null || entityClasses.length == 0) {
            return;
        }
        
        log.info("预加载实体信息，数量: {}", entityClasses.length);
        for (Class<?> entityClass : entityClasses) {
            try {
                getEntityInfo(entityClass);
                log.debug("预加载实体信息成功: {}", entityClass.getName());
            } catch (Exception e) {
                log.warn("预加载实体信息失败: {}", entityClass.getName(), e);
            }
        }
        log.info("实体信息预加载完成");
    }
    
    /**
     * 清除指定实体的缓存
     */
    public void evictEntityInfo(Class<?> entityClass) {
        if (entityClass == null) {
            return;
        }

        // 清除本地缓存
        LOCAL_CACHE.remove(entityClass);

        // 清除统一缓存
        if (globalCacheManager != null) {
            String cacheKey = entityClass.getName();
            globalCacheManager.remove(UnifiedCacheManager.CacheType.ENTITY_CACHE, cacheKey);
            log.debug("清除实体信息缓存: {}", cacheKey);
        }
    }
    
    /**
     * 清除所有实体信息缓存
     */
    public void evictAllEntityInfo() {
        int localCacheSize = LOCAL_CACHE.size();

        // 清除本地缓存
        LOCAL_CACHE.clear();

        // 清除统一缓存
        if (globalCacheManager != null) {
            globalCacheManager.clear(UnifiedCacheManager.CacheType.ENTITY_CACHE);
        }

        log.info("清除所有实体信息缓存，本地缓存数量: {}", localCacheSize);
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("EntityInfo 缓存统计:\n");
        stats.append("本地缓存大小: ").append(LOCAL_CACHE.size()).append("\n");

        if (globalCacheManager != null) {
            stats.append("统一缓存: 已启用 (").append(UnifiedCacheManager.CacheType.ENTITY_CACHE.getName()).append(")\n");
            try {
                // 可以调用 globalCacheManager.logCacheStats() 来输出详细统计
                stats.append("详细统计请查看日志输出\n");
            } catch (Exception e) {
                stats.append("统一缓存统计获取失败: ").append(e.getMessage()).append("\n");
            }
        } else {
            stats.append("统一缓存: 未启用，使用本地缓存\n");
        }

        if (!LOCAL_CACHE.isEmpty()) {
            stats.append("已缓存的实体类:\n");
            LOCAL_CACHE.keySet().forEach(clazz ->
                stats.append("- ").append(clazz.getSimpleName()).append("\n"));
        }

        return stats.toString();
    }
    
    /**
     * 检查实体信息是否已缓存
     */
    public boolean isEntityInfoCached(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        // 检查本地缓存
        if (LOCAL_CACHE.containsKey(entityClass)) {
            return true;
        }

        // 如果启用了统一缓存，也检查统一缓存
        if (globalCacheManager != null) {
            String cacheKey = entityClass.getName();
            // 简化检查，通过尝试获取来判断
            try {
                Object cached = globalCacheManager.get(
                    UnifiedCacheManager.CacheType.ENTITY_CACHE,
                    cacheKey,
                    () -> null  // 不创建新的，只检查是否存在
                );
                return cached != null;
            } catch (Exception e) {
                log.debug("检查统一缓存失败: {}", entityClass.getName(), e);
                return false;
            }
        }

        return false;
    }
}
