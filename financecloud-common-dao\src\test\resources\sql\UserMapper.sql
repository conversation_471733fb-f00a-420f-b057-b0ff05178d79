#selectUserListByIds
select id ,  user_name userName,job_number jobNumber from sys_eve_user_staff where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>


#getMultiLevelManagers
WITH RECURSIVE DeptHierarchy AS (

    SELECT
    u.id,
    u.job_number jobNumber,
    u.user_id,
    p.department_id as dept_id,
    p.department_id AS current_dept_id,
    d.parent_id,
    0 AS level
    FROM sys_eve_user_staff u
    JOIN sys_position p ON u.major_position_id = p.id
    JOIN sys_dept d ON p.department_id = d.dept_id  -- 假设岗位关联部门
    WHERE u.id = #{userId}

    UNION ALL


    SELECT
   dh.id,
    dh.jobNumber,
    dh.user_id,
    d.dept_id,
    d.dept_id AS current_dept_id,
    d.parent_id,
    dh.level + 1
    FROM DeptHierarchy dh
    JOIN sys_dept d ON dh.parent_id = d.dept_id
    WHERE dh.level < #{level}
    ),
    SupervisorFinder AS (
    -- 获取每个层级部门的管理岗人员
    SELECT
     dh.id,
    dh.jobNumber,
    dh.user_id,
    dh.level,
    u.user_id AS supervisor_id
    FROM DeptHierarchy dh
    JOIN sys_position p ON dh.current_dept_id = p.department_id
    JOIN sys_eve_user_staff u ON p.id = u.major_position_id
    WHERE p.manager_flag = 1
    )
SELECT
    id,
    user_id,
    MAX(CASE WHEN level = 1 THEN supervisor_id END) AS level1_supervisor,
    MAX(CASE WHEN level = 2 THEN supervisor_id END) AS level2_supervisor,
    MAX(CASE WHEN level = 3 THEN supervisor_id END) AS level3_supervisor,
    MAX(CASE WHEN level = 4 THEN supervisor_id END) AS level4_supervisor
FROM SupervisorFinder
GROUP BY user_id

#getEmployeePage
SELECT
	eus.id,
	eus.user_name employeeName,
	eus.job_number jobNumber,
	sc.name companyName,
	sd.name deptName,
	sp.name positionName

FROM
	sys_eve_user_staff eus
	LEFT JOIN sys_company sc ON eus.company_id = sc.id
	LEFT JOIN sys_dept sd ON sd.dept_id = eus.department_id
	left join sys_position sp on sp.id = eus.major_position_id
	where eus.del_flag = '0'
	<if test="organizationType != null and organizationType == 1">
      and eus.company_id = #{organizationId}
  </if>
	<if test="organizationType != null and organizationType == 2">
      and eus.department_id = #{organizationId}
  </if>
	<if test="organizationType != null and organizationType == 3">
      and eus.major_position_id = #{organizationId}
  </if>
	<if test="positionList != null">
      and eus.major_position_id in (
        <foreach item="item" collection="positionList" separator="," open="(" close=")">
                #{item}
        </foreach>
      )
  </if>
order by eus.job_number ASC


