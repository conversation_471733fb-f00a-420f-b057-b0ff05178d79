package com.lg.financecloud.common.redis.lock;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 分布式锁自动配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({RedissonClient.class})
@ConditionalOnProperty(prefix = "light.lock", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(DistributedLockProperties.class)
@AutoConfigureAfter(name = "com.lg.financecloud.common.redis.cache.RedisTemplateConfig")
public class DistributedLockAutoConfiguration {

    /**
     * 分布式锁管理器
     */
    @Bean
    @ConditionalOnBean(RedissonClient.class)
    @ConditionalOnMissingBean(DistributedLockManager.class)
    public DistributedLockManager distributedLockManager(RedissonClient redissonClient,
                                                        DistributedLockProperties properties) {
        log.info("初始化分布式锁管理器，配置: {}", properties);
        return new DistributedLockManager(redissonClient, properties);
    }
}
