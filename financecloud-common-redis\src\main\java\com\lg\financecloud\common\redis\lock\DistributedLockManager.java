package com.lg.financecloud.common.redis.lock;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 改进的分布式锁管理器
 * 提供更安全、更易用的分布式锁操作
 *
 * <AUTHOR>
 */
@Slf4j
public class DistributedLockManager {

    private final RedissonClient redissonClient;
    private final DistributedLockProperties properties;

    public DistributedLockManager(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
        this.properties = new DistributedLockProperties(); // 使用默认配置
    }

    public DistributedLockManager(RedissonClient redissonClient, DistributedLockProperties properties) {
        this.redissonClient = redissonClient;
        this.properties = properties;
    }
    
    /**
     * 尝试获取锁并执行业务逻辑
     *
     * @param lockKey 锁键
     * @param supplier 业务逻辑
     * @param <T> 返回类型
     * @return 业务逻辑执行结果
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> supplier) {
        return executeWithLock(lockKey, properties.getDefaultWaitTime(), properties.getDefaultLeaseTime(), supplier);
    }
    
    /**
     * 尝试获取锁并执行业务逻辑
     * 
     * @param lockKey 锁键
     * @param waitTime 等待时间（毫秒）
     * @param leaseTime 租约时间（毫秒）
     * @param supplier 业务逻辑
     * @param <T> 返回类型
     * @return 业务逻辑执行结果
     */
    public <T> T executeWithLock(String lockKey, long waitTime, long leaseTime, Supplier<T> supplier) {
        if (StrUtil.isBlank(lockKey)) {
            throw new IllegalArgumentException("Lock key cannot be null or empty");
        }
        
        String fullLockKey = properties.getPrefix() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        
        try {
            boolean acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
            if (!acquired) {
                log.warn("Failed to acquire lock: {}", fullLockKey);
                throw new LockAcquisitionException("Failed to acquire lock: " + lockKey);
            }
            
            log.debug("Successfully acquired lock: {}", fullLockKey);
            return supplier.get();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Thread interrupted while trying to acquire lock: {}", fullLockKey);
            throw new LockAcquisitionException("Thread interrupted while acquiring lock: " + lockKey, e);
        } catch (Exception e) {
            log.error("Error occurred while executing with lock: {}", fullLockKey, e);
            throw new LockExecutionException("Error occurred while executing with lock: " + lockKey, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.debug("Successfully released lock: {}", fullLockKey);
                } catch (Exception e) {
                    log.error("Failed to release lock: {}", fullLockKey, e);
                }
            }
        }
    }
    
    /**
     * 尝试获取锁并执行业务逻辑（无返回值）
     * 
     * @param lockKey 锁键
     * @param runnable 业务逻辑
     */
    public void executeWithLock(String lockKey, Runnable runnable) {
        executeWithLock(lockKey, () -> {
            runnable.run();
            return null;
        });
    }
    
    /**
     * 尝试获取锁并执行业务逻辑（无返回值）
     * 
     * @param lockKey 锁键
     * @param waitTime 等待时间（毫秒）
     * @param leaseTime 租约时间（毫秒）
     * @param runnable 业务逻辑
     */
    public void executeWithLock(String lockKey, long waitTime, long leaseTime, Runnable runnable) {
        executeWithLock(lockKey, waitTime, leaseTime, () -> {
            runnable.run();
            return null;
        });
    }
    
    /**
     * 检查锁是否被当前线程持有
     * 
     * @param lockKey 锁键
     * @return 是否被当前线程持有
     */
    public boolean isLockedByCurrentThread(String lockKey) {
        if (StrUtil.isBlank(lockKey)) {
            return false;
        }
        
        String fullLockKey = properties.getPrefix() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        return lock.isHeldByCurrentThread();
    }
    
    /**
     * 检查锁是否被任何线程持有
     * 
     * @param lockKey 锁键
     * @return 是否被持有
     */
    public boolean isLocked(String lockKey) {
        if (StrUtil.isBlank(lockKey)) {
            return false;
        }
        
        String fullLockKey = properties.getPrefix() + lockKey;
        RLock lock = redissonClient.getLock(fullLockKey);
        return lock.isLocked();
    }
}
