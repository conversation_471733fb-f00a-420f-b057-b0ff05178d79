# 灵活缓存配置指南

## 设计理念

支持每种缓存类型有自己的缓存大小和失效时间，同时保持配置的简洁性：

✅ **灵活配置** - 每种缓存类型可以独立配置  
✅ **默认值支持** - 未配置的缓存类型使用默认值  
✅ **简洁语法** - 配置语法简单直观  
✅ **类型安全** - 强类型配置，避免错误  

## 配置结构

```java
@ConfigurationProperties(prefix = "light.orm.cache")
public class CacheProperties {
    private boolean enable = false;
    private int defaultMaxSize = 100;
    private int defaultExpireSeconds = 1800;
    private Map<String, CacheConfig> types = new HashMap<>();
    
    public static class CacheConfig {
        private int maxSize;
        private int expireSeconds;
    }
}
```

## 配置方式

### 1. 基本配置（使用默认值）

```yaml
light:
  orm:
    cache:
      enable: true
      default-max-size: 100
      default-expire-seconds: 1800
```

### 2. 完整配置（每种缓存类型独立配置）

```yaml
light:
  orm:
    cache:
      enable: true
      # 默认配置
      default-max-size: 100
      default-expire-seconds: 1800
      
      # 各缓存类型的具体配置
      types:
        # 实体信息缓存（相对稳定，可以缓存更久）
        entity:
          max-size: 200
          expire-seconds: 3600
        
        # SQL模板缓存（基本不变，长期缓存）
        sql_template:
          max-size: 50
          expire-seconds: 7200
        
        # DAO操作缓存（变化较频繁，短期缓存）
        dao:
          max-size: 100
          expire-seconds: 600
        
        # 权限缓存（中等稳定性）
        permission:
          max-size: 50
          expire-seconds: 1800
        
        # 元数据缓存（基本不变）
        metadata:
          max-size: 30
          expire-seconds: 7200
        
        # MyBatis代理缓存（中等稳定性）
        mybatis_proxy:
          max-size: 50
          expire-seconds: 1800
```

### 3. 部分配置（只配置需要特殊处理的缓存类型）

```yaml
light:
  orm:
    cache:
      enable: true
      default-max-size: 100
      default-expire-seconds: 1800
      
      types:
        # 只配置需要特殊处理的缓存类型
        entity:
          max-size: 300
          expire-seconds: 3600
        sql_template:
          max-size: 50
          expire-seconds: 7200
        # 其他缓存类型使用默认配置
```

## 缓存类型说明

| 缓存类型 | 用途 | 推荐大小 | 推荐过期时间 | 说明 |
|---------|------|----------|-------------|------|
| `entity` | 实体信息缓存 | 200-500 | 3600-7200秒 | 相对稳定，可长期缓存 |
| `sql_template` | SQL模板缓存 | 50-100 | 7200-14400秒 | 基本不变，长期缓存 |
| `dao` | DAO操作缓存 | 100-200 | 600-1800秒 | 变化频繁，短期缓存 |
| `permission` | 权限缓存 | 50-100 | 1800-3600秒 | 中等稳定性 |
| `metadata` | 元数据缓存 | 30-50 | 7200-14400秒 | 基本不变 |
| `mybatis_proxy` | MyBatis代理缓存 | 50-100 | 1800-3600秒 | 中等稳定性 |

## 环境配置示例

### 开发环境（较小缓存，快速过期）

```yaml
spring:
  profiles: dev

light:
  orm:
    cache:
      enable: true
      default-max-size: 50
      default-expire-seconds: 900
      types:
        entity:
          max-size: 100
          expire-seconds: 1800
        sql_template:
          max-size: 30
          expire-seconds: 3600
```

### 生产环境（较大缓存，长期缓存）

```yaml
spring:
  profiles: prod

light:
  orm:
    cache:
      enable: true
      default-max-size: 200
      default-expire-seconds: 3600
      types:
        entity:
          max-size: 500
          expire-seconds: 7200
        sql_template:
          max-size: 100
          expire-seconds: 14400
        dao:
          max-size: 300
          expire-seconds: 1800
```

### 测试环境（禁用缓存）

```yaml
spring:
  profiles: test

light:
  orm:
    cache:
      enable: false
```

## 编程式配置

```java
@Configuration
public class CacheConfig {
    
    @Bean
    public UnifiedCacheManager cacheManager() {
        CacheProperties properties = new CacheProperties();
        properties.setEnable(true);
        properties.setDefaultMaxSize(100);
        properties.setDefaultExpireSeconds(1800);
        
        // 配置特定缓存类型
        Map<String, CacheProperties.CacheConfig> types = properties.getTypes();
        types.put("entity", new CacheProperties.CacheConfig(200, 3600));
        types.put("sql_template", new CacheProperties.CacheConfig(50, 7200));
        types.put("dao", new CacheProperties.CacheConfig(100, 600));
        
        return new UnifiedCacheManager(properties);
    }
}
```

## 配置验证

```java
@Test
public void testCacheConfiguration() {
    CacheProperties properties = new CacheProperties();
    properties.setEnable(true);
    properties.setDefaultMaxSize(100);
    properties.setDefaultExpireSeconds(1800);
    
    // 配置实体缓存
    properties.getTypes().put("entity", 
        new CacheProperties.CacheConfig(200, 3600));
    
    UnifiedCacheManager manager = new UnifiedCacheManager(properties);
    manager.afterPropertiesSet();
    
    // 验证实体缓存使用特定配置
    String result = manager.get(
        UnifiedCacheManager.CacheType.ENTITY_CACHE,
        "test-key",
        () -> "test-value"
    );
    assertEquals("test-value", result);
    
    // 验证DAO缓存使用默认配置
    String daoResult = manager.get(
        UnifiedCacheManager.CacheType.DAO_CACHE,
        "dao-key",
        () -> "dao-value"
    );
    assertEquals("dao-value", daoResult);
}
```

## 配置优先级

1. **特定配置** - `types` 中的配置优先级最高
2. **默认配置** - `default-*` 配置作为后备
3. **硬编码默认值** - 代码中的默认值作为最后后备

## 最佳实践

### 1. 根据数据特性配置

- **稳定数据**（如实体信息、SQL模板）：大缓存 + 长过期时间
- **变化数据**（如DAO操作结果）：小缓存 + 短过期时间
- **中等数据**（如权限信息）：中等配置

### 2. 环境差异化配置

- **开发环境**：小缓存，便于调试
- **测试环境**：禁用缓存，确保数据实时性
- **生产环境**：大缓存，提升性能

### 3. 监控和调优

```java
// 定期打印缓存统计
@Scheduled(fixedRate = 300000)
public void printCacheStats() {
    cacheManager.printStats();
}
```

## 总结

这种设计既保持了配置的灵活性，又避免了过度复杂：

✅ **灵活** - 每种缓存类型可以独立配置  
✅ **简洁** - 配置语法简单，易于理解  
✅ **实用** - 支持默认值，减少配置工作量  
✅ **可维护** - 类型安全，避免配置错误  

完美平衡了灵活性和简洁性的需求。
