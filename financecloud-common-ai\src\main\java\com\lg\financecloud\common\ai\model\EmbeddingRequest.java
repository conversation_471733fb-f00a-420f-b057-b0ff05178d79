package com.lg.financecloud.common.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 向量嵌入请求模型
 * 用于文本向量化的标准请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingRequest {
    
    /**
     * 模型类型，如openai、qwen等
     */
    private String modelType;
    
    /**
     * 具体模型名称，如text-embedding-ada-002等
     */
    private String modelName;
    
    /**
     * 需要向量化的文本内容
     * 可以是单条文本或多条文本列表
     */
    private Object inputTexts;
    
    /**
     * 向量维度
     * 部分模型支持指定向量维度
     */
    private Integer dimensions;
    
    /**
     * 是否需要归一化处理
     * 默认为true
     */
    @Builder.Default
    private Boolean normalize = true;
    
    /**
     * 业务数据，可以是任何类型的业务相关数据
     */
    private Object businessData;
}