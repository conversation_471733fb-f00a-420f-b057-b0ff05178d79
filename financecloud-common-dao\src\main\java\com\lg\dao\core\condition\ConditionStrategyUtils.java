package com.lg.dao.core.condition;

import java.util.Collection;

/**
 * 条件策略工具类
 * 用于判断根据给定的策略和值，是否应该添加查询条件
 */
public class ConditionStrategyUtils {
    
    /**
     * 判断是否应该添加条件
     * 
     * @param value 要检查的值
     * @param strategy 条件策略
     * @return true表示应该添加条件，false表示不应该添加
     */
    public static boolean shouldAddCondition(Object value, ConditionStrategy strategy) {
        if (strategy == null) {
            strategy = ConditionStrategy.ALWAYS;
        }
        
        switch (strategy) {
            case ALWAYS:
                return true;
                
            case IS_NOT_NULL:
                return value != null;
                
            case IS_NOT_EMPTY:
                if (value == null) {
                    return false;
                }
                if (value instanceof String) {
                    return !((String) value).isEmpty();
                }
                return true;
                
            case IS_NOT_BLANK:
                if (value == null) {
                    return false;
                }
                if (value instanceof String) {
                    String str = (String) value;
                    return !str.trim().isEmpty();
                }
                return true;
                
            case IS_NULL:
                return value == null;
                
            case IS_EMPTY:
                if (value == null) {
                    return true;
                }
                if (value instanceof String) {
                    return ((String) value).isEmpty();
                }
                return false;
                
            case IS_BLANK:
                if (value == null) {
                    return true;
                }
                if (value instanceof String) {
                    String str = (String) value;
                    return str.trim().isEmpty();
                }
                return false;
                
            case COLLECTION_NOT_EMPTY:
                if (value == null) {
                    return false;
                }
                if (value instanceof Collection) {
                    return !((Collection<?>) value).isEmpty();
                }
                if (value instanceof Object[]) {
                    return ((Object[]) value).length > 0;
                }
                return true;
                
            case COLLECTION_EMPTY:
                if (value == null) {
                    return true;
                }
                if (value instanceof Collection) {
                    return ((Collection<?>) value).isEmpty();
                }
                if (value instanceof Object[]) {
                    return ((Object[]) value).length == 0;
                }
                return false;
                
            default:
                return true;
        }
    }
    
    /**
     * 判断字符串是否为空白
     * 
     * @param str 要检查的字符串
     * @return true表示为空白，false表示不为空白
     */
    private static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空白
     * 
     * @param str 要检查的字符串
     * @return true表示不为空白，false表示为空白
     */
    private static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
}