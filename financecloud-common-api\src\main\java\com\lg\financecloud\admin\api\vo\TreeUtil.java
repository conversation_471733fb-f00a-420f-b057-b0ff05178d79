/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.admin.api.dto.DeptTree;
import com.lg.financecloud.admin.api.dto.MenuTree;
import com.lg.financecloud.admin.api.dto.TreeNode;
import com.lg.financecloud.admin.api.entity.SysMenu;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-09
 */
@UtilityClass
public class TreeUtil {

	/**
	 * 两层循环实现建树
	 * @param treeNodes 传入的树节点列表
	 * @return
	 */
	public <T extends TreeNode> List<T> build(List<T> treeNodes, Object root) {

		List<T> trees = new ArrayList<>();

		for (T treeNode : treeNodes) {

			if (String.valueOf(root).equals(treeNode.getParentId().toString())) {
				trees.add(treeNode);
			}

			if("0".equals(treeNode.getParentId().toString())){
				treeNode.setIdTree("/"+treeNode.getId());
				if(treeNode.getClass().equals(DeptTree.class)){
					((DeptTree)treeNode).setParentNameTree("/"+((DeptTree)treeNode).getName());
				}
			}

			for (T it : treeNodes) {
				if (it.getParentId() == treeNode.getId()||it.getParentId().toString().equals(treeNode.getId().toString())) {
					if (treeNode.getChildren() == null) {
						treeNode.setChildren(new ArrayList<>());
					}
					if(it.getClass().equals(DeptTree.class)){
						((DeptTree)it).setParentName(((DeptTree)treeNode).getName());
					}
					it.setIdTree(ObjectUtil.isEmpty(treeNode.getIdTree())?"/"+it.getId():treeNode.getIdTree()+"/"+it.getId());
					if(it.getClass().equals(DeptTree.class)){
						it.setParentNameTree(ObjectUtil.isEmpty(treeNode.getParentNameTree())?"/"+((DeptTree)it).getName():treeNode.getParentNameTree()+"/"+((DeptTree)it).getName());
					}

					treeNode.add(it);
				}
			}
		}
		return trees;
	}

	/**
	 * 两层循环实现建树
	 * @param treeNodes 传入的树节点列表
	 * @return
	 */
	public <T extends TreeNode> List<T> build(List<T> treeNodes, Object root, Map<Integer,T> map) {

		List<T> trees = new ArrayList<>();

		for (T treeNode : treeNodes) {

			if (root.equals(treeNode.getParentId())) {
				trees.add(treeNode);
			}

			for (T it : treeNodes) {
				if (it.getParentId() == treeNode.getId()) {
					if (treeNode.getChildren() == null) {
						treeNode.setChildren(new ArrayList<>());
					}
					treeNode.add(it);
				}
			}
		}
		return trees;
	}


	/**
	 * 使用递归方法建树
	 * @param treeNodes
	 * @return
	 */
	public <T extends TreeNode> List<T> buildByRecursive(List<T> treeNodes, Object root) {
		List<T> trees = new ArrayList<T>();
		for (T treeNode : treeNodes) {
			if (root.equals(treeNode.getParentId())) {
				trees.add(findChildren(treeNode, treeNodes));
			}
		}
		return trees;
	}

	/**
	 * 递归查找子节点
	 * @param treeNodes
	 * @return
	 */
	public <T extends TreeNode> T findChildren(T treeNode, List<T> treeNodes) {
		for (T it : treeNodes) {
			if (treeNode.getId() == it.getParentId()) {
				if (treeNode.getChildren() == null) {
					treeNode.setChildren(new ArrayList<>());
				}
				treeNode.add(findChildren(it, treeNodes));
			}
		}
		return treeNode;
	}

	/**
	 * 通过sysMenu创建树形节点
	 * @param menus
	 * @param root
	 * @return
	 */
	public List<MenuTree> buildTree(List<SysMenu> menus, int root) {
		List<MenuTree> trees = new ArrayList<>();
		MenuTree node;
		for (SysMenu menu : menus) {
			node = new MenuTree();
			node.setSource(menu.getSource());
			node.setId(menu.getMenuId());
			node.setParentId(menu.getParentId());
			node.setName(menu.getName());
			node.setPath(menu.getPath());
			node.setPermission(menu.getPermission());
			node.setLabel(menu.getName());
			node.setIcon(menu.getIcon());
			node.setType(menu.getType());
			node.setSort(menu.getSort());
			node.setHasChildren(false);
			node.setKeepAlive(menu.getKeepAlive());
			node.setState(menu.getState());
			trees.add(node);
		}
		return TreeUtil.build(trees, root);
	}
	/**
	 * 通过sysMenu创建树形节点
	 * @param menus
	 * @param root
	 * @return
	 */
	public List<MenuTree> buildTreeAndFilterIncludeData(List<SysMenu> menus, int root,List<Integer> topMenuId) {
		List<MenuTree> treeList = buildTree(menus, root);
		if (CollectionUtil.isNotEmpty(topMenuId)){
			return treeList.stream().filter(t->topMenuId.contains(t.getId())).collect(Collectors.toList());
		}
		return treeList;
	}

}
