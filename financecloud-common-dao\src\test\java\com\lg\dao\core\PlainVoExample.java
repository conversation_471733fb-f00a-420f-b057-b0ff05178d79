package com.lg.dao.core;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 普通VO对象映射示例
 * 演示EntityRowMapper对没有任何注解的普通POJO类的支持
 */
@Slf4j
public class PlainVoExample {

    /**
     * 用户信息VO - 普通POJO类，没有任何JPA或MyBatis-Plus注解
     */
    @Data
    public static class UserInfoVo {
        private Long userId;
        private String userName;
        private String email;
        private Integer age;
        private BigDecimal salary;
        private Date createTime;
        private LocalDateTime updateTime;
        private Boolean isActive;
        
        // 驼峰命名字段 - 会自动映射到下划线格式的数据库列
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private String homeAddress;
    }

    /**
     * 统计查询结果VO - 用于复杂查询结果映射
     */
    @Data
    public static class StatisticsVo {
        private String department;
        private Integer employeeCount;
        private BigDecimal totalSalary;
        private BigDecimal averageSalary;
        private BigDecimal maxSalary;
        private BigDecimal minSalary;
        private Date statisticsDate;
        
        // 计算字段
        private String departmentCode;
        private String summary;
    }

    /**
     * 订单详情VO - 包含多种数据类型
     */
    @Data
    public static class OrderDetailVo {
        private Long orderId;
        private String orderNumber;
        private String customerName;
        private String productName;
        private Integer quantity;
        private BigDecimal unitPrice;
        private BigDecimal totalAmount;
        private String orderStatus;
        private Date orderDate;
        private LocalDateTime deliveryTime;
        
        // 关联信息
        private String customerPhone;
        private String customerEmail;
        private String deliveryAddress;
        
        // 业务字段
        private Boolean isPaid;
        private Boolean isDelivered;
        private String remarks;
    }

    public static void main(String[] args) {
        try {
            log.info("=== 普通VO对象映射示例 ===");
            
            // 示例1: 用户信息VO
            demonstrateUserInfoVo();
            
            // 示例2: 统计查询结果VO
            demonstrateStatisticsVo();
            
            // 示例3: 订单详情VO
            demonstrateOrderDetailVo();
            
            log.info("=== 普通VO对象映射示例完成 ===");
            
        } catch (Exception e) {
            log.error("示例执行失败", e);
        }
    }

    private static void demonstrateUserInfoVo() {
        log.info("\n--- 用户信息VO映射示例 ---");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(UserInfoVo.class);
        log.info("表名: {}", entityInfo.getTableName()); // user_info_vo
        log.info("字段数量: {}", entityInfo.getFields().size());
        
        log.info("字段映射关系:");
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("  {} -> {}", field.getPropertyName(), field.getColumn());
        }
        
        // 验证特定字段的映射
        EntityInfo.FieldInfo phoneField = entityInfo.findFieldInfoByColumn("phone_number");
        if (phoneField != null) {
            log.info("phone_number列映射到属性: {}", phoneField.getPropertyName());
        }
        
        log.info("✅ 用户信息VO映射示例完成");
    }

    private static void demonstrateStatisticsVo() {
        log.info("\n--- 统计查询结果VO映射示例 ---");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(StatisticsVo.class);
        log.info("表名: {}", entityInfo.getTableName()); // statistics_vo
        log.info("字段数量: {}", entityInfo.getFields().size());
        
        // 这种VO通常用于复杂的统计查询，如：
        // SELECT 
        //   department,
        //   COUNT(*) as employee_count,
        //   SUM(salary) as total_salary,
        //   AVG(salary) as average_salary,
        //   MAX(salary) as max_salary,
        //   MIN(salary) as min_salary,
        //   CURRENT_DATE as statistics_date
        // FROM employees 
        // GROUP BY department
        
        log.info("适用的SQL查询场景:");
        log.info("  - 分组统计查询");
        log.info("  - 聚合函数查询");
        log.info("  - 多表关联查询");
        
        log.info("✅ 统计查询结果VO映射示例完成");
    }

    private static void demonstrateOrderDetailVo() {
        log.info("\n--- 订单详情VO映射示例 ---");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(OrderDetailVo.class);
        log.info("表名: {}", entityInfo.getTableName()); // order_detail_vo
        log.info("字段数量: {}", entityInfo.getFields().size());
        
        // 验证复杂字段的映射
        String[] complexFields = {"customerPhone", "customerEmail", "deliveryAddress", "isPaid", "isDelivered"};
        for (String fieldName : complexFields) {
            EntityInfo.FieldInfo field = entityInfo.getFieldInfo(fieldName);
            if (field != null) {
                log.info("  {} -> {}", fieldName, field.getColumn());
            }
        }
        
        // 这种VO通常用于复杂的关联查询，如：
        // SELECT 
        //   o.order_id,
        //   o.order_number,
        //   c.customer_name,
        //   p.product_name,
        //   od.quantity,
        //   od.unit_price,
        //   od.total_amount,
        //   o.order_status,
        //   o.order_date,
        //   o.delivery_time,
        //   c.customer_phone,
        //   c.customer_email,
        //   o.delivery_address,
        //   o.is_paid,
        //   o.is_delivered,
        //   o.remarks
        // FROM orders o
        // JOIN order_details od ON o.order_id = od.order_id
        // JOIN customers c ON o.customer_id = c.customer_id
        // JOIN products p ON od.product_id = p.product_id
        
        log.info("适用的SQL查询场景:");
        log.info("  - 多表关联查询");
        log.info("  - 复杂业务查询");
        log.info("  - 报表数据查询");
        
        log.info("✅ 订单详情VO映射示例完成");
    }
}

/**
 * 使用说明：
 * 
 * 1. 普通VO对象支持
 *    - 无需任何JPA或MyBatis-Plus注解
 *    - 自动将驼峰命名转换为下划线格式的数据库列名
 *    - 支持所有常见的Java数据类型
 * 
 * 2. 字段映射规则
 *    - userId -> user_id
 *    - userName -> user_name
 *    - phoneNumber -> phone_number
 *    - isActive -> is_active
 * 
 * 3. 支持的数据类型
 *    - 基本类型：int, long, boolean等
 *    - 包装类型：Integer, Long, Boolean等
 *    - 字符串：String
 *    - 日期时间：Date, LocalDateTime等
 *    - 数值类型：BigDecimal等
 * 
 * 4. 使用场景
 *    - 查询结果映射
 *    - 统计报表数据
 *    - 复杂关联查询结果
 *    - API响应对象
 * 
 * 5. 性能优势
 *    - 预构建字段映射表，O(1)查找效率
 *    - PropertyDescriptor缓存，减少反射开销
 *    - 智能列名匹配，支持多种格式
 */
