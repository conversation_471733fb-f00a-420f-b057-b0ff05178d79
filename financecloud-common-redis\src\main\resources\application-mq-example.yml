# MQ 任务队列配置示例
# 在 application.yml 中添加以下配置来启用/禁用 MQ 相关功能

light:
  mq:
    # 是否启用 MQ 功能 (默认: true)
    enabled: true
    
    # 任务恢复配置
    recovery:
      # 是否启用任务恢复功能 (默认: true)
      enabled: true

      # 恢复执行间隔，单位分钟 (默认: 5)
      interval-minutes: 5

      # 每次恢复处理的任务数量 (默认: 100)
      batch-size: 100

      # 最大处理时间，单位小时 (默认: 2)
      max-processing-hours: 2

      # 等待任务超时时间，单位分钟 (默认: 30)
      waiting-timeout-minutes: 30

      # 最大重试次数 (默认: 3)
      max-retry-attempts: 3

      # 分布式锁超时时间，单位分钟 (默认: 10)
      lock-timeout-minutes: 10
      
      # 恢复 API 配置
      api:
        # 是否启用恢复 API (默认: true)
        enabled: true

    # 任务管理 API 配置
    api:
      # 是否启用任务管理 API (默认: true)
      enabled: true

    # 管理员 API 配置
    admin:
      # 是否启用管理员 API (默认: false，生产环境建议关闭)
      enabled: false

# 开发环境配置
---
spring:
  config:
    activate:
      on-profile: dev
      
light:
  mq:
    recovery:
      enabled: true
      interval-minutes: 3  # 开发环境更频繁的检查
      batch-size: 50       # 开发环境较小的批次
    api:
      enabled: true
    admin:
      enabled: true        # 开发环境启用管理员 API

# 生产环境配置  
---
spring:
  config:
    activate:
      on-profile: prod
      
light:
  mq:
    recovery:
      enabled: true
      interval-minutes: 5
      batch-size: 200      # 生产环境较大的批次
      max-processing-hours: 4  # 生产环境更长的处理时间
      max-retry-attempts: 5    # 生产环境更多的重试次数
    api:
      enabled: true
    admin:
      enabled: false       # 生产环境禁用管理员 API

# 测试环境配置
---
spring:
  config:
    activate:
      on-profile: test
      
light:
  mq:
    recovery:
      enabled: false  # 测试环境可以禁用恢复功能
    api:
      enabled: true
    admin:
      enabled: true   # 测试环境启用管理员 API
