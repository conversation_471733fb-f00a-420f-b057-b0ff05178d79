<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lg</groupId>
		<artifactId>financecloud-common</artifactId>
		<version>3.10.1</version>
	</parent>

	<artifactId>financecloud-common-redis</artifactId>
	<packaging>jar</packaging>

	<description>financecloud redis</description>

	<dependencies>
		<dependency>
			<groupId>com.lg</groupId>
			<artifactId>financecloud-common-core</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.lg</groupId>
			<artifactId>financecloud-common-api</artifactId>
			<version>${project.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.lg</groupId>-->
<!--			<artifactId>financecloud-common-data</artifactId>-->
<!--			<version>3.10.0</version>-->
<!--		</dependency>-->

		<!--redisson-->
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>${redisson.version}</version>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.redisson</groupId>-->
			<!--<artifactId>redisson</artifactId>-->
			<!--<version>3.17.0</version>-->
			<!--<scope>compile</scope>-->
		<!--</dependency>-->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
		</dependency>
		<dependency>
			<groupId>de.ruedigermoeller</groupId>
			<artifactId>fst</artifactId>
			<version>2.57</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.lg</groupId>-->
<!--			<artifactId>financecloud-common-data</artifactId>-->
<!--		</dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
		<dependency>
			<groupId>com.lg</groupId>
			<artifactId>financecloud-common-dao</artifactId>
			<version>${project.version}</version>
<!--			<scope>compile</scope>-->
		</dependency>
		<dependency>
			<groupId>com.lg</groupId>
			<artifactId>financecloud-common-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!--		<dependency>-->
<!--			<groupId>com.github.foxnic</groupId>-->
<!--			<artifactId>foxnic-dao</artifactId>-->
<!--			<version>1.5.2.RELEASE</version>-->
<!--			<scope>compile</scope>-->
<!--		</dependency>-->

		<!-- Caffeine 缓存依赖 -->
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>
</project>