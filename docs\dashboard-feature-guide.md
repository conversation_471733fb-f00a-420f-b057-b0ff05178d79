# 低代码平台数据看板功能指南

## 📊 功能概述

数据看板是低代码平台的核心功能之一，提供了强大的数据可视化能力，支持多种图表类型、实时数据展示、拖拽式布局设计等功能。

## 🎯 核心特性

### 1. 多样化的图表组件
- **基础图表**: 折线图、柱状图、饼图、面积图、散点图
- **高级图表**: 仪表盘、雷达图、热力图、漏斗图、桑基图
- **数据展示**: 指标卡片、数据表格、文本组件
- **交互组件**: 过滤器、时间选择器、参数控件

### 2. 灵活的布局设计
- **网格布局**: 基于栅格系统的响应式布局
- **自由布局**: 像素级精确定位
- **拖拽操作**: 可视化的组件拖拽和调整
- **响应式设计**: 适配不同屏幕尺寸

### 3. 强大的数据源支持
- **数据库**: MySQL、PostgreSQL、Oracle、SQL Server
- **API接口**: REST API、GraphQL
- **文件数据**: Excel、CSV、JSON
- **实时数据**: WebSocket、消息队列

### 4. 丰富的交互功能
- **组件联动**: 点击图表联动其他组件
- **钻取分析**: 多层级数据钻取
- **实时刷新**: 自动或手动刷新数据
- **导出分享**: 支持PDF、图片、链接分享

## 🗃️ 数据库设计

### 核心表结构

#### 1. 看板表 (lc_dashboard)
```sql
CREATE TABLE `lc_dashboard` (
  `id` varchar(64) NOT NULL COMMENT '看板ID',
  `dashboard_code` varchar(100) NOT NULL COMMENT '看板编码',
  `dashboard_name` varchar(200) NOT NULL COMMENT '看板名称',
  `config_json` longtext COMMENT '看板配置JSON',
  `layout_json` longtext COMMENT '看板布局JSON',
  `style_json` text COMMENT '看板样式JSON',
  `status` varchar(50) DEFAULT 'DRAFT' COMMENT '看板状态',
  `is_template` tinyint(1) DEFAULT '0' COMMENT '是否为模板',
  -- 其他字段...
  PRIMARY KEY (`id`)
);
```

#### 2. 组件表 (lc_dashboard_widget)
```sql
CREATE TABLE `lc_dashboard_widget` (
  `id` varchar(64) NOT NULL COMMENT '组件ID',
  `dashboard_id` varchar(64) NOT NULL COMMENT '看板ID',
  `widget_type` varchar(50) NOT NULL COMMENT '组件类型',
  `chart_type` varchar(50) DEFAULT NULL COMMENT '图表类型',
  `query_config` longtext COMMENT '数据查询配置JSON',
  `widget_config` longtext COMMENT '组件配置JSON',
  `layout_config` text COMMENT '布局配置JSON',
  -- 其他字段...
  PRIMARY KEY (`id`)
);
```

#### 3. 数据源表 (lc_datasource)
```sql
CREATE TABLE `lc_datasource` (
  `id` varchar(64) NOT NULL COMMENT '数据源ID',
  `datasource_type` varchar(50) NOT NULL COMMENT '数据源类型',
  `connection_config` longtext COMMENT '连接配置JSON',
  `connection_status` varchar(50) DEFAULT 'DISCONNECTED' COMMENT '连接状态',
  -- 其他字段...
  PRIMARY KEY (`id`)
);
```

## 🔧 API接口

### 看板管理接口

#### 创建看板
```http
POST /api/lc/dashboard/create
Content-Type: application/json

{
  "dashboardCode": "sales_dashboard",
  "dashboardName": "销售数据看板",
  "description": "销售业务数据分析看板",
  "dashboardType": "STANDARD",
  "config": {
    "title": "销售数据看板",
    "backgroundColor": "#f5f5f5",
    "grid": {
      "showGrid": true,
      "gridSize": 20
    }
  },
  "layout": {
    "layoutType": "GRID",
    "columns": 12,
    "rows": 8
  }
}
```

#### 获取看板详情
```http
GET /api/lc/dashboard/get/{dashboardId}
```

#### 发布看板
```http
POST /api/lc/dashboard/publish/{dashboardId}
```

### 组件管理接口

#### 添加组件
```http
POST /api/lc/dashboard/widget/add
Content-Type: application/json

{
  "dashboardId": "dashboard123",
  "widgetCode": "sales_chart",
  "widgetName": "销售趋势图",
  "widgetType": "CHART",
  "chartType": "LINE",
  "datasourceId": "datasource123",
  "queryConfig": {
    "queryType": "SQL",
    "sql": "SELECT date, sales_amount FROM sales_data WHERE date >= ?",
    "params": [
      {
        "name": "startDate",
        "type": "DATE",
        "defaultValue": "2024-01-01"
      }
    ]
  },
  "layoutConfig": {
    "x": 0,
    "y": 0,
    "width": 6,
    "height": 4
  }
}
```

#### 获取组件数据
```http
GET /api/lc/dashboard/widget/data/{widgetId}?startDate=2024-01-01&endDate=2024-12-31
```

### 数据源管理接口

#### 创建数据源
```http
POST /api/lc/datasource/create
Content-Type: application/json

{
  "datasourceCode": "mysql_sales",
  "datasourceName": "销售数据库",
  "datasourceType": "MYSQL",
  "host": "localhost",
  "port": 3306,
  "databaseName": "sales_db",
  "username": "root",
  "password": "password"
}
```

#### 测试连接
```http
POST /api/lc/datasource/test-connection/{datasourceId}
```

## 🎨 前端组件设计

### 看板设计器组件

#### DashboardDesigner.vue
```vue
<template>
  <div class="dashboard-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <el-button @click="addWidget">添加组件</el-button>
      <el-button @click="saveDesign">保存设计</el-button>
      <el-button @click="previewDashboard">预览</el-button>
    </div>
    
    <!-- 设计画布 -->
    <div class="designer-canvas" ref="canvas">
      <grid-layout
        :layout="layout"
        :col-num="12"
        :row-height="30"
        :is-draggable="true"
        :is-resizable="true"
        @layout-updated="onLayoutUpdated"
      >
        <grid-item
          v-for="item in layout"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
        >
          <widget-component
            :widget="getWidget(item.i)"
            :design-mode="true"
            @edit="editWidget"
            @delete="deleteWidget"
          />
        </grid-item>
      </grid-layout>
    </div>
    
    <!-- 属性面板 -->
    <div class="designer-properties">
      <widget-properties
        v-if="selectedWidget"
        :widget="selectedWidget"
        @update="updateWidget"
      />
    </div>
  </div>
</template>
```

### 图表组件

#### ChartWidget.vue
```vue
<template>
  <div class="chart-widget">
    <div v-if="widget.config.showTitle" class="widget-title">
      {{ widget.config.title }}
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <v-chart
        :option="chartOption"
        :loading="loading"
        @click="onChartClick"
        autoresize
      />
    </div>
    
    <div v-if="widget.config.showToolbar" class="widget-toolbar">
      <el-button size="mini" @click="refreshData">刷新</el-button>
      <el-button size="mini" @click="exportChart">导出</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChartWidget',
  props: {
    widget: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chartData: [],
      loading: false
    }
  },
  computed: {
    chartOption() {
      return this.buildChartOption()
    }
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const response = await this.$http.get(`/api/lc/dashboard/widget/data/${this.widget.id}`)
        this.chartData = response.data
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    buildChartOption() {
      const { chartType, dataConfig } = this.widget
      
      switch (chartType) {
        case 'LINE':
          return this.buildLineChart()
        case 'BAR':
          return this.buildBarChart()
        case 'PIE':
          return this.buildPieChart()
        default:
          return {}
      }
    },
    
    buildLineChart() {
      return {
        xAxis: {
          type: 'category',
          data: this.chartData.map(item => item[this.widget.dataConfig.xField])
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: this.chartData.map(item => item[this.widget.dataConfig.yField]),
          type: 'line'
        }]
      }
    }
  }
}
</script>
```

## 🚀 使用流程

### 1. 创建数据源
1. 进入数据源管理页面
2. 点击"新建数据源"
3. 选择数据源类型（MySQL、API等）
4. 配置连接参数
5. 测试连接并保存

### 2. 设计看板
1. 进入看板管理页面
2. 点击"新建看板"
3. 设置看板基本信息
4. 进入看板设计器
5. 拖拽添加组件
6. 配置组件数据源和样式
7. 保存并发布看板

### 3. 查看看板
1. 在看板列表中找到目标看板
2. 点击查看进入看板页面
3. 查看实时数据展示
4. 使用过滤器和交互功能
5. 导出或分享看板

## 📈 图表类型说明

### 基础图表

#### 1. 折线图 (LINE)
- **适用场景**: 趋势分析、时间序列数据
- **配置参数**: X轴字段、Y轴字段、系列字段
- **样式选项**: 线条颜色、粗细、点样式

#### 2. 柱状图 (BAR)
- **适用场景**: 分类数据对比、排名分析
- **配置参数**: 分类字段、数值字段、堆叠设置
- **样式选项**: 柱子颜色、宽度、间距

#### 3. 饼图 (PIE)
- **适用场景**: 占比分析、构成分析
- **配置参数**: 分类字段、数值字段、标签显示
- **样式选项**: 颜色配置、内外半径、标签位置

### 高级图表

#### 1. 仪表盘 (GAUGE)
- **适用场景**: KPI指标展示、进度显示
- **配置参数**: 数值字段、最小值、最大值、目标值
- **样式选项**: 指针样式、刻度设置、颜色区间

#### 2. 雷达图 (RADAR)
- **适用场景**: 多维度数据对比、能力评估
- **配置参数**: 维度字段、数值字段、系列字段
- **样式选项**: 网格样式、填充颜色、线条样式

## 🔄 实时数据更新

### 自动刷新机制
```javascript
// 组件级别的自动刷新
setInterval(() => {
  if (this.widget.autoRefresh) {
    this.loadData()
  }
}, this.widget.refreshInterval * 1000)

// 看板级别的统一刷新
this.$eventBus.$on('dashboard:refresh', () => {
  this.widgets.forEach(widget => {
    widget.refreshData()
  })
})
```

### WebSocket实时推送
```javascript
// 建立WebSocket连接
const ws = new WebSocket(`ws://localhost:8080/ws/dashboard/${dashboardId}`)

ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'widget_data_update') {
    this.updateWidgetData(data.widgetId, data.data)
  }
}
```

## 📊 数据处理

### SQL查询示例
```sql
-- 销售趋势查询
SELECT 
  DATE_FORMAT(order_date, '%Y-%m') as month,
  SUM(order_amount) as total_sales,
  COUNT(*) as order_count
FROM orders 
WHERE order_date >= ? AND order_date <= ?
GROUP BY DATE_FORMAT(order_date, '%Y-%m')
ORDER BY month;

-- 产品销量排行
SELECT 
  product_name,
  SUM(quantity) as total_quantity,
  SUM(amount) as total_amount
FROM order_items oi
JOIN products p ON oi.product_id = p.id
WHERE oi.create_time >= ?
GROUP BY product_name
ORDER BY total_amount DESC
LIMIT 10;
```

### API数据处理
```javascript
// API数据转换
const transformApiData = (apiResponse) => {
  return apiResponse.data.map(item => ({
    x: item.date,
    y: item.value,
    category: item.type
  }))
}

// 数据聚合
const aggregateData = (data, groupField, valueField, aggregateType) => {
  const grouped = data.reduce((acc, item) => {
    const key = item[groupField]
    if (!acc[key]) {
      acc[key] = []
    }
    acc[key].push(item[valueField])
    return acc
  }, {})
  
  return Object.entries(grouped).map(([key, values]) => ({
    [groupField]: key,
    [valueField]: calculateAggregate(values, aggregateType)
  }))
}
```

## 🎯 最佳实践

### 1. 性能优化
- 合理设置缓存时间
- 使用数据分页和限制
- 避免复杂的实时查询
- 优化SQL查询性能

### 2. 用户体验
- 提供加载状态提示
- 设计响应式布局
- 支持键盘快捷键
- 提供操作引导

### 3. 数据安全
- 数据源权限控制
- SQL注入防护
- 敏感数据脱敏
- 访问日志记录

### 4. 扩展性设计
- 插件化的图表组件
- 可配置的数据源类型
- 主题和样式定制
- 国际化支持

这个数据看板功能为低代码平台提供了强大的数据可视化能力，支持从数据源配置到看板设计、发布、分享的完整流程，是低代码平台不可或缺的核心功能！
