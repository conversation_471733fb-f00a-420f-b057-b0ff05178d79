package com.lg.dao.core.interceptor;

import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 拦截器排除管理器
 * 统一管理各种拦截器的排除机制，支持排除多个拦截器
 * 
 * <AUTHOR>
 */
@Slf4j
public class InterceptorExclusionManager {
    
    /**
     * 拦截器类型枚举
     */
    public enum InterceptorType {
        DATA_PERMISSION("数据权限拦截器"),
        TENANT("租户拦截器"),
        SQL_PRINT("SQL打印拦截器"),
        CACHE("缓存拦截器"),
        AUDIT("审计拦截器"),
        PERFORMANCE("性能监控拦截器");
        
        private final String description;
        
        InterceptorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 线程本地变量，存储当前线程排除的拦截器类型
     */
    private static final ThreadLocal<Set<InterceptorType>> EXCLUDED_INTERCEPTORS = 
            ThreadLocal.withInitial(() -> ConcurrentHashMap.newKeySet());
    
    /**
     * 检查指定拦截器是否被排除
     * 
     * @param interceptorType 拦截器类型
     * @return true表示被排除，false表示不排除
     */
    public static boolean isExcluded(InterceptorType interceptorType) {
        return EXCLUDED_INTERCEPTORS.get().contains(interceptorType);
    }
    
    /**
     * 排除指定拦截器
     * 
     * @param interceptorType 要排除的拦截器类型
     */
    public static void exclude(InterceptorType interceptorType) {
        EXCLUDED_INTERCEPTORS.get().add(interceptorType);
        log.debug("排除拦截器: {}", interceptorType.getDescription());
    }
    
    /**
     * 排除多个拦截器
     * 
     * @param interceptorTypes 要排除的拦截器类型数组
     */
    public static void exclude(InterceptorType... interceptorTypes) {
        Set<InterceptorType> excludedSet = EXCLUDED_INTERCEPTORS.get();
        for (InterceptorType type : interceptorTypes) {
            excludedSet.add(type);
            log.debug("排除拦截器: {}", type.getDescription());
        }
    }
    
    /**
     * 取消排除指定拦截器
     * 
     * @param interceptorType 要取消排除的拦截器类型
     */
    public static void include(InterceptorType interceptorType) {
        EXCLUDED_INTERCEPTORS.get().remove(interceptorType);
        log.debug("取消排除拦截器: {}", interceptorType.getDescription());
    }
    
    /**
     * 取消排除多个拦截器
     * 
     * @param interceptorTypes 要取消排除的拦截器类型数组
     */
    public static void include(InterceptorType... interceptorTypes) {
        Set<InterceptorType> excludedSet = EXCLUDED_INTERCEPTORS.get();
        for (InterceptorType type : interceptorTypes) {
            excludedSet.remove(type);
            log.debug("取消排除拦截器: {}", type.getDescription());
        }
    }
    
    /**
     * 清除所有排除的拦截器
     */
    public static void clearAll() {
        Set<InterceptorType> excludedSet = EXCLUDED_INTERCEPTORS.get();
        if (!excludedSet.isEmpty()) {
            log.debug("清除所有排除的拦截器: {}", excludedSet);
            excludedSet.clear();
        }
    }
    
    /**
     * 获取当前排除的拦截器类型
     *
     * @return 排除的拦截器类型集合
     */
    public static Set<InterceptorType> getExcludedInterceptors() {
        return new HashSet<>(EXCLUDED_INTERCEPTORS.get());
    }
    
    /**
     * 执行操作时排除指定拦截器（有返回值）
     *
     * @param supplier 要执行的操作
     * @param interceptorTypes 要排除的拦截器类型
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithExclusion(Supplier<T> supplier, InterceptorType... interceptorTypes) {
        // 保存当前状态
        Set<InterceptorType> originalExcluded = new HashSet<>(EXCLUDED_INTERCEPTORS.get());

        try {
            // 添加要排除的拦截器
            exclude(interceptorTypes);

            // 执行操作
            return supplier.get();

        } finally {
            // 恢复原始状态
            EXCLUDED_INTERCEPTORS.get().clear();
            EXCLUDED_INTERCEPTORS.get().addAll(originalExcluded);
        }
    }
    
    /**
     * 执行操作时排除指定拦截器（无返回值）
     * 
     * @param runnable 要执行的操作
     * @param interceptorTypes 要排除的拦截器类型
     */
    public static void executeWithExclusion(Runnable runnable, InterceptorType... interceptorTypes) {
        executeWithExclusion(() -> {
            runnable.run();
            return null;
        }, interceptorTypes);
    }
    
    /**
     * 执行操作时排除数据权限拦截器（便捷方法）
     * 
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithoutDataPermission(Supplier<T> supplier) {
        return executeWithExclusion(supplier, InterceptorType.DATA_PERMISSION);
    }
    
    /**
     * 执行操作时排除数据权限拦截器（便捷方法，无返回值）
     * 
     * @param runnable 要执行的操作
     */
    public static void executeWithoutDataPermission(Runnable runnable) {
        executeWithExclusion(runnable, InterceptorType.DATA_PERMISSION);
    }
    
    /**
     * 执行操作时排除租户拦截器（便捷方法）
     * 
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithoutTenant(Supplier<T> supplier) {
        return executeWithExclusion(supplier, InterceptorType.TENANT);
    }
    
    /**
     * 执行操作时排除租户拦截器（便捷方法，无返回值）
     * 
     * @param runnable 要执行的操作
     */
    public static void executeWithoutTenant(Runnable runnable) {
        executeWithExclusion(runnable, InterceptorType.TENANT);
    }
    
    /**
     * 清理线程本地变量（在线程结束时调用）
     */
    public static void cleanup() {
        EXCLUDED_INTERCEPTORS.remove();
    }
}
