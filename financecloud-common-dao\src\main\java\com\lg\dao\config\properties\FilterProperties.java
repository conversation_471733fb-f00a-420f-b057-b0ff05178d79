package com.lg.dao.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 数据筛选和权限功能配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.orm.filter")
public class FilterProperties {
    
    /**
     * 是否启用数据筛选和权限功能，默认关闭
     */
    private boolean enable = false;
    
    /**
     * 是否启用数据权限拦截器
     */
    private boolean enableDataPermission = true;
    
    /**
     * 是否启用数据过滤切面
     */
    private boolean enableDataFilter = true;
    
    /**
     * 是否启用元数据缓存
     */
    private boolean enableMetadataCache = true;
    
    /**
     * 元数据缓存过期时间（秒），默认30分钟
     */
    private long metadataCacheExpireSeconds = 1800;
    
    /**
     * 数据权限规则缓存过期时间（秒），默认10分钟
     */
    private long rulesCacheExpireSeconds = 600;
}