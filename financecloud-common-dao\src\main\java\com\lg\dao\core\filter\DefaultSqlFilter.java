package com.lg.dao.core.filter;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * SQL过滤器默认实现
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class DefaultSqlFilter implements SqlFilter {

    @Autowired
    private DataPermissionService dataPermissionService;
    
    @Override
    public String apply(String sql, FilterConfig filterConfig) {
        if (filterConfig == null) {
            return sql;
        }
        
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT * FROM (");
        builder.append(sql);
        builder.append(") FILTER_TEMP");
        
        // 添加过滤条件
        List<String> conditions = buildFilterConditions(filterConfig);
        if (!conditions.isEmpty()) {
            builder.append(" WHERE ");
            builder.append(String.join(" AND ", conditions));
        }
        
        // 添加排序条件
        List<String> sortClauses = buildSortClauses(filterConfig);
        if (!sortClauses.isEmpty()) {
            builder.append(" ORDER BY ");
            builder.append(String.join(", ", sortClauses));
        }
        
        // 添加分页条件
        if (filterConfig.getPage() != null) {
            int pageNum = filterConfig.getPage().getPageNum();
            int pageSize = filterConfig.getPage().getPageSize();
            int offset = (pageNum - 1) * pageSize;
            
            builder.append(" LIMIT ").append(pageSize);
            builder.append(" OFFSET ").append(offset);
        }
        
        return builder.toString();
    }
    
    @Override
    public String apply(String sql, FilterConfig filterConfig, String tableCode, String userId, List<String> roleIds) {
        // 先应用过滤条件
        String filteredSql = apply(sql, filterConfig);
        
        // 再应用数据权限
        return applyDataPermission(filteredSql, tableCode, userId, roleIds);
    }
    
    @Override
    public String applyDataPermission(String sql, String tableCode, String userId, List<String> roleIds) {
        if (StringUtils.isEmpty(tableCode) || StringUtils.isEmpty(userId)) {
            return sql;
        }
        
        // 获取数据权限条件
        String condition = dataPermissionService.getDataPermissionCondition(tableCode, userId, roleIds);
        if (StringUtils.isEmpty(condition)) {
            return sql;
        }
        
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT * FROM (");
        builder.append(sql);
        builder.append(") DP_TEMP");
        builder.append(" WHERE ");
        builder.append(condition);
        
        return builder.toString();
    }
    
    /**
     * 构建过滤条件
     *
     * @param filterConfig 过滤配置
     * @return 过滤条件列表
     */
    private List<String> buildFilterConditions(FilterConfig filterConfig) {
        List<String> conditions = new ArrayList<>();
        
        if (filterConfig == null || filterConfig.getFilters() == null || filterConfig.getFilters().isEmpty()) {
            return conditions;
        }
        
        for (FilterConfig.Filter filter : filterConfig.getFilters()) {
            String condition = buildCondition(filter);
            if (condition != null) {
                conditions.add(condition);
            }
        }
        
        return conditions;
    }
    
    /**
     * 构建单个条件
     *
     * @param filter 过滤条件
     * @return SQL条件
     */
    private String buildCondition(FilterConfig.Filter filter) {
        String field = filter.getField();
        String operator = filter.getOperator();
        Object value = filter.getValue();
        
        if (StringUtils.isEmpty(field) || StringUtils.isEmpty(operator) || value == null) {
            return null;
        }
        
        return buildSqlCondition(field, operator, value);
    }
    
    /**
     * 构建SQL条件
     *
     * @param field 字段名
     * @param operator 操作符
     * @param value 值
     * @return SQL条件
     */
    private String buildSqlCondition(String field, String operator, Object value) {
        switch (operator.toLowerCase()) {
            case "eq":
                return field + " = " + formatValue(value);
            case "neq":
                return field + " != " + formatValue(value);
            case "gt":
                return field + " > " + formatValue(value);
            case "gte":
                return field + " >= " + formatValue(value);
            case "lt":
                return field + " < " + formatValue(value);
            case "lte":
                return field + " <= " + formatValue(value);
            case "like":
                return field + " LIKE '%" + value + "%'";
            case "notlike":
                return field + " NOT LIKE '%" + value + "%'";
            case "in":
                return buildInCondition(field, value);
            case "notin":
                return buildNotInCondition(field, value);
            case "isnull":
                return field + " IS NULL";
            case "isnotnull":
                return field + " IS NOT NULL";
            case "between":
                return buildBetweenCondition(field, value);
            default:
                log.warn("不支持的操作符: {}", operator);
                return null;
        }
    }
    
    /**
     * 构建IN条件
     *
     * @param field 字段名
     * @param value 值
     * @return IN条件
     */
    private String buildInCondition(String field, Object value) {
        if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            if (collection.isEmpty()) {
                return "1 = 0"; // 空集合，永远为false
            }
            
            StringBuilder builder = new StringBuilder();
            builder.append(field).append(" IN (");
            
            boolean first = true;
            for (Object item : collection) {
                if (!first) {
                    builder.append(", ");
                }
                builder.append(formatValue(item));
                first = false;
            }
            
            builder.append(")");
            return builder.toString();
        } else {
            return field + " = " + formatValue(value);
        }
    }
    
    /**
     * 构建NOT IN条件
     *
     * @param field 字段名
     * @param value 值
     * @return NOT IN条件
     */
    private String buildNotInCondition(String field, Object value) {
        if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            if (collection.isEmpty()) {
                return "1 = 1"; // 空集合，永远为true
            }
            
            StringBuilder builder = new StringBuilder();
            builder.append(field).append(" NOT IN (");
            
            boolean first = true;
            for (Object item : collection) {
                if (!first) {
                    builder.append(", ");
                }
                builder.append(formatValue(item));
                first = false;
            }
            
            builder.append(")");
            return builder.toString();
        } else {
            return field + " != " + formatValue(value);
        }
    }
    
    /**
     * 构建BETWEEN条件
     *
     * @param field 字段名
     * @param value 值
     * @return BETWEEN条件
     */
    private String buildBetweenCondition(String field, Object value) {
        if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            if (collection.size() != 2) {
                log.warn("BETWEEN操作符需要两个值");
                return null;
            }
            
            Object[] values = collection.toArray();
            return field + " BETWEEN " + formatValue(values[0]) + " AND " + formatValue(values[1]);
        } else if (value instanceof String) {
            String[] values = ((String) value).split(",");
            if (values.length != 2) {
                log.warn("BETWEEN操作符需要两个值");
                return null;
            }
            
            return field + " BETWEEN " + formatValue(values[0]) + " AND " + formatValue(values[1]);
        } else {
            log.warn("BETWEEN操作符需要集合或字符串");
            return null;
        }
    }
    
    /**
     * 格式化值
     *
     * @param value 值
     * @return 格式化后的值
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof Number) {
            return value.toString();
        } else if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        } else {
            return "'" + value.toString().replace("'", "''") + "'";
        }
    }
    
    /**
     * 构建排序条件
     *
     * @param filterConfig 过滤条件配置
     * @return 排序条件列表
     */
    private List<String> buildSortClauses(FilterConfig filterConfig) {
        List<String> sortClauses = new ArrayList<>();
        
        if (filterConfig.getSorts() == null || filterConfig.getSorts().isEmpty()) {
            return sortClauses;
        }
        
        for (FilterConfig.SortCondition condition : filterConfig.getSorts()) {
            String field = condition.getField();
            String direction = condition.getDirection();
            
            if (StringUtils.isEmpty(field) || StringUtils.isEmpty(direction)) {
                continue;
            }
            
            direction = direction.toUpperCase();
            if (!direction.equals("ASC") && !direction.equals("DESC")) {
                direction = "ASC";
            }
            
            sortClauses.add(field + " " + direction);
        }
        
        return sortClauses;
    }
} 