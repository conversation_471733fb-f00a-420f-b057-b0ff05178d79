package com.lg.dao.core.condition;

/**
 * 条件拼接策略枚举
 * 用于控制在构建查询条件时，什么情况下应该添加条件
 */
public enum ConditionStrategy {
    
    /**
     * 总是添加条件（默认策略）
     */
    ALWAYS,
    
    /**
     * 仅当值不为null时添加条件
     */
    IS_NOT_NULL,
    
    /**
     * 仅当值不为null且不为空字符串时添加条件
     */
    IS_NOT_EMPTY,
    
    /**
     * 仅当值不为null、不为空字符串且不为空白字符串时添加条件
     */
    IS_NOT_BLANK,
    
    /**
     * 仅当值为null时添加条件
     */
    IS_NULL,
    
    /**
     * 仅当值为null或空字符串时添加条件
     */
    IS_EMPTY,
    
    /**
     * 仅当值为null、空字符串或空白字符串时添加条件
     */
    IS_BLANK,
    
    /**
     * 仅当集合不为null且不为空时添加条件
     */
    COLLECTION_NOT_EMPTY,
    
    /**
     * 仅当集合为null或为空时添加条件
     */
    COLLECTION_EMPTY
}