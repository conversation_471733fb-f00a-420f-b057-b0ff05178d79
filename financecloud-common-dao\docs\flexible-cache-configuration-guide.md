# 灵活缓存配置指南

## 📋 概述

本指南介绍如何为 DAO 框架中的不同缓存类型配置独立的参数，实现更灵活的缓存管理。

## 🎯 缓存类型说明

### 支持的缓存类型

| 缓存类型 | 标识符 | 用途 | 默认容量 | 默认过期时间 |
|---------|--------|------|----------|-------------|
| DAO缓存 | `dao` | 数据库查询结果缓存 | 500 | 3600s (1小时) |
| 实体缓存 | `entity` | 实体元数据缓存 | 2000 | 7200s (2小时) |
| 权限缓存 | `permission` | 用户权限信息缓存 | 1000 | 1800s (30分钟) |
| SQL模板缓存 | `sql_template` | 模板渲染结果缓存 | 1500 | 3600s (1小时) |
| 元数据缓存 | `metadata` | 数据库元数据缓存 | 300 | 7200s (2小时) |
| MyBatis代理缓存 | `mybatis_proxy` | Mapper方法缓存 | 200 | 3600s (1小时) |
| MyBatis SQL解析缓存 | `mybatis_sql_parse` | SQL解析结果缓存 | 800 | 1800s (30分钟) |
| 查询结果缓存 | `query_result` | 查询结果缓存 | 1000 | 600s (10分钟) |

## 🔧 配置方式

### 1. 基础配置

```yaml
# 启用缓存功能
light:
  orm:
    cache:
      enable: true
```

### 2. 使用默认配置

如果不配置具体的缓存类型参数，系统会使用每种缓存类型的默认值：

```yaml
light:
  orm:
    cache:
      enable: true
      # 不配置 types，使用默认值
```

### 3. 自定义部分缓存类型

只配置需要调整的缓存类型，其他使用默认值：

```yaml
light:
  orm:
    cache:
      enable: true
      types:
        # 只调整实体缓存的配置
        entity:
          max-size: 5000
          expire-seconds: 10800  # 3小时
        # 只调整权限缓存的配置
        permission:
          max-size: 2000
          expire-seconds: 900    # 15分钟
```

### 4. 完整自定义配置

为所有缓存类型配置自定义参数：

```yaml
light:
  orm:
    cache:
      enable: true
      types:
        # DAO缓存：数据库查询结果
        dao:
          max-size: 1000
          expire-seconds: 7200
          description: "自定义DAO缓存配置"
        
        # 实体缓存：实体元数据信息
        entity:
          max-size: 3000
          expire-seconds: 14400
          description: "自定义实体缓存配置"
        
        # 权限缓存：用户权限信息
        permission:
          max-size: 1500
          expire-seconds: 1200
          description: "自定义权限缓存配置"
        
        # SQL模板缓存：模板渲染结果
        sql_template:
          max-size: 2000
          expire-seconds: 7200
          description: "自定义SQL模板缓存配置"
        
        # 元数据缓存：数据库元数据
        metadata:
          max-size: 500
          expire-seconds: 21600
          description: "自定义元数据缓存配置"
        
        # MyBatis代理缓存：Mapper方法
        mybatis_proxy:
          max-size: 300
          expire-seconds: 7200
          description: "自定义MyBatis代理缓存配置"
        
        # MyBatis SQL解析缓存：SQL解析结果
        mybatis_sql_parse:
          max-size: 1200
          expire-seconds: 3600
          description: "自定义MyBatis SQL解析缓存配置"
        
        # 查询结果缓存：查询结果
        query_result:
          max-size: 1500
          expire-seconds: 300
          description: "自定义查询结果缓存配置"
```

## 🎯 配置建议

### 1. 开发环境配置

```yaml
light:
  orm:
    cache:
      enable: true
      types:
        # 开发环境使用较小的缓存和较短的过期时间
        dao:
          max-size: 100
          expire-seconds: 300
        entity:
          max-size: 200
          expire-seconds: 600
        permission:
          max-size: 100
          expire-seconds: 300
        sql_template:
          max-size: 200
          expire-seconds: 300
        mybatis_sql_parse:
          max-size: 100
          expire-seconds: 300
```

### 2. 生产环境配置

```yaml
light:
  orm:
    cache:
      enable: true
      types:
        # 生产环境使用较大的缓存和较长的过期时间
        dao:
          max-size: 2000
          expire-seconds: 7200
        entity:
          max-size: 5000
          expire-seconds: 14400
        permission:
          max-size: 3000
          expire-seconds: 3600
        sql_template:
          max-size: 3000
          expire-seconds: 7200
        mybatis_sql_parse:
          max-size: 2000
          expire-seconds: 3600
```

### 3. 高并发环境配置

```yaml
light:
  orm:
    cache:
      enable: true
      types:
        # 高并发环境需要更大的缓存容量
        dao:
          max-size: 5000
          expire-seconds: 3600
        entity:
          max-size: 10000
          expire-seconds: 7200
        permission:
          max-size: 5000
          expire-seconds: 1800
        sql_template:
          max-size: 5000
          expire-seconds: 3600
        mybatis_sql_parse:
          max-size: 3000
          expire-seconds: 1800
```

## 🔍 监控和调试

### 1. 启用缓存日志

```yaml
logging:
  level:
    com.lg.dao.core.cache.UnifiedCacheManager: INFO
```

### 2. 启用详细调试日志

```yaml
logging:
  level:
    com.lg.dao.core.cache.UnifiedCacheManager: DEBUG
    com.lg.dao.mybatis.MybatisNativeXmlParser: DEBUG
    com.lg.dao.core.template.MybatisTemplateEngine: DEBUG
    com.lg.dao.core.SqlTemplateManager: DEBUG
```

### 3. 日志输出示例

```
INFO  - 初始化统一缓存管理器，缓存启用状态: true
INFO  - 创建缓存: dao | 容量: 500 | 过期时间: 3600s | 描述: DAO操作缓存，用于缓存数据库查询结果
INFO  - 创建缓存: entity | 容量: 2000 | 过期时间: 7200s | 描述: 实体信息缓存，用于缓存实体元数据
INFO  - === 缓存配置详情 ===
INFO  - 缓存类型: dao | 配置来源: 默认配置 | 容量: 500 | 过期: 3600s | 说明: DAO操作缓存，用于缓存数据库查询结果
INFO  - 缓存类型: entity | 配置来源: 自定义配置 | 容量: 5000 | 过期: 10800s | 说明: 自定义实体缓存配置
INFO  - === 缓存配置详情结束 ===
```

## 📊 性能调优建议

### 1. 缓存容量调优

- **小型应用**：使用默认配置或适当减小容量
- **中型应用**：适当增加常用缓存的容量
- **大型应用**：显著增加所有缓存的容量

### 2. 过期时间调优

- **频繁变化的数据**：使用较短的过期时间
- **相对稳定的数据**：使用较长的过期时间
- **元数据类信息**：使用很长的过期时间

### 3. 内存使用优化

- 监控应用内存使用情况
- 根据实际使用情况调整缓存容量
- 避免缓存容量过大导致内存溢出

## 📝 注意事项

1. **配置优先级**：自定义配置 > 默认配置
2. **内存管理**：合理设置缓存容量，避免内存溢出
3. **过期策略**：根据数据特性设置合适的过期时间
4. **监控重要性**：定期检查缓存命中率和性能指标
5. **环境差异**：不同环境使用不同的缓存配置
