package com.lg.financecloud.common.redis.cache.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Custom cache properties for configuring cache mode
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.cache")
public class CacheProperties {
    
    /**
     * Cache mode: REDIS, MEMORY, TWO_LEVEL
     * Default: REDIS
     */
    private CacheMode cacheMode = CacheMode.TWO_LEVEL;
    
    /**
     * Maximum number of items to store in memory cache (when using TWO_LEVEL or MEMORY mode)
     */
    private int maxSize = 1000;
    
    /**
     * Time to live in seconds for memory cache entries (when using TWO_LEVEL or MEMORY mode)
     * Default: 1 hour
     */
    private long timeToLiveSeconds = 3600;
    
    /**
     * Time to idle in seconds for memory cache entries (when using TWO_LEVEL or MEMORY mode)
     * Default: 30 minutes
     */
    private long timeToIdleSeconds = 1800;
    
    /**
     * Possible cache modes
     */
    public enum CacheMode {
        /**
         * Only use Redis for caching
         */
        REDIS,
        
        /**
         * Only use local memory for caching
         */
        MEMORY,
        
        /**
         * Use both memory and Redis in a two-level cache strategy
         */
        TWO_LEVEL
    }
} 