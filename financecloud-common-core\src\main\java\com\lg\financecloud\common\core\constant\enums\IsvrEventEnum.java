package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 点赞类型
 */
@Getter
@AllArgsConstructor
public enum IsvrEventEnum {
	ISVR_EVENT_NO_START("1", "未开始"),
	ISVR_EVENT_GOING("2", "进行中"),
	ISVR_EVENT_END("3", "已结束");
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 描述
	 */
	private String description;


}
