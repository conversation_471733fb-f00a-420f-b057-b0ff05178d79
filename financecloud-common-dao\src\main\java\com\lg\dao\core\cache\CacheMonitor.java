package com.lg.dao.core.cache;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存监控组件
 * 提供缓存健康检查和监控指标
 * 通过LightORMAutoConfiguration进行Bean配置，不使用@Component注解
 *
 * <AUTHOR>
 */
@Slf4j
public class CacheMonitor implements HealthIndicator {

    @Autowired(required = false)
    private UnifiedCacheManager unifiedCacheManager;

    /**
     * 健康检查
     */
    @Override
    public Health health() {
        if (unifiedCacheManager == null) {
            return Health.down()
                    .withDetail("reason", "UnifiedCacheManager not available")
                    .build();
        }

        try {
            Map<String, CacheStats> stats = unifiedCacheManager.getStats();
            Map<String, Object> details = new HashMap<>();
            
            double totalHitRate = 0;
            long totalRequests = 0;
            int cacheCount = 0;
            
            for (Map.Entry<String, CacheStats> entry : stats.entrySet()) {
                String cacheName = entry.getKey();
                CacheStats cacheStats = entry.getValue();
                
                Map<String, Object> cacheDetail = new HashMap<>();
                cacheDetail.put("hitRate", String.format("%.2f%%", cacheStats.hitRate() * 100));
                cacheDetail.put("requestCount", cacheStats.requestCount());
                cacheDetail.put("hitCount", cacheStats.hitCount());
                cacheDetail.put("missCount", cacheStats.missCount());
                cacheDetail.put("evictionCount", cacheStats.evictionCount());
                
                details.put(cacheName, cacheDetail);
                
                totalHitRate += cacheStats.hitRate();
                totalRequests += cacheStats.requestCount();
                cacheCount++;
            }
            
            // 计算平均命中率
            double avgHitRate = cacheCount > 0 ? totalHitRate / cacheCount : 0;
            Map<String, Object> summary = new HashMap<>();
            summary.put("cacheCount", cacheCount);
            summary.put("avgHitRate", String.format("%.2f%%", avgHitRate * 100));
            summary.put("totalRequests", totalRequests);
            details.put("summary", summary);
            
            // 判断健康状态
            if (avgHitRate < 0.5) { // 平均命中率低于50%
                return Health.down()
                        .withDetail("reason", "Low cache hit rate")
                        .withDetails(details)
                        .build();
            }
            
            return Health.up().withDetails(details).build();
            
        } catch (Exception e) {
            log.error("缓存健康检查失败", e);
            return Health.down()
                    .withDetail("reason", "Health check failed")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    /**
     * 获取缓存指标
     */
    public Map<String, Object> getMetrics() {
        if (unifiedCacheManager == null) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "UnifiedCacheManager not available");
            return errorResult;
        }

        try {
            Map<String, CacheStats> stats = unifiedCacheManager.getStats();
            Map<String, Object> metrics = new HashMap<>();
            
            for (Map.Entry<String, CacheStats> entry : stats.entrySet()) {
                String cacheName = entry.getKey();
                CacheStats cacheStats = entry.getValue();
                
                String prefix = "cache." + cacheName + ".";
                metrics.put(prefix + "hit_rate", cacheStats.hitRate());
                metrics.put(prefix + "request_count", cacheStats.requestCount());
                metrics.put(prefix + "hit_count", cacheStats.hitCount());
                metrics.put(prefix + "miss_count", cacheStats.missCount());
                metrics.put(prefix + "eviction_count", cacheStats.evictionCount());
                metrics.put(prefix + "load_count", cacheStats.loadCount());
                metrics.put(prefix + "average_load_penalty", cacheStats.averageLoadPenalty());
            }
            
            return metrics;
            
        } catch (Exception e) {
            log.error("获取缓存指标失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return errorResult;
        }
    }

    /**
     * 清空指定缓存
     */
    public boolean clearCache(String cacheType) {
        if (unifiedCacheManager == null) {
            return false;
        }

        try {
            UnifiedCacheManager.CacheType type = UnifiedCacheManager.CacheType.valueOf(cacheType.toUpperCase());
            unifiedCacheManager.clear(type);
            log.info("已清空缓存: {}", cacheType);
            return true;
        } catch (Exception e) {
            log.error("清空缓存失败: {}", cacheType, e);
            return false;
        }
    }

    /**
     * 清空所有缓存
     */
    public boolean clearAllCaches() {
        if (unifiedCacheManager == null) {
            return false;
        }

        try {
            unifiedCacheManager.clearAll();
            log.info("已清空所有缓存");
            return true;
        } catch (Exception e) {
            log.error("清空所有缓存失败", e);
            return false;
        }
    }

    /**
     * 执行缓存清理
     */
    public boolean cleanup() {
        if (unifiedCacheManager == null) {
            return false;
        }

        try {
            unifiedCacheManager.cleanup();
            log.info("缓存清理完成");
            return true;
        } catch (Exception e) {
            log.error("缓存清理失败", e);
            return false;
        }
    }
}
