package com.lg.dao.core.filter;

import java.lang.annotation.*;

/**
 * 动态数据过滤注解
 * 用于标记需要进行数据过滤的方法
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DynamicDataFilter {
    /**
     * JSON格式的过滤条件
     * 例如: {"field":"name","value":"test","operator":"eq"}
     */
    String value() default "";
    
    /**
     * 过滤字段配置（兼容旧版本）
     */
    FilterField[] fields() default {};
    
    /**
     * 表编码
     * 用于从元数据服务获取字段映射
     */
    String tableCode() default "";
} 