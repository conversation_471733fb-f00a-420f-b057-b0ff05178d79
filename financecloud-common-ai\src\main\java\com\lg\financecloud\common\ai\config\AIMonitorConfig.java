//package com.lg.financecloud.common.ai.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import java.util.Map;
//
///**
// * AI模型监控配置类
// * 用于管理AI调用的监控和追踪策略
// */
//@Data
//@ConfigurationProperties(prefix = "ai.monitor")
//public class AIMonitorConfig {
//
//    /**
//     * 是否启用监控
//     */
//    private boolean enabled = true;
//
//    /**
//     * 是否启用调用链路追踪
//     */
//    private boolean traceEnabled = true;
//
//    /**
//     * 默认监控配置
//     */
//    private MonitorConfig defaultConfig = new MonitorConfig();
//
//    /**
//     * 各模型的监控配置
//     * key: 模型类型
//     * value: 监控配置
//     */
//    private Map<String, MonitorConfig> models;
//
//    /**
//     * 监控配置
//     */
//    @Data
//    public static class MonitorConfig {
//        /**
//         * 采样率（0-100）
//         */
//        private int samplingRate = 100;
//
//        /**
//         * 慢调用阈值（毫秒）
//         */
//        private long slowCallThresholdMs = 1000;
//
//        /**
//         * 是否记录请求参数
//         */
//        private boolean logRequestParams = true;
//
//        /**
//         * 是否记录响应结果
//         */
//        private boolean logResponse = false;
//
//        /**
//         * 监控指标
//         */
//        private MetricsConfig metrics = new MetricsConfig();
//    }
//
//    /**
//     * 监控指标配置
//     */
//    @Data
//    public static class MetricsConfig {
//        /**
//         * 是否启用JVM指标
//         */
//        private boolean enableJvmMetrics = true;
//
//        /**
//         * 是否启用系统指标
//         */
//        private boolean enableSystemMetrics = true;
//
//        /**
//         * 是否启用请求统计指标
//         */
//        private boolean enableRequestMetrics = true;
//
//        /**
//         * 指标导出间隔（秒）
//         */
//        private int exportIntervalSeconds = 60;
//    }
//}