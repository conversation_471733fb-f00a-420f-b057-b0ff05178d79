//package com.lg.financecloud.common.ai.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import java.util.Map;
//
///**
// * AI模型缓存配置类
// * 用于管理AI请求响应的缓存策略
// */
//@Data
//@ConfigurationProperties(prefix = "ai.cache")
//public class AICacheConfig {
//
//    /**
//     * 是否启用缓存
//     */
//    private boolean enabled = true;
//
//    /**
//     * 默认缓存配置
//     */
//    private CacheConfig defaultConfig = new CacheConfig();
//
//    /**
//     * 各模型的缓存配置
//     * key: 模型类型
//     * value: 缓存配置
//     */
//    private Map<String, CacheConfig> models;
//
//    /**
//     * 缓存配置
//     */
//    @Data
//    public static class CacheConfig {
//        /**
//         * 缓存过期时间（秒）
//         */
//        private long expireAfterWrite = 3600;
//
//        /**
//         * 最大缓存条目数
//         */
//        private long maximumSize = 1000;
//
//        /**
//         * 是否缓存空结果
//         */
//        private boolean cacheNullValues = false;
//
//        /**
//         * 缓存预热策略
//         * NONE: 不预热
//         * EAGER: 立即预热
//         * LAZY: 懒加载预热
//         */
//        private String warmupStrategy = "NONE";
//
//        /**
//         * 缓存淘汰策略
//         * LRU: 最近最少使用
//         * LFU: 最不经常使用
//         * FIFO: 先进先出
//         */
//        private String evictionStrategy = "LRU";
//    }
//}