package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.dto.OneFileTask;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020-12-18
 * <p>
 * 远程oaApp调用接口
 */
@FeignClient(contextId = "oaAppService", value = ServiceNameConstants.OA_SERVICE)
public interface OaAppService {

	/**
	 * 添加app应用
	 *
	 * @param tenantId
	 * @param from
	 * @return
	 */
	@PostMapping("/oaapp/addAllApp")
	R addAllApp(@RequestParam("tenantId") Integer tenantId, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 添加默认班次
	 * @param tenantId 租户id
	 * @param from
	 * @return
	 */
	@PostMapping("/oaclassesnumber/addClassesNumber")
	R addClassesNumber(@RequestParam("tenantId") Integer tenantId, @RequestHeader(SecurityConstants.FROM) String from);

	@PostMapping("/archivalCatalogCfg/initArchivalCatalogCfg")
	R initArchivalCatalogCfg(@RequestParam("tenantId") Integer tenantId,@RequestParam("roleId") Integer roleId,@RequestParam("roleName") String roleName, @RequestHeader(SecurityConstants.FROM) String from);
	@PostMapping("/archival/oneClickArchivals")
	R oneClickArchivals(@RequestBody OneFileTask oneFileTask, @RequestHeader(SecurityConstants.FROM) String from);

}
