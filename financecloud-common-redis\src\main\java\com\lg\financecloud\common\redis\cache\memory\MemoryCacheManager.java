package com.lg.financecloud.common.redis.cache.memory;

import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.common.core.constant.CacheConstants;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.convert.DurationStyle;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.lang.Nullable;

import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于Caffeine的内存缓存管理器实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class MemoryCacheManager implements CacheManager {
    
    private final Map<String, MemoryCache> cacheMap = new ConcurrentHashMap<>();
    private final int maxSize;
    private final long timeToLiveSeconds;
    private final long timeToIdleSeconds;
    
    private static final String SPLIT_FLAG = "#";
    private static final int CACHE_LENGTH = 2;
    
    /**
     * 创建内存缓存管理器
     *
     * @param maxSize 每个缓存的最大条目数
     * @param timeToLiveSeconds 默认存活时间（秒）
     * @param timeToIdleSeconds 默认空闲时间（秒）
     */
    public MemoryCacheManager(int maxSize, long timeToLiveSeconds, long timeToIdleSeconds) {
        this.maxSize = maxSize;
        this.timeToLiveSeconds = timeToLiveSeconds;
        this.timeToIdleSeconds = timeToIdleSeconds;
        log.info("初始化Caffeine内存缓存管理器: 最大条目数={}, 存活时间={}秒, 空闲时间={}秒", 
                maxSize, timeToLiveSeconds, timeToIdleSeconds);
    }
    
    @Override
    @Nullable
    public Cache getCache(String name) {
        // 处理全局缓存名称（不特定于租户）
        if (name.startsWith(CacheConstants.GLOBALLY)) {
            return getOrCreateCache(name);
        }
        
        // 为租户隔离添加租户前缀
        return getOrCreateCache(TenantContextHolder.getTenantId() + StrUtil.COLON + name);
    }
    
    @Override
    public Collection<String> getCacheNames() {
        return Collections.unmodifiableCollection(cacheMap.keySet());
    }
    
    private MemoryCache getOrCreateCache(String name) {
        // 解析缓存名称中的TTL（格式：cacheName#ttl）
        long ttl = timeToLiveSeconds;
        String cacheName = name;
        
        if (StrUtil.isNotBlank(name) && name.contains(SPLIT_FLAG)) {
            String[] cacheArray = name.split(SPLIT_FLAG);
            if (cacheArray.length >= CACHE_LENGTH) {
                cacheName = cacheArray[0];
                try {
                    ttl = Long.parseLong(cacheArray[1]);
                    log.debug("缓存[{}]配置了自定义TTL: {}秒", cacheName, ttl);
                } catch (NumberFormatException e) {
                    // 解析失败时使用默认TTL
                    log.warn("解析缓存[{}]的TTL失败，使用默认TTL: {}秒", name, timeToLiveSeconds);
                }
            }
        }
        
        // 如果缓存不存在则创建
        long finalTtl = ttl;
        return cacheMap.computeIfAbsent(cacheName,
                k -> new MemoryCache(k, maxSize, finalTtl, timeToIdleSeconds));
    }
    
    /**
     * 清除此缓存管理器中的所有缓存
     */
    public void clearAll() {
        log.info("清除所有内存缓存，缓存数: {}", cacheMap.size());
        cacheMap.values().forEach(Cache::clear);
    }
    
    /**
     * 获取所有缓存的统计信息
     *
     * @return 每个缓存的统计信息
     */
    public Map<String, com.github.benmanes.caffeine.cache.stats.CacheStats> getStats() {
        Map<String, com.github.benmanes.caffeine.cache.stats.CacheStats> stats = new ConcurrentHashMap<>();
        cacheMap.forEach((name, cache) -> stats.put(name, cache.stats()));
        return stats;
    }
} 