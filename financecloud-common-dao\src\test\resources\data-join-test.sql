-- 插入用户数据
INSERT INTO t_user (id, username, email, status) VALUES 
(101, 'john', '<EMAIL>', 'active'),
(102, 'mary', '<EMAIL>', 'active'),
(103, 'bob', '<EMAIL>', 'inactive');

-- 插入订单数据
INSERT INTO t_order (id, user_id, amount, create_time, status) VALUES 
(1, 101, 100.00, '2023-01-01 10:00:00', 'PAID'),
(2, 101, 200.00, '2023-01-02 11:00:00', 'SHIPPED'),
(3, 102, 150.00, '2023-01-03 12:00:00', 'PAID'),
(4, 103, 300.00, '2023-01-04 13:00:00', 'CANCELLED');

-- 插入订单项数据
INSERT INTO t_order_item (id, order_id, product_name, quantity, price) VALUES 
(1, 1, 'Product A', 2, 50.00),
(2, 1, 'Product B', 1, 0.00),
(3, 2, 'Product C', 1, 200.00),
(4, 3, 'Product A', 3, 50.00),
(5, 4, 'Product D', 1, 300.00); 