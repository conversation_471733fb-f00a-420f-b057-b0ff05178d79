package com.lg.dao.core.validation;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.EntityInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.*;
import java.util.*;

/**
 * 实体模式验证器
 * 验证实体字段与数据库元数据，生成差异警告和SQL脚本
 */
@Slf4j
public class EntitySchemaValidator {

    private final DataSource dataSource;
    private final JdbcTemplate jdbcTemplate;
    private final String schema;
    private final Set<String> validatedEntities = new HashSet<>();
    private final boolean generateSql;
    private final boolean logWarnings;
    
    /**
     * 构造函数（默认生成SQL脚本和记录警告日志）
     * @param dataSource 数据源
     * @param schema 数据库架构名（可为null）
     */
    public EntitySchemaValidator(DataSource dataSource, String schema) {
        this(dataSource, schema, true, true);
    }
    
    /**
     * 构造函数（可定制参数）
     * @param dataSource 数据源
     * @param schema 数据库架构名（可为null）
     * @param generateSql 是否生成SQL脚本
     * @param logWarnings 是否记录警告日志
     */
    public EntitySchemaValidator(DataSource dataSource, String schema, boolean generateSql, boolean logWarnings) {
        this.dataSource = dataSource;
        this.schema = schema;
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.generateSql = generateSql;
        this.logWarnings = logWarnings;
    }
    
    /**
     * 验证实体与数据库架构
     * @param entityClass 实体类
     * @return 验证结果
     */
    public ValidationResult validate(Class<?> entityClass) {
        return validate(EntityInfoManager.getInstance().getEntityInfo(entityClass));
    }
    
    /**
     * 验证实体与数据库架构
     * @param entityInfo 实体信息
     * @return 验证结果
     */
    public ValidationResult validate(EntityInfo entityInfo) {
        String tableName = entityInfo.getTableName();
        
        // 如果已验证过，跳过
        if (validatedEntities.contains(tableName)) {
            return ValidationResult.builder()
                    .entityInfo(entityInfo)
                    .build();
        }
        
        try {
            // 获取数据库表元数据
            DatabaseTableInfo tableInfo = getDatabaseTableInfo(tableName);
            
            // 比较实体与数据库表
            ValidationResult result = compareEntityWithTable(entityInfo, tableInfo);
            
            // 如果启用日志警告且存在差异，记录警告
            if (logWarnings && !result.getDifferences().isEmpty()) {
                logValidationWarnings(result);
            }
            
            // 标记为已验证
            validatedEntities.add(tableName);
            
            return result;
        } catch (Exception e) {
            log.warn("验证实体架构失败: {}", tableName, e);
            return ValidationResult.builder()
                    .entityInfo(entityInfo)
                    .error("验证失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 比较实体字段与数据库表列
     */
    private ValidationResult compareEntityWithTable(EntityInfo entityInfo, DatabaseTableInfo tableInfo) {
        ValidationResult.ValidationResultBuilder resultBuilder = ValidationResult.builder()
                .entityInfo(entityInfo)
                .databaseTableInfo(tableInfo);
        
        List<SchemaDifference> differences = new ArrayList<>();
        List<String> sqlScripts = new ArrayList<>();
        
        // 检查表是否存在
        if (tableInfo == null) {
            SchemaDifference difference = SchemaDifference.builder()
                    .type(DifferenceType.TABLE_MISSING)
                    .objectName(entityInfo.getTableName())
                    .build();
            differences.add(difference);
            
            if (generateSql) {
                sqlScripts.add(generateCreateTableScript(entityInfo));
            }
            
            return resultBuilder
                    .differences(differences)
                    .sqlScripts(sqlScripts)
                    .build();
        }
        
        // 检查数据库中缺少的列
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            String columnName = fieldInfo.getColumn();
            
            if (!tableInfo.getColumns().containsKey(columnName)) {
                SchemaDifference difference = SchemaDifference.builder()
                        .type(DifferenceType.COLUMN_MISSING)
                        .objectName(columnName)
                        .entityField(fieldInfo.getField().getName())
                        .details("数据库表中缺少该列")
                        .build();
                differences.add(difference);
                
                if (generateSql) {
                    sqlScripts.add(generateAddColumnScript(entityInfo.getTableName(), fieldInfo));
                }
            } else {
                // 列存在，检查类型差异（仅当实体字段定义了类型和长度时）
                if (hasExplicitTypeDefinition(fieldInfo.getField())) {
                    DatabaseColumnInfo columnInfo = tableInfo.getColumns().get(columnName);
                    String entityType = fieldInfo.getDbType();
                    String dbType = columnInfo.getTypeName();
                    
                    if (!isCompatibleType(entityType, dbType)) {
                        SchemaDifference difference = SchemaDifference.builder()
                                .type(DifferenceType.TYPE_MISMATCH)
                                .objectName(columnName)
                                .entityField(fieldInfo.getField().getName())
                                .details("类型不匹配: 实体=" + entityType + ", 数据库=" + dbType)
                                .build();
                        differences.add(difference);
                        
                        if (generateSql) {
                            sqlScripts.add(generateAlterColumnTypeScript(entityInfo.getTableName(), fieldInfo));
                        }
                    }
                }
            }
        }
        
        // 检查数据库中存在但实体中没有的列
        for (String columnName : tableInfo.getColumns().keySet()) {
            boolean found = false;
            for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
                if (columnName.equals(fieldInfo.getColumn())) {
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                SchemaDifference difference = SchemaDifference.builder()
                        .type(DifferenceType.EXTRA_COLUMN)
                        .objectName(columnName)
                        .details("数据库表中存在额外列，未映射到实体")
                        .build();
                differences.add(difference);
            }
        }
        
        return resultBuilder
                .differences(differences)
                .sqlScripts(sqlScripts)
                .build();
    }
    
    /**
     * 检查字段是否显式定义了类型和长度
     */
    private boolean hasExplicitTypeDefinition(Field field) {
        Column column = field.getAnnotation(Column.class);
        if (column != null) {
            return column.length() > 0 || !column.columnDefinition().isEmpty();
        }
        
        // 检查MyBatis-Plus的@TableField注解是否定义了类型
        try {
            if (field.isAnnotationPresent(com.baomidou.mybatisplus.annotation.TableField.class)) {
                return true;
            }
        } catch (Throwable e) {
            // 忽略MyBatis-Plus不可用的情况
        }
        
        return false;
    }
    
    /**
     * 检查数据库类型是否与实体类型兼容
     */
    private boolean isCompatibleType(String entityType, String dbType) {
        if (entityType == null || dbType == null) {
            return false;
        }
        
        // 转换为大写进行比较
        entityType = entityType.toUpperCase();
        dbType = dbType.toUpperCase();
        
        // 简单兼容性检查 - 可以根据需要增强具体规则
        if (entityType.contains(dbType) || dbType.contains(entityType)) {
            return true;
        }
        
        // 常见类型映射
        if ((entityType.contains("VARCHAR") && dbType.contains("CHAR")) ||
            (entityType.contains("INT") && dbType.contains("INTEGER")) ||
            (entityType.contains("DECIMAL") && dbType.contains("NUMERIC")) ||
            (entityType.contains("DATETIME") && dbType.contains("TIMESTAMP"))) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 生成创建表的SQL脚本
     */
    private String generateCreateTableScript(EntityInfo entityInfo) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(entityInfo.getTableName()).append(" (\n");
        
        List<String> columnDefs = new ArrayList<>();
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            String columnDef = "    " + fieldInfo.getColumn() + " " + fieldInfo.getDbType();
            
            if (fieldInfo.isId()) {
                columnDef += " PRIMARY KEY";
                if (fieldInfo.getIdType() != null && 
                    fieldInfo.getIdType() == com.baomidou.mybatisplus.annotation.IdType.AUTO) {
                    columnDef += " AUTO_INCREMENT";
                }
            }
            
            columnDefs.add(columnDef);
        }
        
        sql.append(String.join(",\n", columnDefs));
        sql.append("\n);\n");
        
        return sql.toString();
    }
    
    /**
     * 生成添加列的SQL脚本
     */
    private String generateAddColumnScript(String tableName, EntityInfo.FieldInfo fieldInfo) {
        return String.format("ALTER TABLE %s ADD COLUMN %s %s;", 
                tableName, fieldInfo.getColumn(), fieldInfo.getDbType());
    }
    
    /**
     * 生成修改列类型的SQL脚本
     */
    private String generateAlterColumnTypeScript(String tableName, EntityInfo.FieldInfo fieldInfo) {
        return String.format("ALTER TABLE %s MODIFY COLUMN %s %s;", 
                tableName, fieldInfo.getColumn(), fieldInfo.getDbType());
    }
    
    /**
     * 获取数据库表元数据
     * 公开此方法以便共享元数据信息
     */
    public DatabaseTableInfo getDatabaseTableInfo(String tableName) {
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            // 检查表是否存在
            try (ResultSet rs = metaData.getTables(
                    null, schema, tableName, new String[]{"TABLE"})) {
                if (!rs.next()) {
                    return null; // 表不存在
                }
            }
            
            DatabaseTableInfo tableInfo = new DatabaseTableInfo();
            tableInfo.setTableName(tableName);
            
            // 获取列
            try (ResultSet rs = metaData.getColumns(null, schema, tableName, null)) {
                while (rs.next()) {
                    DatabaseColumnInfo columnInfo = new DatabaseColumnInfo();
                    columnInfo.setColumnName(rs.getString("COLUMN_NAME"));
                    columnInfo.setTypeName(rs.getString("TYPE_NAME"));
                    columnInfo.setColumnSize(rs.getInt("COLUMN_SIZE"));
                    columnInfo.setNullable(rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                    columnInfo.setOrdinalPosition(rs.getInt("ORDINAL_POSITION"));
                    
                    tableInfo.getColumns().put(columnInfo.getColumnName(), columnInfo);
                }
            }
            
            // 获取主键
            try (ResultSet rs = metaData.getPrimaryKeys(null, schema, tableName)) {
                while (rs.next()) {
                    String columnName = rs.getString("COLUMN_NAME");
                    DatabaseColumnInfo columnInfo = tableInfo.getColumns().get(columnName);
                    if (columnInfo != null) {
                        columnInfo.setPrimaryKey(true);
                    }
                }
            }
            
            return tableInfo;
        } catch (SQLException e) {
            log.error("获取表元数据失败: {}", tableName, e);
            return null;
        }
    }
    
    /**
     * 记录验证警告日志
     */
    private void logValidationWarnings(ValidationResult result) {
        if (result.getDifferences().isEmpty()) {
            return;
        }
        
        log.warn("表 {} 发现架构差异", 
                result.getEntityInfo().getTableName());
        
        for (SchemaDifference diff : result.getDifferences()) {
            log.warn("  - {}: {}{}", 
                    diff.getType(), 
                    diff.getObjectName(),
                    StringUtils.hasText(diff.getDetails()) ? " (" + diff.getDetails() + ")" : "");
        }
        
        if (!result.getSqlScripts().isEmpty()) {
            log.warn("修复差异的SQL脚本:");
            for (String sql : result.getSqlScripts()) {
                log.warn("  {}", sql);
            }
        }
    }
    
    /**
     * 数据库表信息
     */
    public static class DatabaseTableInfo {
        private String tableName;
        private Map<String, DatabaseColumnInfo> columns = new HashMap<>();
        
        public String getTableName() {
            return tableName;
        }
        
        public void setTableName(String tableName) {
            this.tableName = tableName;
        }
        
        public Map<String, DatabaseColumnInfo> getColumns() {
            return columns;
        }
        
        public void setColumns(Map<String, DatabaseColumnInfo> columns) {
            this.columns = columns;
        }
    }
    
    /**
     * 数据库列信息
     */
    public static class DatabaseColumnInfo {
        private String columnName;
        private String typeName;
        private int columnSize;
        private boolean nullable;
        private boolean primaryKey;
        private int ordinalPosition;
        
        public String getColumnName() {
            return columnName;
        }
        
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        
        public String getTypeName() {
            return typeName;
        }
        
        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }
        
        public int getColumnSize() {
            return columnSize;
        }
        
        public void setColumnSize(int columnSize) {
            this.columnSize = columnSize;
        }
        
        public boolean isNullable() {
            return nullable;
        }
        
        public void setNullable(boolean nullable) {
            this.nullable = nullable;
        }
        
        public boolean isPrimaryKey() {
            return primaryKey;
        }
        
        public void setPrimaryKey(boolean primaryKey) {
            this.primaryKey = primaryKey;
        }
        
        public int getOrdinalPosition() {
            return ordinalPosition;
        }
        
        public void setOrdinalPosition(int ordinalPosition) {
            this.ordinalPosition = ordinalPosition;
        }
    }
}
