package com.lg.dao.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置属性
 * 支持为每种缓存类型配置独立的参数
 */
@Data
@ConfigurationProperties(prefix = "light.orm.cache")
public class CacheProperties {

    /**
     * 是否启用缓存
     */
    private boolean enable = true;

    /**
     * 各缓存类型的具体配置
     */
    private Map<String, CacheConfig> types = new HashMap<>();

    /**
     * 获取指定缓存类型的配置
     * 如果没有配置，则使用该缓存类型的默认值
     */
    public CacheConfig getConfig(String cacheType) {
        CacheConfig config = types.get(cacheType);
        if (config != null) {
            return config;
        }

        // 返回该缓存类型的默认配置
        return getDefaultConfig(cacheType);
    }

    /**
     * 获取缓存类型的默认配置
     * 根据不同缓存类型的使用特点设计合理的默认值
     */
    private CacheConfig getDefaultConfig(String cacheType) {
        switch (cacheType) {
            case "dao":
                // DAO缓存：中等容量，较长过期时间
                return new CacheConfig(50, 1800, "DAO操作缓存，用于缓存数据库查询结果");

            case "entity":
                // 实体缓存：大容量，长过期时间
                return new CacheConfig(100, 1800, "实体信息缓存，用于缓存实体元数据");
            case "entity_field_mapping":
                // 实体缓存：大容量，长过期时间
                return new CacheConfig(500, 1800, "实体字段缓存，用于缓存实体元数据");

            case "permission":
                // 权限缓存：中等容量，中等过期时间
                return new CacheConfig(1000, 1800, "权限缓存，用于缓存用户权限信息");

            case "sql_template":
                // SQL模板缓存：大容量，长过期时间
                return new CacheConfig(200, 1800, "SQL模板缓存，用于缓存模板渲染结果");

            case "metadata":
                // 元数据缓存：小容量，长过期时间
                return new CacheConfig(100, 1800, "元数据缓存，用于缓存数据库元数据信息");

            case "mybatis_proxy":
                // MyBatis代理缓存：小容量，长过期时间
                return new CacheConfig(200, 1800, "MyBatis代理缓存，用于缓存Mapper方法");

            case "mybatis_sql_parse":
                // MyBatis SQL解析缓存：中等容量，中等过期时间
                return new CacheConfig(200, 1800, "MyBatis SQL解析缓存，用于缓存SQL解析结果");

            case "query_result":
                // 查询结果缓存：大容量，短过期时间
                return new CacheConfig(1000, 1800, "查询结果缓存，用于缓存查询结果");

            default:
                // 默认配置：小容量，中等过期时间
                return new CacheConfig(100, 1800, "默认缓存配置");
        }
    }

    /**
     * 单个缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 缓存最大容量
         */
        private int maxSize;

        /**
         * 缓存过期时间（秒）
         */
        private int expireSeconds;

        /**
         * 缓存描述信息
         */
        private String description;

        public CacheConfig() {}

        public CacheConfig(int maxSize, int expireSeconds) {
            this.maxSize = maxSize;
            this.expireSeconds = expireSeconds;
        }

        public CacheConfig(int maxSize, int expireSeconds, String description) {
            this.maxSize = maxSize;
            this.expireSeconds = expireSeconds;
            this.description = description;
        }
    }

    /**
     * 获取所有缓存类型的默认配置（用于文档生成）
     */
    public Map<String, CacheConfig> getAllDefaultConfigs() {
        Map<String, CacheConfig> defaultConfigs = new HashMap<>();
        String[] cacheTypes = {
            "dao", "entity", "permission", "sql_template",
            "metadata", "mybatis_proxy", "mybatis_sql_parse", "query_result"
        };

        for (String cacheType : cacheTypes) {
            defaultConfigs.put(cacheType, getDefaultConfig(cacheType));
        }

        return defaultConfigs;
    }
}