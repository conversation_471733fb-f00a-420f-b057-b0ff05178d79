//package com.lg.financecloud.common.ai.config;
//
//import com.lg.financecloud.common.ai.factory.AIServiceFactory;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//
///**
// * AI框架自动配置类
// * 提供框架的自动装配功能
// */
//@Configuration
//@EnableConfigurationProperties(AIModelProperties.class)
//@ComponentScan(basePackages = "com.lg.financecloud.common.ai")
//public class AIAutoConfiguration {
//
//    /**
//     * 配置AI服务工厂
//     * 当容器中不存在AIServiceFactory实例时创建默认实例
//     */
//    @Bean
//    @ConditionalOnMissingBean
//    public AIServiceFactory aiServiceFactory() {
//        return new AIServiceFactory();
//    }
//}