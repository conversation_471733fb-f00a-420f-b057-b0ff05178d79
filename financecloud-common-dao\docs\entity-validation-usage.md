# 数据库字段校验功能使用指南

## 功能概述

数据库字段校验功能是 LightORM 框架的一项新特性，它能够自动比对实体字段和数据库元数据，发现差异并提供警告和 SQL 修复脚本。该功能对于确保实体类和数据库模式保持同步非常有用，有助于发现和解决开发过程中的不一致问题。

## 主要特性

1. **自动元数据比对**：比对实体类字段和数据库表结构，检测差异
2. **差异警告日志**：输出详细的差异警告日志，便于快速定位问题
3. **SQL 修复脚本**：自动生成修复差异的 SQL 脚本
4. **智能类型比对**：只比对实体上明确定义了类型和长度的字段
5. **自动配置支持**：通过简单配置即可启用和定制校验功能
6. **异步验证**：使用独立线程执行验证，不阻塞应用启动
7. **开发环境限制**：默认仅在开发环境启用，避免影响生产环境性能
8. **统一租户管理**：使用 TenantContextHolder 统一管理租户信息
9. **通配符包名支持**：支持使用通配符配置要验证的包名

## 配置方式

在 `application.yml` 或 `application.properties` 中添加如下配置：

```yaml
light:
  orm:
    validation:
      enabled: true                     # 启用校验功能
      packages: com.example.*.entity    # 要验证的实体类包路径（支持通配符）
      generate-sql: true                # 是否生成 SQL 修复脚本
      log-warnings: true                # 是否记录警告日志
      async: true                       # 是否异步执行验证（不阻塞应用启动）
      dev-only: true                    # 是否仅在开发环境启用
```

### 通配符包名配置

现在支持使用通配符来配置要验证的包名，提供更灵活的配置方式：

- **单星号 (`*`)**：匹配单层包名，例如 `com.example.*.entity` 匹配 `com.example.module.entity`，但不匹配 `com.example.module.sub.entity`
- **双星号 (`**`)**：匹配多层包名，例如 `com.example.**.entity` 匹配 `com.example.module.entity` 和 `com.example.module.sub.entity`

示例配置：

```yaml
light:
  orm:
    validation:
      packages: com.example.*.entity, com.example.module.**.model
```

这将验证以下包中的实体：
- `com.example.module1.entity`
- `com.example.module2.entity`
- `com.example.module.model`
- `com.example.module.sub.model`
- `com.example.module.sub.sub.model`

## 差异类型

校验功能能够检测以下类型的差异：

1. **表缺失（TABLE_MISSING）**：数据库中缺少实体对应的表
2. **列缺失（COLUMN_MISSING）**：数据库表中缺少实体字段对应的列
3. **额外列（EXTRA_COLUMN）**：数据库表中存在实体类中没有的列
4. **类型不匹配（TYPE_MISMATCH）**：字段类型与数据库列类型不匹配

## 多租户增强功能

校验功能与多租户功能深度集成，提供以下增强：

### 1. SCHEMA 模式增强

在 SCHEMA 模式下，除了使用不同的 schema 外，现在也会检查表中是否存在 `tenant_id` 列，如果存在则同样添加租户条件，提供双重保障。

```java
// 如果表有 tenant_id 列，即使在 SCHEMA 模式下也会添加条件
if (tableHasTenantColumn(tableName)) {
    // 添加 tenant_id = 'xxx' 条件
}
```

### 2. COLUMN 模式增强

在 COLUMN 模式下，现在会先检查表是否实际包含 `tenant_id` 列，避免在不支持的表上添加条件导致 SQL 错误：

```java
// 只在表实际有 tenant_id 列的情况下添加条件
if (tableHasTenantColumn(tableName)) {
    // 添加 tenant_id = 'xxx' 条件
} else {
    log.debug("表 {} 没有租户ID列，无法应用租户过滤", tableName);
}
```

### 3. 平台租户特殊处理

对于平台租户（tenant_id = 1），在 SCHEMA 模式下不会添加 schema 前缀，而是直接使用默认 schema，仅添加租户条件：

```java
// 检查是否是平台租户
boolean isPlatformTenant = tenantId != null && "1".equals(tenantId.toString());
if (isPlatformTenant && properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
    log.debug("平台租户不需要修改SCHEMA");
    // 仅添加租户条件，不修改schema
    return processColumnMode(sql);
}
```

### 4. 统一租户上下文

框架现在统一使用 `TenantContextHolder` 来管理租户信息，替代了原来的 `TenantContext`：

```java
// 获取当前租户ID
Integer tenantId = TenantContextHolder.getTenantId();

// 设置当前租户ID
TenantContextHolder.setTenantId(1);

// 清除租户上下文
TenantContextHolder.clear();

// 检查是否是平台租户
boolean isPlatformTenant = tenantId != null && "1".equals(tenantId.toString());
```

原来的 `TenantContext` 类被保留以保持向后兼容性，但已标记为过时，建议使用 `TenantContextHolder` 代替。

## 使用示例

### 1. 配置校验功能

```yaml
light:
  orm:
    validation:
      enabled: true
      packages: com.example.**.entity, com.example.**.domain
      async: true
      dev-only: true
```

### 2. 应用启动时自动校验

配置完成后，应用启动时会自动扫描并校验指定包中的实体类。校验结果会在日志中输出：

```
INFO: 开始准备实体架构验证: [com.example.**.entity, com.example.**.domain]
INFO: 展开通配符后的包名: [com.example.module1.entity, com.example.module2.entity, com.example.module1.domain]
INFO: 使用异步模式执行实体架构验证
INFO: 找到 42 个实体类待验证
WARN: 表 t_user 发现架构差异
  - COLUMN_MISSING: phone_number (数据库表中缺少该列)
  - TYPE_MISMATCH: email (类型不匹配: 实体=VARCHAR(255), 数据库=VARCHAR(50))
WARN: 修复差异的SQL脚本:
  ALTER TABLE t_user ADD COLUMN phone_number VARCHAR(255);
  ALTER TABLE t_user MODIFY COLUMN email VARCHAR(255);
INFO: 实体验证完成. 发现 2 个实体存在验证错误
```

### 3. 编程式使用校验器

也可以在代码中直接使用 `EntitySchemaValidator`：

```java
@Autowired
private EntitySchemaValidator validator;

public void validateUserEntity() {
    ValidationResult result = validator.validate(User.class);
    
    if (result.hasDifferences()) {
        log.warn("实体与数据库存在差异:");
        result.getDifferences().forEach(diff -> 
            log.warn("  - {}: {}", diff.getType(), diff.getObjectName()));
        
        if (!result.getSqlScripts().isEmpty()) {
            log.warn("修复SQL:");
            result.getSqlScripts().forEach(sql -> log.warn("  {}", sql));
        }
    }
}
```

## 实体信息统一管理

为了提高性能和共享元数据信息，新增了 `EntityInfoRegistry` 统一管理实体信息：

```java
// 获取单例实例
EntityInfoRegistry registry = EntityInfoRegistry.getInstance();

// 初始化（通常由框架自动完成）
registry.initialize(dataSource, schema);

// 获取实体信息
EntityInfo entityInfo = registry.getEntityInfo(User.class);

// 获取数据库表信息
DatabaseTableInfo tableInfo = registry.getDatabaseTableInfo("t_user");

// 检查表是否有特定列
boolean hasTenantColumn = registry.tableHasTenantColumn("t_user", "tenant_id");
```

## 优化特性

1. **性能优化**：使用缓存减少数据库元数据查询，提高验证性能
2. **智能类型检测**：只比对实体上明确定义了类型和长度的字段，避免误报
3. **信息共享**：多租户拦截器复用元数据信息，减少重复查询
4. **统一管理**：通过 `EntityInfoRegistry` 统一管理实体信息和数据库元数据
5. **异步验证**：使用独立线程执行验证，不阻塞应用启动
6. **环境感知**：自动检测当前环境，仅在开发环境启用验证功能
7. **租户上下文统一**：统一使用 `TenantContextHolder` 管理租户信息
8. **通配符支持**：支持使用 `*` 和 `**` 通配符配置要验证的包名

## 最佳实践

1. **开发环境启用**：在开发环境中启用此功能，帮助开发人员及时发现并修复实体与数据库的不一致
2. **生成 SQL 脚本**：利用生成的 SQL 脚本快速修复差异
3. **CI/CD 集成**：在 CI/CD 流程中添加校验步骤，确保部署前发现潜在问题
4. **明确类型定义**：在实体字段上明确定义类型和长度，以便进行准确的类型比对
5. **异步模式**：使用异步模式执行验证，避免延长应用启动时间
6. **使用最新API**：使用 `TenantContextHolder` 替代过时的 `TenantContext`
7. **使用通配符**：使用通配符配置包名，避免手动列举所有包

## 注意事项

1. **性能影响**：校验过程涉及数据库元数据查询，会稍微延长应用启动时间（使用异步模式可缓解）
2. **缓存机制**：为减少性能影响，校验结果和表结构信息会被缓存
3. **生产环境**：默认在生产环境自动禁用，避免不必要的日志和性能开销
4. **多数据源**：当前版本仅支持校验主数据源，对于多数据源场景需要额外配置
5. **租户ID类型**：`TenantContextHolder` 使用 `Integer` 类型的租户ID，而不是字符串
6. **通配符性能**：使用过于宽泛的通配符（如 `com.**`）可能会导致扫描大量不必要的类，影响性能

## 故障排除

### 问题：验证报告类型不匹配但实际兼容

可能的原因：

1. 数据库方言不匹配导致类型比较错误
2. 自定义类型映射未被识别

解决方法：在实体类上使用更精确的注解指定类型，或扩展 `EntityInfo.DbDialect` 接口提供自定义类型映射。

### 问题：启动时验证过程耗时较长

可能的原因：

1. 大量实体需要验证
2. 数据库连接较慢
3. 通配符配置过于宽泛

解决方法：
- 缩小验证包范围
- 确保使用异步验证模式
- 使用更精确的通配符，避免扫描不必要的类 