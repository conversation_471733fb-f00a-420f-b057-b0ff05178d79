package com.lg.dao.core.builder;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.exception.DaoException;
import com.lg.dao.core.fill.MetaObjectHandler;
import com.lg.dao.core.sql.SqlBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.EnumType;
import java.lang.reflect.Field;
import java.util.*;

/**
 * SQL构建服务 - 负责各种SQL的构建
 */
@Slf4j
@RequiredArgsConstructor
public class SqlBuilderService {
    
    private final MetaObjectHandler metaObjectHandler;
    
    /**
     * 构建插入SQL
     */
    public <T> SqlBuilder buildInsertSql(T entity, EntityInfo entityInfo) {
        if (entity == null) {
            throw new DaoException("INVALID_ENTITY", "Entity cannot be null");
        }
        
        // 自动填充
        if (metaObjectHandler != null) {
            metaObjectHandler.insertFill(entity);
        }
        
        SqlBuilder builder = new SqlBuilder();
        List<String> columns = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        
        // 收集字段
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            try {
                Field field = fieldInfo.getField();
                field.setAccessible(true);
                Object value = field.get(entity);
                
                if (shouldIncludeInInsert(fieldInfo, value)) {
                    columns.add(fieldInfo.getColumn());
                    values.add(processFieldValue(value, fieldInfo));
                }
            } catch (IllegalAccessException e) {
                throw new DaoException("FIELD_ACCESS_ERROR", 
                    "Failed to access field: " + fieldInfo.getField().getName(), e);
            }
        }
        
        if (columns.isEmpty()) {
            throw new DaoException("NO_INSERTABLE_FIELDS", "No fields to insert found in entity");
        }
        
        // 构建SQL
        builder.append("INSERT INTO " + entityInfo.getTableName() + " (")
               .append(String.join(", ", columns))
               .append(") VALUES (")
               .append(String.join(", ", Collections.nCopies(values.size(), "?")))
               .append(")");
        
        values.forEach(builder::addParam);
        return builder;
    }
    
    /**
     * 构建更新SQL
     */
    public <T> SqlBuilder buildUpdateSql(T entity, EntityInfo entityInfo) {
        if (entity == null) {
            throw new DaoException("INVALID_ENTITY", "Entity cannot be null");
        }
        
        if (entityInfo.getIdFields().isEmpty()) {
            throw new DaoException("NO_ID_FIELD", 
                "No @Id field found in entity: " + entity.getClass().getName());
        }
        
        // 自动填充
        if (metaObjectHandler != null) {
            metaObjectHandler.updateFill(entity);
        }
        
        SqlBuilder builder = new SqlBuilder();
        List<String> updates = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        
        // 收集需要更新的字段
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            if (fieldInfo.isId()) continue;
            
            try {
                Field field = fieldInfo.getField();
                field.setAccessible(true);
                Object value = field.get(entity);
                
                if (value != null && fieldInfo.isUpdatable()) {
                    updates.add(fieldInfo.getColumn() + " = ?");
                    values.add(processFieldValue(value, fieldInfo));
                }
            } catch (IllegalAccessException e) {
                throw new DaoException("FIELD_ACCESS_ERROR", 
                    "Failed to access field: " + fieldInfo.getField().getName(), e);
            }
        }
        
        if (updates.isEmpty()) {
            throw new DaoException("NO_UPDATABLE_FIELDS", "No non-null fields to update");
        }
        
        // 构建SQL
        builder.append("UPDATE " + entityInfo.getTableName())
               .append(" SET ")
               .append(String.join(", ", updates));
        
        // 添加WHERE条件
        addWhereConditions(builder, entity, entityInfo, values);
        
        values.forEach(builder::addParam);
        return builder;
    }
    
    /**
     * 构建删除SQL
     */
    public <T> SqlBuilder buildDeleteSql(T entity, EntityInfo entityInfo) {
        if (entity == null) {
            throw new DaoException("INVALID_ENTITY", "Entity cannot be null");
        }
        
        if (entityInfo.getIdFields().isEmpty()) {
            throw new DaoException("NO_ID_FIELD", 
                "No @Id field found in entity: " + entity.getClass().getName());
        }
        
        SqlBuilder builder = new SqlBuilder();
        builder.append("DELETE FROM " + entityInfo.getTableName());
        
        List<Object> values = new ArrayList<>();
        addWhereConditions(builder, entity, entityInfo, values);
        
        values.forEach(builder::addParam);
        return builder;
    }
    
    private boolean shouldIncludeInInsert(EntityInfo.FieldInfo fieldInfo, Object value) {
        if (fieldInfo.isId()) {
            if (fieldInfo.getIdType() == null || 
                fieldInfo.getIdType() == com.baomidou.mybatisplus.annotation.IdType.INPUT) {
                return value != null;
            } else if (fieldInfo.getIdType() == com.baomidou.mybatisplus.annotation.IdType.AUTO) {
                return false;
            } else {
                return value != null;
            }
        }
        return value != null && fieldInfo.isInsertable();
    }
    
    private Object processFieldValue(Object value, EntityInfo.FieldInfo fieldInfo) {
        if (value != null && value.getClass().isEnum()) {
            if (fieldInfo.getEnumType() == EnumType.STRING) {
                return ((Enum<?>) value).name();
            } else {
                return ((Enum<?>) value).ordinal();
            }
        }
        return value;
    }
    
    private <T> void addWhereConditions(SqlBuilder builder, T entity, EntityInfo entityInfo, List<Object> values) {
        List<String> whereConditions = new ArrayList<>();
        
        for (EntityInfo.FieldInfo idFieldInfo : entityInfo.getIdFields()) {
            try {
                Field idField = idFieldInfo.getField();
                idField.setAccessible(true);
                Object idValue = idField.get(entity);
                
                if (idValue == null) {
                    throw new DaoException("NULL_ID_VALUE", 
                        "Id value cannot be null: " + idField.getName());
                }
                
                whereConditions.add(idFieldInfo.getColumn() + " = ?");
                values.add(idValue);
            } catch (IllegalAccessException e) {
                throw new DaoException("FIELD_ACCESS_ERROR", "Failed to get id value", e);
            }
        }
        
        builder.append(" WHERE ").append(String.join(" AND ", whereConditions));
    }
}