/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 消息
 *
 * <AUTHOR>
 * @date 2023-03-23 14:09:39
 */
@Data
@TableName("base_notice")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "消息")
public class BaseNotice extends Model<BaseNotice> {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String titile;
    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     * 消息类型:枚举
     */
    @ApiModelProperty(value = "消息类型:枚举")
    private String type;
    /**
     * 通知状态：0 草稿  1已发布
     */
    @ApiModelProperty(value = "通知状态：0 草稿  1已发布")
    private String status;
    /**
     * 是否需要审核：默认0 不需要 1需要
     */
    @ApiModelProperty(value = "是否需要审核：默认0 不需要 1需要")
    private String needProcess;
    /**
     * 消息过期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "消息过期时间")
    private Date expireTime;
    /**
     * 消息来源：1平台  2用户
     */
    @ApiModelProperty(value = "消息来源：0平台  1用户")
    private String resourceType;
    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * create_by
     */
    @ApiModelProperty(value = "create_by")
    private Long createBy;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

    /**
     * 发送通道 对应的模板参数   [{"channel":"",tplId:“”,tplParams:{}}]
     */
    @ApiModelProperty(value = "发送通道 对应的模板参数   [{\"channel\":\"\",tplId:“”,tplParams:{}}]")
    private String sendChannel;
    /**
     * 接受范围[{type:'',ids[]}]
     */
    @ApiModelProperty(value = "接受范围[{type:'',ids[]}]")
    private String targetUser;


}
