package com.lg.dao.core;

import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * 普通VO对象映射测试
 * 测试EntityRowMapper对没有JPA/MyBatis-Plus注解的普通POJO类的支持
 */
@Slf4j
public class PlainVoMappingTest {

    private UnifiedCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        cacheManager = mock(UnifiedCacheManager.class);
        
        // 模拟缓存管理器的get方法，让它直接调用supplier
        when(cacheManager.get(any(), any(), any())).thenAnswer(invocation -> {
            java.util.function.Supplier<?> supplier = invocation.getArgument(2);
            return supplier.get();
        });
        
        // 初始化EntityInfoManager的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    /**
     * 普通VO类 - 没有任何JPA或MyBatis-Plus注解
     */
    @Data
    public static class PlainUserVo {
        private Long userId;
        private String userName;
        private String email;
        private Integer age;
        private BigDecimal salary;
        private Date createTime;
        private LocalDateTime updateTime;
        private Boolean isActive;
        
        // 驼峰命名字段
        private String firstName;
        private String lastName;
        private String phoneNumber;
        
        // 复杂类型字段
        private String jsonData;
    }

    /**
     * 查询结果VO - 用于复杂查询结果映射
     */
    @Data
    public static class QueryResultVo {
        private Long id;
        private String name;
        private String department;
        private BigDecimal totalAmount;
        private Integer recordCount;
        private Date statisticsDate;
        
        // 计算字段
        private BigDecimal averageAmount;
        private String displayName;
    }

    @Test
    void testPlainVoMapping() throws SQLException {
        log.info("=== 测试普通VO对象映射 ===");
        
        EntityRowMapper<PlainUserVo> rowMapper = new EntityRowMapper<>(PlainUserVo.class, cacheManager);
        
        // 创建模拟的ResultSet
        ResultSet rs = createMockResultSetForPlainVo();
        
        // 执行映射
        PlainUserVo vo = rowMapper.mapRow(rs, 1);
        
        assertNotNull(vo);
        assertEquals(1L, vo.getUserId());
        assertEquals("John Doe", vo.getUserName());
        assertEquals("<EMAIL>", vo.getEmail());
        assertEquals(30, vo.getAge());
        assertEquals(new BigDecimal("5000.00"), vo.getSalary());
        assertTrue(vo.getIsActive());
        assertEquals("John", vo.getFirstName());
        assertEquals("Doe", vo.getLastName());
        assertEquals("13800138000", vo.getPhoneNumber());
        assertEquals("{\"key\":\"value\"}", vo.getJsonData());
        
        log.info("普通VO映射测试通过: {}", vo);
    }

    @Test
    void testQueryResultVoMapping() throws SQLException {
        log.info("=== 测试查询结果VO映射 ===");
        
        EntityRowMapper<QueryResultVo> rowMapper = new EntityRowMapper<>(QueryResultVo.class, cacheManager);
        
        // 创建模拟的ResultSet（模拟复杂查询结果）
        ResultSet rs = createMockResultSetForQueryResult();
        
        // 执行映射
        QueryResultVo vo = rowMapper.mapRow(rs, 1);
        
        assertNotNull(vo);
        assertEquals(100L, vo.getId());
        assertEquals("Sales Department", vo.getName());
        assertEquals("Sales", vo.getDepartment());
        assertEquals(new BigDecimal("150000.00"), vo.getTotalAmount());
        assertEquals(50, vo.getRecordCount());
        assertEquals(new BigDecimal("3000.00"), vo.getAverageAmount());
        assertEquals("Sales Department Summary", vo.getDisplayName());
        
        log.info("查询结果VO映射测试通过: {}", vo);
    }

    @Test
    void testEntityInfoForPlainVo() {
        log.info("=== 测试普通VO的EntityInfo创建 ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(PlainUserVo.class);
        assertNotNull(entityInfo);
        
        // 验证表名（应该是类名的下划线形式）
        assertEquals("plain_user_vo", entityInfo.getTableName());
        
        // 验证字段映射
        log.info("字段总数: {}", entityInfo.getFields().size());
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("字段: {} -> 列: {} (类型: {})", 
                field.getPropertyName(), 
                field.getColumn(),
                field.getPropertyType().getSimpleName());
        }
        
        // 验证特定字段的映射
        EntityInfo.FieldInfo userIdField = entityInfo.findFieldInfoByColumn("user_id");
        assertNotNull(userIdField, "user_id字段应该被正确映射");
        assertEquals("userId", userIdField.getPropertyName());
        
        EntityInfo.FieldInfo firstNameField = entityInfo.findFieldInfoByColumn("first_name");
        assertNotNull(firstNameField, "first_name字段应该被正确映射");
        assertEquals("firstName", firstNameField.getPropertyName());
        
        EntityInfo.FieldInfo phoneNumberField = entityInfo.findFieldInfoByColumn("phone_number");
        assertNotNull(phoneNumberField, "phone_number字段应该被正确映射");
        assertEquals("phoneNumber", phoneNumberField.getPropertyName());
        
        log.info("普通VO的EntityInfo创建测试通过");
    }

    @Test
    void testPlainVoWithoutIdField() {
        log.info("=== 测试没有ID字段的普通VO ===");
        
        // QueryResultVo没有标准的ID字段（只有一个普通的id字段）
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(QueryResultVo.class);
        assertNotNull(entityInfo);
        
        // 验证没有@Id注解的字段不会被识别为ID字段
        assertTrue(entityInfo.getIdFields().isEmpty(), "没有@Id注解的字段不应该被识别为ID字段");
        
        // 但是字段映射应该正常工作
        EntityInfo.FieldInfo idField = entityInfo.findFieldInfoByColumn("id");
        assertNotNull(idField, "普通id字段应该被正确映射");
        assertEquals("id", idField.getPropertyName());
        assertFalse(idField.isId(), "普通id字段不应该被标记为主键");
        
        log.info("没有ID字段的普通VO测试通过");
    }

    @Test
    void testPlainVoFieldProperties() {
        log.info("=== 测试普通VO字段属性 ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(PlainUserVo.class);
        
        // 验证所有字段的默认属性
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            // 普通VO字段默认都是可插入和可更新的
            assertTrue(field.isInsertable(), "普通VO字段默认应该可插入: " + field.getPropertyName());
            assertTrue(field.isUpdatable(), "普通VO字段默认应该可更新: " + field.getPropertyName());
            
            // 验证列名转换
            String expectedColumn = camelToUnderline(field.getPropertyName());
            assertEquals(expectedColumn, field.getColumn(), 
                "字段 " + field.getPropertyName() + " 的列名应该是 " + expectedColumn);
        }
        
        log.info("普通VO字段属性测试通过");
    }

    private ResultSet createMockResultSetForPlainVo() throws SQLException {
        ResultSet rs = mock(ResultSet.class);
        ResultSetMetaData rsmd = mock(ResultSetMetaData.class);
        
        // 设置列数
        when(rsmd.getColumnCount()).thenReturn(10);
        
        // 设置列名（数据库下划线格式）
        when(rsmd.getColumnName(1)).thenReturn("user_id");
        when(rsmd.getColumnName(2)).thenReturn("user_name");
        when(rsmd.getColumnName(3)).thenReturn("email");
        when(rsmd.getColumnName(4)).thenReturn("age");
        when(rsmd.getColumnName(5)).thenReturn("salary");
        when(rsmd.getColumnName(6)).thenReturn("is_active");
        when(rsmd.getColumnName(7)).thenReturn("first_name");
        when(rsmd.getColumnName(8)).thenReturn("last_name");
        when(rsmd.getColumnName(9)).thenReturn("phone_number");
        when(rsmd.getColumnName(10)).thenReturn("json_data");
        
        // 设置列标签
        when(rsmd.getColumnLabel(1)).thenReturn("user_id");
        when(rsmd.getColumnLabel(2)).thenReturn("user_name");
        when(rsmd.getColumnLabel(3)).thenReturn("email");
        when(rsmd.getColumnLabel(4)).thenReturn("age");
        when(rsmd.getColumnLabel(5)).thenReturn("salary");
        when(rsmd.getColumnLabel(6)).thenReturn("is_active");
        when(rsmd.getColumnLabel(7)).thenReturn("first_name");
        when(rsmd.getColumnLabel(8)).thenReturn("last_name");
        when(rsmd.getColumnLabel(9)).thenReturn("phone_number");
        when(rsmd.getColumnLabel(10)).thenReturn("json_data");
        
        when(rs.getMetaData()).thenReturn(rsmd);
        
        // 设置值
        when(rs.getObject(1)).thenReturn(1L);
        when(rs.getObject(2)).thenReturn("John Doe");
        when(rs.getObject(3)).thenReturn("<EMAIL>");
        when(rs.getObject(4)).thenReturn(30);
        when(rs.getObject(5)).thenReturn(new BigDecimal("5000.00"));
        when(rs.getObject(6)).thenReturn(true);
        when(rs.getObject(7)).thenReturn("John");
        when(rs.getObject(8)).thenReturn("Doe");
        when(rs.getObject(9)).thenReturn("13800138000");
        when(rs.getObject(10)).thenReturn("{\"key\":\"value\"}");
        
        return rs;
    }

    private ResultSet createMockResultSetForQueryResult() throws SQLException {
        ResultSet rs = mock(ResultSet.class);
        ResultSetMetaData rsmd = mock(ResultSetMetaData.class);
        
        // 设置列数
        when(rsmd.getColumnCount()).thenReturn(7);
        
        // 设置列名
        when(rsmd.getColumnName(1)).thenReturn("id");
        when(rsmd.getColumnName(2)).thenReturn("name");
        when(rsmd.getColumnName(3)).thenReturn("department");
        when(rsmd.getColumnName(4)).thenReturn("total_amount");
        when(rsmd.getColumnName(5)).thenReturn("record_count");
        when(rsmd.getColumnName(6)).thenReturn("average_amount");
        when(rsmd.getColumnName(7)).thenReturn("display_name");
        
        // 设置列标签
        when(rsmd.getColumnLabel(1)).thenReturn("id");
        when(rsmd.getColumnLabel(2)).thenReturn("name");
        when(rsmd.getColumnLabel(3)).thenReturn("department");
        when(rsmd.getColumnLabel(4)).thenReturn("total_amount");
        when(rsmd.getColumnLabel(5)).thenReturn("record_count");
        when(rsmd.getColumnLabel(6)).thenReturn("average_amount");
        when(rsmd.getColumnLabel(7)).thenReturn("display_name");
        
        when(rs.getMetaData()).thenReturn(rsmd);
        
        // 设置值
        when(rs.getObject(1)).thenReturn(100L);
        when(rs.getObject(2)).thenReturn("Sales Department");
        when(rs.getObject(3)).thenReturn("Sales");
        when(rs.getObject(4)).thenReturn(new BigDecimal("150000.00"));
        when(rs.getObject(5)).thenReturn(50);
        when(rs.getObject(6)).thenReturn(new BigDecimal("3000.00"));
        when(rs.getObject(7)).thenReturn("Sales Department Summary");
        
        return rs;
    }

    /**
     * 驼峰转下划线工具方法
     */
    private String camelToUnderline(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        
        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camelCase.charAt(0)));
        
        for (int i = 1; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                result.append('_').append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        
        return result.toString();
    }
}
