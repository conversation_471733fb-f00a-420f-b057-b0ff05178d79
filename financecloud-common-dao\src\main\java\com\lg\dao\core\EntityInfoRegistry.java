package com.lg.dao.core;

import com.lg.dao.core.validation.EntitySchemaValidator;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实体信息注册表
 * 用于统一管理实体信息和数据库元数据
 */
@Slf4j
public class EntityInfoRegistry {
    
    private static final EntityInfoRegistry INSTANCE = new EntityInfoRegistry();
    
    // 移除本地实体信息缓存，统一使用 EntityInfoManager
    
    // 数据库表信息缓存
    private final Map<String, EntitySchemaValidator.DatabaseTableInfo> databaseTableInfoCache = new ConcurrentHashMap<>();
    
    // 表是否包含租户ID字段的缓存
    private final Map<String, Boolean> tableHasTenantColumnCache = new ConcurrentHashMap<>();
    
    // 数据源，用于获取数据库元数据
    private DataSource dataSource;
    
    // 表验证器
    private EntitySchemaValidator validator;
    
    // 架构名称
    private String schema;
    
    private EntityInfoRegistry() {
        // 私有构造函数，使用单例模式
    }
    
    /**
     * 获取单例实例
     */
    public static EntityInfoRegistry getInstance() {
        return INSTANCE;
    }
    
    /**
     * 初始化注册表
     * @param dataSource 数据源
     * @param schema 数据库架构名
     */
    public void initialize(DataSource dataSource, String schema) {
        this.dataSource = dataSource;
        this.schema = schema;
        this.validator = new EntitySchemaValidator(dataSource, schema);
    }
    
    /**
     * 获取实体信息（统一管理）
     * @param entityClass 实体类
     * @return 实体信息
     */
    public EntityInfo getEntityInfo(Class<?> entityClass) {
        return EntityInfoManager.getInstance().getEntityInfo(entityClass);
    }
    
    /**
     * 获取数据库表信息
     * @param tableName 表名
     * @return 数据库表信息
     */
    public EntitySchemaValidator.DatabaseTableInfo getDatabaseTableInfo(String tableName) {
        return databaseTableInfoCache.computeIfAbsent(tableName, name -> {
            if (validator == null) {
                log.warn("验证器未初始化，无法获取数据库表信息");
                return null;
            }
            return validator.getDatabaseTableInfo(name);
        });
    }
    
    /**
     * 清除数据库表信息缓存
     * @param tableName 表名
     */
    public void clearDatabaseTableInfo(String tableName) {
        databaseTableInfoCache.remove(tableName);
        tableHasTenantColumnCache.remove(tableName);
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCaches() {
        databaseTableInfoCache.clear();
        tableHasTenantColumnCache.clear();
    }
    
    /**
     * 检查表是否包含租户ID字段
     * @param tableName 表名
     * @param tenantField 租户字段名
     * @return 是否包含租户字段
     */
    public boolean tableHasTenantColumn(String tableName, String tenantField) {
        return tableHasTenantColumnCache.computeIfAbsent(tableName, name -> {
            EntitySchemaValidator.DatabaseTableInfo tableInfo = getDatabaseTableInfo(name);
            if (tableInfo == null) {
                return false;
            }
            return tableInfo.getColumns().containsKey(tenantField);
        });
    }
    
    /**
     * 获取实体验证器
     */
    public EntitySchemaValidator getValidator() {
        return validator;
    }
} 