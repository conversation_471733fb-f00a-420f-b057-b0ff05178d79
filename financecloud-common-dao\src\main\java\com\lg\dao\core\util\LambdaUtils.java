package com.lg.dao.core.util;

import com.lg.dao.core.func.LFunction;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;

/**
 * Lambda工具类 - 用于解析Lambda表达式获取字段名和类信息
 */
public class LambdaUtils {
    
    /**
     * 从Lambda表达式获取字段名
     */
    @SuppressWarnings("unchecked")
    public static <T, R> String getFieldName(LFunction<T, R> getter) {
        try {
            SerializedLambda serializedLambda = resolveSerializedLambda(getter);
            String methodName = serializedLambda.getImplMethodName();
            
            // 处理静态方法引用的情况
            String className = serializedLambda.getImplClass().replace('/', '.');
            if (isStaticMethodReference(serializedLambda)) {
                // 对于静态方法引用，获取目标实体类
                try {
                    Class<?> implClass = Class.forName(className);
                    Method method = implClass.getDeclaredMethod(methodName, Class.forName(serializedLambda.getInstantiatedMethodType().split(";")[0].substring(1).replace('/', '.')));
                    // 这里假设方法名就是get + 字段名
                    return methodToFieldName(methodName);
                } catch (Exception e) {
                    return methodToFieldName(methodName);
                }
            }
            
            return methodToFieldName(methodName);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get field name from lambda", e);
        }
    }
    
    /**
     * 从Lambda表达式获取类信息
     */
    @SuppressWarnings("unchecked")
    public static <T, R> Class<?> getLambdaClass(LFunction<T, R> getter) {
        try {
            SerializedLambda serializedLambda = resolveSerializedLambda(getter);
            
            // 处理静态方法引用的情况
            if (isStaticMethodReference(serializedLambda)) {
                // 对于静态方法引用，获取方法参数类型作为实体类
                String parameterTypeName = serializedLambda.getInstantiatedMethodType().split(";")[0].substring(1).replace('/', '.');
                return Class.forName(parameterTypeName);
            }
            
            String implClass = serializedLambda.getImplClass().replace('/', '.');
            return Class.forName(implClass);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get class from lambda", e);
        }
    }
    
    /**
     * 获取Lambda表达式对应的实体类
     * 支持任意类型的Lambda表达式
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static Class<?> getEntityClass(LFunction<?, ?> lambda) {
        try {
            SerializedLambda serializedLambda = resolveSerializedLambda((Serializable) lambda);
            
            // 处理静态方法引用的情况
            if (isStaticMethodReference(serializedLambda)) {
                // 对于静态方法引用，获取方法参数类型作为实体类
                String parameterTypeName = serializedLambda.getInstantiatedMethodType().split(";")[0].substring(1).replace('/', '.');
                return Class.forName(parameterTypeName);
            }
            
            String implClass = serializedLambda.getImplClass().replace('/', '.');
            return Class.forName(implClass);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 判断是否为静态方法引用
     */
    private static boolean isStaticMethodReference(SerializedLambda serializedLambda) {
        return serializedLambda.getImplMethodKind() == 6; // MethodHandleInfo.REF_invokeStatic
    }
    
    /**
     * 解析SerializedLambda
     */
    public static SerializedLambda resolveSerializedLambda(Serializable lambda) {
        try {
            Method writeReplace = lambda.getClass().getDeclaredMethod("writeReplace");
            writeReplace.setAccessible(true);
            return (SerializedLambda) writeReplace.invoke(lambda);
        } catch (Exception e) {
            throw new RuntimeException("Failed to resolve SerializedLambda", e);
        }
    }
    
    /**
     * 方法名转字段名
     */
    private static String methodToFieldName(String methodName) {
        if (methodName.startsWith("get") && methodName.length() > 3) {
            return Character.toLowerCase(methodName.charAt(3)) + methodName.substring(4);
        } else if (methodName.startsWith("is") && methodName.length() > 2) {
            return Character.toLowerCase(methodName.charAt(2)) + methodName.substring(3);
        } else {
            // 处理其他方法名模式
            return methodName;
        }
    }
}