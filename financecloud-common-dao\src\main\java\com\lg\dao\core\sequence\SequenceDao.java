package com.lg.dao.core.sequence;

import com.lg.dao.core.EntityRowMapper;
import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 序列DAO - 直接使用JdbcTemplate以避免拦截器，提高性能
 */
@Repository
public class SequenceDao extends  GenericDao<Sequence, String> {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Transactional(isolation = Isolation.SERIALIZABLE)
    public long getAndIncrement(String tenantId, String sequenceName, int increment) {
        try {


            
            // 先获取当前值
            String selectSql = "SELECT current_value FROM sys_seq WHERE tenant_id = ? AND sequence_name = ? FOR UPDATE";
            Long currentValue = jdbcTemplate.queryForObject(selectSql, Long.class, tenantId, sequenceName);
            
            if (currentValue == null) {
                throw new RuntimeException("Sequence not found: " + tenantId + "." + sequenceName);
            }
            
            // 计算新值
            long newValue = currentValue + increment;
            
            // 更新值
            String updateSql = "UPDATE sys_seq SET current_value = ?, update_time = NOW() WHERE tenant_id = ? AND sequence_name = ?";
            int updated = jdbcTemplate.update(updateSql, newValue, tenantId, sequenceName);
            
            if (updated == 0) {
                throw new RuntimeException("Failed to update sequence: " + tenantId + "." + sequenceName);
            }
            
            return newValue;
        } catch (Exception e) {
            throw new RuntimeException("Failed to get and increment sequence", e);
        }
    }
    
    /**
     * 重置序列值
     */
    public void reset(String tenantId, String sequenceName, long value) {

        String sql = "UPDATE sys_seq SET current_value = ?, last_reset_time = NOW(), update_time = NOW() " +
                    "WHERE tenant_id = ? AND sequence_name = ?";
        jdbcTemplate.update(sql, value, tenantId, sequenceName);
    }

    /**
     * 根据租户ID和序列名获取序列
     */
    public Sequence getByTenantAndName(String tenantId, String sequenceName) {
        String sql = "SELECT * FROM sys_seq WHERE tenant_id = ? AND sequence_name = ?";
        List<Sequence> results = jdbcTemplate.query(
            sql, 
            new Object[]{tenantId, sequenceName}, 
            new EntityRowMapper<>(Sequence.class)
        );
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 检查序列是否存在
     */
    public boolean exists(String tenantId, String sequenceName) {
        String sql = "SELECT COUNT(*) FROM sys_seq WHERE tenant_id = ? AND sequence_name = ?";
        return jdbcTemplate.queryForObject(
            sql, 
            Integer.class, 
            tenantId, 
            sequenceName
        ) > 0;
    }
}