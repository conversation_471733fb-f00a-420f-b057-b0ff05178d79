package com.lg.dao.mybatis;

import cn.hutool.json.JSONUtil;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.config.properties.CacheProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis 原生解析器测试
 */
public class MybatisNativeParserTest {

    private MybatisNativeXmlParser parser;
    private MybatisNativeXmlParser parserWithCache;

    @BeforeEach
    public void setUp() throws Exception {
        // 不带缓存的解析器
        parser = new MybatisNativeXmlParser();
        parser.setMapperLocations("classpath*:test-mapper/**/*.xml");
        parser.afterPropertiesSet();

        // 带缓存的解析器
        UnifiedCacheManager cacheManager = createCacheManager();
        parserWithCache = new MybatisNativeXmlParser(cacheManager);
        parserWithCache.setMapperLocations("classpath*:test-mapper/**/*.xml");
        parserWithCache.afterPropertiesSet();
    }

    private UnifiedCacheManager createCacheManager() throws Exception {
        CacheProperties cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);


        UnifiedCacheManager cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();
        return cacheManager;
    }
    
    /**
     * 测试简单查询解析
     */
    @Test
    public void testSimpleSelectParsing() {
        String statementId = "com.lg.dao.test.UserMapper.findById";
        
        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.isSelect(), "应该是查询语句");
        
        System.out.println("解析后的 SQL: " + sqlInfo.getSql());
        System.out.println("参数数量: " + sqlInfo.getParameterCount());
    }
    
    /**
     * 测试动态 SQL 解析
     */
    @Test
    public void testDynamicSqlParsing() {
        String statementId = "com.lg.dao.test.UserMapper.findByCondition";
        
        Map<String, Object> params = new HashMap<>();
        params.put("name", "张三");
        params.put("age", 25);
        params.put("email", "<EMAIL>");
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.isSelect(), "应该是查询语句");
        
        System.out.println("动态 SQL 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    
    /**
     * 测试 foreach 标签解析
     */
    @Test
    public void testForeachParsing() {
        String statementId = "com.lg.dao.test.UserMapper.findByIds";
        
        Map<String, Object> params = new HashMap<>();
        params.put("ids", java.util.Arrays.asList("1", "2", "3"));
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.getSql().contains("IN"), "应该包含 IN 子句");
        
        System.out.println("Foreach 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    /**
     * 测试 foreach 标签解析
     */
    @Test
    public void testXXXXXX() {

        /***
         *  <if test="instanceStatus != null">
         *             AND wfi.status = #{instanceStatus}
         *         </if>
         *         <if test="title != null">
         *             AND wfi.title LIKE #{title}
         *         </if>
         *         <if test="category != null">
         *             AND wfd.category = #{category}
         *         </if>
         *         <if test="startTime != null">
         *             AND wft.create_time >= #{startTime}
         *         </if>
         *         <if test="endTime != null">
         *             AND wft.create_time <![CDATA[= #{endTime}
         *         ]]></if>
         */

        String statementId = "com.lg.dao.test.UserMapper.XXXXXX";

        Map<String, Object> params = new HashMap<>();
        params.put("instanceStatus", "1");
        params.put("title", "1");
        params.put("category", "1");
        params.put("startTime", "1");
        params.put("endTime", "1");



        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);

        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.getSql().contains("IN"), "应该包含 IN 子句");

        System.out.println("Foreach 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }

    /**
     * 测试插入语句解析
     */
    @Test
    public void testInsertParsing() {
        String statementId = "com.lg.dao.test.UserMapper.insertUser";
        
        TestUser user = new TestUser();
        user.setId("123");
        user.setName("张三");
        user.setAge(25);
        user.setEmail("<EMAIL>");
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, user);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.isInsert(), "应该是插入语句");
        
        System.out.println("插入 SQL 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    
    /**
     * 测试更新语句解析
     */
    @Test
    public void testUpdateParsing() {
        String statementId = "com.lg.dao.test.UserMapper.updateUser";
        
        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");
        params.put("name", "李四");
        params.put("age", 30);
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.isUpdate(), "应该是更新语句");
        
        System.out.println("更新 SQL 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    
    /**
     * 测试删除语句解析
     */
    @Test
    public void testDeleteParsing() {
        String statementId = "com.lg.dao.test.UserMapper.deleteUser";
        
        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        assertTrue(sqlInfo.isDelete(), "应该是删除语句");
        
        System.out.println("删除 SQL 解析结果: " + sqlInfo.getSql());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    
    /**
     * 测试复杂动态 SQL
     */
    @Test
    public void testComplexDynamicSql() {
        String statementId = "com.lg.dao.test.UserMapper.findUsersWithComplexCondition";
        
        Map<String, Object> params = new HashMap<>();
        params.put("name", "张");
        params.put("minAge", 18);
        params.put("maxAge", 60);
        params.put("statusList", java.util.Arrays.asList("ACTIVE", "PENDING"));
        params.put("orderBy", "created_time");
        params.put("orderDirection", "DESC");
        
        ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
        
        assertNotNull(sqlInfo, "解析结果不应为空");
        assertNotNull(sqlInfo.getSql(), "SQL 不应为空");
        
        System.out.println("复杂动态 SQL 解析结果:");
        System.out.println(sqlInfo.getSql());
        System.out.println("参数数量: " + sqlInfo.getParameterCount());
        System.out.println("参数: " + java.util.Arrays.toString(sqlInfo.getParameters()));
    }
    
    /**
     * 测试缓存功能
     */
    @Test
    public void testCacheFunction() {
        String statementId = "com.lg.dao.test.UserMapper.findById";

        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");

        // 第一次调用，应该执行解析并缓存
        ParsedSqlInfo sqlInfo1 = parserWithCache.getParsedSql(statementId, params);
        assertNotNull(sqlInfo1, "第一次解析结果不应为空");

        // 第二次调用，应该从缓存获取
        ParsedSqlInfo sqlInfo2 = parserWithCache.getParsedSql(statementId, params);
        assertNotNull(sqlInfo2, "第二次解析结果不应为空");

        // 验证结果一致性
        assertEquals(sqlInfo1.getSql(), sqlInfo2.getSql(), "缓存的 SQL 应该一致");
        assertEquals(sqlInfo1.getParameterCount(), sqlInfo2.getParameterCount(), "缓存的参数数量应该一致");

        System.out.println("缓存测试通过，SQL: " + sqlInfo1.getSql());
    }

    /**
     * 测试集合参数不缓存
     */
    @Test
    public void testCollectionParamNoCache() {
        String statementId = "com.lg.dao.test.UserMapper.findByIds";

        Map<String, Object> params = new HashMap<>();
        List<String> ids = new ArrayList<>();
        ids.add("1");
        ids.add("2");
        ids.add("3");
        params.put("ids", ids);

        // 多次调用，每次都应该重新解析（因为包含集合参数）
        ParsedSqlInfo sqlInfo1 = parserWithCache.getParsedSql(statementId, params);
        ParsedSqlInfo sqlInfo2 = parserWithCache.getParsedSql(statementId, params);
        System.err.println(JSONUtil.toJsonStr(sqlInfo1));
        System.err.println(JSONUtil.toJsonStr(sqlInfo2));

        assertNotNull(sqlInfo1, "第一次解析结果不应为空");
        assertNotNull(sqlInfo2, "第二次解析结果不应为空");

        // 虽然不缓存，但结果应该一致
        assertEquals(sqlInfo1.getSql(), sqlInfo2.getSql(), "SQL 应该一致");

        System.out.println("集合参数不缓存测试通过，SQL: " + sqlInfo1.getSql());
    }

    /**
     * 测试性能对比（有缓存 vs 无缓存）
     */
    @Test
    public void testPerformanceComparison() {
        String statementId = "com.lg.dao.test.UserMapper.findById";

        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");

        int iterations = 1000;

        // 测试无缓存性能
        long startTime1 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
            assertNotNull(sqlInfo);
        }
        long endTime1 = System.nanoTime();
        long noCacheTime = endTime1 - startTime1;

        // 预热缓存
        parserWithCache.getParsedSql(statementId, params);

        // 测试有缓存性能
        long startTime2 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            ParsedSqlInfo sqlInfo = parserWithCache.getParsedSql(statementId, params);
            assertNotNull(sqlInfo);
        }
        long endTime2 = System.nanoTime();
        long cacheTime = endTime2 - startTime2;

        System.out.println("无缓存总时间: " + noCacheTime / 1_000_000 + " ms");
        System.out.println("有缓存总时间: " + cacheTime / 1_000_000 + " ms");
        System.out.println("性能提升: " + (noCacheTime / (double) cacheTime) + "x");

        // 缓存应该显著提升性能
        assertTrue(cacheTime < noCacheTime, "缓存应该提升性能");
    }

    /**
     * 测试性能
     */
    @Test
    public void testPerformance() {
        String statementId = "com.lg.dao.test.UserMapper.findById";

        Map<String, Object> params = new HashMap<>();
        params.put("id", "123");

        // 预热
        for (int i = 0; i < 100; i++) {
            parser.getParsedSql(statementId, params);
        }

        // 性能测试
        long startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            ParsedSqlInfo sqlInfo = parser.getParsedSql(statementId, params);
            assertNotNull(sqlInfo);
        }
        long endTime = System.nanoTime();

        long avgTime = (endTime - startTime) / 1000;
        System.out.println("平均解析时间: " + avgTime + " ns");
        System.out.println("总解析时间: " + (endTime - startTime) / 1_000_000 + " ms");

        // 验证性能（应该很快，因为有缓存）
        assertTrue(avgTime < 100_000, "平均解析时间应该小于 100μs");
    }
    
    /**
     * 测试用户类
     */
    public static class TestUser {
        private String id;
        private String name;
        private Integer age;
        private String email;
        
        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }
}
