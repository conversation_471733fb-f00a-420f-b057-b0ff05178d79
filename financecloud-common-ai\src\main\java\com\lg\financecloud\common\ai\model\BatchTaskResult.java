package com.lg.financecloud.common.ai.model;


import lombok.Builder;
import lombok.Data;
import java.util.Map;

@Data
@Builder
public class BatchTaskResult {
    /**
     * 任务ID (对应API返回的id)
     */
    private String id;

    /**
     * 对象类型，固定值"batch"
     */
    private String object;

    /**
     * 访问路径
     */
    private String endpoint;

    /**
     * 输入文件ID
     */
    private String inputFileId;

    /**
     * 输出文件ID
     */
    private String outputFileId;

    /**
     * 错误文件ID
     */
    private String errorFileId;

    /**
     * 任务状态（推荐使用枚举）
     * @see BatchStatus
     */
    private String status;

    /**
     * 完成时间窗口（示例："24h"）
     */
    private String completionWindow;

    /**
     * 任务创建时间戳（秒）
     */
    private Long createdAt;

    /**
     * 任务最后更新时间戳（秒）
     */
    private Long updatedAt;

    // 新增状态时间戳（单位：秒）
    private Long inProgressAt;
    private Long expiresAt;
    private Long finalizingAt;
    private Long completedAt;
    private Long failedAt;
    private Long expiredAt;
    private Long cancellingAt;
    private Long cancelledAt;

    /**
     * 请求统计
     */
    private Map<String, Integer> requestCounts;

    /**
     * 元数据信息
     */
    private Map<String, String> metadata;

    /**
     * 错误详情
     */
    private Map<String, Object> errors;

    // 建议添加状态枚举
    public enum BatchStatus {
        VALIDATING("validating"),
        IN_PROGRESS("in_progress"),
        FINALIZING("finalizing"),
        COMPLETED("completed"),
        FAILED("failed"),
        EXPIRED("expired"),
        CANCELLING("cancelling"),
        CANCELLED("cancelled");

        private final String value;

        BatchStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
