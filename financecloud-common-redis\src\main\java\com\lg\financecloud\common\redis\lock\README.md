# 分布式锁组件使用指南

## 概述

本组件提供了基于 Redisson 的分布式锁管理功能，支持自动配置和灵活的参数设置。

## 特性

- ✅ 自动配置，开箱即用
- ✅ 支持自定义锁前缀、超时时间等参数
- ✅ 提供安全的锁操作，自动处理锁的获取和释放
- ✅ 支持函数式编程风格的业务逻辑执行
- ✅ 完善的异常处理和日志记录
- ✅ 支持锁状态检查

## 快速开始

### 1. 添加依赖

确保项目中已包含 `financecloud-common-redis` 模块。

### 2. 配置

在 `application.yml` 中添加配置（可选，有默认值）：

```yaml
light:
  lock:
    enabled: true                    # 是否启用分布式锁
    prefix: "app_lock:"              # 锁前缀
    default-wait-time: 3000          # 默认等待时间(ms)
    default-lease-time: 30000        # 默认租约时间(ms)
    enable-metrics: false            # 是否启用监控
    timeout-alert-threshold: 10000   # 超时告警阈值(ms)
```

### 3. 使用

注入 `DistributedLockManager` 并使用：

```java
@Service
public class UserService {
    
    @Autowired
    private DistributedLockManager lockManager;
    
    public void updateUser(Long userId) {
        String lockKey = "user:" + userId + ":update";
        
        // 使用默认配置
        lockManager.executeWithLock(lockKey, () -> {
            // 业务逻辑
            doUpdateUser(userId);
        });
    }
    
    public String processOrder(Long orderId) {
        String lockKey = "order:" + orderId + ":process";
        
        // 自定义超时时间
        return lockManager.executeWithLock(lockKey, 2000L, 10000L, () -> {
            // 业务逻辑
            return doProcessOrder(orderId);
        });
    }
}
```

## API 说明

### 主要方法

#### executeWithLock(String lockKey, Supplier<T> supplier)
使用默认配置执行带锁的操作，有返回值。

#### executeWithLock(String lockKey, Runnable runnable)
使用默认配置执行带锁的操作，无返回值。

#### executeWithLock(String lockKey, long waitTime, long leaseTime, Supplier<T> supplier)
使用自定义超时时间执行带锁的操作，有返回值。

#### executeWithLock(String lockKey, long waitTime, long leaseTime, Runnable runnable)
使用自定义超时时间执行带锁的操作，无返回值。

#### isLocked(String lockKey)
检查锁是否被任何线程持有。

#### isLockedByCurrentThread(String lockKey)
检查锁是否被当前线程持有。

### 异常处理

- `LockAcquisitionException`: 锁获取失败时抛出
- `LockExecutionException`: 业务逻辑执行过程中出错时抛出

## 最佳实践

1. **锁粒度**: 使用细粒度的锁键，避免不必要的竞争
2. **超时设置**: 根据业务场景合理设置等待时间和租约时间
3. **异常处理**: 妥善处理锁获取失败的情况
4. **避免死锁**: 确保锁的获取顺序一致
5. **监控**: 在生产环境中启用监控功能

## 注意事项

- 锁的租约时间应该大于业务逻辑的执行时间
- 避免在锁内执行耗时的 I/O 操作
- 确保 Redis 连接的稳定性
- 在集群环境中注意时钟同步
