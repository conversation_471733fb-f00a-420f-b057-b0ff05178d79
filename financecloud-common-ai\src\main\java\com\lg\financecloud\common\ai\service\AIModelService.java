package com.lg.financecloud.common.ai.service;

import com.lg.financecloud.common.ai.model.*;
import com.lg.financecloud.common.ai.exception.AIServiceException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.InputStream;

/**
 * AI模型服务接口
 * 定义与AI模型交互的标准接口
 */
public interface AIModelService {
    // 新增回调接口
    interface StreamCallback {
        void onData(String chunk, String sessionId);
        void onComplete(String sessionId, long totalTokens);
        void onError(Throwable error, String sessionId);
    }

    /**
     * 流式处理接口（带自定义回调）
     * @param request 请求参数
     * @param callback 自定义回调处理器
     * @param timeoutSeconds 超时时间（秒）
     */
    void processStreamWithCallback(AIRequest request,
                                   StreamCallback callback,
                                   int timeoutSeconds) throws AIServiceException;


    // 返回InputStream用于流式读取
    InputStream downloadBatchResultAsStream(String fileId, String apiUrl, String apiKey) throws AIServiceException;

    // 可选：直接写入本地文件
    void downloadBatchResultToFile(String fileId, String outputPath, String apiUrl, String apiKey) throws AIServiceException;

    FileUploadResponse uploadFile(File file, String purpose,String apiUrl, String apiKey) throws AIServiceException;

    BatchTaskResult createBatchTask(BatchRequest request, String apiUrl,String apiKey) throws AIServiceException;

    /**
     * 获取批量任务的结果
     * 
     * @param taskId 任务ID
     * @param apiUrl API地址
     * @param apiKey API密钥
     * @return 批量任务结果响应
     * @throws AIServiceException 处理异常时抛出
     */
    BatchTaskResult queryBatchTaskResult(String taskId, String apiUrl, String apiKey) throws AIServiceException;

    /**
     * 处理AI请求并返回响应
     * 
     * @param request AI请求
     * @return AI响应
     * @throws AIServiceException 处理异常时抛出
     */
    AIResponse process(AIRequest request) throws AIServiceException;
    
    /**
     * 流式处理AI请求并返回响应流
     * 支持实时返回生成内容，返回类型改为ResponseBodyEmitter
     * 输出格式为SSE格式，包含id、event、data等字段
     * 
     * @param request AI请求
     * @return AI响应流，使用ResponseBodyEmitter
     * @throws AIServiceException 处理异常时抛出
     */
    ResponseBodyEmitter processStream(AIRequest request) throws AIServiceException;
    
//    /**
//     * 获取模型类型
//     *
//     * @return 模型类型标识符
//     */
//    String getModelType();
//
//    /**
//     * 处理向量嵌入请求
//     * 将文本转换为向量表示
//     *
//     * @param request 向量嵌入请求
//     * @return 向量嵌入响应
//     * @throws AIServiceException 处理异常时抛出
//     */
//    EmbeddingResponse embed(EmbeddingRequest request) throws AIServiceException;
//
//    /**
//     * 异步处理向量嵌入请求
//     * 适用于大批量文本向量化场景
//     *
//     * @param request 向量嵌入请求
//     * @return 向量嵌入响应的Mono
//     */
//    Mono<EmbeddingResponse> embedAsync(EmbeddingRequest request);
}