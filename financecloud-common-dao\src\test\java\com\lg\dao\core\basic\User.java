package com.lg.dao.core.basic;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;

import javax.persistence.Table;
import java.time.LocalDateTime;

// 测试实体类 - 修正表名和字段名
@Table(name = "t_user")
@Data
public class User {
    @Id
    private Long id;
    @Column(name = "user_name")
    private String userName;
    private Integer age;
    @Column(name = "tenant_id")
    private String tenantId;
    @Column(name = "create_time")
    private LocalDateTime createTime;
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
