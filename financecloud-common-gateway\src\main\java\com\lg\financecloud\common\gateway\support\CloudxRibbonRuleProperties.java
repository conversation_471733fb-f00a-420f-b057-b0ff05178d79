/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.gateway.support;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * Ribbon 配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@RefreshScope
@ConfigurationProperties("ribbon.rule")
public class CloudxRibbonRuleProperties {

	/**
	 * 是否开启，默认：true
	 */
	private boolean enabled = Boolean.TRUE;

	/**
	 * 是否开启灰度路由，默认:false
	 */
	private boolean grayEnabled;

	/**
	 * 优先的ip列表，支持通配符，例如：**********、10.20.0.*
	 */
	private List<String> priorIpPattern = new ArrayList<>();

}
