/**
 * Created by <PERSON><PERSON><PERSON> on 2017-02-04.
 */
import DatabaseTree from '../tree/DatabaseTree.js';
import SpringTree from '../tree/SpringTree.js';
import BuildinTree from '../tree/BuildinTree.js';
import MagicApiTree from '../tree/MagicApiTree.js';
import RestApiTree from '../tree/RestApiTree.js';
import DatasourceDialog from '../dialog/DatasourceDialog.js';
import SpringDialog from '../dialog/SpringDialog.js';
import MagicApiDialog from '../dialog/MagicApiDialog.js';
import RestApiDialog from '../dialog/RestApiDialog.js';
import BuildinDatasourceSelectDialog from '../dialog/BuildinDatasourceSelectDialog.js';

export default class DatasourcePanel{
    constructor(context){
        this.context=context;
        context.datasourcePanel=this;
        const reportDef=context.reportDef;
        if(!reportDef.datasources){
            reportDef.datasources=[];
        }
        this.datasources=reportDef.datasources;
    }
    buildPanel(){
        const panel=$(`<div style="width:100%;"></div>`);
        const toolbar=$(`<div class="btn-group ud-toolbar"></div>`);
        panel.append(toolbar);
        const addSqlBtn=$(`<button class="btn btn-default" style="border:none;border-radius:0;background: #f8f8f8;padding: 6px 8px;" title="${window.i18n.property.datasource.title}">
            <i class="ureport ureport-database"></i>
        </button>`);

		if(window.isAdmin){
			  toolbar.append(addSqlBtn);
		}
      

        this.treeContainer=$(`<div style="height: 500px;overflow: auto">`);
        panel.append(this.treeContainer);

        this.datasourceDialog=new DatasourceDialog(this.datasources);
        const _this=this;

        addSqlBtn.click(function(){
            _this.datasourceDialog.show(function(name,username,password,driver,url){
                const ds={name,username,password,driver,url};
                const tree=new DatabaseTree(_this.treeContainer,_this.datasources,ds,_this.datasourceDialog,_this.context);
                _this.datasources.push(tree);
            });
        });
        const addSpringBtn=$(`<button class="btn btn-default" style="border:none;border-radius:0;background: #f8f8f8;padding: 6px 8px;" title="${window.i18n.property.datasource.addBean}">
            <i class="ureport ureport-leaf"></i>
        </button>`);
		if(window.isAdmin){
			 toolbar.append(addSpringBtn);
		}
       
        this.springDialog=new SpringDialog(this.datasources);
        addSpringBtn.click(function(){
            _this.springDialog.show(function(name,beanId){
                const ds={name,beanId};
                const tree=new SpringTree(_this.treeContainer,_this.datasources,ds,_this.springDialog,_this.context);
                _this.datasources.push(tree);
            });
        });

        const addBuildinBtn=$(`<button class="btn btn-default" style="border:none;border-radius:0;background: #f8f8f8;padding: 6px 8px;" title="${window.i18n.property.datasource.addBuildin}">
            <i class="ureport ureport-shareconnection"></i>
        </button>`);
		if(window.isAdmin){
        toolbar.append(addBuildinBtn);
		}

        // 添加MagicApi数据源按钮
        const addMagicApiBtn=$(`<button class="btn btn-default" style="border:none;border-radius:0;background: #f8f8f8;padding: 6px 8px;" title="添加MagicApi连接">
            <i class="ureport ureport-api"></i>
        </button>`);
		if(window.isAdmin){
        toolbar.append(addMagicApiBtn);
		}

        // 添加RestApi数据源按钮
        const addRestApiBtn=$(`<button class="btn btn-default" style="border:none;border-radius:0;background: #f8f8f8;padding: 6px 8px;" title="添加RestApi连接">
            <i class="ureport ureport-rest"></i>
        </button>`);
		if(window.isAdmin){
        toolbar.append(addRestApiBtn);
		}

        // MagicApi数据源对话框和事件
        this.magicApiDialog=new MagicApiDialog(this.datasources);
        addMagicApiBtn.click(function(){
            _this.magicApiDialog.show(function(name, baseUrl, apiPath, headers){
                const ds={name, baseUrl, apiPath, headers, type: 'magicapi'};
                const tree=new MagicApiTree(_this.treeContainer,_this.datasources,ds,_this.context);
                _this.datasources.push(tree);
            });
        });

        // RestApi数据源对话框和事件
        this.restApiDialog=new RestApiDialog(this.datasources);
        addRestApiBtn.click(function(){
            _this.restApiDialog.show(function(name, baseUrl, authType, authToken, timeout, headers){
                const ds={name, baseUrl, authType, authToken, timeout, headers, type: 'restapi'};
                const tree=new RestApiTree(_this.treeContainer,_this.datasources,ds,_this.context);
                _this.datasources.push(tree);
            });
        });
        const buildinDatasourceSelectDialog=new BuildinDatasourceSelectDialog(this.datasources);
        addBuildinBtn.click(function(){
            buildinDatasourceSelectDialog.show(function(name){
                const ds={name};
                const tree=new BuildinTree(_this.treeContainer,_this.datasources,ds,_this.context);
                _this.datasources.push(tree);
            });
        });
        this.buildDatasources();
        this.initEventListeners();
        return panel;
    }

    initEventListeners() {
        const _this = this;

        // 监听MagicApi数据源编辑事件
        document.addEventListener('editMagicApiDatasource', function(event) {
            const detail = event.detail;
            _this.magicApiDialog.show(function(name, baseUrl, apiPath, headers) {
                detail.datasource.name = name;
                detail.datasource.baseUrl = baseUrl;
                detail.datasource.apiPath = apiPath;
                detail.datasource.headers = headers;
                detail.datasource.datasourceLi.find('.ds_name').html(name);
            }, detail.data);
        });

        // 监听RestApi数据源编辑事件
        document.addEventListener('editRestApiDatasource', function(event) {
            const detail = event.detail;
            _this.restApiDialog.show(function(name, baseUrl, authType, authToken, timeout, headers) {
                detail.datasource.name = name;
                detail.datasource.baseUrl = baseUrl;
                detail.datasource.authType = authType;
                detail.datasource.authToken = authToken;
                detail.datasource.timeout = timeout;
                detail.datasource.headers = headers;
                detail.datasource.datasourceLi.find('.ds_name').html(name);
            }, detail.data);
        });
    }

    buildDatasources(){
        this.treeContainer.empty();
        for(let ds of this.datasources){
            if(ds.type==='jdbc'){
                new DatabaseTree(this.treeContainer,this.datasources,ds,this.datasourceDialog,this.context);
            }else if(ds.type==='spring'){
                new SpringTree(this.treeContainer,this.datasources,ds,this.springDialog,this.context);
            }else if(ds.type==='buildin'){
                new BuildinTree(this.treeContainer,this.datasources,ds,this.context);
            }else if(ds.type==='magicapi'){
                new MagicApiTree(this.treeContainer,this.datasources,ds,this.context);
            }else if(ds.type==='restapi'){
                new RestApiTree(this.treeContainer,this.datasources,ds,this.context);
            }
        }
    }
}