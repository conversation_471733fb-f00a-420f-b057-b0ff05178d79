/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.dto.StaffInfo;
import com.lg.financecloud.admin.api.dto.UserInfo;
import com.lg.financecloud.admin.api.entity.SysUser;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteStaffService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteStaffService {
	@GetMapping("/staff/{id}")
	R<StaffInfo> getStaffById(@PathVariable("id") String staffId);
	/**
	*更新员工计提工资科目信息
	 */
	@PostMapping("/staff/updateStaffAccrualSubject")
	R updateStaffAccrualSubject(@RequestParam("deptId") Long deptId,
					 @RequestParam("newSubjectId") Long newSubjectId,
					 @RequestParam(value = "oldSubjectId",required = false) Long oldSubjectId, @RequestHeader(SecurityConstants.FROM) String from);


}
