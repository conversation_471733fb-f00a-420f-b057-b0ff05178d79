package com.lg.dao.mybatis;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * LightDao Mapper注册器
 * 支持更灵活的配置，可以替换特定的Mapper接口
 */
public class LightDaoMapperRegistrar implements ImportBeanDefinitionRegistrar , EnvironmentAware {

    private static final Logger logger = LoggerFactory.getLogger(LightDaoMapperRegistrar.class);

    private Environment environment;


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }
    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        // 获取EnableLightDaoMappers注解的属性

        Map<String, Object> attributes = importingClassMetadata.getAnnotationAttributes(EnableLightDaoMappers.class.getName());
        String property = environment.getProperty("light.orm.mybatis.enable");
        if( !(StrUtil.isNotEmpty( property) && "true".equals( property))){
            return ;
        }
        
        if (attributes != null) {
            // 获取配置参数
            String[] basePackages = (String[]) attributes.get("basePackages");
            Class<?>[] mapperClasses = (Class<?>[]) attributes.get("mapperClasses");
            String mapperLocations = (String) attributes.get("mapperLocations");
            boolean lazyInit = (boolean) attributes.get("lazyInit");
            boolean replaceExisting = (boolean) attributes.get("replaceExisting");
            
            // 扫描所有符合条件的Mapper接口
            Set<Class<?>> mapperInterfaces = new HashSet<>();
            
            // 处理指定的Mapper类
            if (mapperClasses != null && mapperClasses.length > 0) {
                mapperInterfaces.addAll(Arrays.asList(mapperClasses));
            }
            
            // 处理扫描的包
            if (basePackages != null && basePackages.length > 0) {
                for (String basePackage : basePackages) {
                    if (StringUtils.hasText(basePackage)) {
                        mapperInterfaces.addAll(scanPackage(basePackage, registry));
                    }
                }
            }
            
            // 如果没有指定包或类，使用注解所在包
            if (mapperInterfaces.isEmpty()) {
                String basePackage = ClassUtils.getPackageName(importingClassMetadata.getClassName());
                mapperInterfaces.addAll(scanPackage(basePackage, registry));
            }
            

            
            // 注册Mapper接口
            if (lazyInit) {
                // 使用延迟加载
                registerLazyMappers(mapperInterfaces, registry, replaceExisting);
            } else {
                // 立即注册
                registerMappers(mapperInterfaces, registry, replaceExisting);
            }
            
            logger.info("注册LightDao Mapper完成，共注册{}个Mapper接口", mapperInterfaces.size());
        }
    }
    
    /**
     * 扫描包中的接口
     */
    private Set<Class<?>> scanPackage(String basePackage, BeanDefinitionRegistry registry) {
        Set<Class<?>> interfaces = new HashSet<>();
        
        try {
            MapperScanner scanner = new MapperScanner(registry);
            scanner.addIncludeFilter(new AssignableTypeFilter(Object.class) {
                @Override
                protected boolean matchClassName(String className) {
                    return true;
                }
            });
            
            Set<BeanDefinitionHolder> beanDefinitions = scanner.doScan(basePackage);
            
            ClassLoader classLoader = LightDaoMapperRegistrar.class.getClassLoader();
            
            for (BeanDefinitionHolder holder : beanDefinitions) {
                BeanDefinition definition = holder.getBeanDefinition();
                if (definition instanceof AbstractBeanDefinition) {
                    String className = ((AbstractBeanDefinition) definition).getBeanClassName();
                    if (StringUtils.hasText(className)) {
                        try {
                            Class<?> clazz = ClassUtils.forName(className, classLoader);
                            if (clazz.isInterface()) {
                                interfaces.add(clazz);
                            }
                        } catch (ClassNotFoundException e) {
                            logger.error("无法加载类: {}", className, e);
                        }
                    }
                }
            }
            
            logger.debug("从包{}中扫描到{}个接口", basePackage, interfaces.size());
        } catch (Exception e) {
            logger.error("扫描包失败: {}", basePackage, e);
        }
        
        return interfaces;
    }
    
    /**
     * 注册延迟加载的Mapper
     */
    private void registerLazyMappers(Set<Class<?>> mapperInterfaces, BeanDefinitionRegistry registry, boolean replaceExisting) {
        // 直接注册Mapper代理，简化逻辑
        for (Class<?> mapperInterface : mapperInterfaces) {
            String beanName = mapperInterface.getName();
            if (replaceExisting && registry.containsBeanDefinition(beanName)) {
                registry.removeBeanDefinition(beanName);
            }

            if (!registry.containsBeanDefinition(beanName)) {
                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MybatisMapperProxy.class);
                builder.addConstructorArgValue(mapperInterface);
                builder.setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE);

                AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
                beanDefinition.setPrimary(true);

                registry.registerBeanDefinition(beanName, beanDefinition);
                logger.debug("注册Mapper代理: {}", mapperInterface.getName());
            }
        }
    }
    
    /**
     * 注册Mapper接口（立即加载）
     */
    private void registerMappers(Set<Class<?>> mapperInterfaces, BeanDefinitionRegistry registry, boolean replaceExisting) {
        for (Class<?> mapperInterface : mapperInterfaces) {
            String beanName = mapperInterface.getName();
            
            // 如果需要替换已存在的Bean
            if (replaceExisting && registry.containsBeanDefinition(beanName)) {
                registry.removeBeanDefinition(beanName);
            }
            
            if (!registry.containsBeanDefinition(beanName)) {
                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MybatisMapperProxy.class);
                builder.addConstructorArgValue(mapperInterface);
                builder.setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE);
                
                AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
                beanDefinition.setPrimary(true); // 设置为主要Bean，覆盖可能存在的其他Bean
                
                registry.registerBeanDefinition(beanName, beanDefinition);
                logger.debug("注册Mapper: {}", mapperInterface.getName());
            }
        }
    }
    
    /**
     * 注册XML解析器
     */
    private void registerXmlParser(BeanDefinitionRegistry registry, String mapperLocations) {
        if (!registry.containsBeanDefinition("mybatisXmlParser")) {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MybatisNativeXmlParser.class);
            
            if (StringUtils.hasText(mapperLocations)) {
                builder.addPropertyValue("mapperLocations", mapperLocations);
            }
            
            registry.registerBeanDefinition("mybatisXmlParser", builder.getBeanDefinition());
        }
    }
    
    /**
     * Mapper扫描器
     */
    private static class MapperScanner extends ClassPathBeanDefinitionScanner {
        
        public MapperScanner(BeanDefinitionRegistry registry) {
            super(registry, false);
        }
        
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
        }
        
        @Override
        protected Set<BeanDefinitionHolder> doScan(String... basePackages) {
            return super.doScan(basePackages);
        }
    }
} 