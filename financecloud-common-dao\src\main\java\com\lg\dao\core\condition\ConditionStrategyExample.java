package com.lg.dao.core.condition;

import com.lg.dao.core.GenericDao;
import com.lg.dao.core.query.LambdaQuery;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 条件策略使用示例
 * 演示如何使用条件策略来动态构建查询条件
 */
public class ConditionStrategyExample {
    
    // 假设有一个用户实体
    @Data
    public static class User {
        private Long id;
        private String name;
        private String email;
        private Integer age;
        private String status;
        
        // getters and setters...
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
    
    // 假设有一个查询参数对象
    public static class UserQueryParam {
        private String name;      // 可能为null或空字符串
        private String email;     // 可能为null或空字符串  
        private Integer minAge;   // 可能为null
        private List<String> statusList; // 可能为null或空集合
        
        // getters and setters...
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public Integer getMinAge() { return minAge; }
        public void setMinAge(Integer minAge) { this.minAge = minAge; }
        public List<String> getStatusList() { return statusList; }
        public void setStatusList(List<String> statusList) { this.statusList = statusList; }
    }
    
    /**
     * 示例1：使用条件策略的基本用法
     */
    public void example1_BasicUsage(GenericDao<User,  Long> userDao) {
        UserQueryParam param = new UserQueryParam();
        param.setName("");           // 空字符串
        param.setEmail("<EMAIL>"); // 有值
        param.setMinAge(null);       // null值
        param.setStatusList(Arrays.asList("ACTIVE", "PENDING")); // 有值的集合
        
        // 使用IS_NOT_EMPTY策略，只有非空非null的值才会添加到查询条件中
        List<User> users = userDao.lambdaQuery()
            .eq(User::getName, param.getName(), ConditionStrategy.IS_NOT_EMPTY)     // 不会添加（空字符串）
            .eq(User::getEmail, param.getEmail(), ConditionStrategy.IS_NOT_EMPTY)   // 会添加
            .ge(User::getAge, param.getMinAge(), ConditionStrategy.IS_NOT_NULL)     // 不会添加（null）
            .in(User::getStatus, param.getStatusList(), ConditionStrategy.COLLECTION_NOT_EMPTY) // 会添加
            .list();
        
        // 最终生成的SQL类似于：
        // SELECT * FROM user WHERE email = ? AND status IN (?, ?)
    }
    
    /**
     * 示例2：使用便捷方法
     */
    public void example2_ConvenienceMethods(GenericDao<User,Long> userDao) {
        UserQueryParam param = new UserQueryParam();
        param.setName("张三");
        param.setEmail("");  // 空字符串
        
        List<User> users = userDao.lambdaQuery()
            .eqIfNotEmpty(User::getName, param.getName())   // 会添加
            .likeIfNotEmpty(User::getEmail, param.getEmail()) // 不会添加（空字符串）
            .eqIfNotNull(User::getAge, param.getMinAge())   // 不会添加（null）
            .list();
        
        // 最终生成的SQL类似于：
        // SELECT * FROM user WHERE name = ?
    }
    
    /**
     * 示例3：不同策略的对比
     */
    public void example3_StrategyComparison(GenericDao<User,Long> userDao) {
        String emptyString = "";
        String blankString = "   ";
        String normalString = "test";
        
        // IS_NOT_NULL策略：只要不是null就添加条件
        userDao.lambdaQuery()
            .eq(User::getName, emptyString, ConditionStrategy.IS_NOT_NULL)  // 会添加
            .eq(User::getName, blankString, ConditionStrategy.IS_NOT_NULL)  // 会添加
            .eq(User::getName, normalString, ConditionStrategy.IS_NOT_NULL) // 会添加
            .list();
        
        // IS_NOT_EMPTY策略：不是null且不是空字符串才添加条件
        userDao.lambdaQuery()
            .eq(User::getName, emptyString, ConditionStrategy.IS_NOT_EMPTY)  // 不会添加
            .eq(User::getName, blankString, ConditionStrategy.IS_NOT_EMPTY)  // 会添加
            .eq(User::getName, normalString, ConditionStrategy.IS_NOT_EMPTY) // 会添加
            .list();
        
        // IS_NOT_BLANK策略：不是null、不是空字符串、不是空白字符串才添加条件
        userDao.lambdaQuery()
            .eq(User::getName, emptyString, ConditionStrategy.IS_NOT_BLANK)  // 不会添加
            .eq(User::getName, blankString, ConditionStrategy.IS_NOT_BLANK)  // 不会添加
            .eq(User::getName, normalString, ConditionStrategy.IS_NOT_BLANK) // 会添加
            .list();
    }
    
    /**
     * 示例4：在更新和删除操作中使用条件策略
     */
    public void example4_UpdateAndDelete(GenericDao<User,Long> userDao) {
        UserQueryParam param = new UserQueryParam();
        param.setName("张三");
        param.setEmail("");  // 空字符串
        
        // 更新操作
        userDao.lambdaUpdate()
            .set(User::getStatus, "INACTIVE")
            .eqIfNotEmpty(User::getName, param.getName())   // 会添加
            .eqIfNotEmpty(User::getEmail, param.getEmail()) // 不会添加
            .update();
        
        // 删除操作
        userDao.lambdaDelete()
            .eqIfNotEmpty(User::getName, param.getName())   // 会添加
            .eqIfNotEmpty(User::getEmail, param.getEmail()) // 不会添加
            .execute();
    }
    
    /**
     * 示例5：复杂查询场景
     */
    public void example5_ComplexQuery(GenericDao<User,Long> userDao) {
        // 模拟一个复杂的查询参数对象，其中有些字段有值，有些为空
        UserQueryParam param = new UserQueryParam();
        param.setName("张");  // 用于模糊查询
        param.setEmail(null); // null值
        param.setMinAge(18);  // 最小年龄
        param.setStatusList(Arrays.asList("ACTIVE")); // 状态列表
        
        List<User> users = userDao.lambdaQuery()
            // 姓名模糊查询（如果不为空）
            .likeIfNotEmpty(User::getName, param.getName())
            // 邮箱精确匹配（如果不为空）
            .eqIfNotEmpty(User::getEmail, param.getEmail())
            // 年龄大于等于（如果不为null）
            .ge(User::getAge, param.getMinAge(), ConditionStrategy.IS_NOT_NULL)
            // 状态在列表中（如果列表不为空）
            .inIfNotEmpty(User::getStatus, param.getStatusList())
            // 排序和分页
            .orderBy(User::getId)
            .limit(10)
            .list();
        
        // 最终生成的SQL类似于：
        // SELECT * FROM user WHERE name LIKE ? AND age >= ? AND status IN (?) ORDER BY id LIMIT 10
    }
    
    /**
     * 示例6：allEq 方法使用示例
     */
    public void example6_AllEqUsage(GenericDao<User,Long> userDao) {
        // 创建查询条件实体
        User queryCondition = new User();
        queryCondition.setName("张三");
        queryCondition.setAge(25);
        queryCondition.setStatus("ACTIVE");
        // email 为 null，不会被包含在查询条件中
        
        // 使用 allEq 方法进行查询（默认使用 IS_NOT_EMPTY 策略）
        List<User> allEqResult1 = userDao.lambdaQuery()
                .allEq(queryCondition)  // 自动为所有非空属性生成等值条件
                .list();
        System.out.println("allEq 查询结果1: " + allEqResult1.size() + " 条记录");
        
        // 使用 allEq 方法并指定策略
        List<User> allEqResult2 = userDao.lambdaQuery()
                .allEq(queryCondition, ConditionStrategy.IS_NOT_NULL)  // 只要不为 null 就添加条件
                .list();
        System.out.println("allEq 查询结果2: " + allEqResult2.size() + " 条记录");
        
        // 在更新操作中使用 allEq
        User updateCondition = new User();
        updateCondition.setStatus("ACTIVE");
        updateCondition.setAge(30);
        
        int updateCount = userDao.lambdaUpdate()
                .set(User::getEmail, "<EMAIL>")
                .allEq(updateCondition)  // 根据条件实体生成 WHERE 条件
                .update();
        System.out.println("更新记录数: " + updateCount);
        
        // 在删除操作中使用 allEq
        User deleteCondition = new User();
        deleteCondition.setStatus("INACTIVE");
        deleteCondition.setAge(60);
        
        int deleteCount = userDao.lambdaDelete()
                .allEq(deleteCondition, ConditionStrategy.IS_NOT_NULL)
                .execute();
        System.out.println("删除记录数: " + deleteCount);
        
        // allEq 与其他条件组合使用
        User baseCondition = new User();
        baseCondition.setStatus("ACTIVE");
        
        List<User> combinedResult = userDao.lambdaQuery()
                 .allEq(baseCondition)  // 基础条件
                 .ge(User::getAge, 18)   // 额外条件
                 .orderByDesc(User::getId)
                 .list();
         System.out.println("组合查询结果: " + combinedResult.size() + " 条记录");
     }

     /**
      * 示例7：inIfNotEmpty 和 limit 方法使用示例
      */
     public void example7_InIfNotEmptyAndLimit(GenericDao<User,Long> userDao) {
         // inIfNotEmpty 方法使用示例 - List 参数
         List<String> statusList = Arrays.asList("ACTIVE", "PENDING");
         List<User> result1 = userDao.lambdaQuery()
                 .inIfNotEmpty(User::getStatus, statusList)  // 只有当 statusList 不为空时才添加 IN 条件
                 .list();
         System.out.println("inIfNotEmpty(List) 查询结果: " + result1.size() + " 条记录");
         
         // inIfNotEmpty 方法使用示例 - 数组参数
         List<User> result2 = userDao.lambdaQuery()
                 .inIfNotEmpty(User::getStatus, "ACTIVE", "PENDING", "INACTIVE")  // 数组参数
                 .list();
         System.out.println("inIfNotEmpty(数组) 查询结果: " + result2.size() + " 条记录");
         
         // 空集合测试 - 不会添加条件
         List<String> emptyList = new ArrayList<>();
         List<User> result3 = userDao.lambdaQuery()
                 .eqIfNotEmpty(User::getName, "张三")
                 .inIfNotEmpty(User::getStatus, emptyList)  // 空集合，不会添加 IN 条件
                 .list();
         System.out.println("空集合测试结果: " + result3.size() + " 条记录");
         
         // limit 方法使用示例
         List<User> limitedResult = userDao.lambdaQuery()
                 .eqIfNotEmpty(User::getStatus, "ACTIVE")
                 .orderByDesc(User::getId)
                 .limit(10)  // 限制返回 10 条记录
                 .list();
         System.out.println("limit 查询结果: " + limitedResult.size() + " 条记录（最多10条）");
         
         // 组合使用示例
         List<Integer> ageList = Arrays.asList(25, 30, 35);
         List<User> complexResult = userDao.lambdaQuery()
                 .eqIfNotEmpty(User::getStatus, "ACTIVE")
                 .inIfNotEmpty(User::getAge, ageList)
                 .likeIfNotEmpty(User::getName, "张")
                 .orderBy(User::getId)
                 .limit(5)
                 .list();
         System.out.println("复杂组合查询结果: " + complexResult.size() + " 条记录");
         
         // 生成的 SQL 示例：
         // SELECT * FROM user WHERE status = ? AND age IN (?, ?, ?) AND name LIKE ? ORDER BY id LIMIT 5
     }
 }