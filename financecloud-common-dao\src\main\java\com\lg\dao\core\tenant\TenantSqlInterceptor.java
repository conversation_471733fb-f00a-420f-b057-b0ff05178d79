package com.lg.dao.core.tenant;

import com.lg.dao.config.properties.TenantProperties;
import com.lg.dao.core.EntityInfoRegistry;
import com.lg.dao.core.interceptor.LightOrmSqlInterceptor;
import com.lg.dao.core.interceptor.SqlInterceptorContext;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.springframework.core.Ordered;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.*;

/**
 * 多租户SQL拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class TenantSqlInterceptor implements LightOrmSqlInterceptor {
    private final TenantProperties properties;
    private Set<String> ignoreTables;
    
    // 实体信息注册表，用于获取表结构信息
    private EntityInfoRegistry entityInfoRegistry;
    
    // 平台租户ID，通常为"1"，平台租户不需要带SCHEMA
    private static final String PLATFORM_TENANT_ID = "1";

    public TenantSqlInterceptor(TenantProperties.TenantMode mode, String tenantField, String schemaPrefix, List<String> ignoreTables) {
        this.properties = new TenantProperties();
        this.properties.setMode(mode);
        this.properties.setTenantField(tenantField);
        this.properties.setSchemaPrefix(schemaPrefix);
        this.properties.setIgnoreTables(ignoreTables);
        
        this.ignoreTables = new HashSet<>(ignoreTables);
    }
    
    /**
     * 设置数据源和实体信息注册表
     * @param dataSource 数据源
     * @return this
     */
    public TenantSqlInterceptor withDataSource(DataSource dataSource) {
        // 获取或初始化实体信息注册表
        this.entityInfoRegistry = EntityInfoRegistry.getInstance();
        if (dataSource != null) {
            String schema = null;
            if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
                schema = properties.getSchemaPrefix() + "default";
            }
            entityInfoRegistry.initialize(dataSource, schema);
        }
        return this;
    }

    @Override
    public String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
        if (!properties.isEnable()) {
            log.debug("租户拦截器被禁用: {}", properties);
            return sql;
        }
        
        // 检查是否是平台租户，平台租户在SCHEMA模式下不需要修改schema
        Integer tenantId = TenantContextHolder.getTenantId();
        boolean isPlatformTenant = tenantId != null && PLATFORM_TENANT_ID.equals(tenantId.toString());
        
        if (isPlatformTenant && properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            log.debug("平台租户({})不需要修改SCHEMA", tenantId);
            // 如果是平台租户，仍然需要添加tenant_id条件
            return processColumnMode(sql);
        }

        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                processSelect((Select) statement);
            } else if (statement instanceof Insert) {
                processInsert((Insert) statement);
            } else if (statement instanceof Update) {
                processUpdate((Update) statement);
            } else if (statement instanceof Delete) {
                processDelete((Delete) statement);
            }
            return statement.toString();
        } catch (JSQLParserException e) {
            log.warn("SQL解析失败: {}", sql, e);
            return sql;
        }
    }
    
    /**
     * 处理COLUMN模式下的SQL
     * 仅添加tenant_id条件，不修改schema
     */
    private String processColumnMode(String sql) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                processSelectColumnMode((Select) statement);
            } else if (statement instanceof Insert) {
                processInsertColumnMode((Insert) statement);
            } else if (statement instanceof Update) {
                processUpdateColumnMode((Update) statement);
            } else if (statement instanceof Delete) {
                processDeleteColumnMode((Delete) statement);
            }
            return statement.toString();
        } catch (JSQLParserException e) {
            log.warn("SQL解析失败: {}", sql, e);
            return sql;
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE - 100;
    }

    /**
     * 处理查询语句
     */
    private void processSelect(Select select) {
        if (!(select.getSelectBody() instanceof PlainSelect)) {
            return;
        }

        PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            processTable((Table) fromItem, plainSelect);
        }

        // 处理JOIN
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    processJoinTable((Table) rightItem, join);
                }
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的查询语句
     */
    private void processSelectColumnMode(Select select) {
        if (!(select.getSelectBody() instanceof PlainSelect)) {
            return;
        }

        PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            processTableColumnMode((Table) fromItem, plainSelect);
        }

        // 处理JOIN
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    processJoinTableColumnMode((Table) rightItem, join);
                }
            }
        }
    }

    /**
     * 处理表和租户条件
     */
    private void processTable(Table table, PlainSelect plainSelect) {
        if (ignoreTables.contains(table.getName())) {
            return;
        }

        if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            // 如果用户已经指定了schema，则不覆盖
            if (table.getSchemaName() == null) {
                Integer tenantId = TenantContextHolder.getTenantId();
                if (tenantId != null) {
                    String schema = properties.getSchemaPrefix() + tenantId;
                    table.setSchemaName(schema);
                }
            }
            
            // 增强功能：在SCHEMA模式下，也检查并添加tenant_id条件（如果表存在该字段）
            if (tableHasTenantColumn(table.getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(plainSelect.getWhere())) {
                    Expression where = plainSelect.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    plainSelect.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            }
        } else {
            // COLUMN模式 - 检查表是否有tenant_id列
            if (tableHasTenantColumn(table.getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(plainSelect.getWhere())) {
                    Expression where = plainSelect.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    plainSelect.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            } else {
                log.debug("表 {} 没有租户ID列，无法应用租户过滤", table.getName());
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的表和租户条件
     */
    private void processTableColumnMode(Table table, PlainSelect plainSelect) {
        if (ignoreTables.contains(table.getName())) {
            return;
        }

        if (tableHasTenantColumn(table.getName())) {
            // 检查是否已存在租户条件
            if (!hasTenantCondition(plainSelect.getWhere())) {
                Expression where = plainSelect.getWhere();
                Expression tenantExpression = createTenantExpression();
                plainSelect.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
            }
        } else {
            log.debug("表 {} 没有租户ID列，无法应用租户过滤", table.getName());
        }
    }

    /**
     * 处理JOIN表和租户条件
     */
    private void processJoinTable(Table table, Join join) {
        if (ignoreTables.contains(table.getName())) {
            return;
        }

        if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            // 如果用户已经指定了schema，则不覆盖
            if (table.getSchemaName() == null) {
                Integer tenantId = TenantContextHolder.getTenantId();
                if (tenantId != null) {
                    String schema = properties.getSchemaPrefix() + tenantId;
                    table.setSchemaName(schema);
                }
            }
            
            // 增强功能：在SCHEMA模式下，也检查并添加tenant_id条件（如果表存在该字段）
            if (tableHasTenantColumn(table.getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(join.getOnExpression())) {
                    Expression onExpression = join.getOnExpression();
                    Expression tenantExpression = createTenantExpression();
                    join.setOnExpression(onExpression == null ? tenantExpression : 
                        new AndExpression(onExpression, tenantExpression));
                }
            }
        } else {
            // COLUMN模式 - 检查表是否有tenant_id列
            if (tableHasTenantColumn(table.getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(join.getOnExpression())) {
                    Expression onExpression = join.getOnExpression();
                    Expression tenantExpression = createTenantExpression();
                    join.setOnExpression(onExpression == null ? tenantExpression : 
                        new AndExpression(onExpression, tenantExpression));
                }
            } else {
                log.debug("表 {} 没有租户ID列，无法在JOIN中应用租户过滤", 
                        table.getName());
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的JOIN表和租户条件
     */
    private void processJoinTableColumnMode(Table table, Join join) {
        if (ignoreTables.contains(table.getName())) {
            return;
        }

        if (tableHasTenantColumn(table.getName())) {
            // 检查是否已存在租户条件
            if (!hasTenantCondition(join.getOnExpression())) {
                Expression onExpression = join.getOnExpression();
                Expression tenantExpression = createTenantExpression();
                join.setOnExpression(onExpression == null ? tenantExpression : 
                    new AndExpression(onExpression, tenantExpression));
            }
        } else {
            log.debug("表 {} 没有租户ID列，无法在JOIN中应用租户过滤", 
                    table.getName());
        }
    }

    /**
     * 处理插入语句
     */
    private void processInsert(Insert insert) {
        if (ignoreTables.contains(insert.getTable().getName())) {
            return;
        }

        if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            // 如果用户已经指定了schema，则不覆盖
            if (insert.getTable().getSchemaName() == null) {
                Integer tenantId = TenantContextHolder.getTenantId();
                if (tenantId != null) {
                    String schema = properties.getSchemaPrefix() + tenantId;
                    insert.getTable().setSchemaName(schema);
                }
            }
            
            // 增强功能：在SCHEMA模式下，也检查并添加tenant_id值（如果表存在该字段）
            if (tableHasTenantColumn(insert.getTable().getName())) {
                // 检查是否已包含租户字段
                if (!hasTenantColumn(insert.getColumns())) {
                    // 添加租户字段
                    insert.getColumns().add(new Column(properties.getTenantField()));
                    
                    // 添加租户值
                    if (insert.getSelect() != null) {
                        processSelect(insert.getSelect());
                    } else {
                        List<Expression> expressions = new ArrayList<>(insert.getValues().getExpressions());
                        Integer tenantId = TenantContextHolder.getTenantId();
                        expressions.add(new StringValue(tenantId != null ? tenantId.toString() : ""));
                        insert.getValues().setExpressions((ExpressionList<Expression>) expressions);
                    }
                }
            }
        } else {
            // COLUMN模式 - 检查表是否有tenant_id列
            if (tableHasTenantColumn(insert.getTable().getName())) {
                // 检查是否已包含租户字段
                if (!hasTenantColumn(insert.getColumns())) {
                    // 添加租户字段
                    insert.getColumns().add(new Column(properties.getTenantField()));
                    
                    // 添加租户值
                    if (insert.getSelect() != null) {
                        processSelect(insert.getSelect());
                    } else {
                        List<Expression> expressions = new ArrayList<>(insert.getValues().getExpressions());
                        Integer tenantId = TenantContextHolder.getTenantId();
                        expressions.add(new StringValue(tenantId != null ? tenantId.toString() : ""));
                        insert.getValues().setExpressions((ExpressionList<Expression>) expressions);
                    }
                }
            } else {
                log.debug("表 {} 没有租户ID列，无法在INSERT中应用租户过滤", 
                        insert.getTable().getName());
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的插入语句
     */
    private void processInsertColumnMode(Insert insert) {
        if (ignoreTables.contains(insert.getTable().getName())) {
            return;
        }

        if (tableHasTenantColumn(insert.getTable().getName())) {
            // 检查是否已包含租户字段
            if (!hasTenantColumn(insert.getColumns())) {
                // 添加租户字段
                insert.getColumns().add(new Column(properties.getTenantField()));
                
                // 添加租户值
                if (insert.getSelect() != null) {
                    processSelectColumnMode(insert.getSelect());
                } else {
                    List<Expression> expressions = new ArrayList<>(insert.getValues().getExpressions());
                    Integer tenantId = TenantContextHolder.getTenantId();
                    expressions.add(new StringValue(tenantId != null ? tenantId.toString() : ""));
                    insert.getValues().setExpressions((ExpressionList<Expression>) expressions);
                }
            }
        } else {
            log.debug("表 {} 没有租户ID列，无法在INSERT中应用租户过滤", 
                    insert.getTable().getName());
        }
    }

    /**
     * 处理更新语句
     */
    private void processUpdate(Update update) {
        if (ignoreTables.contains(update.getTable().getName())) {
            return;
        }

        if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            // 如果用户已经指定了schema，则不覆盖
            if (update.getTable().getSchemaName() == null) {
                Integer tenantId = TenantContextHolder.getTenantId();
                if (tenantId != null) {
                    String schema = properties.getSchemaPrefix() + tenantId;
                    update.getTable().setSchemaName(schema);
                }
            }
            
            // 增强功能：在SCHEMA模式下，也检查并添加tenant_id条件（如果表存在该字段）
            if (tableHasTenantColumn(update.getTable().getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(update.getWhere())) {
                    Expression where = update.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    update.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            }
        } else {
            // COLUMN模式 - 检查表是否有tenant_id列
            if (tableHasTenantColumn(update.getTable().getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(update.getWhere())) {
                    Expression where = update.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    update.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            } else {
                log.debug("表 {} 没有租户ID列，无法在UPDATE中应用租户过滤", 
                        update.getTable().getName());
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的更新语句
     */
    private void processUpdateColumnMode(Update update) {
        if (ignoreTables.contains(update.getTable().getName())) {
            return;
        }

        if (tableHasTenantColumn(update.getTable().getName())) {
            // 检查是否已存在租户条件
            if (!hasTenantCondition(update.getWhere())) {
                Expression where = update.getWhere();
                Expression tenantExpression = createTenantExpression();
                update.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
            }
        } else {
            log.debug("表 {} 没有租户ID列，无法在UPDATE中应用租户过滤", 
                    update.getTable().getName());
        }
    }

    /**
     * 处理删除语句
     */
    private void processDelete(Delete delete) {
        if (ignoreTables.contains(delete.getTable().getName())) {
            return;
        }

        if (properties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            // 如果用户已经指定了schema，则不覆盖
            if (delete.getTable().getSchemaName() == null) {
                Integer tenantId = TenantContextHolder.getTenantId();
                if (tenantId != null) {
                    String schema = properties.getSchemaPrefix() + tenantId;
                    delete.getTable().setSchemaName(schema);
                }
            }
            
            // 增强功能：在SCHEMA模式下，也检查并添加tenant_id条件（如果表存在该字段）
            if (tableHasTenantColumn(delete.getTable().getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(delete.getWhere())) {
                    Expression where = delete.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    delete.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            }
        } else {
            // COLUMN模式 - 检查表是否有tenant_id列
            if (tableHasTenantColumn(delete.getTable().getName())) {
                // 检查是否已存在租户条件
                if (!hasTenantCondition(delete.getWhere())) {
                    Expression where = delete.getWhere();
                    Expression tenantExpression = createTenantExpression();
                    delete.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
                }
            } else {
                log.debug("表 {} 没有租户ID列，无法在DELETE中应用租户过滤", 
                        delete.getTable().getName());
            }
        }
    }
    
    /**
     * 处理COLUMN模式下的删除语句
     */
    private void processDeleteColumnMode(Delete delete) {
        if (ignoreTables.contains(delete.getTable().getName())) {
            return;
        }

        if (tableHasTenantColumn(delete.getTable().getName())) {
            // 检查是否已存在租户条件
            if (!hasTenantCondition(delete.getWhere())) {
                Expression where = delete.getWhere();
                Expression tenantExpression = createTenantExpression();
                delete.setWhere(where == null ? tenantExpression : new AndExpression(where, tenantExpression));
            }
        } else {
            log.debug("表 {} 没有租户ID列，无法在DELETE中应用租户过滤", 
                    delete.getTable().getName());
        }
    }

    /**
     * 检查表是否包含租户ID字段
     * 使用EntityInfoRegistry获取表结构信息
     */
    private boolean tableHasTenantColumn(String tableName) {
        if (entityInfoRegistry == null) {
            // 如果实体信息注册表未初始化，假设表有租户列
            return true;
        }
        
        return entityInfoRegistry.tableHasTenantColumn(tableName, properties.getTenantField());
    }

    /**
     * 检查是否已存在租户条件
     */
    private boolean hasTenantCondition(Expression expression) {
        if (expression == null) {
            return false;
        }

        // 递归检查表达式
        if (expression instanceof AndExpression) {
            AndExpression and = (AndExpression) expression;
            return hasTenantCondition(and.getLeftExpression()) || 
                   hasTenantCondition(and.getRightExpression());
        }

        if (expression instanceof EqualsTo) {
            EqualsTo equals = (EqualsTo) expression;
            if (equals.getLeftExpression() instanceof Column) {
                Column column = (Column) equals.getLeftExpression();
                return column.getColumnName().equals(properties.getTenantField());
            }
        }

        return false;
    }

    /**
     * 检查是否已包含租户字段
     */
    private boolean hasTenantColumn(List<Column> columns) {
        return columns != null && columns.stream()
                .anyMatch(col -> col.getColumnName().equals(properties.getTenantField()));
    }

    /**
     * 创建租户表达式
     */
    private Expression createTenantExpression() {
        Integer tenantId = TenantContextHolder.getTenantId();
        if (tenantId == null) {
            throw new IllegalStateException("上下文中未找到租户ID");
        }

        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column(properties.getTenantField()));
        equalsTo.setRightExpression(new StringValue(tenantId.toString()));
        return equalsTo;
    }
}