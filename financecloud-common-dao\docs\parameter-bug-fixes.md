# 参数处理 Bug 修复文档

## 问题概述

在 financecloud-common-dao 框架中发现了多个参数处理相关的 bug，主要涉及：

1. **Lambda IN 查询参数处理错误**
2. **MyBatis 代理 Map 参数顺序问题**
3. **SQL 模板管理参数顺序错误**

## 问题详情

### 1. Lambda IN 查询 Bug

**问题描述：**
当使用 `lambdaQuery().in(User::getId, Arrays.asList("308437967765505"))` 时，生成的 SQL 为：
```sql
SELECT * FROM sys_user WHERE id IN ('[308437967765505]')
```

**问题原因：**
`LambdaQuery.in(LFunction<T, R> column, Object... values)` 方法接受可变参数，但当传入 `List` 对象时，整个 `List` 被当作一个参数处理。

**修复方案：**
1. 在 `LambdaQuery` 中添加支持 `List` 参数的重载方法
2. 在 `QueryBuilder` 中添加支持 `List` 参数的重载方法
3. 在 `LambdaJoinQuery` 中添加支持可变参数的重载方法

### 2. MyBatis 代理 Map 参数问题

**问题描述：**
当 SQL 为 `select * from a where name=? and age=?`，传入参数为单个 Map 时，参数顺序可能不正确。

**问题原因：**
`MapperMethod.prepareParam()` 方法对 Map 参数的处理不当，直接使用 `params.values()` 可能导致参数顺序与 SQL 中的占位符不匹配。

**修复方案：**
1. 修改 `MapperMethod.execute()` 方法中的参数处理逻辑
2. 添加 `countPlaceholders()` 方法来计算 SQL 中占位符数量
3. 按照 `param1`, `param2`... 的顺序从 Map 中提取参数

### 3. SQL 模板管理参数顺序问题

**问题描述：**
使用 SQL 模板时，参数顺序可能与模板渲染后的 SQL 中的占位符顺序不一致。

**问题原因：**
`BaseDao` 中的模板方法使用 `new ArrayList<>(params.values())` 来构建参数列表，但这个顺序可能与 MyBatis 模板引擎处理后的参数顺序不一致。

**修复方案：**
1. 在 `MybatisTemplateEngine` 中添加 `renderWithParams()` 方法
2. 在 `SqlTemplateManager` 中添加 `getSqlWithParams()` 方法
3. 修改 `BaseDao` 中所有使用模板的方法，使用正确的参数顺序

## 修复内容

### 1. LambdaQuery 修复

```java
// 添加支持 List 参数的 in 方法
public <R> LambdaQuery<T> in(LFunction<T, R> column, List<?> values) {
    if (values != null && !values.isEmpty()) {
        queryBuilder.in(getColumnName(column), values.toArray());
    }
    return this;
}
```

### 2. QueryBuilder 修复

```java
// 添加支持 List 参数的 in 方法
public QueryBuilder<T> in(String field, List<?> values) {
    if (values != null && !values.isEmpty()) {
        String placeholders = String.join(",", Collections.nCopies(values.size(), "?"));
        addCondition(field + " IN (" + placeholders + ")", values.toArray());
    }
    return this;
}
```

### 3. MapperMethod 修复

```java
// 修复 Map 参数处理逻辑
else if (param instanceof Map) {
    Map<?, ?> paramMap = (Map<?, ?>) param;
    String sql = sqlCommand.getSql();
    if (sql != null) {
        int placeholderCount = countPlaceholders(sql);
        if (placeholderCount > 0) {
            for (int i = 1; i <= placeholderCount; i++) {
                String key = "param" + i;
                if (paramMap.containsKey(key)) {
                    finalParams.add(paramMap.get(key));
                } else {
                    Object[] values = paramMap.values().toArray();
                    if (i - 1 < values.length) {
                        finalParams.add(values[i - 1]);
                    } else {
                        finalParams.add(null);
                    }
                }
            }
        }
    }
}
```

### 4. 模板引擎修复

```java
// 添加返回参数顺序的渲染方法
public RenderResult renderWithParams(String template, Map<String, Object> params) {
    // 使用 MyBatis 的 BoundSql 来获取正确的参数顺序
    // ...
    return new RenderResult(sql, orderedParams);
}
```

### 5. BaseDao 修复

```java
// 使用正确的参数顺序
public <T> List<T> selectListByTemplate(Class<T> entityClass, String templateName, Map<String, Object> params) {
    MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
    return lightOrmSqlExecutor.executeQuery(result.getSql(), result.getParams(), entityClass);
}
```

## 测试验证

创建了 `ParameterBugFixTest` 测试类来验证修复效果：

1. **testQueryBuilderInWithList()** - 验证 QueryBuilder 的 List 参数处理
2. **testSingleElementListBugFix()** - 验证单元素 List 的 bug 修复
3. **testMybatisMapperProxyMapParams()** - 验证 MyBatis 代理的 Map 参数处理
4. **testTemplateEngineParameterOrder()** - 验证模板引擎的参数顺序处理

## 影响范围

这些修复影响以下组件：

1. **LambdaQuery** - Lambda 风格的查询构建器
2. **QueryBuilder** - 基础查询构建器
3. **LambdaJoinQuery** - Lambda 风格的 JOIN 查询构建器
4. **MapperMethod** - MyBatis 代理方法处理
5. **MybatisTemplateEngine** - MyBatis 风格的模板引擎
6. **SqlTemplateManager** - SQL 模板管理器
7. **BaseDao** - 基础数据访问对象

## 兼容性

所有修复都是向后兼容的，不会影响现有代码的使用。新增的方法提供了更好的参数处理能力，而原有的方法仍然可以正常使用。

## 建议

1. 在使用 Lambda IN 查询时，推荐使用 `List` 参数而不是可变参数
2. 在使用 MyBatis 代理时，确保 Map 参数的键名遵循 `param1`, `param2`... 的命名规范
3. 在使用 SQL 模板时，推荐使用新的 `getSqlWithParams()` 方法来确保参数顺序正确
