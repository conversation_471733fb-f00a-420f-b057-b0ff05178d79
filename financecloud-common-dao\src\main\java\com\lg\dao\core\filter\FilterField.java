package com.lg.dao.core.filter;

import java.lang.annotation.*;

/**
 * 过滤字段注解
 * 用于配置过滤字段
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FilterField {
    /**
     * 字段名
     */
    String field() default "";
    
    /**
     * 操作符
     * 可选值：eq, neq, gt, gte, lt, lte, like, in, between
     */
    String operator() default "eq";
    
    /**
     * 字段值
     */
    String value() default "";
} 