# BaseDao 实体信息缓存优化

## 优化背景

BaseDao 中的 `ENTITY_INFO_CACHE` 原本使用独立的 Caffeine 缓存，这与框架的统一缓存管理理念不符，需要整合到 UnifiedCacheManager 中。

## 优化前后对比

### 优化前

```java
// 独立的 Caffeine 缓存
private static final Cache<Class<?>, EntityInfo> ENTITY_INFO_CACHE = 
    Caffeine.newBuilder()
        .maximumSize(500)  // 固定大小
        .build();

public EntityInfo getEntityInfo(Class<?> entityClass) {
    return ENTITY_INFO_CACHE.get(entityClass, EntityInfo::of);
}
```

**问题**：
- ❌ 独立缓存管理，无法统一配置
- ❌ 固定缓存大小 500，不够灵活
- ❌ 无法与其他缓存统一监控
- ❌ 增加了框架的复杂性

### 优化后

```java
// 使用统一缓存管理器
public EntityInfo getEntityInfo(Class<?> entityClass) {
    if (unifiedCacheManager != null) {
        // 使用统一缓存管理器
        String cacheKey = entityClass.getName();
        return unifiedCacheManager.get(
            UnifiedCacheManager.CacheType.ENTITY_CACHE,
            cacheKey,
            () -> EntityInfo.of(entityClass)  // 仅在 EntityInfoManager 内部使用
        );
    } else {
        // 降级到直接创建
        return EntityInfo.of(entityClass);
    }
}
```

**优势**：
- ✅ 统一缓存管理，配置更灵活
- ✅ 缓存大小可配置（默认 3000）
- ✅ 支持过期时间配置
- ✅ 统一的缓存监控和统计
- ✅ 降级处理，提高健壮性

## 缓存配置

### ENTITY_CACHE 配置

```java
ENTITY_CACHE("entity", 3000, 3600, 1800)
```

- **缓存名称**: entity
- **最大条目数**: 3000（比原来的 500 更大）
- **写入后过期**: 3600 秒（1 小时）
- **访问后过期**: 1800 秒（30 分钟）

### 配置优势

1. **更大容量**: 从 500 提升到 3000，支持更多实体类型
2. **智能过期**: 支持写入后过期和访问后过期两种策略
3. **统一管理**: 与其他缓存统一配置和监控
4. **可扩展性**: 可根据需要调整缓存参数

## 性能影响

### 内存使用

- **优化前**: 独立 Caffeine 缓存，固定开销
- **优化后**: 统一缓存管理，更高效的内存使用

### 缓存命中率

- **优化前**: 固定 500 条目，可能因容量不足导致频繁淘汰
- **优化后**: 3000 条目 + 智能过期，更高的命中率

### 监控能力

- **优化前**: 无法监控缓存状态
- **优化后**: 统一的缓存统计和监控

## 使用示例

### 基本使用

```java
@Service
public class UserService {
    
    @Autowired
    private BaseDao baseDao;
    
    public void processUser(User user) {
        // 获取实体信息，自动使用统一缓存
        EntityInfo entityInfo = baseDao.getEntityInfo(User.class);
        
        // 使用实体信息进行业务处理
        String tableName = entityInfo.getTableName();
        List<String> columns = entityInfo.getColumnNames();
        
        // ... 业务逻辑
    }
}
```

### 缓存监控

```java
@Service
public class CacheMonitorService {
    
    @Autowired
    private UnifiedCacheManager cacheManager;
    
    public void printEntityCacheStats() {
        // 打印实体缓存统计信息
        cacheManager.printStats();
        
        // 输出示例：
        // 缓存: entity, 大小: 150, 命中率: 95.2%, 请求数: 1000, 命中数: 952, 未命中数: 48
    }
}
```

## 向后兼容性

### 完全兼容

- ✅ `getEntityInfo()` 方法签名不变
- ✅ 返回结果完全一致
- ✅ 性能只会更好，不会变差
- ✅ 无需修改任何业务代码

### 降级处理

当 UnifiedCacheManager 不可用时，自动降级到直接创建 EntityInfo，确保系统正常运行。

## 测试验证

```java
@Test
public void testEntityInfoCache() {
    // 第一次获取
    EntityInfo info1 = baseDao.getEntityInfo(User.class);
    
    // 第二次获取，应该从缓存获取
    EntityInfo info2 = baseDao.getEntityInfo(User.class);
    
    // 验证结果一致
    assertEquals(info1.getTableName(), info2.getTableName());
}
```

## 总结

这次优化将 BaseDao 的实体信息缓存完全整合到统一缓存管理体系中，实现了：

1. **统一管理** - 所有缓存都通过 UnifiedCacheManager 管理
2. **配置灵活** - 支持动态调整缓存参数
3. **监控完善** - 统一的缓存监控和统计
4. **性能提升** - 更大的缓存容量和更智能的过期策略
5. **代码简化** - 移除了独立的 Caffeine 缓存依赖

这是框架走向统一缓存管理的重要一步，为后续的缓存优化奠定了基础。
