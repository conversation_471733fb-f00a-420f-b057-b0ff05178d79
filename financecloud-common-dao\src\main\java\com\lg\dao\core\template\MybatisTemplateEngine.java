package com.lg.dao.core.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.Configuration;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.util.Collections;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.Queue;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.xml.sax.InputSource;

/**
 * MyBatis风格的模板引擎
 * 支持以下标签：
 * 1. &lt;if test="condition"&gt;
 * 2. &lt;foreach collection="list" item="item" separator="," open="(" close=")"&gt;
 * 3. ${} 直接替换
 * 4. #{} 参数替换
 *
 * 优化说明：
 * - 使用共享的Configuration实例，避免为每个SQL模板创建独立的Configuration对象
 * - 为每个SQL模板生成唯一的namespace和MappedStatement ID，避免在共享Configuration中冲突
 * - 保留MappedStatement缓存，因为每个SQL模板需要独立的MappedStatement
 * - 大幅减少内存占用和对象创建开销
 *
 * 重要修复：
 * - 修复了共享Configuration导致的MappedStatement覆盖问题
 * - 确保每个SQL模板都有独立的解析结果，不会相互干扰
 *
 * <AUTHOR>
 */
@Slf4j
public class MybatisTemplateEngine {

    // 匹配CDATA部分的正则表达式
    private static final Pattern CDATA_PATTERN = Pattern.compile("<\\!\\[CDATA\\[(.*?)\\]\\]>", Pattern.DOTALL);

    // 匹配MyBatis标签的正则表达式
    private static final Pattern MYBATIS_TAG_PATTERN = Pattern.compile("<(/?)(if|foreach|where|set|choose|when|otherwise|trim)[^>]*>", Pattern.CASE_INSENSITIVE);

    // 匹配MyBatis参数占位符的正则表达式
    private static final Pattern MYBATIS_PARAM_PATTERN = Pattern.compile("(#|\\$)\\{([^}]+)\\}");

    // 匹配HTML注释的正则表达式
    private static final Pattern HTML_COMMENT_PATTERN = Pattern.compile("<!--.*?-->", Pattern.DOTALL);

    // 匹配连续空白字符的正则表达式
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");

    // 缓存相关
    private final UnifiedCacheManager unifiedCacheManager;
    private final Configuration sharedConfiguration;

    /**
     * MappedStatement 缓存，通过统一缓存管理器管理
     * 如果没有统一缓存管理器，则使用简单的本地缓存作为备用
     */
    private final Map<String, MappedStatement> localMappedStatementCache = new ConcurrentHashMap<>();

    /**
     * Configuration 工厂，用于创建轻量级的 Configuration 实例
     * 通过预配置模板减少重复初始化开销
     */
    private volatile Configuration templateConfiguration;

    public MybatisTemplateEngine() {
        this(null);
    }

    public MybatisTemplateEngine(UnifiedCacheManager unifiedCacheManager) {
        this.unifiedCacheManager = unifiedCacheManager;
        this.sharedConfiguration = createSharedConfiguration();
    }

    /**
     * 创建共享的Configuration实例
     */
    private Configuration createSharedConfiguration() {
        Configuration configuration = new Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.SIMPLE);

        // 不再需要缓存配置，使用池化管理

        return configuration;
    }

    /**
     * 获取模板 Configuration，使用双重检查锁定模式确保线程安全
     */
    private Configuration getTemplateConfiguration() {
        if (templateConfiguration == null) {
            synchronized (this) {
                if (templateConfiguration == null) {
                    templateConfiguration = createOptimizedConfiguration();
                    log.debug("创建优化的模板Configuration");
                }
            }
        }
        return templateConfiguration;
    }

    /**
     * 创建优化的 Configuration 实例
     * 预设常用配置，减少每次创建的开销
     */
    private Configuration createOptimizedConfiguration() {
        Configuration configuration = new Configuration();

        // 基础配置
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.SIMPLE);

        // 性能优化配置
        configuration.setCacheEnabled(false);  // 禁用二级缓存，避免内存泄漏
        configuration.setLazyLoadingEnabled(false);  // 禁用懒加载
        configuration.setAggressiveLazyLoading(false);

        return configuration;
    }

    /**
     * 为特定模板创建独立的 Configuration
     * 基于模板 Configuration 快速克隆，减少初始化开销
     */
    private Configuration createConfigurationForTemplate(String templateKey) {
        Configuration template = getTemplateConfiguration();

        // 创建新的Configuration实例，复制基础配置
        Configuration config = new Configuration();
        config.setMapUnderscoreToCamelCase(template.isMapUnderscoreToCamelCase());
        config.setUseGeneratedKeys(template.isUseGeneratedKeys());
        config.setDefaultExecutorType(template.getDefaultExecutorType());
        config.setCacheEnabled(template.isCacheEnabled());
        config.setLazyLoadingEnabled(template.isLazyLoadingEnabled());
        config.setAggressiveLazyLoading(template.isAggressiveLazyLoading());

        log.debug("为模板创建独立Configuration: {}", templateKey);
        return config;
    }

    /**
     * 渲染模板
     *
     * @param template 模板内容
     * @param params 参数
     * @return 渲染后的内容
     */
    public String render(String template, Map<String, Object> params) {
        try {
            return parseSql(template, params);
        } catch (Exception e) {
            log.error("Failed to render template", e);
            throw new RuntimeException("Failed to render template", e);
        }
    }

    /**
     * 渲染模板并返回参数顺序信息
     *
     * @param template 模板内容
     * @param params 参数
     * @return 渲染结果，包含SQL和参数列表
     */
    @Deprecated
    public RenderResult renderWithParams(String template, Map<String, Object> params) {
        return renderWithParams(null, template, params);
    }

    /**
     * 渲染模板并返回参数顺序信息（支持模板名称）
     *
     * @param templateName 模板名称（如 user.findById），用于生成有意义的namespace
     * @param template 模板内容
     * @param params 参数
     * @return 渲染结果，包含SQL和参数列表
     */
    public RenderResult renderWithParams(String templateName, String template, Map<String, Object> params) {
        try {
            // 如果启用了统一缓存且参数不包含集合类型，使用缓存
            if (unifiedCacheManager != null && !containsCollectionParam(params)) {
                String cacheKey = buildCacheKey(template, params);
                log.debug("使用缓存渲染模板: {}", cacheKey);
                return unifiedCacheManager.get(
                    UnifiedCacheManager.CacheType.SQL_TEMPLATE_CACHE,
                    cacheKey,
                    () -> {
                        log.debug("缓存未命中，执行模板渲染");
                        return parseSqlWithParams(templateName, template, params);
                    }
                );
            }

            // 直接渲染（不缓存）
//            log.debug("跳过缓存，直接渲染模板 (原因: {})",
//                     unifiedCacheManager == null ? "未启用缓存" : "参数包含集合类型");
            return parseSqlWithParams(templateName, template, params);
        } catch (Exception e) {
            log.error("Failed to render template with params", e);
            throw new RuntimeException("Failed to render template with params", e);
        }
    }

    /**
     * 处理模板中的CDATA部分
     * 
     * @param template 原始模板
     * @return 处理后的模板
     */
    private String processCdataContent(String template) {
        if (template == null || !template.contains("CDATA")) {
            return template;
        }
        
        // 使用StringBuilder来构建结果
        StringBuilder result = new StringBuilder();
        Matcher matcher = CDATA_PATTERN.matcher(template);
        int lastEnd = 0;
        
        // 查找所有CDATA部分
        while (matcher.find()) {
            // 添加CDATA之前的内容
            result.append(template, lastEnd, matcher.start());
            
            // 获取CDATA内容并添加（不带CDATA标记）
            String cdataContent = matcher.group(1);
            result.append(cdataContent);
            
            // 更新lastEnd
            lastEnd = matcher.end();
        }
        
        // 添加最后一个CDATA之后的内容
        if (lastEnd < template.length()) {
            result.append(template.substring(lastEnd));
        }
        
        return result.toString();
    }
    
    /**
     * 转义XML中的特殊字符，但保留MyBatis标签和参数占位符
     * 
     * @param sql SQL模板
     * @return 转义后的SQL
     */
    private String escapeXmlSpecialChars(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }
        
        // 先保护MyBatis标签和参数占位符
        List<String> mybatisTags = new ArrayList<>();
        List<String> mybatisParams = new ArrayList<>();
        
        // 替换并保存MyBatis标签
        Matcher tagMatcher = MYBATIS_TAG_PATTERN.matcher(sql);
        StringBuffer tagBuffer = new StringBuffer();
        int tagIndex = 0;
        
        while (tagMatcher.find()) {
            String placeholder = "##MYBATIS_TAG_" + tagIndex + "##";
            mybatisTags.add(tagMatcher.group());
            tagMatcher.appendReplacement(tagBuffer, placeholder);
            tagIndex++;
        }
        tagMatcher.appendTail(tagBuffer);
        sql = tagBuffer.toString();
        
        // 替换并保存MyBatis参数占位符
        Matcher paramMatcher = MYBATIS_PARAM_PATTERN.matcher(sql);
        StringBuffer paramBuffer = new StringBuffer();
        int paramIndex = 0;
        
        while (paramMatcher.find()) {
            String placeholder = "##MYBATIS_PARAM_" + paramIndex + "##";
            mybatisParams.add(paramMatcher.group());
            paramMatcher.appendReplacement(paramBuffer, placeholder);
            paramIndex++;
        }
        paramMatcher.appendTail(paramBuffer);
        sql = paramBuffer.toString();
        
        // 转义XML特殊字符
        sql = sql.replace("&", "&amp;")
                 .replace("<", "&lt;")
                 .replace(">", "&gt;")
                 .replace("\"", "&quot;")
                 .replace("'", "&apos;");
        
        // 恢复MyBatis参数占位符
        for (int i = 0; i < mybatisParams.size(); i++) {
            sql = sql.replace("##MYBATIS_PARAM_" + i + "##", mybatisParams.get(i));
        }
        
        // 恢复MyBatis标签
        for (int i = 0; i < mybatisTags.size(); i++) {
            sql = sql.replace("##MYBATIS_TAG_" + i + "##", mybatisTags.get(i));
        }
        
        return sql;
    }
    
    /**
     * 清理SQL，移除HTML注释并规范化空白字符
     * 
     * @param sql 原始SQL
     * @return 清理后的SQL
     */
    private String cleanupSql(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }
        
        // 移除HTML注释
        sql = HTML_COMMENT_PATTERN.matcher(sql).replaceAll("");
        
        // 规范化空白字符（将多个连续空白字符替换为单个空格）
        sql = WHITESPACE_PATTERN.matcher(sql).replaceAll(" ");
        
        // 修复WHERE子句中可能存在的多余空格
        sql = sql.replaceAll("WHERE\\s+AND", "WHERE");
        sql = sql.replaceAll("WHERE\\s+OR", "WHERE");
        
        // 修复GROUP BY、ORDER BY子句中可能存在的多余空格
        sql = sql.replaceAll("GROUP BY\\s+,", "GROUP BY");
        sql = sql.replaceAll("ORDER BY\\s+,", "ORDER BY");
        
        return sql.trim();
    }

    public String parseSql(String xmlSql, Map<String, Object> params) {
        try {
            // 获取或创建 MappedStatement
            MappedStatement mappedStatement = getOrCreateMappedStatement(null, xmlSql);

            // 使用 MyBatis 的 BoundSql 获取解析后的 SQL
            BoundSql boundSql = mappedStatement.getBoundSql(params);
            String sql = boundSql.getSql();

            // 清理SQL
            sql = cleanupSql(sql);

            log.debug("SQL:" + sql);
            return sql;
        } catch (Exception e) {
            log.error("Error parsing SQL", e);
            throw new RuntimeException("Error parsing SQL", e);
        }
    }

    public RenderResult parseSqlWithParams(String xmlSql, Map<String, Object> params) {
        return parseSqlWithParams(null, xmlSql, params);
    }

    public RenderResult parseSqlWithParams(String templateName, String xmlSql, Map<String, Object> params) {
        try {
            // 获取或创建 MappedStatement
            MappedStatement mappedStatement = getOrCreateMappedStatement(templateName, xmlSql);

            // 使用 MyBatis 的 BoundSql 获取解析后的 SQL
            BoundSql boundSql = mappedStatement.getBoundSql(params);
            String sql = boundSql.getSql();
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();

            // 清理SQL
            sql = cleanupSql(sql);

            // 按照参数映射的顺序提取参数值
            List<Object> orderedParams = new ArrayList<>();
            for (ParameterMapping parameterMapping : parameterMappings) {
                String property = parameterMapping.getProperty();
                Object value = boundSql.getAdditionalParameter(property);
                if (value == null && params != null) {
                    value = params.get(property);
                }
                orderedParams.add(value);
            }

//            log.debug("SQL:" + sql);
//            log.debug("Ordered params:" + orderedParams);

            return new RenderResult(sql, orderedParams);
        } catch (Exception e) {
            log.error("Error parsing SQL with params. SQL template: {}", xmlSql, e);
            throw new RuntimeException("Error parsing SQL with params: " + e.getMessage(), e);
        }
    }

    /**
     * 获取或创建 MappedStatement（使用统一缓存管理器优化）
     */
    private MappedStatement getOrCreateMappedStatement(String templateName, String xmlSql) {
        // 生成模板的哈希键
        String templateKey = String.valueOf(xmlSql.hashCode());

        // 如果启用了统一缓存管理器，使用统一缓存
        if (unifiedCacheManager != null) {
            String cacheKey = "mappedStatement:" + templateKey;
           // log.debug("使用统一缓存获取 MappedStatement: {}", cacheKey);
            return unifiedCacheManager.get(
                UnifiedCacheManager.CacheType.MYBATIS_PROXY_CACHE,
                cacheKey,
                () -> {
                   // log.debug("统一缓存未命中，创建新的 MappedStatement: {}", templateKey);
                    return createMappedStatement(templateName, xmlSql, templateKey);
                }
            );
        }

        // 回退到本地缓存
        MappedStatement cachedStatement = localMappedStatementCache.get(templateKey);
        if (cachedStatement != null) {
           // log.debug("使用本地缓存的 MappedStatement: {}", templateKey);
            return cachedStatement;
        }

        // 本地缓存未命中，创建新的 MappedStatement
       // log.debug("本地缓存未命中，创建新的 MappedStatement: {}", templateKey);
        MappedStatement newStatement = createMappedStatement(templateName, xmlSql, templateKey);
        localMappedStatementCache.put(templateKey, newStatement);
        return newStatement;
    }

    /**
     * 创建新的 MappedStatement
     */
    private MappedStatement createMappedStatement(String templateName, String xmlSql, String templateKey) {
        try {
            // 处理CDATA部分
            String processedSql = processCdataContent(xmlSql);
            // 转义XML特殊字符，但保留MyBatis标签
            processedSql = escapeXmlSpecialChars(processedSql);
            String sqlForUse = processedSql.trim();

            // 使用模板键生成唯一的namespace，确保不同模板不会冲突
            String namespace = "template_" + Math.abs(templateKey.hashCode());
            String statementId = "selectDataSet";
            String fullStatementId = namespace + "." + statementId;

            // 如果有模板名称，在日志中显示
            if (templateName != null) {
                log.debug("处理模板: {} (独立Configuration, namespace: {})", templateName, namespace);
            }

            // 为每个模板创建独立的Configuration，避免冲突
            Configuration configuration = new Configuration();

            // 生成XML并解析
            String xml = MessageFormat.format("<?xml version=\"1.0\" encoding=\"UTF-8\" ?><!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\"><mapper namespace=\"{0}\"><select id=\"{1}\">{2}</select></mapper>",
                namespace, statementId, sqlForUse);

            if (log.isTraceEnabled()) {
               // log.trace("Generated XML for parsing: {}", xml);
            }

            try (InputStream inputStream = IoUtil.toStream(xml, "UTF-8")) {
                XMLMapperBuilder mapperParser = new XMLMapperBuilder(inputStream, configuration, null, null);
                mapperParser.parse();

                MappedStatement mappedStatement = configuration.getMappedStatement(fullStatementId);
              //  log.debug("成功创建MappedStatement: {} (模板: {})", fullStatementId, templateName);
                return mappedStatement;
            }
        } catch (Exception e) {
            log.error("Error creating MappedStatement for template: {} (key: {})", templateName, templateKey, e);
            throw new RuntimeException("Error creating MappedStatement", e);
        }
    }



    /**
     * 检测参数中是否包含集合类型
     */
    private boolean containsCollectionParam(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return false;
        }

        for (Object value : params.values()) {
            if (value instanceof Collection || value instanceof Object[] || value instanceof Map) {
                return true;
            }
        }
        return false;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String template, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "template:" + template.hashCode() + ":null";
        }

        StringBuilder keyBuilder = new StringBuilder("template:")
                .append(template.hashCode())
                .append(":");

        // 根据参数类型构建更稳定的缓存键
        if (params.size() == 1) {
            Object singleValue = params.values().iterator().next();
            if (singleValue instanceof String ||
                singleValue instanceof Number ||
                singleValue instanceof Boolean) {
                keyBuilder.append("primitive:").append(singleValue);
            } else {
                keyBuilder.append("object:").append(singleValue.hashCode());
            }
        } else {
            // 多参数使用键集合的哈希
            keyBuilder.append("map:")
                     .append(params.size())
                     .append(":")
                     .append(params.keySet().hashCode());
        }

        return keyBuilder.toString();
    }

    /**
     * 渲染结果类
     */
    public static class RenderResult {
        private final String sql;
        private final List<Object> params;

        public RenderResult(String sql, List<Object> params) {
            this.sql = sql;
            this.params = params;
        }

        public String getSql() {
            return sql;
        }

        public List<Object> getParams() {
            return params;
        }
    }

    /**
     * 获取缓存统计信息（通过统一缓存管理器）
     */
    public String getCacheStats() {
        if (unifiedCacheManager != null) {
            return "使用统一缓存管理器，请通过 CacheMonitor 查看详细统计";
        } else {
            return String.format("本地MappedStatement缓存 - 大小: %d", localMappedStatementCache.size());
        }
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        if (unifiedCacheManager != null) {
            log.info("请通过统一缓存管理器清空缓存");
        } else {
            localMappedStatementCache.clear();
            log.info("本地MappedStatement缓存已清空");
        }
    }
}