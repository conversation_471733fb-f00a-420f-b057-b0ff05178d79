package com.lg.financecloud.admin.api.dto;

import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.common.core.constant.enums.DeviceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class SessionUser implements Serializable {
    /**
     * 用户ID
     */
    @Getter
    @Setter
    private Long id;
    @Getter
    @Setter
    private Integer tenantId;

    @Getter
    @Setter
    private Long deptId;

    @Getter
    @Setter
    private String deptName;
    @Getter
    @Setter
    private String username;
    @Getter
    @Setter
    private String nickname;
    @Getter
    @Setter
    private String phone;

    @Getter
    @Setter
    private String token;

    @Getter
    @Setter
    private String avatar;
    
    /**
     * 创建时间/登录时间
     */
    @Getter
    @Setter
    private Date createTime;
    
    /**
     * 设备类型
     */
    @Getter
    @Setter
    private String deviceType;

    /**
     * 权限标识集合
     */
    @Getter
    @Setter
    private String[] permissions;

    /**
     * 角色集合
     */
    @Getter
    @Setter
    private String[] roles;
    @Getter
    @Setter
    private List<RoleInfoVo> roleInfoList;

    @Getter
    @Setter
    private String lang;



    public Boolean isAdmin(){
        if(roleInfoList==null||roleInfoList.size()==0){
            return false;
        }
        for(RoleInfoVo roleInfoVo:roleInfoList){
            if(roleInfoVo.getRoleCode().equals("ROLE_ADMIN")){
                return true;
            }
        }
        return false;
    }

    public Boolean isSuperAdmin(){
        return this.id.equals(123123);
    }

    /**
     * 用户关联的员工信息
     */
    @Getter
    @Setter
    private StaffInfo staffInfo;

    /**
     * 是否为开发模式模拟用户
     */
    @Getter
    @Setter
    private boolean mockUser = false;

    public Integer[] getRoleIds(){

        if(roles!=null&& roles.length>0){
            Integer [] roleIds =new Integer[roles.length];
            for(int i=0;i< roles.length;i++){
                roleIds[i] =Integer.valueOf( roles[i].split(StrUtil.COLON)[1]);
            }
            return roleIds;
        }
        return null;
    }

    public String[] getRoleCodes(){
        if(roles!=null&& roles.length>0){
            String [] roleCodes =new String[roles.length];
            for(int i=0;i< roles.length;i++){
                roleCodes[i] =  roles[i].split(StrUtil.COLON)[0] ;
            }
            return roleCodes;
        }
        return null;
    }
}
