package com.lg.financecloud.admin.api.dto;

import com.lg.financecloud.admin.api.entity.BaseNotice;
import com.lg.financecloud.admin.api.entity.BaseNoticePublishRoute;
import com.lg.financecloud.admin.api.entity.BaseNoticePublishScope;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BaseNoticeDto extends BaseNotice {


    private Boolean delByTaskId=false;

    private Long id;

    private String typeName;

    private String sendType;// 0立即发送 1定时发送

    private String taskIdentity;

    private String dataSources;

    /**
     * 发送渠道
     */
    private List<BaseNoticePublishRoute> publishRouteList = new ArrayList<>();
    /**
     * 发送对象
     */
    private List<BaseNoticePublishScope> publishScopeList = new ArrayList<>();

    public  void addPublishRoute(BaseNoticePublishRoute route){
        publishRouteList.add(route);
    }
    public  void addPublishRoute(List<BaseNoticePublishRoute> routes){
        publishRouteList.addAll(routes);
    }

    public  void addPublishScope(BaseNoticePublishScope scope){
        publishScopeList.add(scope);
    }

    public  void addPublishScope(List<BaseNoticePublishScope> scopes){
        publishScopeList.addAll(scopes);
    }


}
