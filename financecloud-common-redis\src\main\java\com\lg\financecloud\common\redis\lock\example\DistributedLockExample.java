package com.lg.financecloud.common.redis.lock.example;

import com.lg.financecloud.common.redis.lock.DistributedLockManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分布式锁使用示例
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DistributedLockExample {
    
    @Autowired
    private DistributedLockManager lockManager;
    
    /**
     * 示例1: 简单的锁使用
     */
    public void simpleExample() {
        String lockKey = "user:123:update";
        
        // 使用默认配置执行带锁的操作
        String result = lockManager.executeWithLock(lockKey, () -> {
            // 业务逻辑
            log.info("执行用户更新操作");
            return "更新成功";
        });
        
        log.info("操作结果: {}", result);
    }
    
    /**
     * 示例2: 自定义超时时间
     */
    public void customTimeoutExample() {
        String lockKey = "order:456:process";
        long waitTime = 2000L;  // 等待2秒
        long leaseTime = 10000L; // 持有锁10秒
        
        lockManager.executeWithLock(lockKey, waitTime, leaseTime, () -> {
            log.info("执行订单处理操作");
            // 模拟耗时操作
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * 示例3: 检查锁状态
     */
    public void checkLockStatusExample() {
        String lockKey = "resource:789:access";
        
        // 检查锁是否被持有
        if (lockManager.isLocked(lockKey)) {
            log.info("资源正在被其他线程使用");
            return;
        }
        
        // 执行操作
        lockManager.executeWithLock(lockKey, () -> {
            log.info("访问资源");
            
            // 在锁内检查是否被当前线程持有
            if (lockManager.isLockedByCurrentThread(lockKey)) {
                log.info("确认锁被当前线程持有");
            }
        });
    }
    
    /**
     * 示例4: 处理锁获取失败的情况
     */
    public void handleLockFailureExample() {
        String lockKey = "critical:resource";
        
        try {
            lockManager.executeWithLock(lockKey, 100L, 5000L, () -> {
                log.info("执行关键操作");
            });
        } catch (Exception e) {
            log.error("获取锁失败或执行过程中出错: {}", e.getMessage());
            // 处理失败情况
            handleFailure();
        }
    }
    
    private void handleFailure() {
        log.info("执行失败处理逻辑");
    }
}
