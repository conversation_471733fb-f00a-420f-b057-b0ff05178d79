# 低代码平台菜单注册API文档

## 📋 概述

低代码平台提供了完整的菜单注册API，支持将低代码页面自动注册到cloudx-ui系统的菜单管理中，实现系统间的无缝集成。

## 🔧 API接口列表

### 1. 注册单个菜单

**接口地址**: `POST /api/lc/menu-registration/register`

**请求参数**:
```json
{
  "name": "页面名称",
  "code": "lowcode_page_demo",
  "icon": "el-icon-document",
  "path": "/lowcode/runtime/page123",
  "url": "/lowcode/runtime/page123",
  "type": "menu",
  "serviceType": "lowcode",
  "openType": "iframe",
  "permission": "lowcode:page:demo",
  "parentCode": "lowcode_pages",
  "sortOrder": 999,
  "status": "ACTIVE",
  "description": "演示页面",
  "meta": {
    "lowcodePage": true,
    "pageId": "page123",
    "pageCode": "demo",
    "appId": "app001"
  }
}
```

**响应结果**:
```json
{
  "success": true,
  "message": "菜单注册成功",
  "data": {
    "menuId": "menu123",
    "registrationTime": "2024-01-01T10:00:00"
  }
}
```

### 2. 批量注册菜单

**接口地址**: `POST /api/lc/menu-registration/batch-register`

**请求参数**:
```json
{
  "menus": [
    {
      "name": "用户管理页面",
      "code": "lowcode_page_user_mgmt",
      "icon": "el-icon-user",
      "url": "/lowcode/runtime/user-mgmt",
      "type": "menu",
      "serviceType": "lowcode",
      "openType": "iframe",
      "permission": "lowcode:page:user_mgmt",
      "parentCode": "lowcode_pages",
      "sortOrder": 1
    },
    {
      "name": "订单管理页面",
      "code": "lowcode_page_order_mgmt",
      "icon": "el-icon-shopping-cart-2",
      "url": "/lowcode/runtime/order-mgmt",
      "type": "menu",
      "serviceType": "lowcode",
      "openType": "iframe",
      "permission": "lowcode:page:order_mgmt",
      "parentCode": "lowcode_pages",
      "sortOrder": 2
    }
  ],
  "source": "lowcode-platform",
  "override": true,
  "targetSystem": "cloudx-ui"
}
```

### 3. 注册页面菜单

**接口地址**: `POST /api/lc/menu-registration/register-page`

**请求参数**:
```json
{
  "pageId": "page123",
  "pageCode": "demo_page",
  "pageName": "演示页面",
  "appId": "app001",
  "appCode": "demo_app",
  "icon": "el-icon-document",
  "permission": "lowcode:page:demo_page",
  "parentCode": "lowcode_pages",
  "sortOrder": 999,
  "openType": "iframe",
  "enabled": true,
  "description": "这是一个演示页面"
}
```

### 4. 同步已发布页面菜单

**接口地址**: `POST /api/lc/menu-registration/sync-published-pages`

**请求参数**: 无

**响应结果**:
```json
{
  "success": true,
  "message": "页面菜单同步完成",
  "syncCount": 15,
  "successCount": 14,
  "failCount": 1,
  "details": [
    {
      "success": true,
      "message": "菜单注册成功",
      "data": {...}
    }
  ]
}
```

### 5. 移除菜单

**接口地址**: `DELETE /api/lc/menu-registration/remove/{menuCode}`

**路径参数**:
- `menuCode`: 菜单编码

**响应结果**:
```json
{
  "success": true,
  "message": "菜单移除成功"
}
```

### 6. 批量移除菜单

**接口地址**: `DELETE /api/lc/menu-registration/batch-remove`

**请求参数**:
```json
["lowcode_page_demo1", "lowcode_page_demo2", "lowcode_page_demo3"]
```

### 7. 清理低代码平台菜单

**接口地址**: `DELETE /api/lc/menu-registration/cleanup`

**请求参数**: 无

**响应结果**:
```json
{
  "success": true,
  "message": "低代码菜单清理成功"
}
```

### 8. 获取菜单注册状态

**接口地址**: `GET /api/lc/menu-registration/status`

**响应结果**:
```json
{
  "success": true,
  "targetSystemConnected": true,
  "registeredMenuCount": 25,
  "publishedPageCount": 30,
  "autoSyncEnabled": true,
  "lastSyncTime": "2024-01-01T10:00:00"
}
```

### 9. 获取已注册菜单列表

**接口地址**: `GET /api/lc/menu-registration/registered-menus`

**响应结果**:
```json
[
  {
    "id": "menu123",
    "name": "演示页面",
    "code": "lowcode_page_demo",
    "icon": "el-icon-document",
    "url": "/lowcode/runtime/page123",
    "status": "ACTIVE",
    "registrationTime": "2024-01-01T10:00:00"
  }
]
```

### 10. 更新菜单同步配置

**接口地址**: `PUT /api/lc/menu-registration/sync-config`

**请求参数**:
```json
{
  "autoSync": true,
  "syncInterval": 30,
  "targetApiUrl": "http://localhost:8080",
  "authToken": "your-auth-token",
  "syncScope": "published",
  "syncPermissions": true
}
```

### 11. 获取菜单同步配置

**接口地址**: `GET /api/lc/menu-registration/sync-config`

**响应结果**:
```json
{
  "autoSync": true,
  "syncInterval": 30,
  "targetApiUrl": "http://localhost:8080",
  "authToken": "your-auth-token",
  "syncScope": "published",
  "syncPermissions": true
}
```

### 12. 手动触发菜单同步

**接口地址**: `POST /api/lc/menu-registration/trigger-sync`

**请求参数**: 无

### 13. 检查目标系统连接状态

**接口地址**: `GET /api/lc/menu-registration/check-connection`

**响应结果**:
```json
{
  "connected": true,
  "responseTime": 1640995200000,
  "statusCode": 200
}
```

### 14. 预览菜单注册效果

**接口地址**: `POST /api/lc/menu-registration/preview`

**请求参数**: (同注册单个菜单)

**响应结果**:
```json
{
  "success": true,
  "preview": {
    "name": "页面名称",
    "code": "lowcode_page_demo",
    "url": "/lowcode/runtime/page123",
    "source": "lowcode-platform"
  }
}
```

### 15. 验证菜单配置

**接口地址**: `POST /api/lc/menu-registration/validate`

**请求参数**: (同注册单个菜单)

**响应结果**:
```json
{
  "valid": true,
  "errors": []
}
```

## 🔄 自动同步机制

### 页面发布时自动注册菜单

当页面在低代码平台中发布时，系统会自动：

1. 监听页面发布事件
2. 构建菜单注册信息
3. 调用菜单注册API
4. 记录注册结果

### 定时同步任务

系统提供定时同步任务，每30分钟自动：

1. 检查目标系统连接状态
2. 获取已发布页面列表
3. 同步菜单到目标系统
4. 记录同步日志

## ⚙️ 配置说明

### 环境变量配置

```bash
# 目标系统URL
LOWCODE_TARGET_SYSTEM_URL=http://localhost:8080

# 认证Token
LOWCODE_AUTH_TOKEN=your-auth-token

# 是否启用自动同步
LOWCODE_AUTO_SYNC=true

# 同步间隔（分钟）
LOWCODE_SYNC_INTERVAL=30
```

### 应用配置文件

```yaml
financecloud:
  lowcode:
    menu:
      target-system-url: http://localhost:8080
      auth-token: your-auth-token
      auto-sync: true
      sync-interval: 30
      connection-timeout: 30000
      request-timeout: 60000
      retry-count: 3
      retry-interval: 5000
      sync-permissions: true
      default-parent-code: lowcode_pages
      menu-code-prefix: lowcode_page_
      cache-enabled: true
      cache-ttl: 3600
```

## 🚨 错误处理

### 常见错误码

- `MENU_001`: 菜单名称不能为空
- `MENU_002`: 菜单编码不能为空
- `MENU_003`: 菜单编码已存在
- `MENU_004`: 目标系统连接失败
- `MENU_005`: 权限验证失败
- `MENU_006`: 菜单注册失败

### 错误响应格式

```json
{
  "success": false,
  "message": "菜单注册失败: 菜单编码已存在",
  "errorCode": "MENU_003",
  "timestamp": "2024-01-01T10:00:00"
}
```

## 📝 使用示例

### JavaScript调用示例

```javascript
// 注册单个菜单
const registerMenu = async (menuData) => {
  try {
    const response = await fetch('/api/lc/menu-registration/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify(menuData)
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('菜单注册成功:', result.data);
    } else {
      console.error('菜单注册失败:', result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};

// 同步已发布页面菜单
const syncPublishedPages = async () => {
  try {
    const response = await fetch('/api/lc/menu-registration/sync-published-pages', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });
    
    const result = await response.json();
    console.log('同步结果:', result);
  } catch (error) {
    console.error('同步失败:', error);
  }
};
```

### Java调用示例

```java
@Autowired
private MenuRegistrationService menuRegistrationService;

// 注册页面菜单
public void registerPageMenu(String pageId) {
    PageMenuRegistrationDTO pageMenuDTO = new PageMenuRegistrationDTO();
    pageMenuDTO.setPageId(pageId);
    pageMenuDTO.setPageCode("demo_page");
    pageMenuDTO.setPageName("演示页面");
    // ... 设置其他属性
    
    Map<String, Object> result = menuRegistrationService.registerPageMenu(pageMenuDTO);
    if ((Boolean) result.get("success")) {
        log.info("页面菜单注册成功");
    } else {
        log.error("页面菜单注册失败: {}", result.get("message"));
    }
}
```

## 📞 技术支持

如有问题或建议，请联系开发团队：
- API文档: [Swagger UI地址]
- 技术支持: [技术支持邮箱]
- 问题反馈: [GitHub Issues地址]
