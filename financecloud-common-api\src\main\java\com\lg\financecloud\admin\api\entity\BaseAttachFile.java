/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 附件管理
 *
 * <AUTHOR>
 * @date 2021-07-16 11:21:45
 */
@Data
@TableName("base_attach_file")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "附件管理")
public class BaseAttachFile extends Model<BaseAttachFile> {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id",type = IdType.INPUT)
    @ApiModelProperty(value="编号")
    private Long id;
    /**
     * 附件类型
     */
    @ApiModelProperty(value="附件类型")
    private Long groupId;
    /**
     * 文件名称
     */
    @ApiModelProperty(value="文件名称")
    private String fileName;
    /**
     * 原始文件名称
     */
    @ApiModelProperty(value="原始文件名称")
    private String orignFileName;
    /**
     * 文件大小
     */
    @ApiModelProperty(value="文件大小")
    private Long fileSize;

    /**
     *
     */
    @ApiModelProperty(value="")
    private String useFlag;

    @ApiModelProperty(value="")
    private Integer sort;

    /**
     *
     */
    @ApiModelProperty(value="")
    private LocalDateTime createTime;


    /**
     * 返回的文件全路径
     */
    @TableField(exist = false)
    @ApiModelProperty(value="返回的文件全路径")
    private String fileUrl;

    /**
     * 返回的文件全路径
     */
    @TableField(exist = false)
    @ApiModelProperty(value="相对地址")
    private String relativePath;

    @TableField(exist = false)
    private String isPub;
    /**
     *
     */
    @ApiModelProperty(value="")
    @TableLogic
    private String delFlag;


}
