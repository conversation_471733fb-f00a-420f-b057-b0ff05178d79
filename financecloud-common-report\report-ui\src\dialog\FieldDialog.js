/**
 * 字段配置对话框
 * Created by UReport Team on 2024-01-10.
 */
import {alert} from '../MsgBox.js';

export default class FieldDialog {
    constructor() {
        this.dialog = $(`
            <div class="modal fade" role="dialog" aria-hidden="true" style="z-index: 10000">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                                &times;
                            </button>
                            <h4 class="modal-title">字段配置</h4>
                        </div>
                        <div class="modal-body"></div>
                        <div class="modal-footer"></div>
                    </div>
                </div>
            </div>
        `);
        
        const body = this.dialog.find('.modal-body');
        const footer = this.dialog.find('.modal-footer');
        this.init(body, footer);
        $('body').append(this.dialog);
    }
    
    init(body, footer) {
        // 字段名
        const nameRow = $(`
            <div class="row" style="margin-bottom: 10px;">
                <div class="col-md-3" style="text-align:right;margin-top:5px">字段名：</div>
                <div class="col-md-9">
                    <input type="text" class="form-control" placeholder="请输入字段名">
                </div>
            </div>
        `);
        this.nameEditor = nameRow.find('input');
        body.append(nameRow);
        
        // 显示名
        const displayNameRow = $(`
            <div class="row" style="margin-bottom: 10px;">
                <div class="col-md-3" style="text-align:right;margin-top:5px">显示名：</div>
                <div class="col-md-9">
                    <input type="text" class="form-control" placeholder="请输入显示名">
                </div>
            </div>
        `);
        this.displayNameEditor = displayNameRow.find('input');
        body.append(displayNameRow);
        
        // 数据类型
        const typeRow = $(`
            <div class="row" style="margin-bottom: 10px;">
                <div class="col-md-3" style="text-align:right;margin-top:5px">数据类型：</div>
                <div class="col-md-9">
                    <select class="form-control">
                        <option value="String">String</option>
                        <option value="Integer">Integer</option>
                        <option value="Float">Float</option>
                        <option value="Double">Double</option>
                        <option value="Boolean">Boolean</option>
                        <option value="Date">Date</option>
                        <option value="BigDecimal">BigDecimal</option>
                    </select>
                </div>
            </div>
        `);
        this.typeEditor = typeRow.find('select');
        body.append(typeRow);
        
        // 按钮
        const cancelButton = $('<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>');
        const saveButton = $('<button type="button" class="btn btn-primary">保存</button>');
        
        footer.append(cancelButton);
        footer.append(saveButton);
        
        saveButton.click(() => {
            this.save();
        });
    }
    
    save() {
        const name = this.nameEditor.val().trim();
        const displayName = this.displayNameEditor.val().trim();
        const type = this.typeEditor.val();
        
        if (!name) {
            alert('请输入字段名');
            return;
        }
        
        if (this.onSave) {
            this.onSave.call(this, name, displayName, type);
        }
        
        this.dialog.modal('hide');
    }
    
    show(onSave, field) {
        this.onSave = onSave;
        
        if (field) {
            // 编辑模式
            this.nameEditor.val(field.name || '');
            this.displayNameEditor.val(field.displayName || '');
            this.typeEditor.val(field.type || 'String');
        } else {
            // 新增模式
            this.nameEditor.val('');
            this.displayNameEditor.val('');
            this.typeEditor.val('String');
        }
        
        this.dialog.modal('show');
    }
}
