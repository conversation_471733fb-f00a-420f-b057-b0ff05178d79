package com.lg.financecloud.common.ai.exception;

/**
 * AI模型未找到异常
 * 当请求的AI模型不存在或未正确配置时抛出此异常
 */
public class AIModelNotFoundException extends AIServiceException {
    private static final String ERROR_CODE = "MODEL_NOT_FOUND";

    /**
     * 构造函数
     * @param modelType 模型类型
     */
    public AIModelNotFoundException(String modelType) {
        super(ERROR_CODE, String.format("AI model type '%s' not found or not properly configured", modelType));
    }

    /**
     * 构造函数
     * @param modelType 模型类型
     * @param modelName 模型名称
     */
    public AIModelNotFoundException(String modelType, String modelName) {
        super(ERROR_CODE, String.format("AI model '%s' of type '%s' not found or not properly configured", modelName, modelType));
    }
}