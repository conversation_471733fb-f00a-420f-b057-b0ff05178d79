# 低代码平台前端API接口文档

## 📋 概述

本文档详细描述了低代码平台前端 `lowcode-ui` 项目所需的后端API接口，包括用户认证、用户管理、菜单管理、角色管理、租户管理、系统参数管理和字典管理等核心功能模块。

## 🔐 用户认证API

### 基础路径
```
Base URL: http://localhost:8080
```

### 1. 用户登录
```http
POST /login
Content-Type: application/json

Request Body:
{
  "username": "admin",
  "password": "123456",
  "captcha": "1234",
  "uuid": "captcha-uuid"
}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 7200,
    "refresh_token": "refresh_token_value",
    "user_info": {
      "userId": "1",
      "username": "admin",
      "nickname": "管理员",
      "avatar": "avatar_url",
      "roles": ["admin"],
      "permissions": ["*:*:*"]
    }
  }
}
```

### 2. 用户登出
```http
DELETE /logout
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "退出成功",
  "data": null
}
```

### 3. 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

Request Body:
{
  "refresh_token": "refresh_token_value"
}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "access_token": "new_access_token",
    "expires_in": 7200
  }
}
```

### 4. 获取用户信息
```http
GET /user/info
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": "1",
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "avatar_url",
    "roles": ["admin"],
    "permissions": ["*:*:*"],
    "tenantId": "1"
  }
}
```

## 👥 用户管理API

### 基础路径
```
Base URL: http://localhost:8080/user
```

### 1. 分页查询用户列表
```http
GET /page?current=1&size=20&username=admin&status=0
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "userId": "1",
        "username": "admin",
        "nickname": "管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "avatar_url",
        "status": "0",
        "createTime": "2024-01-01 10:00:00",
        "roles": [
          {
            "roleId": "1",
            "roleName": "超级管理员",
            "roleCode": "admin"
          }
        ]
      }
    ],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  }
}
```

### 2. 新增用户
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "username": "testuser",
  "nickname": "测试用户",
  "password": "123456",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "status": "0",
  "roleIds": ["2", "3"],
  "deptId": "1"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 3. 更新用户
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "userId": "2",
  "username": "testuser",
  "nickname": "测试用户更新",
  "email": "<EMAIL>",
  "phone": "13800138002",
  "status": "0",
  "roleIds": ["2"],
  "deptId": "1"
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 4. 删除用户
```http
DELETE /{userId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 5. 重置用户密码
```http
PUT /{userId}/password/reset
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "newPassword": "123456"
}

Response:
{
  "code": 200,
  "msg": "密码重置成功",
  "data": true
}
```

## 📋 菜单管理API

### 基础路径
```
Base URL: http://localhost:8080/menu
```

### 1. 获取菜单树
```http
GET /tree
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "menuId": "1",
      "menuName": "系统管理",
      "menuCode": "system",
      "parentId": "0",
      "path": "/system",
      "component": "Layout",
      "menuType": "M",
      "icon": "system",
      "sort": 1,
      "visible": true,
      "status": "0",
      "children": [
        {
          "menuId": "2",
          "menuName": "用户管理",
          "menuCode": "system:user",
          "parentId": "1",
          "path": "user",
          "component": "system/user/index",
          "menuType": "C",
          "icon": "user",
          "sort": 1,
          "visible": true,
          "status": "0",
          "perms": "system:user:list"
        }
      ]
    }
  ]
}
```

### 2. 新增菜单
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "menuName": "测试菜单",
  "menuCode": "test:menu",
  "parentId": "1",
  "path": "test",
  "component": "test/index",
  "menuType": "C",
  "icon": "test",
  "sort": 99,
  "visible": true,
  "status": "0",
  "perms": "test:menu:list"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 3. 更新菜单
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "menuId": "10",
  "menuName": "测试菜单更新",
  "menuCode": "test:menu:updated",
  "parentId": "1",
  "path": "test-updated",
  "component": "test/updated/index",
  "menuType": "C",
  "icon": "test-updated",
  "sort": 98,
  "visible": true,
  "status": "0",
  "perms": "test:menu:updated:list"
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 4. 删除菜单
```http
DELETE /{menuId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 5. 获取用户菜单权限
```http
GET /user/menus
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "menuId": "1",
      "menuName": "系统管理",
      "path": "/system",
      "component": "Layout",
      "redirect": "/system/user",
      "meta": {
        "title": "系统管理",
        "icon": "system",
        "noCache": false
      },
      "children": [...]
    }
  ]
}
```

## 👑 角色管理API

### 基础路径
```
Base URL: http://localhost:8080/role
```

### 1. 分页查询角色列表
```http
GET /page?current=1&size=20&roleName=管理员
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "roleId": "1",
        "roleName": "超级管理员",
        "roleCode": "admin",
        "roleDesc": "超级管理员角色",
        "status": "0",
        "createTime": "2024-01-01 10:00:00",
        "menuIds": ["1", "2", "3"],
        "userCount": 1
      }
    ],
    "total": 10,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取所有角色列表
```http
GET /list
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "roleId": "1",
      "roleName": "超级管理员",
      "roleCode": "admin"
    },
    {
      "roleId": "2",
      "roleName": "普通用户",
      "roleCode": "user"
    }
  ]
}
```

### 3. 新增角色
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "roleName": "测试角色",
  "roleCode": "test_role",
  "roleDesc": "测试角色描述",
  "status": "0",
  "menuIds": ["1", "2", "3"]
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 4. 更新角色
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "roleId": "3",
  "roleName": "测试角色更新",
  "roleCode": "test_role_updated",
  "roleDesc": "测试角色描述更新",
  "status": "0",
  "menuIds": ["1", "2"]
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 5. 删除角色
```http
DELETE /{roleId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 6. 角色授权
```http
PUT /{roleId}/permissions
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "menuIds": ["1", "2", "3", "4"]
}

Response:
{
  "code": 200,
  "msg": "授权成功",
  "data": true
}
```

## 🏢 租户管理API

### 基础路径
```
Base URL: http://localhost:8080/tenant
```

### 1. 分页查询租户列表
```http
GET /page?current=1&size=20&name=测试租户
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "1",
        "name": "默认租户",
        "code": "default",
        "contactPerson": "管理员",
        "contactPhone": "13800138000",
        "contactEmail": "<EMAIL>",
        "status": "0",
        "expireTime": "2025-12-31 23:59:59",
        "createTime": "2024-01-01 10:00:00",
        "userCount": 10,
        "packageName": "标准版"
      }
    ],
    "total": 5,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 新增租户
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "name": "测试租户",
  "code": "test_tenant",
  "contactPerson": "张三",
  "contactPhone": "13800138001",
  "contactEmail": "<EMAIL>",
  "status": "0",
  "expireTime": "2025-12-31 23:59:59",
  "packageId": "1",
  "maxUsers": 100,
  "description": "测试租户描述"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 3. 更新租户
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "id": "2",
  "name": "测试租户更新",
  "code": "test_tenant_updated",
  "contactPerson": "李四",
  "contactPhone": "13800138002",
  "contactEmail": "<EMAIL>",
  "status": "0",
  "expireTime": "2026-12-31 23:59:59",
  "packageId": "2",
  "maxUsers": 200,
  "description": "测试租户描述更新"
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 4. 删除租户
```http
DELETE /{tenantId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 5. 获取租户详情
```http
GET /{tenantId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "1",
    "name": "默认租户",
    "code": "default",
    "contactPerson": "管理员",
    "contactPhone": "13800138000",
    "contactEmail": "<EMAIL>",
    "status": "0",
    "expireTime": "2025-12-31 23:59:59",
    "createTime": "2024-01-01 10:00:00",
    "packageInfo": {
      "packageId": "1",
      "packageName": "标准版",
      "maxUsers": 100,
      "features": ["用户管理", "角色管理", "菜单管理"]
    }
  }
}
```

## ⚙️ 系统参数管理API

### 基础路径
```
Base URL: http://localhost:8080/param
```

### 1. 分页查询参数列表
```http
GET /page?current=1&size=20&publicKey=system
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "publicId": "1",
        "publicName": "系统名称",
        "publicKey": "system.name",
        "publicValue": "低代码平台",
        "publicType": "string",
        "status": "0",
        "remark": "系统名称配置",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 20,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 根据Key获取参数值
```http
GET /publicValue/{publicKey}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": "低代码平台"
}
```

### 3. 新增参数
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "publicName": "测试参数",
  "publicKey": "test.param",
  "publicValue": "test_value",
  "publicType": "string",
  "status": "0",
  "remark": "测试参数说明"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 4. 更新参数
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "publicId": "2",
  "publicName": "测试参数更新",
  "publicKey": "test.param.updated",
  "publicValue": "test_value_updated",
  "publicType": "string",
  "status": "0",
  "remark": "测试参数说明更新"
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 5. 删除参数
```http
DELETE /{publicId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

## 📚 字典管理API

### 基础路径
```
Base URL: http://localhost:8080/dict
```

### 1. 分页查询字典列表
```http
GET /page?current=1&size=20&dictType=user_status
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "1",
        "dictType": "user_status",
        "dictName": "用户状态",
        "status": "0",
        "remark": "用户状态字典",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 10,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取字典项列表
```http
GET /items/{dictType}

Response:
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "dictCode": "0",
      "dictLabel": "正常",
      "dictValue": "0",
      "dictType": "user_status",
      "cssClass": "success",
      "listClass": "primary",
      "isDefault": "Y",
      "status": "0",
      "sort": 1
    },
    {
      "dictCode": "1",
      "dictLabel": "停用",
      "dictValue": "1",
      "dictType": "user_status",
      "cssClass": "danger",
      "listClass": "danger",
      "isDefault": "N",
      "status": "0",
      "sort": 2
    }
  ]
}
```

### 3. 新增字典
```http
POST /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "dictType": "test_type",
  "dictName": "测试字典",
  "status": "0",
  "remark": "测试字典说明"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 4. 新增字典项
```http
POST /item
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "dictLabel": "测试项",
  "dictValue": "test_value",
  "dictType": "test_type",
  "cssClass": "primary",
  "listClass": "primary",
  "isDefault": "N",
  "status": "0",
  "sort": 1,
  "remark": "测试字典项"
}

Response:
{
  "code": 200,
  "msg": "新增成功",
  "data": true
}
```

### 5. 更新字典
```http
PUT /
Authorization: Bearer {access_token}
Content-Type: application/json

Request Body:
{
  "id": "2",
  "dictType": "test_type_updated",
  "dictName": "测试字典更新",
  "status": "0",
  "remark": "测试字典说明更新"
}

Response:
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 6. 删除字典
```http
DELETE /{dictId}
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

## 🔧 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "操作失败：具体错误信息",
  "data": null
}
```

### 分页响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [], // 数据列表
    "total": 100,  // 总记录数
    "size": 20,    // 每页大小
    "current": 1,  // 当前页码
    "pages": 5     // 总页数
  }
}
```

## 🔒 权限说明

### 权限码格式
```
模块:功能:操作
例如：
- system:user:list   (用户列表查询)
- system:user:add    (用户新增)
- system:user:edit   (用户编辑)
- system:user:del    (用户删除)
- system:role:list   (角色列表查询)
- system:menu:list   (菜单列表查询)
```

### 请求头格式
```http
Authorization: Bearer {access_token}
Content-Type: application/json
```

---

**注意事项：**
1. 所有需要认证的接口都必须在请求头中携带有效的 `Authorization` token
2. 分页参数：`current` 表示当前页码（从1开始），`size` 表示每页大小
3. 状态字段：通常 `0` 表示正常/启用，`1` 表示停用/禁用
4. 时间格式：统一使用 `yyyy-MM-dd HH:mm:ss` 格式
5. 所有删除操作都是逻辑删除，不会物理删除数据
