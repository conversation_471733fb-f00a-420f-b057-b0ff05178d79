package com.lg.dao.core.query;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.core.condition.ConditionStrategy;
import com.lg.dao.core.condition.ConditionStrategyUtils;
import com.lg.dao.core.func.LFunction;
import com.lg.dao.core.util.LambdaUtils;

import java.io.Serializable;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Lambda查询构建器
 */
public class LambdaQuery<T> {
    private final GenericDao<T, ?> dao;
    private final Class<T> entityClass;
    private final QueryBuilder<T> queryBuilder;

    private final EntityInfo entityInfo;

    public LambdaQuery(GenericDao<T, ?> dao, Class<T> entityClass) {
        this.dao = dao;
        this.entityClass = entityClass;
        entityInfo = dao.getEntityInfo(entityClass);
        this.queryBuilder = new QueryBuilder<>(dao, entityClass,entityInfo);
    }

    /**
     * 等于条件
     */
    public <R> LambdaQuery<T> eq(LFunction<T, R> column, Object value) {
        queryBuilder.eq(getColumnName(column), value);
        return this;
    }

    public <R> LambdaQuery<T> isNotNull(LFunction<T, R> column) {
        queryBuilder.isNotNull(getColumnName(column));
        return this;
    }

    /**
     * 不等于条件
     */
    public <R> LambdaQuery<T> ne(LFunction<T, R> column, Object value) {
        queryBuilder.ne(getColumnName(column), value);
        return this;
    }

    /**
     * 大于条件
     */
    public <R> LambdaQuery<T> gt(LFunction<T, R> column, Object value) {
        queryBuilder.gt(getColumnName(column), value);
        return this;
    }

    /**
     * 大于等于条件
     */
    public <R> LambdaQuery<T> ge(LFunction<T, R> column, Object value) {
        queryBuilder.ge(getColumnName(column), value);
        return this;
    }

    /**
     * 小于条件
     */
    public <R> LambdaQuery<T> lt(LFunction<T, R> column, Object value) {
        queryBuilder.lt(getColumnName(column), value);
        return this;
    }

    /**
     * 小于等于条件
     */
    public <R> LambdaQuery<T> le(LFunction<T, R> column, Object value) {
        queryBuilder.le(getColumnName(column), value);
        return this;
    }

    /**
     * LIKE条件
     */
    public <R> LambdaQuery<T> like(LFunction<T, R> column, String value) {
        queryBuilder.like(getColumnName(column), value);
        return this;
    }

    /**
     * IN条件
     */
    public <R> LambdaQuery<T> in(LFunction<T, R> column, Object... values) {
        queryBuilder.in(getColumnName(column), values);
        return this;
    }

    /**
     * IN条件 - 支持List参数
     */
    public <R> LambdaQuery<T> in(LFunction<T, R> column, List<?> values) {
        if (values != null && !values.isEmpty()) {
            queryBuilder.in(getColumnName(column), values.toArray());
        }
        return this;
    }

    /**
     * 添加OR连接符
     * 用于连接两个条件，如 status = 'ACTIVE' OR status = 'PENDING'
     * @return 当前查询构建器
     */
    public LambdaQuery<T> or() {
        queryBuilder.or();
        return this;
    }

    /**
     * 添加AND连接符
     * 用于连接两个条件，如 status = 'ACTIVE' AND role = 'ADMIN'
     * @return 当前查询构建器
     */
    public LambdaQuery<T> and() {
        queryBuilder.and();
        return this;
    }

    /**
     * 嵌套AND条件分组
     * 用于创建 (condition1 AND condition2 AND...) 形式的查询条件
     * @param consumer 条件构建器消费函数
     * @return 当前查询构建器
     */
    public LambdaQuery<T> and(Consumer<LambdaQuery<T>> consumer) {
        queryBuilder.beginAndGroup();
        LambdaQuery<T> nestedQuery = new LambdaQuery<>(dao, entityClass);
        consumer.accept(nestedQuery);
        queryBuilder.appendNestedCondition(nestedQuery.queryBuilder);
        queryBuilder.endGroup();
        return this;
    }

    /**
     * 嵌套OR条件分组
     * 用于创建 (condition1 OR condition2 OR...) 形式的查询条件
     * @param consumer 条件构建器消费函数
     * @return 当前查询构建器
     */
    public LambdaQuery<T> or(Consumer<LambdaQuery<T>> consumer) {
        queryBuilder.beginOrGroup();
        LambdaQuery<T> nestedQuery = new LambdaQuery<>(dao, entityClass);
        consumer.accept(nestedQuery);
        queryBuilder.appendNestedCondition(nestedQuery.queryBuilder);
        queryBuilder.endGroup();
        return this;
    }

    /**
     * 排序
     */
    public <R> LambdaQuery<T> orderBy(LFunction<T, R> column) {
        queryBuilder.orderBy(getColumnName(column));
        return this;
    }

    /**
     * 降序排序
     */
    public <R> LambdaQuery<T> orderByDesc(LFunction<T, R> column) {
        queryBuilder.orderByDesc(getColumnName(column));
        return this;
    }
    /**
     * 降序排序
     */
    public <R> LambdaQuery<T> orderByAsc(LFunction<T, R> column) {
        queryBuilder.orderByAsc(getColumnName(column));
        return this;
    }

    /**
     * 执行查询返回列表
     */
    public List<T> list() {
        return queryBuilder.list();
    }

    /**
     * 执行查询返回单个结果
     */
    public T one() {
        return queryBuilder.one();
    }

    /**
     * 执行分页查询
     */
    public Page<T> page(int pageNum, int pageSize) {
        return queryBuilder.page(pageNum, pageSize);
    }
    public Page<T> page(Long pageNum, Long pageSize) {
        return queryBuilder.page(pageNum.intValue(), pageSize.intValue());
    }

    /**
     * 统计数量
     */
    public long count() {
        return queryBuilder.count();
    }

    /**
     * 获取列名
     */
    private <R> String getColumnName(LFunction<T, R> column) {
        String fieldName = LambdaUtils.getFieldName(column);
        return entityInfo.getColumnName(fieldName);
    }

    // ==================== 支持条件策略的方法 ====================

    /**
     * 根据条件策略动态添加等于条件
     */
    public <R> LambdaQuery<T> eq(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return eq(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加不等于条件
     */
    public <R> LambdaQuery<T> ne(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return ne(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加大于条件
     */
    public <R> LambdaQuery<T> gt(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return gt(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加大于等于条件
     */
    public <R> LambdaQuery<T> ge(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return ge(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加小于条件
     */
    public <R> LambdaQuery<T> lt(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return lt(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加小于等于条件
     */
    public <R> LambdaQuery<T> le(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return le(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加模糊查询条件
     */
    public <R> LambdaQuery<T> like(LFunction<T, R> getter, String value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return like(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加IN条件
     */
    public <R> LambdaQuery<T> in(LFunction<T, R> getter, List<R> values, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(values, strategy)) {
            return in(getter, values.toArray());
        }
        return this;
    }

    // ==================== 便捷方法：使用默认策略 ====================

    /**
     * 使用IS_NOT_EMPTY策略的等于条件（常用）
     */
    public <R> LambdaQuery<T> eqIfNotEmpty(LFunction<T, R> getter, R value) {
        return eq(getter, value, ConditionStrategy.IS_NOT_EMPTY);
    }

    /**
     * 使用IS_NOT_EMPTY策略的模糊查询条件（常用）
     */
    public <R> LambdaQuery<T> likeIfNotEmpty(LFunction<T, R> getter, String value) {
        return like(getter, value, ConditionStrategy.IS_NOT_EMPTY);
    }

    /**
     * 使用IS_NOT_NULL策略的等于条件（常用）
     */
    public <R> LambdaQuery<T> eqIfNotNull(LFunction<T, R> getter, R value) {
        return eq(getter, value, ConditionStrategy.IS_NOT_NULL);
    }

    /**
     * 使用IS_NOT_EMPTY策略的IN条件（常用）
     */
    public <R> LambdaQuery<T> inIfNotEmpty(LFunction<T, R> getter, List<R> values) {
        return in(getter, values, ConditionStrategy.IS_NOT_NULL);
    }

    /**
     * 使用IS_NOT_EMPTY策略的IN条件（常用）
     */
    @SafeVarargs
    public final <R> LambdaQuery<T> inIfNotEmpty(LFunction<T, R> getter, R... values) {
        if (values != null && values.length > 0) {
            return in(getter, values);
        }
        return this;
    }

    /**
     * 限制返回数量
     */
    public LambdaQuery<T> limit(int count) {
        queryBuilder.limit(count);
        return this;
    }

    /**
     * 根据实体对象的非空属性构建等值条件
     * @param entity 实体对象
     * @param strategy 条件策略
     * @return 当前查询构建器
     */
    public LambdaQuery<T> allEq(T entity, ConditionStrategy strategy) {
        if (entity == null) {
            return this;
        }

        try {
            java.lang.reflect.Field[] fields = entity.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(entity);
                if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
                    String columnName = entityInfo.getColumnName(field.getName());
                    if (columnName != null) {
                        queryBuilder.eq(columnName, value);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to build allEq conditions", e);
        }

        return this;
    }

    /**
     * 根据实体对象的非空属性构建等值条件（使用默认策略）
     * @param entity 实体对象
     * @return 当前查询构建器
     */
    public LambdaQuery<T> allEq(T entity) {
        return allEq(entity, ConditionStrategy.IS_NOT_NULL);
    }
}