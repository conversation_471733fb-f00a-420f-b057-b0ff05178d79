package com.lg.dao.core.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import com.lg.dao.core.BaseDao;
import com.lg.dao.helper.DaoHelper;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 元数据服务
 * 用于加载和管理表元数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MetadataService {

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired(required = false)
    private UnifiedCacheManager unifiedCacheManager;

    /**
     * 表元数据缓存
     */
    private final Map<String, TableMetadata> tableMetadataCache = new ConcurrentHashMap<>();

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
    }
    

    
    /**
     * 获取表元数据
     *
     * @param tableCode 表编码
     * @return 表元数据
     */
    public TableMetadata getTableMetadata(String tableCode) {
        GenericDao<TableMetadata, Long> dao = DaoHelper.dao(TableMetadata.class);
        // 使用统一缓存管理器
        if (unifiedCacheManager != null) {
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.METADATA_CACHE, "table:" + tableCode, () -> {

                // 本地缓存中没有，从数据库加载
                try {
                    TableMetadata      tableMetadata = dao.lambdaQuery()
                        .eq(TableMetadata::getTableCode, tableCode)
                        .eq(TableMetadata::getDelFlag, 0)
                        .one();

                    return tableMetadata;
                } catch (Exception e) {
                    log.error("获取表元数据失败: {}", tableCode, e);
                    return null;
                }
            });
        }
           return  dao.lambdaQuery()
                .eq(TableMetadata::getTableCode, tableCode)
                .eq(TableMetadata::getDelFlag, 0)
                .one();

    }
    
    /**
     * 刷新表元数据
     *
     * @param tableCode 表编码
     */
    public void refreshTableMetadata(String tableCode) {
        // 清除本地缓存
        tableMetadataCache.remove(tableCode);
        
        // 清除统一缓存
        if (unifiedCacheManager != null) {
            unifiedCacheManager.remove(UnifiedCacheManager.CacheType.METADATA_CACHE, "table:" + tableCode);
        }
        
        // 重新加载
        getTableMetadata(tableCode);
    }
    
    /**
     * 获取字段配置
     *
     * @param tableCode 表编码
     * @param fieldName 字段名
     * @return 字段配置
     */
    public TableMetadata.FieldConfig getFieldConfig(String tableCode, String fieldName) {
        TableMetadata tableMetadata = getTableMetadata(tableCode);
        if (tableMetadata == null) {
            return null;
        }
        Map<String, TableMetadata.FieldConfig> fieldMapping = tableMetadata.getFieldMapping();
        return fieldMapping.get(fieldName);
    }

}