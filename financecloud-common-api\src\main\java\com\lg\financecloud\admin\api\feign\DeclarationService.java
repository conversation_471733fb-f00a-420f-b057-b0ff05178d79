package com.lg.financecloud.admin.api.feign;


import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "declarationService", value = ServiceNameConstants.FINANCE_SERVICE)
public interface DeclarationService {

    @PostMapping("/declarationList/taxBureauRetrieval")
    public R taxBureauRetrieval(@RequestParam("json") String json,
                                @RequestParam("tenantId") Integer tenantId,
                                @RequestParam("accountId")  Long accountId,
                                @RequestParam("runParam")  String runParam);

    /**
     * 获取财务申报信息
     * @param period
     * @param type
     * @param tenantId
     * @param accountId
     * @return
     */
    @PostMapping("/declarationList/taxReturn")
    public R taxReturn(@RequestParam("period") Integer period, @RequestParam("type") String type,
                       @RequestParam("tenantId")Integer tenantId, @RequestParam("accountId")Long accountId);


}
