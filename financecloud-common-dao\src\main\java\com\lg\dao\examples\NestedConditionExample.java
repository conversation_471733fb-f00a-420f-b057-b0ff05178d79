package com.lg.dao.examples;

import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 嵌套条件分组查询示例
 */
//@Service
public class NestedConditionExample {

    /**
     * 用户实体类
     */
    public static class User {
        private Long id;
        private String userName;
        private String status;
        private String role;
        private String department;
        private String tenantId;
        private Integer age;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public String getDepartment() { return department; }
        public void setDepartment(String department) { this.department = department; }
        
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }

    // 使用DaoHelper创建DAO
    private final GenericDao<User, Long> userDao = DaoHelper.dao(User.class);

    /**
     * 简单的AND条件分组示例
     */
    public List<User> findUsersWithAndGroup(String tenantId) {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, tenantId)
            .and(wrapper -> wrapper
                .eq(User::getStatus, "ACTIVE")
                .or()
                .eq(User::getStatus, "PENDING")
            )
            .list();
    }

    /**
     * 简单的OR条件分组示例
     */
    public List<User> findUsersWithOrGroup(String tenantId) {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, tenantId)
            .or(wrapper -> wrapper
                .eq(User::getRole, "ADMIN")
                .eq(User::getDepartment, "技术部")
            )
            .list();
    }

    /**
     * 多层嵌套条件示例
     */
    public List<User> findUsersWithNestedGroups(String tenantId) {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, tenantId)
            .and(wrapper -> wrapper
                .eq(User::getStatus, "ACTIVE")
                .or(subWrapper -> subWrapper
                    .eq(User::getRole, "ADMIN")
                    .eq(User::getDepartment, "技术部")
                )
            )
            .list();
    }

    /**
     * 复杂条件组合示例
     */
    public List<User> findUsersWithComplexConditions(String tenantId, List<String> roles, Integer minAge) {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, tenantId)
            .and(wrapper -> wrapper
                .eq(User::getStatus, "ACTIVE")
                .and(subWrapper -> subWrapper
                    .in(User::getRole, roles.toArray())
                    .or()
                    .ge(User::getAge, minAge)
                )
            )
            .or(wrapper -> wrapper
                .eq(User::getRole, "SUPER_ADMIN")
            )
            .orderByDesc(User::getId)
            .list();
    }

    /**
     * 动态条件与嵌套分组结合示例
     */
    public List<User> findUsersWithDynamicConditions(String tenantId, String status, String role, String department) {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, tenantId)
            .and(wrapper -> {
                // 只有当status不为null时才添加此条件
                if (status != null) {
                    wrapper.eq(User::getStatus, status);
                }
                
                // 只有当role和department都不为null时才添加此OR条件组
                if (role != null && department != null) {
                    wrapper.or(subWrapper -> subWrapper
                        .eq(User::getRole, role)
                        .eq(User::getDepartment, department)
                    );
                }
            })
            .list();
    }
} 