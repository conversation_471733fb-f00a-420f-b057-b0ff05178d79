package com.lg.financecloud.oa.api.feign;

import cn.hutool.json.JSONObject;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(contextId = "workFlowService", value = ServiceNameConstants.WORKFLOW_SERVICE)
public interface WorkFlowService {


    @PostMapping("/workflow/startWorkflow")
    R startWorkflow(@RequestBody JSONObject jsonObject);

    @PostMapping("/instances/rollback")
    R rollback(@RequestBody JSONObject jsonObject);




}
