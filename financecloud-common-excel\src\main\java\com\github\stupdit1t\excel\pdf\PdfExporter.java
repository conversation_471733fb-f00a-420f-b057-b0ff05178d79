package com.github.stupdit1t.excel.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF导出器
 * 将Excel数据转换为PDF格式
 */
public class PdfExporter {

    private PdfExportConfig config;
    private com.itextpdf.text.Font chineseFont;
    private com.itextpdf.text.Font headerFont;

    public PdfExporter() {
        this(new PdfExportConfig());
    }

    public PdfExporter(PdfExportConfig config) {
        this.config = config;
        initializeFonts();
    }

    /**
     * 初始化字体
     */
    private void initializeFonts() {
        try {
            if (config.isEnableChineseFont()) {
                BaseFont baseFont;
                if (config.getCustomFontPath() != null) {
                    baseFont = BaseFont.createFont(config.getCustomFontPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                } else {
                    // 使用iText内置的中文字体
                    baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                }
                chineseFont = new com.itextpdf.text.Font(baseFont, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
                headerFont = new com.itextpdf.text.Font(baseFont, config.getFontSize(), com.itextpdf.text.Font.BOLD);
            } else {
                chineseFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
                headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.BOLD);
            }
        } catch (Exception e) {
            // 如果中文字体加载失败，使用默认字体
            chineseFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
            headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.BOLD);
        }
    }

    /**
     * 从Excel工作簿导出PDF
     *
     * @param workbook Excel工作簿
     * @param outputStream 输出流
     * @throws Exception 导出异常
     */
    public void exportFromWorkbook(Workbook workbook, OutputStream outputStream) throws Exception {
        Document document = createDocument();
        PdfWriter writer = PdfWriter.getInstance(document, outputStream);
        document.open();

        // 遍历所有Sheet
        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            if (sheetIndex > 0) {
                document.newPage(); // 新页面
            }
            exportSheet(document, sheet);
        }

        document.close();
        writer.close();
    }

    /**
     * 导出到文件路径
     *
     * @param workbook Excel工作簿
     * @param filePath 文件路径
     * @throws Exception 导出异常
     */
    public void exportFromWorkbook(Workbook workbook, String filePath) throws Exception {
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath)) {
            exportFromWorkbook(workbook, fos);
        }
    }

    /**
     * 导出到HTTP响应
     *
     * @param workbook Excel工作簿
     * @param response HTTP响应
     * @param fileName 文件名
     * @throws Exception 导出异常
     */
    public void exportFromWorkbook(Workbook workbook, HttpServletResponse response, String fileName) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        exportFromWorkbook(workbook, response.getOutputStream());
    }

    /**
     * 创建PDF文档
     */
    private Document createDocument() {
        Rectangle pageSize = getPageSize();
        if (config.getOrientation() == PdfExportConfig.PageOrientation.LANDSCAPE) {
            pageSize = pageSize.rotate();
        }

        return new Document(pageSize, 
            config.getMarginLeft(), 
            config.getMarginRight(), 
            config.getMarginTop(), 
            config.getMarginBottom());
    }

    /**
     * 获取页面大小
     */
    private Rectangle getPageSize() {
        switch (config.getPageSize()) {
            case A3: return PageSize.A3;
            case A5: return PageSize.A5;
            case LETTER: return PageSize.LETTER;
            case LEGAL: return PageSize.LEGAL;
            default: return PageSize.A4;
        }
    }

    /**
     * 导出单个Sheet
     */
    private void exportSheet(Document document, Sheet sheet) throws DocumentException {
        if (sheet.getPhysicalNumberOfRows() == 0) {
            return;
        }

        // 添加Sheet标题
        if (sheet.getSheetName() != null && !sheet.getSheetName().isEmpty()) {
            Paragraph title = new Paragraph(sheet.getSheetName(), headerFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(10f);
            document.add(title);
        }

        // 分析表格结构
        TableStructure tableStructure = analyzeTableStructure(sheet);
        
        // 创建PDF表格
        PdfPTable table = new PdfPTable(tableStructure.getColumnCount());
        table.setWidthPercentage(100);
        
        // 设置列宽
        if (tableStructure.getColumnWidths() != null) {
            table.setWidths(tableStructure.getColumnWidths());
        }

        // 添加表格内容
        addTableContent(table, sheet, tableStructure);
        
        document.add(table);
    }

    /**
     * 分析表格结构
     */
    private TableStructure analyzeTableStructure(Sheet sheet) {
        int maxColumns = 0;
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();

        // 找到最大列数
        for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row != null) {
                maxColumns = Math.max(maxColumns, row.getLastCellNum());
            }
        }

        // 计算列宽
        float[] columnWidths = new float[maxColumns];
        for (int col = 0; col < maxColumns; col++) {
            columnWidths[col] = sheet.getColumnWidth(col) / 256f; // POI列宽转换
        }

        return new TableStructure(maxColumns, columnWidths);
    }

    /**
     * 添加表格内容
     */
    private void addTableContent(PdfPTable table, Sheet sheet, TableStructure structure) {
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        boolean isFirstRow = true;

        for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                // 添加空行
                for (int col = 0; col < structure.getColumnCount(); col++) {
                    table.addCell(createCell("", chineseFont, false));
                }
                continue;
            }

            for (int col = 0; col < structure.getColumnCount(); col++) {
                Cell cell = row.getCell(col);
                String cellValue = getCellValue(cell);
                
                // 判断是否为表头行
                boolean isHeader = isFirstRow && isHeaderRow(row);
                com.itextpdf.text.Font font = isHeader ? headerFont : chineseFont;
                
                PdfPCell pdfCell = createCell(cellValue, font, isHeader);
                table.addCell(pdfCell);
            }
            
            if (isFirstRow) {
                isFirstRow = false;
            }
        }
    }

    /**
     * 判断是否为表头行
     */
    private boolean isHeaderRow(Row row) {
        // 简单判断：如果第一行的单元格有样式或者字体加粗，认为是表头
        for (Cell cell : row) {
            if (cell != null && cell.getCellStyle() != null) {
                org.apache.poi.ss.usermodel.Font font = cell.getSheet().getWorkbook().getFontAt(cell.getCellStyle().getFontIndex());
                if (font.getBold()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 创建PDF单元格
     */
    private PdfPCell createCell(String content, com.itextpdf.text.Font font, boolean isHeader) {
        PdfPCell cell = new PdfPCell(new Phrase(content, font));
        
        if (isHeader) {
            cell.setBackgroundColor(new BaseColor(config.getHeaderBackgroundColor()));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        } else {
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        }
        
        if (config.isShowGridLines()) {
            cell.setBorder(Rectangle.BOX);
        } else {
            cell.setBorder(Rectangle.NO_BORDER);
        }
        
        cell.setPadding(4f);
        return cell;
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 表格结构信息
     */
    private static class TableStructure {
        private int columnCount;
        private float[] columnWidths;

        public TableStructure(int columnCount, float[] columnWidths) {
            this.columnCount = columnCount;
            this.columnWidths = columnWidths;
        }

        public int getColumnCount() {
            return columnCount;
        }

        public float[] getColumnWidths() {
            return columnWidths;
        }
    }
}
