/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.data.tenant;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.lg.financecloud.admin.api.dto.SessionUser;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @date 2018/10/4 租户工具类
 */
@UtilityClass
public class TenantContextHolder {

    private final ThreadLocal<Integer> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();
    private final ThreadLocal<Long> CURRENT_LOCAL_USER = new TransmittableThreadLocal<>();
    private final ThreadLocal<Long> THREAD_LOCAL_ACCOUNT = new TransmittableThreadLocal<>();
    private final ThreadLocal<SessionUser> THREAD_LOCAL_SESSION_USER = new TransmittableThreadLocal<>();


    private final ThreadLocal<Integer> THREAD_LOCAL_RUNAS_TENANT = new TransmittableThreadLocal<>();

    /***
     * 当前用户的当前账套 缓存 租户:用户：current_account
     */
    public static final String CURRENT_ACCOUNT_CACHE_KEY="current_account_id:%s";

    /**
     * TTL 设置租户ID<br/>
     * <b>谨慎使用此方法,避免嵌套调用。尽量使用 {@code TenantBroker} </b>
     *
     * @param tenantId
     * @see TenantBroker
     */
    public void setTenantId(Integer tenantId) {
        THREAD_LOCAL_TENANT.set(tenantId);

    }

    /**
     * 获取TTL中的租户ID
     *
     * @return
     */
    public Integer getTenantId() {
        // 优先从 租户执行器中获取租户 再从用户 会话上取租户
        if(THREAD_LOCAL_RUNAS_TENANT.get()!=null){
            return THREAD_LOCAL_RUNAS_TENANT.get();
        }
        return THREAD_LOCAL_SESSION_USER.get()==null? THREAD_LOCAL_TENANT.get():THREAD_LOCAL_SESSION_USER.get().getTenantId() ;
    }

    public void clear() {
        THREAD_LOCAL_TENANT.remove();
    }

    public void clearAll() {
        THREAD_LOCAL_TENANT.remove();
        THREAD_LOCAL_ACCOUNT.remove();
        THREAD_LOCAL_SESSION_USER.remove();
        CURRENT_LOCAL_USER.remove();
        THREAD_LOCAL_RUNAS_TENANT.remove();

    }


    public void setAccountId(Long accountId) {
        THREAD_LOCAL_ACCOUNT.set(accountId);
    }
    public void setUserId(Long userId) {
        CURRENT_LOCAL_USER.set(userId);
    }


    public void setCurrentSessionUser(SessionUser sessionUser) {
        THREAD_LOCAL_SESSION_USER.set(sessionUser);
    }

    public SessionUser getCurrentSessionUser() {
       return THREAD_LOCAL_SESSION_USER.get();
    }
    /**
     * 获取TTL中的账套ID
     *
     * @return
     */
    public Long getAccountId() {
        return THREAD_LOCAL_ACCOUNT.get();
    }

    public void clearAccountId() {
        THREAD_LOCAL_ACCOUNT.remove();
    }

    /**
     * 获取TTL中的用户Id
     *
     * @return
     */
    public Long getUserId() {
        return THREAD_LOCAL_SESSION_USER.get()==null? CURRENT_LOCAL_USER.get():THREAD_LOCAL_SESSION_USER.get().getId() ;
    }

    public void clearUserId() {
        CURRENT_LOCAL_USER.remove();
    }

    public   void setThreadLocalRunasTenant(Integer tenant) {
          THREAD_LOCAL_RUNAS_TENANT.set(tenant);
    }

    public   void cleanThreadLocalRunasTenant() {
        THREAD_LOCAL_RUNAS_TENANT.remove();
    }
}
