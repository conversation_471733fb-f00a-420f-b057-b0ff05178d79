/**
 * 字段别名管理对话框
 * 用于设置字段的用户友好显示名称
 */
import {dialog} from '../MsgBox.js';

export default class FieldAliasDialog {
    constructor() {
        this.initDialog();
    }

    initDialog() {
        this.dialogContent = $(`
            <div class="field-alias-dialog">
                <div class="form-group">
                    <label>字段名称：</label>
                    <input type="text" class="form-control field-name" readonly style="background-color: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>显示名称：</label>
                    <input type="text" class="form-control display-name" placeholder="请输入用户友好的显示名称">
                    <small class="help-block">设置后将在数据源面板中显示此名称，便于用户理解字段含义</small>
                </div>
                <div class="form-group">
                    <label>预览效果：</label>
                    <div class="preview-area" style="border: 1px solid #ddd; padding: 8px; background: #f9f9f9; border-radius: 4px;">
                        <i class="ureport ureport-property"></i> 
                        <span class="preview-text">字段名称</span>
                    </div>
                </div>
            </div>
        `);

        // 绑定实时预览
        this.dialogContent.find('.display-name').on('input', () => {
            this.updatePreview();
        });
    }

    updatePreview() {
        const fieldName = this.dialogContent.find('.field-name').val();
        const displayName = this.dialogContent.find('.display-name').val();
        const previewText = displayName.trim() || fieldName;
        
        this.dialogContent.find('.preview-text').text(previewText);
    }

    show(field, callback) {
        this.field = field;
        this.callback = callback;

        // 填充当前值
        this.dialogContent.find('.field-name').val(field.name);
        this.dialogContent.find('.display-name').val(field.displayName || '');
        
        // 更新预览
        this.updatePreview();

        const _this = this;
        dialog('设置字段显示名称', this.dialogContent, function() {
            const displayName = _this.dialogContent.find('.display-name').val().trim();
            
            // 更新字段对象
            if (displayName) {
                field.displayName = displayName;
            } else {
                // 如果显示名称为空，删除displayName属性
                delete field.displayName;
            }
            
            if (_this.callback) {
                _this.callback(field);
            }
        });
    }
}
