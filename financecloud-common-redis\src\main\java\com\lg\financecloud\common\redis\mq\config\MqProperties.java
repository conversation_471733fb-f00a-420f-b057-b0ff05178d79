package com.lg.financecloud.common.redis.mq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * MQ 配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.mq")
public class MqProperties {
    
    /**
     * 是否启用 MQ 功能
     */
    private boolean enabled = true;
    
    /**
     * 任务恢复配置
     */
    private Recovery recovery = new Recovery();
    
    /**
     * API 配置
     */
    private Api api = new Api();
    
    /**
     * 管理员 API 配置
     */
    private Admin admin = new Admin();
    
    @Data
    public static class Recovery {
        /**
         * 是否启用任务恢复功能
         */
        private boolean enabled = true;
        
        /**
         * 恢复执行间隔，单位分钟
         */
        private int intervalMinutes = 5;
        
        /**
         * 每次恢复处理的任务数量
         */
        private int batchSize = 100;
        
        /**
         * 最大处理时间，单位小时
         */
        private int maxProcessingHours = 2;
        
        /**
         * 等待任务超时时间，单位分钟
         */
        private int waitingTimeoutMinutes = 30;
        
        /**
         * 最大重试次数
         */
        private int maxRetryAttempts = 3;
        
        /**
         * 分布式锁超时时间，单位分钟
         */
        private int lockTimeoutMinutes = 10;
        
        /**
         * 恢复 API 配置
         */
        private RecoveryApi api = new RecoveryApi();
        
        @Data
        public static class RecoveryApi {
            /**
             * 是否启用恢复 API
             */
            private boolean enabled = true;
        }
    }
    
    @Data
    public static class Api {
        /**
         * 是否启用任务管理 API
         */
        private boolean enabled = true;
    }
    
    @Data
    public static class Admin {
        /**
         * 是否启用管理员 API
         */
        private boolean enabled = false;
    }
}
