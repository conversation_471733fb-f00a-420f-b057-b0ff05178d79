package com.lg.dao.core.sql;

/**
 * JOIN类型枚举
 */
public enum JoinType {
    /**
     * INNER JOIN
     */
    INNER("INNER JOIN"),
    
    /**
     * LEFT JOIN
     */
    LEFT("LEFT JOIN"),
    
    /**
     * RIGHT JOIN
     */
    RIGHT("RIGHT JOIN"),
    
    /**
     * FULL JOIN
     */
    FULL("FULL JOIN");
    
    private final String sql;
    
    JoinType(String sql) {
        this.sql = sql;
    }
    
    public String getSql() {
        return sql;
    }
}