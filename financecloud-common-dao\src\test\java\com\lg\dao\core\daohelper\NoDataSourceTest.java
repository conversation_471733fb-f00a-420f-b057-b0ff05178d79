package com.lg.dao.core.daohelper;

import com.lg.dao.core.GenericDao;
import com.lg.dao.core.basic.User;
import com.lg.dao.core.basic.UserDao;
import com.lg.dao.helper.DaoHelper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 无数据源环境测试
 * 验证在没有配置数据源的情况下自动降级到内存数据库
 */
//@SpringBootTest(classes = TestApplication.class)
@SpringBootTest(classes = {
        com.lg.dao.config.LightORMAutoConfiguration.class,
        com.lg.dao.config.MemoryDataSourceConfiguration.class
})
@TestPropertySource(properties = {
    "spring.datasource.url=",
    "spring.datasource.driver-class-name=",
    "spring.datasource.username=",
    "spring.datasource.password="
})
public class NoDataSourceTest {

    @Test
    @DisplayName("测试无数据源自动降级")
    void testCustomDao() {
        // 在没有数据源配置的情况下，应该自动使用内存数据库
        UserDao customDao = DaoHelper.getCustomDao(UserDao.class);
        customDao.findActiveUsers();

    }
    @Test
    @DisplayName("测试无数据源自动降级")
    void testAutoFallbackToMemoryDatabase() {
        // 在没有数据源配置的情况下，应该自动使用内存数据库
        GenericDao<User, Long> dao = DaoHelper.dao(User.class);
        assertNotNull(dao, "无数据源情况下应该能获取DAO实例");


        // 测试基本操作
        User user = new User();
        user.setId(1L);
        user.setUserName("无数据源测试用户");
        user.setAge(30);
        user.setTenantId("no_ds_tenant");
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 应该能正常插入和查询
        int insertResult = dao.insert(user);
        assertEquals(1, insertResult, "无数据源情况下插入应该成功");

        User foundUser = dao.getById(1L);
        assertNotNull(foundUser, "无数据源情况下查询应该成功");
        assertEquals("无数据源测试用户", foundUser.getUserName(), "数据应该正确");

        System.out.println("✅ 无数据源自动降级测试通过");
    }

    @Test
    @DisplayName("测试强制内存模式")
    void testForceMemoryMode() {
        // 强制使用内存模式
        GenericDao<User, Long> memoryDao = DaoHelper.memoryDao(User.class);
        assertNotNull(memoryDao, "强制内存模式应该成功");
        
        User user = new User();
        user.setId(2L);
        user.setUserName("强制内存用户");
        user.setAge(35);
        user.setTenantId("force_memory");
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        int result = memoryDao.insert(user);
        assertEquals(1, result, "强制内存模式插入应该成功");
        
        System.out.println("✅ 强制内存模式测试通过");
    }
}