<template>
  <!-- MRP需求列表 -->
  <div class="conten_body_main margintop10">
    <top-search ref="topSearch" @query="fun_getTableListData" @reset="fun_searchReset">
      <template #otherContion>
        
      </template>

      <template #toolBar>
        <vxe-button 
          icon="el-icon-edit" 
          :content="$t('批量调整') + '(' + selectedRows.length + ')'" 
          status="primary"
          :disabled="selectedRows.length === 0"
          @click="batchEdit">
        </vxe-button>
        <vxe-button 
          icon="el-icon-shopping-cart" 
          :content="$t('生成采购订单') + '(' + selectedRows.length + ')'" 
          status="success"
          :disabled="selectedRows.length === 0"
          @click="generatePurchaseOrders">
        </vxe-button>
        <vxe-button 
          icon="el-icon-s-operation" 
          :content="$t('生成生产计划单') + '(' + selectedRows.length + ')'"
          status="warning"
          :disabled="selectedRows.length === 0"
          @click="generateProductionOrders">
        </vxe-button>
        <vxe-button 
          icon="el-icon-download" 
          :content="$t('导出Excel')"
          @click="exportRequirements">
        </vxe-button>
        <vxe-button :content="$t('刷新')" icon="el-icon-refresh" @click="fun_mainTableRefresh"></vxe-button>
        <vxe-toolbar ref="xToolbar" :custom="{icon:'vxe-button--icon vxe-icon-custom-column'}"></vxe-toolbar>
      </template>
    </top-search>

    <el-container>
      <el-main>
        <vxe-table
          ref="ListTableRef"
          :data="requirementList"
          :loading="loading"
          :height="contentStyleObj.height"
          show-overflow="tooltip"
          show-header-overflow
          size="mini"
          border
          stripe
          header-align="left"
          row-id="id"
          id="mrpRequirementList"
          :custom-config="{storage: true}"
          :column-config="{resizable:true}"
          :filter-config="{remote:true}"
          @filter-change="handleFilterChange"
          :sort-config="{remote:true}"
          @sort-change="handeleSortChange"
          :checkbox-config="{checkRowKeys:selectedRows, reserve: true, highlight: true, checkMethod: checkMethod}"
          @checkbox-change="checkChange"
          @checkbox-all="selectAll">
        
        <!-- 选择列 -->
        <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
        
        <!-- 序号列 -->
        <vxe-column type="seq" :title="$t('序号')" width="60" fixed="left"></vxe-column>
        
        <!-- MRP计划信息 -->
        <vxe-column field="planCode" :title="$t('MRP计划编码')" width="130" show-overflow-tooltip :params="{searchType:'text', fuzzySearch: true }" :filters="[{ data: {filterSos:{}} }]" :filter-render="{ name: 'filterComplex' }" sortable>
          <template #default="{ row }">
            <el-link type="primary" @click="viewPlanDetail(row)" v-if="row.planCode">{{ row.planCode }}</el-link>
            <span v-else>-</span>
          </template>
        </vxe-column>
        
        <vxe-column field="planName" :title="$t('MRP计划名称')" width="150" show-overflow-tooltip></vxe-column>
        
        <vxe-column field="generatedOrderNo" :title="$t('生成单号')" width="140" show-overflow-tooltip :params="{searchType:'text', fuzzySearch: true }" :filters="[{ data: {filterSos:{}} }]" :filter-render="{ name: 'filterComplex' }" sortable>
          <template #default="{ row }">
            <el-link type="success" @click="viewOrderDetail(row)" v-if="row.generatedOrderNo">{{ row.generatedOrderNo }}</el-link>
            <span v-else class="text-muted">{{ $t('未生成') }}</span>
          </template>
        </vxe-column>
        
        <!-- 基本信息 -->
        <vxe-column field="materialCode" :title="$t('物料编码')" width="120" :params="{searchType:'text', fuzzySearch: true }" :filters="[{ data: {filterSos:{}} }]" :filter-render="{ name: 'filterComplex' }" sortable >
          <template #default="{ row }">
            <el-link type="primary" @click="viewMaterialDetail(row)">{{ row.materialCode }}</el-link>
          </template>
        </vxe-column>
        
        <vxe-column field="materialName" :title="$t('物料名称')" width="150" show-overflow-tooltip :params="{searchType:'text', fuzzySearch: true }" :filters="[{ data: {filterSos:{}} }]" :filter-render="{ name: 'filterComplex' }" sortable></vxe-column>
        
        <vxe-column field="materialSpec" :title="$t('物料型号')" width="120" show-overflow-tooltip></vxe-column>
        
        <vxe-column field="unit" :title="$t('单位')" width="80" align="center"></vxe-column>
        
        <vxe-column field="materialType" :title="$t('物料类型')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getMaterialTypeColor(row.materialType)" size="mini">
              {{ getMaterialTypeText(row.materialType) }}
            </el-tag>
          </template>
        </vxe-column>
        
        <vxe-column field="requirementDate" :title="$t('需求日期')" width="110">
          <template #default="{ row }">
            {{ formatDate(row.requirementDate) }}
          </template>
        </vxe-column>
        

        
        <vxe-column field="sourceType" :title="$t('需求来源')" width="100">
          <template #default="{ row }">
            {{ getDictText('RequirementSourceTypeEnum', row.sourceType) }}
          </template>
        </vxe-column>
        
        <vxe-column field="sourceNo" :title="$t('来源单据')" width="120" show-overflow-tooltip></vxe-column>
        
        <!-- 数量信息 -->
        <vxe-column field="grossRequirement" :title="$t('毛需求数量')" width="110" align="right">
          <template #default="{ row }">
            <span class="number-text">{{ formatNumber(row.grossRequirement) }}</span>
          </template>
        </vxe-column>
        
        <vxe-column field="netRequirement" :title="$t('净需求数量')" width="110" align="right">
          <template #default="{ row }">
            <span class="number-text highlight">{{ formatNumber(row.netRequirement) }}</span>
          </template>
        </vxe-column>
        
        <vxe-column field="bomLevel" :title="$t('BOM层级')" width="80" align="center"></vxe-column>
        
        <!-- 操作列 -->
        <vxe-column field="op" width="250" fixed="right" :title="$t('操作')" align="center" header-align="center">
          <template #default="{ row }">
            <div class="text-butons-group">
              <el-button
                @click="editRequirement(row)"
                type="text"
                icon="el-icon-edit">
                {{ $t('调整') }}
              </el-button>
              <el-button
                @click="viewDetail(row)"
                type="text"
                icon="el-icon-view">
                {{ $t('详情') }}
              </el-button>
              <el-button
                type="text"
                icon="el-icon-document-copy"
                @click="handleViewChanges(row)">
                {{ $t('查看变更') }}
              </el-button>
            </div>
          </template>
        </vxe-column>
      </vxe-table>
      
      <vxe-pager
        align="center"
        size="mini"
        :current-page.sync="page.current"
        :page-size.sync="page.size"
        :total="page.total"
        perfect
        background
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange">
      </vxe-pager>
    </el-main>
  </el-container>

  <!-- 需求调整对话框 -->
  <RequirementEditDialog
    v-if="editDialogVisible"
    :visible="editDialogVisible"
    :requirement="currentRequirement"
    :is-batch="isBatchEdit"
    :selected-requirements="selectedRows"
    @close="editDialogVisible = false"
    @success="handleEditSuccess" />

  <!-- 需求详情对话框 -->
  <RequirementDetailDialog
    v-if="detailDialogVisible"
    :visible="detailDialogVisible"
    :requirement-id="currentRequirementId"
    @close="detailDialogVisible = false"
    @edit="handleEditFromDetail" />

  <!-- 变更历史对话框 -->
  <vxe-modal
    v-model="changesDialogVisible"
    :title="$t('数据变更历史')"
    width="90%"
    height="600px"
    resize
    remember
    show-zoom
    :before-hide-method="handleChangesDialogClose"
  >
    <business-change-history
      v-if="changesDialogVisible"
      :business-id="currentBusinessId"
      :business-type="currentBusinessType"
      @close="changesDialogVisible = false"
    />
  </vxe-modal>
</div>
</template>

<script>
import TopSearch from "@/components/filterate-search/index";
import { getRequirementPageList, exportRequirements, generatePurchaseOrders, generateProductionOrders } from '@/api/erp/mrp-requirement'
import { getMrpPlanOptions } from '@/api/erp/mrp'
import { calculateMrpAsync } from '@/api/erp/mrp-calculation'
import dictMixin from '@/mixins/dictMixin'
import RequirementEditDialog from './components/RequirementEditDialog.vue'
import RequirementDetailDialog from './components/RequirementDetailDialog.vue'
import BusinessChangeHistory from '@/views/common/audit/components/BusinessChangeHistory.vue'

export default {
  name: 'MrpRequirementList',
  components: {
    TopSearch,
    RequirementEditDialog,
    RequirementDetailDialog,
    BusinessChangeHistory
  },
  mixins: [dictMixin],
  data() {
    return {
      loading: false,
      
      // 查询条件
      searchForm: {
        planId: null,
        planCode: null,
        materialCode: null,
        generatedOrderNo: null,
        requirementType: null
      },
      dateRange: null,
      
      // 数据
      requirementList: [],
      selectedRows: [],
      planOptions: [],
      
      // 分页
      page: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 对话框
      editDialogVisible: false,
      detailDialogVisible: false,
      currentRequirement: null,
      currentRequirementId: null,
      isBatchEdit: false,
      
      // 变更历史对话框
      changesDialogVisible: false,
      currentBusinessId: '',
      currentBusinessType: 'MRP_REQUIREMENT'
    }
  },
  
  async created() {
    // 预加载字典数据
    await this.preloadPageDictionaries()
    
    this.loadPlanOptions()
    this.fun_getTableListData()
  },
  
  methods: {
    /**
     * 预加载页面所需的字典数据
     */
    async preloadPageDictionaries() {
      const dictTypes = [
        'RequirementSourceTypeEnum'    // 需求来源类型
      ]
      
      try {
        console.log('开始预加载字典数据...', dictTypes)
        await this.preloadDictionaries(dictTypes)
        console.log('字典数据预加载完成')
      } catch (error) {
        console.error('预加载字典数据失败:', error)
        // 不阻塞页面加载，继续执行
      }
    },

    // 加载计划选项
    async loadPlanOptions() {
      try {
        const response = await getMrpPlanOptions({ status: 'COMPLETED' })
        this.planOptions = response.data.data || []
      } catch (error) {
        console.error('加载计划选项失败:', error)
      }
    },

    // 加载需求列表
    async fun_getTableListData(params = {}) {
      this.loading = true
      try {
        const requestParams = {
          ...this.searchForm,
          ...params,
          current: this.page.current,
          size: this.page.size
        }

        // 处理日期范围
        if (this.dateRange && this.dateRange.length === 2) {
          requestParams.requirementDateStart = this.dateRange[0]
          requestParams.requirementDateEnd = this.dateRange[1]
        }

        const response = await getRequirementPageList(requestParams)
        const data = response.data?.data || response.data

        this.requirementList = data.records || []
        this.page.total = data.total || 0

      } catch (error) {
        console.error('加载需求列表失败:', error)
        this.$message.error('加载需求列表失败')
      } finally {
        this.loading = false
      }
    },

    // 兼容原方法名
    loadRequirementList() {
      this.fun_getTableListData()
    },

    // 搜索重置
    fun_searchReset() {
      this.searchForm = {
        planId: null,
        planCode: null,
        materialCode: null,
        generatedOrderNo: null,
        requirementType: null
      }
      this.dateRange = null
      this.page.current = 1
      this.fun_getTableListData()
    },

    // 表格刷新
    fun_mainTableRefresh() {
      this.fun_getTableListData()
    },

    // 分页处理
    handlePageChange({ currentPage, pageSize }) {
      this.page.current = currentPage
      this.page.size = pageSize
      this.fun_getTableListData()
    },

    // 选择变更
    checkChange() {
      this.selectedRows = this.$refs.ListTableRef.getCheckboxRecords()
    },

    // 全选
    selectAll() {
      this.selectedRows = this.$refs.ListTableRef.getCheckboxRecords()
    },

    // 过滤变更
    handleFilterChange(params) {
      this.fun_getTableListData(params)
    },

    // 排序变更
    handeleSortChange(params) {
      this.fun_getTableListData(params)
    },

    // 复选框禁用控制
    checkMethod({ row }) {
      // 如果已经生成了单号，则禁用复选框
      return !row.generatedOrderNo
    },



    // 获取需求类型颜色
    getRequirementTypeColor(type) {
      const colorMap = {
        'INDEPENDENT': 'primary',
        'DEPENDENT': 'success',
        'SAFETY_STOCK': 'warning',
        'SERVICE': 'info',
        'SCRAP': 'danger'
      }
      return colorMap[type] || 'info'
    },
    
    // 获取物料类型颜色
    getMaterialTypeColor(type) {
      const colorMap = {
        1: 'success',  // 自产 - 绿色
        2: 'warning'   // 外购 - 橙色
      }
      return colorMap[type] || 'info'
    },
    
    // 获取物料类型文本
    getMaterialTypeText(type) {
      const textMap = {
        1: '自产',
        2: '外购'
      }
      return textMap[type] || '未知'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    // 格式化数字
    formatNumber(number) {
      if (number == null) return '0'
      return Number(number).toLocaleString('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      })
    },

    // 单个调整
    editRequirement(row) {
      this.currentRequirement = row
      this.isBatchEdit = false
      this.editDialogVisible = true
    },

    // 批量调整
    batchEdit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要调整的需求记录')
        return
      }
      this.currentRequirement = null
      this.isBatchEdit = true
      this.editDialogVisible = true
    },

    // 查看详情
    viewDetail(row) {
      this.currentRequirementId = row.id
      this.detailDialogVisible = true
    },

    // 从详情弹窗触发的调整需求
    handleEditFromDetail(requirement) {
      // 关闭详情弹窗
      this.detailDialogVisible = false

      // 打开调整弹窗
      this.currentRequirement = requirement
      this.isBatchEdit = false
      this.editDialogVisible = true
    },

    // 查看物料详情
    viewMaterialDetail(row) {
      // TODO: 跳转到物料详情页面
      this.$message.info('跳转到物料详情页面: ' + row.materialCode)
    },

    // 查看MRP计划详情
    viewPlanDetail(row) {
      if (row.planId) {
        this.jump({path: '/erp/mrp/MrpPlanDetail', query: {id: row.planId}})
      }
    },

    // 查看生成订单详情
    viewOrderDetail(row) {
      if (row.generatedOrderNo) {
        // 根据物料类型判断跳转页面
        if (row.materialType === 1 || row.materialType === '1') {
          // 自产物料，跳转到生产计划详情页面
          this.jump({
            path: `/production/plan/productionPlanDetail?id=${row.generatedOrderNo}`
          })
        } else if (row.materialType === 2 || row.materialType === '2') {
          // 外购物料，跳转到采购申请页面
          this.jump({
            path: `/erp/purchaseApplication/purchaseApplicationForm$opType=view?id=${row.generatedOrderNo}&orderType=CGSQD&moduleSource=0`
          })
        } else {
          this.$message.warning('未知的物料类型，无法跳转')
        }
      }
    },

    // 调整成功回调
    handleEditSuccess() {
      this.editDialogVisible = false
      this.loadRequirementList()
      this.$message.success('需求调整成功')
    },

    // 导出需求数据
    async exportRequirements() {
      try {
        const params = { ...this.searchForm }
        if (this.dateRange && this.dateRange.length === 2) {
          params.requirementDateStart = this.dateRange[0]
          params.requirementDateEnd = this.dateRange[1]
        }

        const loading = this.$loading({
          lock: true,
          text: '正在导出Excel文件...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          const response = await exportRequirements(params)
          this.downloadFile(response.data, 'excel')
          loading.close()
          this.$message.success('Excel文件导出成功')
        } catch (error) {
          loading.close()
          throw error
        }

      } catch (error) {
        console.error('导出失败:', error)
        if (error.response && error.response.status === 404) {
          this.$message.error('导出功能暂未实现，请联系管理员')
        } else {
          this.$message.error('导出失败: ' + (error.message || '未知错误'))
        }
      }
    },

    // 文件下载处理
    downloadFile(data, format, customFileName = null) {
      try {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        const fileName = customFileName || this.generateFileName(format)
        link.download = fileName

        document.body.appendChild(link)
        link.click()

        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

      } catch (error) {
        console.error('文件下载失败:', error)
        this.$message.error('文件下载失败')
      }
    },

    // 生成文件名
    generateFileName(format) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      return `MRP需求数据_${timestamp}.xlsx`
    },

    // 查看变更历史
    handleViewChanges(row) {
      this.currentBusinessId = row.id
      this.currentBusinessType = 'MRP_REQUIREMENT'
      this.changesDialogVisible = true
    },
    
    // 生成采购订单
    generatePurchaseOrders() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要生成采购订单的需求记录')
        return
      }
      
      // 筛选外购物料（materialType = 2）
      const purchaseRequirements = this.selectedRows.filter(row => row.materialType == 2)
      
      if (purchaseRequirements.length === 0) {
        this.$message.warning('所选需求中没有外购物料，无法生成采购订单')
        return
      }
      
      this.$confirm(`确定要为${purchaseRequirements.length}条外购物料需求生成采购订单吗？`, '确认生成采购订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fun_generatePurchaseOrders(purchaseRequirements)
      }).catch(() => {
        this.$message.info('已取消生成采购订单')
      })
    },
    
    // 生成生产订单
    generateProductionOrders() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要生成生产订单的需求记录')
        return
      }
      
      // 筛选自产物料（materialType = 1）
      const productionRequirements = this.selectedRows.filter(row => row.materialType == 1)
      
      if (productionRequirements.length === 0) {
        this.$message.warning('所选需求中没有自产物料，无法生成生产订单')
        return
      }
      
      this.$confirm(`确定要为${productionRequirements.length}条自产物料需求生成生产计划单吗？`, '确认生成生产计划单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fun_generateProductionOrders(productionRequirements)
      }).catch(() => {
        this.$message.info('已取消生成生产计划单')
      })
    },
    
    // 执行生成采购订单
    async fun_generatePurchaseOrders(requirements) {
      try {
        this.loading = true
        
        // 获取planId，优先从查询条件获取，如果为空则从需求记录中获取
        let planId = this.searchForm.planId
        if (!planId && requirements.length > 0) {
          planId = requirements[0].planId
        }
        
        // 验证planId是否存在
        if (!planId) {
          this.$message.error('无法获取MRP计划ID，请先选择MRP计划或确保需求记录包含计划信息')
          return
        }
        
        // 构造请求参数
        const params = {
          planId: planId,
          requirementIds: requirements.map(req => req.id),
          materialType: 2, // 外购物料
          remark: '从MRP需求生成的采购订单'
        }
        
        // 调用后端API生成采购订单
        const response = await generatePurchaseOrders(params)
        
        this.$message.success('生成采购订单成功')
        
        // 刷新列表
        await this.fun_getTableListData()
        
        // 清空选择
        this.$refs.ListTableRef.clearCheckboxRow()
        
      } catch (error) {
        console.error('生成采购订单失败:', error)
        this.$message.error('生成采购订单失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    // 执行生成生产订单
    async fun_generateProductionOrders(requirements) {
      try {
        this.loading = true
        
        // 获取planId，优先从查询条件获取，如果为空则从需求记录中获取
        let planId = this.searchForm.planId
        if (!planId && requirements.length > 0) {
          planId = requirements[0].planId
        }
        
        // 验证planId是否存在
        if (!planId) {
          this.$message.error('无法获取MRP计划ID，请先选择MRP计划或确保需求记录包含计划信息')
          return
        }
        
        // 构造请求参数
        const params = {
          planId: planId,
          requirementIds: requirements.map(req => req.id),
          materialType: 1, // 自产物料
          remark: '从MRP需求生成的生产计划单',
          // departmentId: null // 不传递departmentId，让后端自动从最近的生产计划单获取
        }
        
        // 调用后端API生成生产订单
        const response = await generateProductionOrders(params)
        
        this.$message.success('生成生产计划单成功，部门信息已自动填充')
        
        // 刷新列表
        await this.fun_getTableListData()
        
        // 清空选择
        this.$refs.ListTableRef.clearCheckboxRow()
        
      } catch (error) {
        console.error('生成生产计划单失败:', error)
        this.$message.error('生成生产计划单失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    // 关闭变更历史对话框
    handleChangesDialogClose() {
      this.changesDialogVisible = false
      this.currentBusinessId = ''
    }
  }
}
</script>

<style scoped>
.conten_body_main {
  padding: 0 15px 15px 15px;
  .el-container {
    .el-main {
      padding: 0;
      .vxe-table {
        margin-top: 10px;
      }
    }
  }
}

.text-butons-group {
  .el-button {
    padding: 0 5px;
    font-size: 12px;
    & + .el-button {
      margin-left: 5px;
    }
  }
}

.number-text {
  font-family: 'Courier New', monospace;
}

.number-text.highlight {
  font-weight: bold;
  color: #409eff;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.delete-btn {
  color: #f56c6c;
  &:hover {
    color: #f78989;
  }
}
</style>
