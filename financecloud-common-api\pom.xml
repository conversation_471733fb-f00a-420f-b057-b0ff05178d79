<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, ebag All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: cloudx
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lg</groupId>
        <artifactId>financecloud-common</artifactId>
        <version>3.10.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>financecloud-common-api</artifactId>
    <packaging>jar</packaging>
    <description>financecloud 公共API模块 - 底层公共VO和对外暴露的API接口</description>

    <!-- 移除重复的properties定义 -->
    <!-- 删除如下内容：
    <properties>
        <hutool.version>5.8.11</hutool.version>
        <swagger.fox.version>2.9.2</swagger.fox.version>
        <knife4j.version>2.0.9</knife4j.version>
    </properties>
    -->

    <dependencies>
        <!-- 依赖声明中不需要指定版本号，会从BOM中继承 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${ttl.version}</version>
        </dependency>

        <!-- 添加缺失的依赖 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
    </dependencies>
</project>
