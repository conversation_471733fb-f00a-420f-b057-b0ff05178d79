package com.lg.financecloud.common.redis.mq;

import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 延时任务监听接口
 *
 */
@Slf4j
public abstract class RedisAmqListener <T extends BaseTask>  implements Serializable {

    /**
     * 延时调用方法
     *
     * @param t 任务元素
     */
    protected abstract void execute(T t) ;
    public   void doExecute(T t) {
        if(t.getTenantId()!=null) {
            TenantContextHolder.setTenantId(t.getTenantId());
        }
        execute(t);
    }


}
