package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 优化后的JobConsumer类，增加了异常处理和对线程安全的注释说明。
 */
@Data
@Slf4j
public class JobConsumer<T extends  BaseTask> implements Runnable, Serializable {

    private static final long serialVersionUID = 1L;

    private final T data;
    private final Callback<T> callback;

    /**
     * 构造函数，初始化JobConsumer。
     * @param data 任务数据，将由回调函数处理。
     * @param callback 处理任务的回调接口。
     */
    public JobConsumer(T data, Callback<T> callback) {
        this.data = data;
        this.callback = callback;
    }
    public JobConsumer(String jobId,T data, Callback<T> callback) {
        this.data = data;
        this.callback = callback;
    }



    @Override
    public void run() {
        // 注意：调用回调函数process时，应确保该方法或其调用的方法是线程安全的。
        MqDbDao mqDbDao = new MqDbDao();
        String originalTaskId = data.getTaskIdentity();

        try {
            // 先标记任务为处理中，防止重复执行
            int updateResult = mqDbDao.updateMqLogState2processing(data);
            if (updateResult == 0) {
                // 如果更新失败，可能任务不存在，插入新记录
                data.setState(BaseTask.TASK_STATE_PROCESSING);
                mqDbDao.insertMqLog(data);
                log.info("插入新的任务处理记录: {}", originalTaskId);
            }

            // 执行任务
            callback.process(data);

            // 更新为成功状态
            int i = mqDbDao.updateMqLogState2complate(originalTaskId, BaseTask.TASK_STATE_SUCCESS, "");
            if(i == 0){
                // 🚨 修复：不要生成新ID，这会导致重复执行
                log.warn("任务完成状态更新失败，任务可能已被其他线程处理: {}", originalTaskId);
                // 检查任务是否已经存在
                com.lg.financecloud.common.redis.mq.JobMateMationDto existingTask = mqDbDao.getQueueLogById(originalTaskId);
                if (existingTask == null) {
                    // 只有在任务确实不存在时才插入
                    data.setTaskIdentity(originalTaskId); // 保持原ID
                    data.setState(BaseTask.TASK_STATE_SUCCESS);
                    mqDbDao.insertMqLog(data);
                }
            }

        }catch (Exception e){
            try {
                int rowCount = mqDbDao.updateMqLogState2complate(originalTaskId, BaseTask.TASK_STATE_FAIL, e.getMessage());
                if(rowCount == 0){
                    // 🚨 修复：不要生成新ID
                    log.warn("任务失败状态更新失败: {}", originalTaskId);
                    com.lg.financecloud.common.redis.mq.JobMateMationDto existingTask = mqDbDao.getQueueLogById(originalTaskId);
                    if (existingTask == null) {
                        data.setTaskIdentity(originalTaskId); // 保持原ID
                        data.setState(BaseTask.TASK_STATE_FAIL);
                        mqDbDao.insertMqLog(data);
                    }
                }
            } catch (Exception ex) {
                log.error("更新任务状态失败: {}", originalTaskId, ex);
            }
            log.error("任务执行失败: {}", originalTaskId, e);
        }
    }

    @FunctionalInterface
    public interface Callback<T extends  BaseTask> extends Serializable {

        /**
         * 处理任务结果的方法。
         * @param result 任务处理结果。
         */
        void process(T result);
    }
}