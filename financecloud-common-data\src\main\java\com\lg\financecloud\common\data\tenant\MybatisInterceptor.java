package com.lg.financecloud.common.data.tenant;

import cn.hutool.core.date.StopWatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Properties;

@Intercepts(
        {@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
        })
@Slf4j
public class MybatisInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (invocation.getTarget() instanceof StatementHandler) {

            StopWatch stopWatch = new StopWatch("getSwithDbSql4SqlParse");
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            BoundSql boundSql = statementHandler.getBoundSql();
            // 获取到原始sql语句
            String sql = boundSql.getSql();
            // 增强sql
            // 通过反射，拦截方法上带有自定义@InterceptAnnotation注解的方法，并增强sql
            stopWatch.start();
            String mSql = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
            stopWatch.stop();
            if(log.isInfoEnabled())
            log.info("duri sql parse cost nanos:{} cost ms: {},sql:{}" ,stopWatch.getTotalTimeNanos(),stopWatch.getTotalTimeMillis() , mSql);
            // 直接增强sql
            //通过反射修改sql语句
            Field field = boundSql.getClass().getDeclaredField("sql");
            field.setAccessible(true);
            field.set(boundSql, mSql);

        }


        return invocation.proceed();

    }


    @Override
    public Object plugin(Object o) {


        return Plugin.wrap(o, this);

    }

    @Override
    public void setProperties(Properties properties) {

    }
}
