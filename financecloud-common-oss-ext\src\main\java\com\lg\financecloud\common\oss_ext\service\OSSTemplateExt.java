package com.lg.financecloud.common.oss_ext.service;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.lg.financecloud.common.core.util.R;
import com.lg.financecloud.common.oss_ext.OssExtProperties;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class OSSTemplateExt implements InitializingBean {

	public static final String PROTOCOL = "http://";

	public static final String HTTPS_PROTOCOL = "https://";

	public static final String ALI_CS = "aliyuncs";

	private    OSSClient ossClient=null;

	private final OssExtProperties ossExtProperties;

	public static final String  STORAGE_MODE_OSS = "oss";
	public static final String  STORAGE_MODE_DISK = "disk";
	/***
	 * oss  本地代理
	 */
	public static final String  STORAGE_MODE_OSS_LOCAL = "oss_local";

	@Value("${storage.mode:oss}")
	@Setter
	@Getter
	private  String storageMode;

	@Value("${storage.disk:}")
	@Setter
	@Getter
	private  String storageDisk;

	@Value("${storage.accessFileApi:/admin/sys-file}")
	@Setter
	@Getter
	private  String accessFileHost;


	public OSSClient getOssClient(){
		return this.ossClient;
	}


	public R<Map<String,String>> uploadByInputStream(MultipartFile multipartFile, String bucketName,
													 String objectName) throws IOException{
		R<Map<String, String>> mapR = uploadByInputStream(multipartFile.getInputStream(), bucketName, objectName);
		Map<String, String> data = mapR.getData();
		data.put("size",String.valueOf(multipartFile.getSize()));
		data.put("orignFileName",multipartFile.getOriginalFilename() );
		return mapR;
	}



	/**
	 *
	 * @Title: uploadByInputStream
	 * @Description: 通过输入流上传文件
	 * @param inputStream 	输入流
	 * @param bucketName 	bucket名称
	 * @param objectName 	上传文件目录和（包括文件名） 例如“test/a.jpg”
	 * @return void 		返回类型
	 * @throws
	 */
	public R<Map<String,String>> uploadByInputStream(InputStream inputStream, String bucketName,
												  String objectName) {
		try {
			if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS) || StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL) ){
				ossClient.putObject(bucketName, objectName, inputStream);
			}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK)){
				// 兼容linux
				File storagePath = new File(storageDisk + "/"+ StrUtil.subBefore(objectName,"/" ,true), StrUtil.subAfter(objectName,"/" ,true));
				log.info("upload parent path={},filename={}",storagePath.getParent(),storagePath.getName() );
				FileUtil.touch(storagePath);
				IoUtil.copy(inputStream, new FileOutputStream(storagePath));
			}

		} catch (Exception e) {
			log.error("uploadByInputStream error ,bucketName={},objectName={}",bucketName,objectName,e);
		}finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		Map<String,String> resultMap = new HashMap<>();
		//  AAA/BBBB/20230516/289234556616706.pdf
		List<String> splitFilePath = StrUtil.split(objectName,"/" );
		String splitObjectName = CollectionUtil.join(splitFilePath.subList(splitFilePath.size() - 2, splitFilePath.size()), "/");
		// 去除租户和存储目录
		resultMap.put("fileName", splitObjectName);
		resultMap.put("url", this.getFileUrl(objectName));
		return R.ok(resultMap);
	}

	/**
	 *
	 * @Title: uploadByFile
	 * @Description: 通过file上传文件
	 * @param file 			上传的文件
	 * @param bucketName 	bucket名称
	 * @param objectName 	上传文件目录和（包括文件名） 例如“test/a.jpg”
	 * @return void 		返回类型
	 * @throws
	 */
	public  void uploadByFile( File file, String bucketName, String objectName) {
		try {

			if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS) ||StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL) ){
				ossClient.putObject(bucketName, objectName, file);
			}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK)){
				File storagePath = new File(storageDisk,objectName);
				IoUtil.copy(new FileInputStream(file), new FileOutputStream(storagePath));
			}


		} catch (Exception e) {
			log.error("uploadByFile error ,bucketName={},objectName={}",bucketName,objectName,e);
		}
	}

	/**
	 *
	 * @Title: uploadByFile
	 * @Description: 通过file上传文件
	 * @param file 			上传的文件
	 * @param bucketName 	bucket名称
	 * @param objectName 	上传文件目录和（包括文件名） 例如“test/a.jpg”
	 * @return void 		返回类型
	 * @throws
	 */
	public  void uploadByFile( File file, String bucketName, String objectName,String contentType) {
		try {

			if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS) || StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL) ){
				ObjectMetadata objectMetadata = new ObjectMetadata();
				objectMetadata.setContentType(contentType);
				ossClient.putObject(bucketName, objectName, file,objectMetadata);
			}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK)){
				File storagePath = new File(storageDisk,objectName);
				IoUtil.copy(new FileInputStream(file), new FileOutputStream(storagePath));
			}

		} catch (Exception e) {
			log.error("uploadByFile error ,bucketName={},objectName={}",bucketName,objectName,e);
		}
	}

	public void downloadFile(String fileName, HttpServletResponse response){

		R result = null;


		try {
			// TODO  暂时先 通过后缀判断 是预览 还是 附件下载
            // base64 解码
			fileName = Base64Decoder.decodeStr(fileName);
			String downloadType = "ATTACHMENT";
			if(StrUtil.endWithAnyIgnoreCase(fileName,"jpg","png","gif","jpeg","pdf","odf")){
               downloadType = "INLINE";
			}
			response.setHeader("Content-Disposition",downloadType+"; filename=" + new String(fileName.getBytes("UTF-8"), "ISO8859-1"));

			InputStream inputStream = getInputStream(fileName);
			if(inputStream==null){
				result = R.failed("文件不存在");
			}else{
				IoUtil.copy(inputStream,response.getOutputStream() );
			}


		} catch (IOException e) {
			log.error("文件下载失败",e);
		}


		if(result!=null){
			response.setCharacterEncoding("UTF-8");
			try {
				response.getWriter().write(JSONUtil.toJsonStr( result));
			} catch (IOException e) {

			}
		}

	}


	/**
	 *
	 * @Title: deleteFile
	 * @Description: 根据key删除oss服务器上的文件
	 * @param bucketName		bucket名称
	 * @param key    		文件路径/名称，例如“test/a.txt”
	 * @return void    		返回类型
	 * @throws
	 */
	public  void deleteFile(  String bucketName, String key) {
		ossClient.deleteObject(bucketName, key);

	}

	public  void deleteFile(String bucketName, List<String>   keys) {

		DeleteObjectsRequest req = new DeleteObjectsRequest(bucketName);
		req.setKeys(keys);
		ossClient.deleteObjects(req);
	}


	/**
	 *
	 * @Title: getInputStreamByOSS
	 * @Description:根据key获取服务器上的文件的输入流
	 * @param objectName 			文件路径和名称
	 * @return InputStream 	文件输入流
	 * @throws
	 */
	public  InputStream getInputStream(  String objectName) {
		InputStream content = null;
		if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK)){
			try {
				File diskFile = new File(storageDisk,objectName );
				content  = new FileInputStream(diskFile);
			} catch (FileNotFoundException e) {
				log.error("文件不存在",e);
				 return null;
			}

		}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS) || StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL)){

			String bucketName = getOssConfig().getBucketPub();
			try {
				OSSObject ossObj = ossClient.getObject(bucketName, objectName);
				content = ossObj.getObjectContent();
			} catch (Exception e) {
				log.error("getInputStreamByOSS error ,bucketName={},objectName={}",bucketName,objectName,e);
				return null;
			}

		}

		return content;

	}


	public String getFileUrlById(String attachId){
		if(StrUtil.isEmpty(attachId))
			return "";
		if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS)){

		}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK) || StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL)){
			return accessFileHost + "/"+ Base64Encoder.encodeUrlSafe(attachId);
		}
		return "";
	}

	/***
	 * 兼容 cnd  加速访问
	 * @param key
	 * @param expireMillis
	 * @return
	 */
	public  String getFileUrl(String key,Long expireMillis){


		if(StrUtil.isEmpty(key))
			return "";


		if(StrUtil.equalsIgnoreCase(storageMode,"oss")){

			if(expireMillis!=null){
				// 设置URL过期时间为1小时
				Date expiration = new Date(new Date().getTime() + expireMillis);
				GeneratePresignedUrlRequest generatePresignedUrlRequest ;
				generatePresignedUrlRequest =new GeneratePresignedUrlRequest(ossExtProperties.getBucketPrivate(), key);
				generatePresignedUrlRequest.setExpiration(expiration);
				URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
				// 支持cnd
				if(StrUtil.isNotEmpty( ossExtProperties.getPrivateCdn())){
					String cdnUrl = url.toString().replace(getOssConfig().getBucketPrivate() + StrUtil.C_DOT + getOssConfig().getBucketEndpoint(), getOssConfig().getPrivateCdn());
					return cdnUrl.replace(PROTOCOL,HTTPS_PROTOCOL);

				}else {
					return url.toString();
				}
			} else {
				if(StrUtil.isNotEmpty( getOssConfig().getPubCdn())){

					// 全路径 则 换cnd  路径
					if(isOssFullFileUrl(key)){
						key = key.replace(getOssConfig().getBucketPub()+ StrUtil.C_DOT  +getOssConfig().getBucketEndpoint(),getOssConfig().getPubCdn());
						return key.replace(PROTOCOL,HTTPS_PROTOCOL);

					}else if( isFullFileUrl(key)){
						return key;

					}else{
						return getOssConfig().getPubCdn()+StrUtil.C_SLASH +key;
					}

				}else{
					//https://ebag-pub-dev.oss-cn-shenzhen.aliyuncs.com/CAI%5Cgrade1%5Cphase2%5Cdl%5Cpage1.jpg
					if (isFullFileUrl(key)){
						return key;
					}else {

						return  HTTPS_PROTOCOL+getOssConfig().getBucketPub()+ StrUtil.C_DOT  +getOssConfig().getBucketEndpoint()+StrUtil.C_SLASH  +key;
					}

				}

			}

		}else if(StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_DISK) ||StrUtil.equalsIgnoreCase(storageMode,STORAGE_MODE_OSS_LOCAL)){
			return accessFileHost + "/"+ Base64Encoder.encodeUrlSafe(key);

		}
		return "";

	}

	private boolean isFullFileUrl (String key){
		// 兼容处理 存在全路径数据 则 直接返回
		if(StrUtil.contains(key,PROTOCOL) || StrUtil.contains(key,HTTPS_PROTOCOL) ||StrUtil.contains(key,ALI_CS)){
			return true;
		}
		return false;
	}

	private boolean isOssFullFileUrl (String key){
		// 兼容处理 存在全路径数据 则 直接返回
		if(StrUtil.contains(key,ALI_CS)){
			return true;
		}
		return false;
	}


	/***
	 * 兼容cnd  加速访问
	 * @param key

	 * @return
	 */
	public  String getFileUrl(String key){
          return  getFileUrl(key, null);
	}


	@Override
	public void afterPropertiesSet() throws Exception {

		String endpoint=null;
		// 如果内网地址没有配置则 走外网 ，一般是测试环境 是自由服务器的情况 不会配置内网 地址
		endpoint = ossExtProperties.getBucketEndpointInner();
		if(StrUtil.isEmpty(endpoint)){
			endpoint = ossExtProperties.getBucketEndpoint();
		}
		String accessKeyId = ossExtProperties.getAssessKey();;
		String accessKeySecret =ossExtProperties.getSecret();;
		// 创建OSSClient实例。
		this.ossClient =  new OSSClient(endpoint, accessKeyId,accessKeySecret);
	}


	public OssExtProperties getOssConfig(){
		return this.ossExtProperties;
	}
}
