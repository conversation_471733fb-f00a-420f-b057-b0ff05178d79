# DAO框架缓存配置示例

light:
  orm:
    cache:
      enable: true
      # 默认配置（未单独配置的缓存类型使用此配置）
      default-max-size: 100
      default-expire-seconds: 1800

      # 各缓存类型的具体配置
      types:
        # 实体信息缓存（相对稳定，可以缓存更久）
        entity:
          max-size: 200
          expire-seconds: 3600

        # SQL模板缓存（基本不变，长期缓存）
        sql_template:
          max-size: 50
          expire-seconds: 7200

        # DAO操作缓存（变化较频繁，短期缓存）
        dao:
          max-size: 100
          expire-seconds: 600

        # 权限缓存（中等稳定性）
        permission:
          max-size: 50
          expire-seconds: 1800

        # 元数据缓存（基本不变）
        metadata:
          max-size: 30
          expire-seconds: 7200

        # MyBatis代理缓存（中等稳定性）
        mybatis_proxy:
          max-size: 50
          expire-seconds: 1800

# 生产环境配置
---
spring:
  profiles: prod

light:
  orm:
    cache:
      enable: true
      default-max-size: 200
      default-expire-seconds: 3600
      types:
        entity:
          max-size: 500
          expire-seconds: 7200
        sql_template:
          max-size: 100
          expire-seconds: 14400

# 开发环境配置
---
spring:
  profiles: dev

light:
  orm:
    cache:
      enable: true
      default-max-size: 50
      default-expire-seconds: 900
      types:
        entity:
          max-size: 100
          expire-seconds: 1800

# 测试环境配置
---
spring:
  profiles: test

light:
  orm:
    cache:
      enable: false  # 禁用缓存
