package com.lg.financecloud.common.redis.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务恢复监控控制器
 * 提供恢复服务的监控和管理接口
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/mq/recovery")
public class TaskRecoveryController {
    
    @Autowired(required = false)
    private TaskRecoveryService taskRecoveryService;
    
    /**
     * 获取恢复服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> result = new HashMap<>();
        
        if (taskRecoveryService != null) {
            result.put("enabled", true);
            result.put("stats", taskRecoveryService.getRecoveryStats());
            result.put("config", taskRecoveryService.getConfigInfo());
        } else {
            result.put("enabled", false);
            result.put("message", "任务恢复服务未启用");
        }
        
        return result;
    }
    
    /**
     * 手动触发恢复
     */
    @PostMapping("/trigger")
    public Map<String, Object> triggerRecovery() {
        Map<String, Object> result = new HashMap<>();
        
        if (taskRecoveryService != null) {
            try {
                taskRecoveryService.manualRecovery();
                result.put("success", true);
                result.put("message", "手动恢复触发成功");
            } catch (Exception e) {
                log.error("手动触发恢复失败", e);
                result.put("success", false);
                result.put("message", "手动恢复触发失败: " + e.getMessage());
            }
        } else {
            result.put("success", false);
            result.put("message", "任务恢复服务未启用");
        }
        
        return result;
    }
    
    /**
     * 获取恢复统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats() {
        Map<String, Object> result = new HashMap<>();
        
        if (taskRecoveryService != null) {
            TaskRecoveryService.RecoveryStats stats = taskRecoveryService.getRecoveryStats();
            result.put("totalRecovered", stats.getTotalRecovered());
            result.put("totalFailed", stats.getTotalFailed());
            result.put("batchSize", stats.getBatchSize());
            result.put("maxProcessingHours", stats.getMaxProcessingHours());
            result.put("waitingTimeoutMinutes", stats.getWaitingTimeoutMinutes());
            result.put("maxRetryAttempts", stats.getMaxRetryAttempts());
        } else {
            result.put("message", "任务恢复服务未启用");
        }
        
        return result;
    }
    
    /**
     * 重置统计信息
     */
    @PostMapping("/stats/reset")
    public Map<String, Object> resetStats() {
        Map<String, Object> result = new HashMap<>();
        
        if (taskRecoveryService != null) {
            try {
                taskRecoveryService.resetStats();
                result.put("success", true);
                result.put("message", "统计信息重置成功");
            } catch (Exception e) {
                log.error("重置统计信息失败", e);
                result.put("success", false);
                result.put("message", "重置统计信息失败: " + e.getMessage());
            }
        } else {
            result.put("success", false);
            result.put("message", "任务恢复服务未启用");
        }
        
        return result;
    }
    
    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        Map<String, Object> result = new HashMap<>();
        
        if (taskRecoveryService != null) {
            result.put("config", taskRecoveryService.getConfigInfo());
        } else {
            result.put("message", "任务恢复服务未启用");
        }
        
        return result;
    }
}
