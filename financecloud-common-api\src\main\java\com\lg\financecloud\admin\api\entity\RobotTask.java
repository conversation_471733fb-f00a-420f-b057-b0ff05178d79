/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流程自动化机器人
 *
 * <AUTHOR>
 * @date 2023-09-06 10:01:38
 */
@Data
@TableName("robot_task")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "流程自动化机器人")
public class RobotTask extends Model<RobotTask> {
private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(type= IdType.INPUT)
    @ApiModelProperty(value="")
    private Long id;
    /**
     * 租户id
     */
    @ApiModelProperty(value="租户id",hidden=true)
    private Integer tenantId;
    /**
     * 账套id
     */
    @ApiModelProperty(value="账套id")
    private Long accountId;
    /**
     * 流程自动化id
     */
    @ApiModelProperty(value="流程自动化id")
    private Integer robotDefId;
    /**
     * 流程自动化code
     */
    @ApiModelProperty(value="流程自动化code")
    private String robotDefCode;
    /**
     * 任务版本，默认为0，如已被客户端持有则 +1
     */
    @ApiModelProperty(value="任务版本，默认为0，如已被客户端持有则 +1")
    private Integer ver;
    /**
     * 
     */
    @ApiModelProperty(value="")
    private LocalDateTime createTime;
    /**
     * 
     */
    @ApiModelProperty(value="")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    @ApiModelProperty(value="机器人编码")
    private String robotNum;

    /**
     * 运行时参数
     */
    private String runParam;

    }
