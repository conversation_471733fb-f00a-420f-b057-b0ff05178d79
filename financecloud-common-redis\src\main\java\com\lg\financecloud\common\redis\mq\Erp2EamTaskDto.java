package com.lg.financecloud.common.redis.mq;


/****
 *  erp 往eam 发送的通用消息队列
 */

public class Erp2EamTaskDto extends BaseTask {
    //定义消息类型：initCompany 创建公司
    public final static String MSG_TYPE_INITCOMPANY = "initCompany";
    // addDept 增加部门
    public final static String MSG_TYPE_ADDDEPT = "addDept";
    //delDept 删除部门
    public final static String MSG_TYPE_DELDEPT = "delDept";
    //delDept 更新部门
    public final static String MSG_TYPE_UPDDEPT = "updDept";
    //增加岗位 addPosition
    public final static String MSG_TYPE_ADDPOSITION = "addPosition";
    //删除岗位 addPosition
    public final static String MSG_TYPE_DELPOSITION = "delPosition";
    //更新岗位 addPosition
    public final static String MSG_TYPE_UPDPOSITION = "updPosition";

    //增加员工 addStaff
    public final static String MSG_TYPE_ADDSTAFF = "addStaff";
    public final static String MSG_TYPE_UPDSTAFF = "updStaff";
    public final static String MSG_TYPE_DELSTAFF = "delStaff";
    public final static String MSG_TYPE_ADDSTAFFS = "addStaffs";

    //资产通知类型
    public final static String MSG_TYPE_UPDASSET_STATE="updAssetState";

    @Override
    public String queueCode() {
        return "erp2eam";
    }









    // 消息类型
    private String messageType;

    // json 自定义消息体
    private String messageBody;

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }
}
