# PDF模板导出功能

## 概述

PDF模板导出功能允许您使用Excel文件作为模板，通过变量替换和循环数据处理，生成格式化的PDF文档。这个功能特别适合生成报表、发票、证书等需要固定格式的文档。

## 快速开始

### 1. 添加依赖

确保您的项目中包含以下依赖：

```xml
<!-- PDF导出支持 -->
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itextpdf</artifactId>
    <version>5.5.13.3</version>
</dependency>
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext-asian</artifactId>
    <version>5.2.0</version>
</dependency>
```

### 2. 基本使用

```java
// 使用Excel模板生成PDF
ExcelHelper.opsPdfTemplate("template.xlsx")
    .var("companyName", "测试科技有限公司")
    .var("reportTitle", "员工信息报表")
    .var("reportDate", "2025年08月21日")
    .loop("employees", employeeList)
    .fontSize(12f)
    .enableChineseFont(true)
    .exportTo("result.pdf");
```

## 模板语法

### 变量替换

在Excel模板中使用 `${variableName}` 格式定义变量：

- `${companyName}` - 公司名称
- `${reportDate}` - 报表日期
- `${totalAmount}` - 总金额

支持的数据类型：
- 字符串
- 数字
- 日期
- 布尔值

### 循环数据

使用循环标记处理列表数据：

```
${#foreach listName}
${propertyName}
${/foreach}
```

特殊变量：
- `${index}` - 当前循环索引（从1开始）

## Excel模板示例

### 基本模板

| 列A | 列B | 列C |
|-----|-----|-----|
| ${companyName} | | |
| ${reportTitle} | | 日期: ${reportDate} |
| 员工姓名 | 部门 | 职位 |
| ${#foreach employees} | | |
| ${name} | ${department} | ${position} |
| ${/foreach} | | |

### 复杂模板

| 列A | 列B | 列C | 列D | 列E |
|-----|-----|-----|-----|-----|
| ${companyName} | | | | |
| ${reportTitle} | | | 报表日期: ${reportDate} | 制表人: ${reportPerson} |
| 总金额: ${totalAmount} | | 总订单数: ${totalOrders} | | |
| 销售明细 | | | | |
| 产品名称 | 销售数量 | 单价 | 金额 | 销售员 |
| ${#foreach sales} | | | | |
| ${productName} | ${quantity} | ${price} | ${amount} | ${salesperson} |
| ${/foreach} | | | | |

## API参考

### 基本方法

```java
// 创建PDF模板操作对象
OpsPdfTemplate template = ExcelHelper.opsPdfTemplate();
OpsPdfTemplate template = ExcelHelper.opsPdfTemplate("templatePath");
OpsPdfTemplate template = ExcelHelper.opsPdfTemplate(inputStream);
```

### 数据设置

```java
// 设置单个变量
template.var("key", "value");

// 批量设置变量
Map<String, Object> variables = new HashMap<>();
variables.put("companyName", "公司名称");
variables.put("reportDate", "2025-08-21");
template.vars(variables);

// 设置循环数据
List<Employee> employees = getEmployees();
template.loop("employees", employees);
```

### 格式配置

```java
// 字体设置
template.fontSize(12f);
template.enableChineseFont(true);
template.customFont("/path/to/font.ttf");

// 页面设置
template.orientation(PdfTemplateConfig.PageOrientation.PORTRAIT);
template.pageSize(PdfTemplateConfig.PageSize.A4);
template.margins(20f);
template.margins(20f, 15f, 20f, 15f); // 上右下左

// 样式设置
template.showGridLines(true);
template.headerBackgroundColor(0xF0F0F0);
template.headerFontColor(0x000000);
template.dataFontColor(0x333333);
```

### 导出方法

```java
// 导出到文件
template.exportTo("output.pdf");

// 导出到输出流
FileOutputStream outputStream = new FileOutputStream("output.pdf");
template.exportTo(outputStream);

// 导出到HTTP响应
template.exportTo(response, "filename.pdf");
```

## 完整示例

### 1. 员工信息报表

```java
public void exportEmployeeReport() {
    // 准备数据
    Map<String, Object> variables = new HashMap<>();
    variables.put("companyName", "测试科技有限公司");
    variables.put("reportTitle", "员工信息报表");
    variables.put("reportDate", LocalDate.now().toString());
    variables.put("reportPerson", "HR部门");
    
    List<Employee> employees = employeeService.getAllEmployees();
    
    // 导出PDF
    try {
        ExcelHelper.opsPdfTemplate("templates/employee_template.xlsx")
            .vars(variables)
            .loop("employees", employees)
            .fontSize(12f)
            .enableChineseFont(true)
            .orientation(PdfTemplateConfig.PageOrientation.PORTRAIT)
            .pageSize(PdfTemplateConfig.PageSize.A4)
            .margins(20f)
            .showGridLines(true)
            .exportTo("reports/employee_report.pdf");
            
        System.out.println("员工报表导出成功！");
    } catch (Exception e) {
        System.err.println("导出失败: " + e.getMessage());
    }
}
```

### 2. 销售报表

```java
public void exportSalesReport() {
    // 准备数据
    List<SalesRecord> salesData = salesService.getMonthlySales();
    double totalAmount = salesData.stream()
        .mapToDouble(SalesRecord::getAmount)
        .sum();
    
    // 导出PDF
    try {
        ExcelHelper.opsPdfTemplate("templates/sales_template.xlsx")
            .var("companyName", "金融云科技有限公司")
            .var("reportTitle", "月度销售报表")
            .var("reportDate", "2025年08月")
            .var("totalAmount", String.format("¥%.2f", totalAmount))
            .var("totalOrders", salesData.size())
            .loop("sales", salesData)
            .fontSize(10f)
            .enableChineseFont(true)
            .orientation(PdfTemplateConfig.PageOrientation.LANDSCAPE)
            .pageSize(PdfTemplateConfig.PageSize.A4)
            .margins(15f)
            .showGridLines(true)
            .headerBackgroundColor(0xE6F3FF)
            .headerFontColor(0x003366)
            .exportTo("reports/sales_report.pdf");
            
        System.out.println("销售报表导出成功！");
    } catch (Exception e) {
        System.err.println("导出失败: " + e.getMessage());
    }
}
```

### 3. Web应用集成

```java
@RestController
@RequestMapping("/api/reports")
public class ReportController {
    
    @GetMapping("/employee/pdf")
    public void exportEmployeePdf(HttpServletResponse response) {
        try {
            List<Employee> employees = employeeService.getAllEmployees();
            
            ExcelHelper.opsPdfTemplate("templates/employee_template.xlsx")
                .var("companyName", "我的公司")
                .var("exportDate", LocalDate.now().toString())
                .var("totalEmployees", employees.size())
                .loop("employees", employees)
                .enableChineseFont(true)
                .fontSize(11f)
                .exportTo(response, "员工列表.pdf");
                
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    @PostMapping("/sales/pdf")
    public void exportSalesPdf(@RequestBody SalesReportRequest request, 
                              HttpServletResponse response) {
        try {
            List<SalesRecord> sales = salesService.getSalesByDateRange(
                request.getStartDate(), request.getEndDate());
            
            ExcelHelper.opsPdfTemplate("templates/sales_template.xlsx")
                .var("startDate", request.getStartDate().toString())
                .var("endDate", request.getEndDate().toString())
                .var("totalSales", sales.size())
                .loop("sales", sales)
                .enableChineseFont(true)
                .orientation(PdfTemplateConfig.PageOrientation.LANDSCAPE)
                .exportTo(response, "销售报表.pdf");
                
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
```

## 注意事项

1. **模板文件格式**：支持 .xlsx 格式的Excel文件
2. **中文字体**：建议启用中文字体支持以正确显示中文内容
3. **循环嵌套**：目前不支持循环嵌套，请使用平铺的数据结构
4. **性能考虑**：大量数据时建议分页处理或使用流式处理
5. **异常处理**：请妥善处理模板文件不存在、数据格式错误等异常情况

## 故障排除

### 常见问题

1. **中文显示乱码**
   - 确保启用了中文字体：`.enableChineseFont(true)`
   - 检查是否添加了 itext-asian 依赖

2. **模板变量未替换**
   - 检查变量名是否正确
   - 确保变量值不为 null

3. **循环数据未显示**
   - 检查循环标记是否正确：`${#foreach listName}` 和 `${/foreach}`
   - 确保数据列表不为空

4. **PDF生成失败**
   - 检查是否添加了 iText 依赖
   - 确保模板文件路径正确
   - 检查输出路径是否有写权限

### 调试技巧

1. 先生成Excel文件验证模板和数据是否正确
2. 使用简单的模板测试基本功能
3. 逐步添加复杂的格式和数据
4. 查看异常堆栈信息定位问题

## 扩展功能

### 自定义模板处理器

如果需要更复杂的模板处理逻辑，可以扩展 `PdfTemplateProcessor` 类：

```java
public class CustomPdfTemplateProcessor extends PdfTemplateProcessor {
    
    public CustomPdfTemplateProcessor(PdfTemplateConfig config) {
        super(config);
    }
    
    @Override
    protected String getVariableValue(String variableName, Object dataItem, int dataIndex) {
        // 自定义变量处理逻辑
        if ("customDate".equals(variableName)) {
            return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        }
        return super.getVariableValue(variableName, dataItem, dataIndex);
    }
}
```

### 模板缓存

对于频繁使用的模板，建议实现模板缓存机制：

```java
@Component
public class TemplateCache {
    
    private final Map<String, Workbook> templateCache = new ConcurrentHashMap<>();
    
    public Workbook getTemplate(String templatePath) {
        return templateCache.computeIfAbsent(templatePath, path -> {
            try {
                return WorkbookFactory.create(new FileInputStream(path));
            } catch (Exception e) {
                throw new RuntimeException("加载模板失败: " + path, e);
            }
        });
    }
}
```
