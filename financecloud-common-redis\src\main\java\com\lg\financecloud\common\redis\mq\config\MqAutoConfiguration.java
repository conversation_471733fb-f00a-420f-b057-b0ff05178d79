package com.lg.financecloud.common.redis.mq.config;

import com.lg.financecloud.common.redis.mq.TaskAdminController;
import com.lg.financecloud.common.redis.mq.TaskController;
import com.lg.financecloud.common.redis.mq.TaskRecoveryController;
import com.lg.financecloud.common.redis.mq.TaskRecoveryService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * MQ 自动配置类
 * 根据配置条件自动装配 MQ 相关组件
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedissonClient.class)
@EnableConfigurationProperties(MqProperties.class)
public class MqAutoConfiguration {

    /**
     * 任务恢复服务配置
     */
    @Configuration
    @EnableScheduling
    @ConditionalOnProperty(prefix = "light.mq.recovery", name = "enabled", havingValue = "true", matchIfMissing = false)
    static class TaskRecoveryConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public TaskRecoveryService taskRecoveryService(MqProperties properties, 
                                                       RedissonClient redissonClient) {
            log.info("创建任务恢复服务 Bean");
            return new TaskRecoveryService(properties, redissonClient);
        }
        
        @Bean
        @ConditionalOnMissingBean
        @ConditionalOnProperty(prefix = "light.mq.recovery.api", name = "enabled", havingValue = "true", matchIfMissing = false)
        public TaskRecoveryController taskRecoveryController() {
            log.info("创建任务恢复控制器 Bean");
            return new TaskRecoveryController();
        }
    }

    /*l
     * 任务管理 API 配置
     */
    @Configuration
    @ConditionalOnProperty(prefix = "light.mq.api", name = "enabled", havingValue = "true", matchIfMissing = false)
    static class TaskApiConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public TaskController taskController() {
            log.info("创建任务管理控制器 Bean");
            return new TaskController();
        }
    }

    /**
     * 管理员 API 配置
     */
    @Configuration
    @ConditionalOnProperty(prefix = "light.mq.admin", name = "enabled", havingValue = "true", matchIfMissing = false)
    static class TaskAdminApiConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public TaskAdminController taskAdminController() {
            log.info("创建任务管理员控制器 Bean");
            return new TaskAdminController();
        }
    }
}
