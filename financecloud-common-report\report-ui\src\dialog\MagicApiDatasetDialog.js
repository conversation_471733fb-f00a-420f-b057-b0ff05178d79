/**
 * MagicApi数据集配置对话框
 * Created by UReport Team on 2024-01-10.
 */
import {alert} from '../MsgBox.js';
import FieldDialog from './FieldDialog.js';
import ParameterDialog from './ParameterDialog.js';

export default class MagicApiDatasetDialog {
    constructor(datasourceTree, dataset) {
        this.datasourceTree = datasourceTree;
        this.dataset = dataset || {};
        this.fields = dataset.fields || [];
        this.parameters = dataset.parameters || [];
        
        this.dialog = $(`
            <div class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                            <h4 class="modal-title">MagicApi数据集配置</h4>
                        </div>
                        <div class="modal-body"></div>
                        <div class="modal-footer"></div>
                    </div>
                </div>
            </div>
        `);
        $('body').append(this.dialog);
        this.initBody();
        this.initFooter();
    }
    
    initBody() {
        const body = this.dialog.find('.modal-body');
        
        // 数据集名称
        const nameRow = this.createFormGroup("数据集名称", this.nameEditor = $('<input type="text" class="form-control" placeholder="请输入数据集名称">'));
        body.append(nameRow);
        
        // API方法路径
        const apiMethodRow = this.createFormGroup("API方法", this.apiMethodEditor = $('<input type="text" class="form-control" placeholder="/user/list">'));
        body.append(apiMethodRow);
        
        // HTTP方法
        this.httpMethodSelect = $(`
            <select class="form-control">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
            </select>
        `);
        const httpMethodRow = this.createFormGroup("HTTP方法", this.httpMethodSelect);
        body.append(httpMethodRow);
        
        // 字段配置
        body.append('<h5 style="margin-top: 20px;">字段配置</h5>');
        const fieldsContainer = $('<div class="fields-container"></div>');
        body.append(fieldsContainer);
        
        this.fieldsTable = $(`
            <table class="table table-bordered table-condensed">
                <thead>
                    <tr>
                        <th width="30%">字段名</th>
                        <th width="30%">显示名</th>
                        <th width="20%">类型</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);
        fieldsContainer.append(this.fieldsTable);
        
        const addFieldBtn = $('<button type="button" class="btn btn-sm btn-default">添加字段</button>');
        addFieldBtn.click(() => this.showFieldDialog());
        fieldsContainer.append(addFieldBtn);
        
        // 参数配置
        body.append('<h5 style="margin-top: 20px;">参数配置</h5>');
        const parametersContainer = $('<div class="parameters-container"></div>');
        body.append(parametersContainer);
        
        this.parametersTable = $(`
            <table class="table table-bordered table-condensed">
                <thead>
                    <tr>
                        <th width="25%">参数名</th>
                        <th width="25%">显示名</th>
                        <th width="20%">类型</th>
                        <th width="15%">默认值</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `);
        parametersContainer.append(this.parametersTable);
        
        const addParameterBtn = $('<button type="button" class="btn btn-sm btn-default">添加参数</button>');
        addParameterBtn.click(() => this.showParameterDialog());
        parametersContainer.append(addParameterBtn);
        
        this.buildFieldsTable();
        this.buildParametersTable();
    }
    
    createFormGroup(label, input) {
        const row = $(`<div class="row" style="margin-bottom: 10px;"></div>`);
        const labelCol = $(`<div class="col-md-3" style="text-align:right;margin-top:5px">${label}：</div>`);
        const inputCol = $(`<div class="col-md-9"></div>`);
        inputCol.append(input);
        row.append(labelCol);
        row.append(inputCol);
        return row;
    }
    
    buildFieldsTable() {
        const tbody = this.fieldsTable.find('tbody');
        tbody.empty();
        
        for (let i = 0; i < this.fields.length; i++) {
            const field = this.fields[i];
            const row = $(`
                <tr>
                    <td>${field.name}</td>
                    <td>${field.displayName || ''}</td>
                    <td>${field.type || 'String'}</td>
                    <td>
                        <button type="button" class="btn btn-xs btn-primary edit-field" data-index="${i}">编辑</button>
                        <button type="button" class="btn btn-xs btn-danger delete-field" data-index="${i}">删除</button>
                    </td>
                </tr>
            `);
            tbody.append(row);
        }
        
        // 绑定事件
        tbody.find('.edit-field').click((e) => {
            const index = parseInt($(e.target).data('index'));
            this.showFieldDialog(this.fields[index], index);
        });
        
        tbody.find('.delete-field').click((e) => {
            const index = parseInt($(e.target).data('index'));
            this.fields.splice(index, 1);
            this.buildFieldsTable();
        });
    }
    
    buildParametersTable() {
        const tbody = this.parametersTable.find('tbody');
        tbody.empty();
        
        for (let i = 0; i < this.parameters.length; i++) {
            const param = this.parameters[i];
            const row = $(`
                <tr>
                    <td>${param.name}</td>
                    <td>${param.displayName || ''}</td>
                    <td>${param.type || 'String'}</td>
                    <td>${param.defaultValue || ''}</td>
                    <td>
                        <button type="button" class="btn btn-xs btn-primary edit-param" data-index="${i}">编辑</button>
                        <button type="button" class="btn btn-xs btn-danger delete-param" data-index="${i}">删除</button>
                    </td>
                </tr>
            `);
            tbody.append(row);
        }
        
        // 绑定事件
        tbody.find('.edit-param').click((e) => {
            const index = parseInt($(e.target).data('index'));
            this.showParameterDialog(this.parameters[index], index);
        });
        
        tbody.find('.delete-param').click((e) => {
            const index = parseInt($(e.target).data('index'));
            this.parameters.splice(index, 1);
            this.buildParametersTable();
        });
    }
    
    showFieldDialog(field, index) {
        const fieldDialog = new FieldDialog();
        fieldDialog.show((name, displayName, type) => {
            const fieldData = {name, displayName, type};
            if (index !== undefined) {
                this.fields[index] = fieldData;
            } else {
                this.fields.push(fieldData);
            }
            this.buildFieldsTable();
        }, field);
    }
    
    showParameterDialog(parameter, index) {
        const parameterDialog = new ParameterDialog(this.parameters);
        parameterDialog.show((name, type, defaultValue) => {
            const paramData = {name, displayName: name, type, defaultValue};
            if (index !== undefined) {
                this.parameters[index] = paramData;
            } else {
                this.parameters.push(paramData);
            }
            this.buildParametersTable();
        }, parameter);
    }
    
    initFooter() {
        const footer = this.dialog.find('.modal-footer');
        
        // 测试数据集按钮
        const testButton = $('<button type="button" class="btn btn-info">测试数据集</button>');
        footer.append(testButton);
        testButton.click(() => this.testDataset());
        
        // 取消按钮
        const cancelButton = $('<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>');
        footer.append(cancelButton);
        
        // 保存按钮
        const saveButton = $('<button type="button" class="btn btn-primary">保存</button>');
        footer.append(saveButton);
        saveButton.click(() => this.save());
    }
    
    testDataset() {
        const name = this.nameEditor.val().trim();
        const apiMethod = this.apiMethodEditor.val().trim();
        const httpMethod = this.httpMethodSelect.val();
        
        if (!name) {
            alert('请输入数据集名称');
            return;
        }
        
        if (!apiMethod) {
            alert('请输入API方法');
            return;
        }
        
        // 发送测试请求
        $.ajax({
            url: window._server + "/datasource/testMagicApiDataset",
            type: 'POST',
            data: {
                baseUrl: this.datasourceTree.baseUrl,
                apiPath: this.datasourceTree.apiPath,
                apiMethod: apiMethod,
                httpMethod: httpMethod,
                headers: JSON.stringify(this.datasourceTree.headers || {})
            },
            success: (result) => {
                if (result.success) {
                    alert('数据集测试成功！返回 ' + (result.data ? result.data.length : 0) + ' 条记录');

                    // 如果返回了字段信息，自动填充到字段列表
                    if (result.fields && result.fields.length > 0) {
                        this.populateFields(result.fields);
                    }
                } else {
                    alert('数据集测试失败：' + (result.message || '未知错误'));
                }
            },
            error: () => {
                alert('数据集测试失败：网络错误');
            }
        });
    }
    
    /**
     * 自动填充字段列表
     */
    populateFields(fields) {
        if (!fields || fields.length === 0) {
            return;
        }

        // 询问用户是否要替换现有字段
        let shouldReplace = true;
        if (this.fields.length > 0) {
            shouldReplace = confirm('检测到API返回了字段信息，是否要替换当前的字段列表？');
        }

        if (shouldReplace) {
            // 清空现有字段
            this.fields = [];

            // 添加新字段
            fields.forEach(field => {
                this.fields.push({
                    name: field.name,
                    displayName: field.displayName || field.name, // 默认别名和字段名一样
                    type: field.type || 'String'
                });
            });

            // 重新构建字段表格
            this.buildFieldsTable();

            alert(`成功解析到 ${fields.length} 个字段，已自动添加到字段列表中`);
        }
    }

    save() {
        const name = this.nameEditor.val().trim();
        const apiMethod = this.apiMethodEditor.val().trim();
        const httpMethod = this.httpMethodSelect.val();

        if (!name) {
            alert('请输入数据集名称');
            return;
        }

        if (!apiMethod) {
            alert('请输入API方法');
            return;
        }

        if (this.onSave) {
            this.onSave.call(this, name, apiMethod, httpMethod, this.fields, this.parameters);
        }

        this.dialog.modal('hide');
    }
    
    show(onSave, dataset) {
        this.onSave = onSave;
        
        if (dataset) {
            // 编辑模式
            this.nameEditor.val(dataset.name || '');
            this.apiMethodEditor.val(dataset.apiMethod || '');
            this.httpMethodSelect.val(dataset.httpMethod || 'GET');
            this.fields = dataset.fields ? [...dataset.fields] : [];
            this.parameters = dataset.parameters ? [...dataset.parameters] : [];
        } else {
            // 新增模式
            this.nameEditor.val('');
            this.apiMethodEditor.val('');
            this.httpMethodSelect.val('GET');
            this.fields = [];
            this.parameters = [];
        }
        
        this.buildFieldsTable();
        this.buildParametersTable();
        this.dialog.modal('show');
    }
}
