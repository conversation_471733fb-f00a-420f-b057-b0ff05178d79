//package com.lg.financecloud.common.ai.controller;
//
//import com.lg.financecloud.common.ai.exception.AIServiceException;
//import com.lg.financecloud.common.ai.factory.AIServiceFactory;
//import com.lg.financecloud.common.ai.model.AIRequest;
//import com.lg.financecloud.common.ai.model.AIResponse;
//import com.lg.financecloud.common.ai.service.AIModelService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Flux;
//
//import java.util.List;
//
///**
// * AI控制器
// * 提供AI服务的REST API接口
// */
//@RestController
//@RequestMapping("/api/ai")
//public class AIController {
//
//    @Autowired
//    private AIServiceFactory aiServiceFactory;
//
//    /**
//     * 获取所有可用的AI模型类型
//     *
//     * @return 可用的AI模型类型列表
//     */
//    @GetMapping("/models")
//    public ResponseEntity<List<String>> getAvailableModels() {
//        return ResponseEntity.ok(aiServiceFactory.getAvailableModelTypes());
//    }
//
//    /**
//     * 处理AI请求
//     *
//     * @param request AI请求
//     * @return AI响应
//     */
//    @PostMapping("/process")
//    public ResponseEntity<AIResponse> processRequest(@RequestBody AIRequest request) {
//        AIModelService aiService;
//        if (request.getModelType() != null) {
//            aiService = aiServiceFactory.getService(request.getModelType());
//        } else {
//            aiService = aiServiceFactory.getDefaultService();
//        }
//
//        AIResponse response = aiService.process(request);
//        return ResponseEntity.ok(response);
//    }
//
//    /**
//     * 流式处理AI请求
//     * 使用Server-Sent Events (SSE)实现实时流式输出
//     *
//     * @param request AI请求
//     * @return AI响应流
//     */
//    @PostMapping(value = "/process/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    public Flux<AIResponse> processStreamRequest(@RequestBody AIRequest request) {
//        AIModelService aiService;
//        if (request.getModelType() != null) {
//            aiService = aiServiceFactory.getService(request.getModelType());
//        } else {
//            aiService = aiServiceFactory.getDefaultService();
//        }
//
//        // 强制设置为流式输出
//        request.setStreamOutput(true);
//
//        return aiService.processStream(request);
//    }
//}