/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.redis.cache;

import com.lg.financecloud.common.redis.cache.properties.CacheProperties;
import com.lg.financecloud.common.redis.cache.properties.CacheProperties.CacheMode;
import lombok.AllArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.FstCodec;
import org.redisson.config.Config;
import org.redisson.spring.starter.RedissonProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.IOException;

/**
 * RedisTemplate 配置
 *
 * <AUTHOR>
 */
@EnableCaching
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(CacheProperties.class)
@AutoConfigureBefore(name = { "org.redisson.spring.starter.RedissonAutoConfiguration",
		"org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration" })
public class RedisTemplateConfig {

	@Autowired
	private RedissonProperties redissonProperties;
	
	@Autowired
	private CacheProperties cacheProperties;
	
	/**
	 * Redisson client bean that's conditionally created when Redis is needed
	 * (REDIS or TWO_LEVEL cache mode)
	 */
	@Bean
	@Primary
	@ConditionalOnExpression("'${light.cache.cache-mode:REDIS}'.equals('REDIS') || '${light.cache.cache-mode}'.equals('TWO_LEVEL')")
	public RedissonClient redisson() {
		Config config = null;
		if (redissonProperties.getConfig() != null) {
			try {
				config = Config.fromYAML(redissonProperties.getConfig());
			} catch (IOException e) {
				try {
					config = Config.fromJSON(redissonProperties.getConfig());
				} catch (IOException e1) {
					throw new IllegalArgumentException("Can't parse config", e1);
				}
			}
		}
		config.setCodec(new FstCodec());
		return Redisson.create(config);
	}

	/**
	 * Redis template bean that's conditionally created when Redis is needed
	 * (REDIS or TWO_LEVEL cache mode)
	 */
	@Bean
	@Primary
	@ConditionalOnExpression("'${light.cache.cache-mode:REDIS}'.equals('REDIS') || '${light.cache.cache-mode}'.equals('TWO_LEVEL')")
	public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
		redisTemplate.setKeySerializer(new StringRedisSerializer());
		redisTemplate.setHashKeySerializer(new StringRedisSerializer());
		redisTemplate.setValueSerializer(new FSTSerializer());
		redisTemplate.setHashValueSerializer(new FSTSerializer());
		redisTemplate.setConnectionFactory(redisConnectionFactory);
		return redisTemplate;
	}

}
