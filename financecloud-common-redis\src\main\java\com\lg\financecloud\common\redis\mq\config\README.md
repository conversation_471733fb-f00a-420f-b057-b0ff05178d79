# MQ 自动配置说明

## 概述

为了避免不必要的 Bean 实例化，MQ 模块现在使用自动配置类来管理组件的创建。所有组件都支持条件化配置，只有在需要时才会被创建。

## 自动配置类

### MqAutoConfiguration

主要的自动配置类，包含三个内部配置类：

1. **TaskRecoveryConfiguration** - 任务恢复服务配置
2. **TaskApiConfiguration** - 任务管理 API 配置  
3. **TaskAdminApiConfiguration** - 管理员 API 配置

## 配置属性

### MqProperties

配置前缀：`light.mq`

```java
@ConfigurationProperties(prefix = "light.mq")
public class MqProperties {
    private boolean enabled = true;           // 是否启用 MQ 功能
    private Recovery recovery = new Recovery(); // 恢复配置
    private Api api = new Api();               // API 配置
    private Admin admin = new Admin();         // 管理员配置
}
```

### 恢复配置 (Recovery)

```java
public static class Recovery {
    private boolean enabled = true;                    // 是否启用恢复功能
    private int intervalMinutes = 5;                   // 恢复间隔(分钟)
    private int batchSize = 100;                       // 批处理大小
    private int maxProcessingHours = 2;                // 最大处理时间(小时)
    private int waitingTimeoutMinutes = 30;            // 等待超时(分钟)
    private int maxRetryAttempts = 3;                  // 最大重试次数
    private int lockTimeoutMinutes = 10;               // 锁超时(分钟)
    private RecoveryApi api = new RecoveryApi();       // 恢复API配置
}
```

## Bean 创建条件

### TaskRecoveryService

**创建条件：**
- `light.mq.recovery.enabled=true` (默认 true)
- 存在 `RedissonClient` Bean

**依赖：**
- `MqProperties` - 配置属性
- `RedissonClient` - Redis 客户端

### TaskRecoveryController

**创建条件：**
- `light.mq.recovery.enabled=true` (默认 true)
- `light.mq.recovery.api.enabled=true` (默认 true)

### TaskController

**创建条件：**
- `light.mq.api.enabled=true` (默认 true)

### TaskAdminController

**创建条件：**
- `light.mq.admin.enabled=true` (默认 false)

## 配置示例

### 基础配置

```yaml
light:
  mq:
    enabled: true
    
    # 任务恢复配置
    recovery:
      enabled: true
      interval-minutes: 5
      batch-size: 100
      api:
        enabled: true
    
    # 任务管理 API
    api:
      enabled: true
    
    # 管理员 API (默认禁用)
    admin:
      enabled: false
```

### 环境特定配置

```yaml
# 开发环境
---
spring:
  config:
    activate:
      on-profile: dev
      
light:
  mq:
    recovery:
      enabled: true
      interval-minutes: 3
    admin:
      enabled: true  # 开发环境启用管理员 API

# 生产环境
---
spring:
  config:
    activate:
      on-profile: prod
      
light:
  mq:
    recovery:
      enabled: true
      batch-size: 200
    admin:
      enabled: false  # 生产环境禁用管理员 API

# 测试环境
---
spring:
  config:
    activate:
      on-profile: test
      
light:
  mq:
    recovery:
      enabled: false  # 测试环境禁用恢复功能
    admin:
      enabled: true
```

## 禁用组件

### 禁用任务恢复

```yaml
light:
  mq:
    recovery:
      enabled: false
```

### 禁用所有 API

```yaml
light:
  mq:
    api:
      enabled: false
    recovery:
      api:
        enabled: false
    admin:
      enabled: false
```

### 仅启用核心功能

```yaml
light:
  mq:
    enabled: true
    recovery:
      enabled: false
    api:
      enabled: false
    admin:
      enabled: false
```

## 自定义 Bean

如果需要自定义实现，可以提供自己的 Bean：

```java
@Configuration
public class CustomMqConfiguration {
    
    @Bean
    @Primary
    public TaskRecoveryService customTaskRecoveryService(
            MqProperties properties, 
            RedissonClient redissonClient) {
        return new CustomTaskRecoveryService(properties, redissonClient);
    }
}
```

## 注意事项

1. **条件化创建**: 所有 Bean 都支持 `@ConditionalOnMissingBean`，允许用户提供自定义实现
2. **配置优先级**: 环境特定配置会覆盖默认配置
3. **依赖检查**: 自动配置会检查必要的依赖是否存在
4. **向后兼容**: 保持与现有代码的完全兼容性
5. **性能优化**: 只创建实际需要的 Bean，减少内存占用
