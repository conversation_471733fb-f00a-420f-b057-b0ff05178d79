/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DirectionEnum {

	/**
	 * 金额发生方向
	 */
	debit( "0","借"),
	crebit("1","贷");





	/**
	 * 描述
	 */
	private String code;
	private String name;


	private static final Map<String, DirectionEnum> lookup = new HashMap<String, DirectionEnum>();

	static {
		for (DirectionEnum s : EnumSet.allOf(DirectionEnum.class)) {
			lookup.put(s.getCode(), s);
			}
		}

	public static DirectionEnum lookup(String code) {
		return lookup.get(code);
	}



}
