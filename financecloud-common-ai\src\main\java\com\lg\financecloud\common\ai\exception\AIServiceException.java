package com.lg.financecloud.common.ai.exception;

/**
 * AI服务异常类
 * 统一的异常处理机制
 */
public class AIServiceException extends RuntimeException {
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 默认构造函数
     */
    public AIServiceException() {
        this("AI服务异常");
    }
    
    /**
     * 带错误消息的构造函数
     * 
     * @param message 错误消息
     */
    public AIServiceException(String message) {
        this("500", message);
    }
    
    /**
     * 带错误消息和原因的构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     */
    public AIServiceException(String message, Throwable cause) {
        this("500", message, cause);
    }
    
    /**
     * 带错误代码和错误消息的构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public AIServiceException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 带错误代码、错误消息和原因的构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原因
     */
    public AIServiceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 设置错误代码
     * 
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}