package com.lg.dao.core.filter;

import com.lg.dao.core.interceptor.InterceptorExclusionManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据过滤切面
 * 用于在执行方法前设置当前方法，以便DataPermissionInterceptor能够获取到方法上的注解
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
public class DataFilterAspect {

    @Autowired
    private JsonFilterParser jsonFilterParser;

    /**
     * 定义切点，拦截所有带有DynamicDataFilter注解的方法
     */
    @Pointcut("@annotation(com.lg.dao.core.filter.DynamicDataFilter)")
    public void dynamicDataFilterPointcut() {
    }

    /**
     * 定义切点，拦截所有带有ExcludeDataFilter注解的方法
     */
    @Pointcut("@annotation(com.lg.dao.core.filter.ExcludeDataFilter)")
    public void excludeDataFilterPointcut() {
    }
    
    /**
     * 环绕通知，在执行方法前设置当前方法
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("dynamicDataFilterPointcut() || excludeDataFilterPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 清除旧的线程本地变量，避免内存泄漏
        DataPermissionInterceptor.clearAllThreadLocals();
        
        // 设置当前方法到线程本地变量
        DataPermissionInterceptor.setCurrentMethod(method);
        
        // 如果方法上有DynamicDataFilter注解，获取注解中的过滤条件
        if (method.isAnnotationPresent(DynamicDataFilter.class)) {
            DynamicDataFilter annotation = method.getAnnotation(DynamicDataFilter.class);
            
            // 优先使用JSON格式的过滤条件
            String filterJson = annotation.value();
            if (!StringUtils.isEmpty(filterJson)) {
                try {
                    DataPermissionInterceptor.setFilterConfig(filterJson);
                    log.debug("已设置JSON格式的过滤条件: {}", filterJson);
                } catch (Exception e) {
                    log.error("设置JSON格式的过滤条件失败: {}", filterJson, e);
                }
            } 
            // 如果没有JSON格式的过滤条件，尝试使用字段配置
            else {
                FilterField[] fields = annotation.fields();
                if (fields != null && fields.length > 0) {
                    try {
                        FilterConfig filterConfig = convertFieldsToFilterConfig(fields);
                        DataPermissionInterceptor.setFilterConfig(filterConfig);
                        log.debug("已设置字段配置的过滤条件: {} 个字段", fields.length);
                    } catch (Exception e) {
                        log.error("设置字段配置的过滤条件失败", e);
                    }
                }
            }
        }

        try {
            // 如果方法上有ExcludeDataFilter注解，使用统一拦截器排除管理器排除数据权限拦截器
            if (method.isAnnotationPresent(ExcludeDataFilter.class)) {
                return InterceptorExclusionManager.executeWithoutDataPermission(() -> {
                    try {
                        return joinPoint.proceed();
                    } catch (Throwable e) {
                        if (e instanceof RuntimeException) {
                            throw (RuntimeException) e;
                        }
                        throw new RuntimeException("Method execution failed", e);
                    }
                });
            } else {
                // 没有排除注解，正常执行
                return joinPoint.proceed();
            }
        } catch (Throwable e) {
            log.error("方法执行异常: {}", method.getName(), e);
            throw e;
        } finally {
            // 清除所有线程本地变量
            DataPermissionInterceptor.clearAllThreadLocals();
        }
    }
    
    /**
     * 将字段配置转换为过滤配置
     *
     * @param fields 字段配置数组
     * @return 过滤配置
     */
    private FilterConfig convertFieldsToFilterConfig(FilterField[] fields) {
        FilterConfig filterConfig = new FilterConfig();
        List<FilterConfig.Filter> filters = new ArrayList<>();
        
        for (FilterField field : fields) {
            FilterConfig.Filter filter = new FilterConfig.Filter();
            filter.setField(field.field());
            filter.setOperator(field.operator());
            filter.setValue(field.value());
            filters.add(filter);
        }
        
        filterConfig.setFilters(filters);
        return filterConfig;
    }
} 