package com.lg.dao.core.interceptor;

import org.springframework.util.StopWatch;

import java.util.List;

/**
 * SQL 拦截器接口
 * 允许在 SQL 执行的不同阶段进行拦截和处理
 */
public interface LightOrmSqlInterceptor {

    /**
     * SQL 执行前的拦截点
     * @param sql 原始 SQL
     * @param params SQL 参数
     * @return 处理后的 SQL
     */
    default String beforeExecute(String sql, List<Object> params) {
        return beforeExecute(sql, params, new SqlInterceptorContext());
    }

    /**
     * SQL 执行前的拦截点（带上下文）
     * @param sql 原始 SQL
     * @param params SQL 参数
     * @param context 拦截器上下文
     * @return 处理后的 SQL
     */
    default String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
        return sql;
    }

    /**
     * SQL 执行后的拦截点
     * @param sql 执行的 SQL
     * @param params SQL 参数
     * @param result 执行结果
     * @param stopWatch 执行计时器
     */
    default void afterExecute(String sql, List<Object> params, Object result, StopWatch stopWatch) {
        afterExecute(sql, params, result, new SqlInterceptorContext());
    }

    /**
     * SQL 执行后的拦截点（带上下文）
     * @param sql 执行的 SQL
     * @param params SQL 参数
     * @param result 执行结果
     * @param context 拦截器上下文
     */
    default void afterExecute(String sql, List<Object> params, Object result, SqlInterceptorContext context) {
    }

    /**
     * 获取拦截器优先级
     * 数字越小优先级越高
     */
    default int getOrder() {
        return 0;
    }
}