package com.github.stupdit1t.excel.core.export;

import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.function.Function;

/**
 * 主从表导出处理器
 * 负责处理主从表数据的Excel导出逻辑
 */
public class MasterDetailExportProcessor {

    /**
     * 处理主从表导出
     *
     * @param sheet Excel工作表
     * @param masterDetailSheet 主从表配置
     * @param <M> 主表数据类型
     * @param <D> 明细表数据类型
     */
    public static <M, D> void processMasterDetailExport(Sheet sheet, OpsMasterDetailSheet<M, D> masterDetailSheet) {
        List<M> masterData = masterDetailSheet.getMasterData();
        Function<M, List<D>> detailExtractor = masterDetailSheet.getDetailDataExtractor();
        
        int currentRow = 0;
        
        // 写入主表表头
        if (masterDetailSheet.getMasterHeader() != null) {
            currentRow = writeMasterHeader(sheet, masterDetailSheet, currentRow);
        }
        
        // 写入明细表表头（如果需要且只显示一次）
        boolean detailHeaderWritten = false;
        if (masterDetailSheet.isShowDetailHeader() && !masterDetailSheet.isRepeatDetailHeaderForEachMaster()) {
            currentRow = writeDetailHeader(sheet, masterDetailSheet, currentRow);
            detailHeaderWritten = true;
        }
        
        // 遍历主表数据
        for (int masterIndex = 0; masterIndex < masterData.size(); masterIndex++) {
            M masterRecord = masterData.get(masterIndex);
            
            // 写入主表数据
            currentRow = writeMasterData(sheet, masterDetailSheet, masterRecord, currentRow);
            
            // 获取对应的明细数据
            List<D> detailData = detailExtractor.apply(masterRecord);
            
            if (detailData != null && !detailData.isEmpty()) {
                // 如果需要为每个主表记录重复明细表头
                if (masterDetailSheet.isShowDetailHeader() && 
                    masterDetailSheet.isRepeatDetailHeaderForEachMaster() && 
                    !detailHeaderWritten) {
                    currentRow = writeDetailHeader(sheet, masterDetailSheet, currentRow);
                }
                
                // 写入明细数据
                currentRow = writeDetailData(sheet, masterDetailSheet, detailData, currentRow);
            }
            
            // 添加间隔行（除了最后一条记录）
            if (masterIndex < masterData.size() - 1) {
                currentRow += masterDetailSheet.getSpacingRows();
            }
            
            detailHeaderWritten = false; // 重置标志
        }
    }

    /**
     * 写入主表表头
     */
    private static <M, D> int writeMasterHeader(Sheet sheet, OpsMasterDetailSheet<M, D> masterDetailSheet, int startRow) {
        OpsHeader<M> masterHeader = masterDetailSheet.getMasterHeader();
        if (masterHeader == null) {
            return startRow;
        }

        // 这里需要根据具体的表头类型（简单或复杂）来处理
        // 简化处理，假设是简单表头
        if (masterHeader.simple != null) {
            Row headerRow = sheet.createRow(startRow);
            OpsColumn<M> masterColumn = masterDetailSheet.getMasterColumn();
            
            if (masterColumn != null && masterColumn.columns != null) {
                int colIndex = 0;
                for (OutColumn<?> column : masterColumn.columns) {
                    Cell cell = headerRow.createCell(colIndex++);
                    // 从表头配置中获取标题，这里简化处理
                    cell.setCellValue(column.field); // 实际应该从表头配置获取
                    
                    // 应用表头样式
                    CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
                    Font headerFont = sheet.getWorkbook().createFont();
                    headerFont.setBold(true);
                    headerStyle.setFont(headerFont);
                    cell.setCellStyle(headerStyle);
                }
            }
            return startRow + 1;
        }
        
        return startRow;
    }

    /**
     * 写入明细表表头
     */
    private static <M, D> int writeDetailHeader(Sheet sheet, OpsMasterDetailSheet<M, D> masterDetailSheet, int startRow) {
        OpsHeader<D> detailHeader = masterDetailSheet.getDetailHeader();
        if (detailHeader == null) {
            return startRow;
        }

        // 简化处理，假设是简单表头
        if (detailHeader.simple != null) {
            Row headerRow = sheet.createRow(startRow);
            OpsColumn<D> detailColumn = masterDetailSheet.getDetailColumn();
            
            if (detailColumn != null && detailColumn.columns != null) {
                int colIndex = 0;
                for (OutColumn<?> column : detailColumn.columns) {
                    Cell cell = headerRow.createCell(colIndex++);
                    cell.setCellValue(column.field); // 实际应该从表头配置获取
                    
                    // 应用表头样式
                    CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
                    Font headerFont = sheet.getWorkbook().createFont();
                    headerFont.setBold(true);
                    headerStyle.setFont(headerFont);
                    cell.setCellStyle(headerStyle);
                }
            }
            return startRow + 1;
        }
        
        return startRow;
    }

    /**
     * 写入主表数据
     */
    private static <M, D> int writeMasterData(Sheet sheet, OpsMasterDetailSheet<M, D> masterDetailSheet, 
                                              M masterRecord, int startRow) {
        OpsColumn<M> masterColumn = masterDetailSheet.getMasterColumn();
        if (masterColumn == null || masterColumn.columns == null) {
            return startRow;
        }

        Row dataRow = sheet.createRow(startRow);
        int colIndex = 0;
        
        for (OutColumn<?> column : masterColumn.columns) {
            Cell cell = dataRow.createCell(colIndex++);
            
            // 获取字段值
            Object fieldValue = getFieldValue(masterRecord, column.field);
            setCellValue(cell, fieldValue);
            
            // 应用列样式
            if (column.style != null) {
                applyCellStyle(cell, column.style);
            }
        }
        
        return startRow + 1;
    }

    /**
     * 写入明细数据
     */
    private static <M, D> int writeDetailData(Sheet sheet, OpsMasterDetailSheet<M, D> masterDetailSheet, 
                                              List<D> detailData, int startRow) {
        OpsColumn<D> detailColumn = masterDetailSheet.getDetailColumn();
        if (detailColumn == null || detailColumn.columns == null) {
            return startRow;
        }

        int currentRow = startRow;
        
        for (D detailRecord : detailData) {
            Row dataRow = sheet.createRow(currentRow++);
            int colIndex = 0;
            
            for (OutColumn<?> column : detailColumn.columns) {
                Cell cell = dataRow.createCell(colIndex++);
                
                // 获取字段值
                Object fieldValue = getFieldValue(detailRecord, column.field);
                setCellValue(cell, fieldValue);
                
                // 应用列样式
                if (column.style != null) {
                    applyCellStyle(cell, column.style);
                }
            }
        }
        
        return currentRow;
    }

    /**
     * 获取字段值（使用反射）
     */
    private static Object getFieldValue(Object obj, String fieldName) {
        try {
            // 简化实现，实际应该使用更完善的反射工具
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            // 如果反射失败，尝试getter方法
            try {
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                java.lang.reflect.Method getter = obj.getClass().getMethod(getterName);
                return getter.invoke(obj);
            } catch (Exception ex) {
                return "";
            }
        }
    }

    /**
     * 设置单元格值
     */
    private static void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof java.util.Date) {
            cell.setCellValue((java.util.Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 应用单元格样式
     */
    private static void applyCellStyle(Cell cell, OutColumn.Style style) {
        CellStyle cellStyle = cell.getSheet().getWorkbook().createCellStyle();
        
        // 应用对齐方式
        if (style.align != null) {
            cellStyle.setAlignment(style.align);
        }
        if (style.valign != null) {
            cellStyle.setVerticalAlignment(style.valign);
        }
        
        // 应用背景色
        if (style.backColor != null) {
            cellStyle.setFillForegroundColor(style.backColor.index);
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }
        
        // 应用字体
        if (style.color != null) {
            Font font = cell.getSheet().getWorkbook().createFont();
            font.setColor(style.color.index);
            cellStyle.setFont(font);
        }
        
        cell.setCellStyle(cellStyle);
    }
}
