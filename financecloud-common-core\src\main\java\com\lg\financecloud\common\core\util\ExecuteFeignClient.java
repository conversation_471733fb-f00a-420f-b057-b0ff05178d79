/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.lg.financecloud.common.core.util;

import cn.hutool.json.JSONUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * @ClassName: ExecuteFeignClient
 * @Description: 调用外部系统接口时可以用这个统一管理异常信息
 * @author: skyeye云系列--卫志强
 * @date: 2022/2/2 17:38
 * @Copyright: 2021 . All rights reserved.
 *
 */
public class ExecuteFeignClient {

    private static Logger LOGGER = LoggerFactory.getLogger(ExecuteFeignClient.class);

    public static ResultEntity get(Supplier loader) {
        Object result = null;
        try {
            result = loader.get();
        } catch (Exception ee){
            LOGGER.warn("ExecuteFeignClient is error, {}", ee);
            throw new RuntimeException("call rest api failed");
        }
        ResultEntity resultEntity = JSONUtil.toBean(result.toString(), ResultEntity.class);
        if(resultEntity.getReturnCode() != 0){
            throw new RuntimeException(resultEntity.getReturnMessage());
        }
        return resultEntity;
    }



}
