package com.lg.dao.core.filter;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 数据权限规则
 * 对应表：sys_dp_rule
 *
 * <AUTHOR>
 */
@Data
@Table(name = "sys_dp_rule")
public class DataPermissionRule {

    /**
     * 主键
     */
    @Id
    private Long id;
    
    /**
     * 归属类型（0 系统 1角色 2用户）
     */
    private String ownType;
    
    /**
     * 对应的ownID
     */
    private String ownId;
    
    /**
     * 表格配置编码
     */
    private String tableCode;
    
    /**
     * 权限规则
     */
    private String dpRule;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除
     */
    private Boolean delFlag;
    
    /**
     * 删除人
     */
    private String deleteBy;
    
    /**
     * 删除时间
     */
    private Date deleteTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 是否启用
     */
    private String enabledFlag;
    
    // 兼容性字段 - 为了向后兼容保留
    /**
     * 规则名称（兼容字段）
     */
    private String ruleName;
    
    /**
     * 角色ID（兼容字段）
     */
    private String roleId;
    
    /**
     * 条件JSON（兼容字段）
     */
    private String conditionJson;
}