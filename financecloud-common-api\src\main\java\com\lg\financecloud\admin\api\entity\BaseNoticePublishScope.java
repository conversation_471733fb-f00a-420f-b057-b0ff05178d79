/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 通知发送范围
 *
 * <AUTHOR>
 * @date 2023-03-23 14:09:39
 */
@Data
@ApiModel(value = "通知发送范围")
public class  BaseNoticePublishScope   {
private static final long serialVersionUID = 1L;



    /**
     * 0 全体  1部门  2 角色 3 用户
     */
    @ApiModelProperty(value="0 全体  1部门  2 角色 3 用户")
    private String scopeType;
    /**
     * 具体发送对象可以是 deptId,roleId,userId
     */
    @ApiModelProperty(value="具体发送对象可以是 deptId,roleId,userId")
    private Long targetId;

    @TableField(exist = false)
    private String targetName;
    @TableField(exist = false)
    private List<Long>targetIds;

    }
