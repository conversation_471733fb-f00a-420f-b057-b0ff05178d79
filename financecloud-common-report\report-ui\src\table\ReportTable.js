/**
 * Created by <PERSON><PERSON><PERSON> on 2017-01-26.
 */
import * as utils from '../Utils.js';
import {afterRenderer} from './CellRenderer.js';
import buildMenuConfigure from './ContextMenu.js';
import Handsontable from 'handsontable';
import {alert} from '../MsgBox.js';

export default class ReportTable{
    constructor(container,callback){
        this.container=container;
        this.hot=new Handsontable(container,{
            startCols:1,
            startRows:1,
            fillHandle:{
                autoInsertRow:false
            },
            colHeaders:true,
            rowHeaders:true,
            autoColumnSize:false,
            autoRowSize:false,
            manualColumnResize:true,
            manualRowResize:true,
            maxColsNumber:700,
            outsideClickDeselects:false,
            // 启用单元格编辑功能
            editor: 'text',
            // 允许回车键编辑
            enterMoves: {row: 0, col: 0}
        });
        this.buildMenu();
        this.hot.addHook("afterRenderer",afterRenderer);
        this.setupCellEditingHooks();
        let file=utils.getParameter("_u");
        if(!file || file===null || file===''){
            file='classpath:template/template.ureport.xml';
        }else{
            window._reportFile=file;
        }
        this.cellsMap=new Map();
        this.loadFile(file,callback);
        this.hot.addHook('afterRowResize', function(currentRow,newSize) {
            let rowHeights=this.getSettings().rowHeights;
            let oldRowHeights=rowHeights.concat([]);
            let newRowHeights=rowHeights.concat([]);
            newRowHeights.splice(currentRow,1,newSize);
            this.updateSettings({
                rowHeights:newRowHeights,
                manualRowResize:newRowHeights
            });
            const _this=this;
            utils.undoManager.add({
                redo:function(){
                    rowHeights=_this.getSettings().rowHeights;
                    oldRowHeights=rowHeights.concat([]);
                    newRowHeights.splice(currentRow,1,newSize);
                    _this.updateSettings({
                        rowHeights:newRowHeights,
                        manualRowResize:newRowHeights
                    });
                    utils.setDirty();
                },
                undo:function(){
                    _this.updateSettings({
                        rowHeights:oldRowHeights,
                        manualRowResize:oldRowHeights
                    });
                    utils.setDirty();
                }
            });
            utils.setDirty();
        });
        this.hot.addHook('afterColumnResize',function(currentColumn,newSize){
            let colWidths=this.getSettings().colWidths;
            let newColWidths=colWidths.concat([]);
            let oldColWidths=colWidths.concat([]);
            newColWidths.splice(currentColumn,1,newSize);
            this.updateSettings({
                colWidths:newColWidths,
                manualColumnResize:newColWidths
            });
            const _this=this;
            utils.undoManager.add({
                redo:function(){
                    colWidths=_this.getSettings().colWidths;
                    newColWidths=colWidths.concat([]);
                    oldColWidths=colWidths.concat([]);
                    newColWidths.splice(currentColumn,1,newSize);
                    _this.updateSettings({
                        colWidths:newColWidths,
                        manualColumnResize:newColWidths
                    });
                    utils.setDirty();
                },
                undo:function(){
                    _this.updateSettings({
                        colWidths:oldColWidths,
                        manualColumnResize:oldColWidths
                    });
                    utils.setDirty();
                }
            });
            utils.setDirty();
        });
    }

    loadFile(file,callback){
        const _this=this;
        const url=window._server+"/designer/loadReport";
        $.ajax({
            url,
            type:'POST',
            data:{file},
            success:function(reportDef){
                _this.reportDef=reportDef;
                _this._buildReportData(reportDef);
                if(callback){
                    callback.call(_this,reportDef);
                }
                _this.hot.render();
                if(file!=='classpath:template/template.ureport.xml'){
                    _this.hot.context.fileInfo.setFile(file);
                }else{
                    _this.hot.context.fileInfo.setFile(`${window.i18n.table.report.tip}`);
                }
                if(reportDef.paper.bgImage){
                    $('.ht_master').css('background',`url(${reportDef.paper.bgImage}) 50px 26px no-repeat`);
                }else{
                    $('.ht_master').css('background','transparent');
                }
            },
            error:function(response){
                if(response && response.responseText){
                    alert("服务端错误："+response.responseText+"");
                }else{
                    alert(`${window.i18n.table.report.load}${file}${window.i18n.table.report.fail}`);
                }
            }
        });
    }

    _buildReportData(data){
        this.cellsMap.clear();
        const rows=data.rows;
        const rowHeights=[];
        for(let row of rows){
            const height=row.height;
            rowHeights.push(utils.pointToPixel(height));
        }
        const columns =data.columns;
        const colWidths=[];
        for(let col of columns){
            const width=col.width;
            colWidths.push(utils.pointToPixel(width));
        }
        const cellsMap=data.cellsMap;
        const dataArray=[],mergeCells=[];
        for(let row of rows){
            const rowData=[];
            for(let col of columns){
                let key=row.rowNumber+","+col.columnNumber;
                let cell=cellsMap[key];
                if(cell){
                    this.cellsMap.set(key,cell);
                    rowData.push(cell.value.value || "");
                    let rowspan=cell.rowSpan,colspan=cell.colSpan;
                    if(rowspan>0 || colspan>0){
                        if(rowspan===0)rowspan=1;
                        if(colspan===0)colspan=1;
                        mergeCells.push({
                            rowspan,
                            colspan,
                            row:row.rowNumber-1,
                            col:col.columnNumber-1
                        });
                    }
                }else{
                    rowData.push("");
                }
            }
            dataArray.push(rowData);
        }
        this.hot.loadData(dataArray);
        this.hot.updateSettings({
            colWidths,
            rowHeights,
            mergeCells,
            readOnly:false  // 改为允许编辑
        });
    }

    buildMenu(){
        this.hot.updateSettings({
            contextMenu: buildMenuConfigure()
        });
    }

    bindSelectionEvent(callback){
        const _this=this;
        Handsontable.hooks.add("afterSelectionEnd",function(rowIndex,colIndex,row2Index,col2Index){
            callback.call(_this,rowIndex,colIndex,row2Index,col2Index);
        },this.hot);
    }

    setupCellEditingHooks(){
        const _this = this;

        // 选中单元格后自动进入编辑模式
        this.hot.addHook('afterSelectionEnd', function(row, col, row2, col2) {
            // 只有选中单个单元格时才处理
            if (row === row2 && col === col2) {
                // 修复：使用正确的1-based坐标key
                const correctKey = (row + 1) + "," + (col + 1);
                const cellDef = _this.cellsMap.get(correctKey);

              //  console.log(`调试 - afterSelectionEnd: 选中单元格 (${row},${col}), key=${correctKey}, 找到cellDef:`, !!cellDef);

                if (!cellDef) {
                    // 只有在确实没有cellDef的情况下才创建新的简单单元格
                  //  console.log(`调试 - 为空白单元格创建新的simple cellDef: ${correctKey}`);
                    const newCellDef = {
                        rowNumber: row + 1,
                        columnNumber: col + 1,
                        value: {
                            type: 'simple',
                            value: ''
                        },
                        cellStyle: {},
                        expand: 'None'
                    };
                    _this.cellsMap.set(correctKey, newCellDef);
                    if (_this.hot.context) {
                        _this.hot.context.addCell(newCellDef);
                    }
                } else {
                    //console.log(`调试 - 找到现有cellDef，类型: ${cellDef.value ? cellDef.value.type : 'undefined'}`);
                }

                // 对于简单类型的单元格，准备编辑状态
                if (!cellDef || cellDef.value.type === 'simple') {
                    // 不自动开始编辑，等待用户输入
                    _this.readyToEdit = true;
                    _this.editRow = row;
                    _this.editCol = col;
                } else {
                    _this.readyToEdit = false;
                }
            }
        });

        // 监听键盘输入，当用户输入可打印字符时开始编辑
        this.hot.addHook('beforeKeyDown', function(event) {
            // 如果已经在编辑状态，不处理
            if (_this.hot.getActiveEditor() && _this.hot.getActiveEditor().isOpened()) {
                return;
            }

            // 检查是否准备好编辑
            if (_this.readyToEdit && event.key && event.key.length === 1 &&
                !event.ctrlKey && !event.altKey && !event.metaKey) {

                // 开始编辑并输入字符
                setTimeout(() => {
                    if (_this.hot.getActiveEditor() && !_this.hot.getActiveEditor().isOpened()) {
                        _this.hot.getActiveEditor().beginEditing();
                        // 清空原内容并输入新字符
                        setTimeout(() => {
                            const editor = _this.hot.getActiveEditor();
                            if (editor && editor.TEXTAREA) {
                                editor.TEXTAREA.value = event.key;
                                editor.TEXTAREA.setSelectionRange(1, 1);
                            }
                        }, 10);
                    }
                }, 10);
            }
        });

        // 在编辑开始前检查单元格类型
        this.hot.addHook('beforeEdit', function(row, col, prop, TD, originalValue) {
            // 修复：使用正确的1-based坐标key
            const cellDef = _this.cellsMap.get((row + 1) + "," + (col + 1));
            if (!cellDef) {
                return true; // 允许编辑新单元格
            }

            // 只允许编辑简单类型的单元格
            if (cellDef.value.type === 'simple') {
                return true;
            } else {
                // 对于复杂类型，静默阻止编辑（不显示弹窗）
                return false;
            }
        });

        // 编辑完成后更新数据模型
        this.hot.addHook('afterChange', function(changes, source) {
            if (source === 'edit' && changes) {
                changes.forEach(function([row, col, oldValue, newValue]) {
                    let cellDef = _this.cellsMap.get((row + 1) + "," + (col + 1));

                    if (!cellDef && newValue !== null && newValue !== undefined && newValue !== '') {
                        // 如果没有cellDef但有新值，创建一个
                        cellDef = {
                            rowNumber: row + 1,
                            columnNumber: col + 1,
                            value: {
                                type: 'simple',
                                value: newValue
                            },
                            cellStyle: {},
                            expand: 'None'
                        };
                        _this.cellsMap.set((row + 1) + "," + (col + 1), cellDef);
                        if (_this.hot.context) {
                            _this.hot.context.addCell(cellDef);
                        }
                        console.log(`编辑时自动创建单元格定义: ${_this.hot.context.getCellName(row, col)} = "${newValue}"`);
                    } else if (cellDef) {
                        // 更新现有cellDef
                        if (cellDef.value.type === 'simple') {
                            cellDef.value.value = newValue;
                        }
                    }

                    // 标记为已修改
                    if (typeof utils !== 'undefined' && typeof utils.setDirty === 'function') {
                        utils.setDirty();
                    }
                });
            }
        });

        // 编辑状态的视觉反馈
        this.hot.addHook('afterBeginEditing', function(row, col) {
            const cell = _this.hot.getCell(row, col);
            if (cell) {
                cell.classList.add('editing');
            }
        });

        // 编辑结束后清理样式
        this.hot.addHook('afterFinishEditing', function(row, col, newValue, ctrlDown) {
            const cell = _this.hot.getCell(row, col);
            if (cell) {
                cell.classList.remove('editing');
            }
        });
    }
};
