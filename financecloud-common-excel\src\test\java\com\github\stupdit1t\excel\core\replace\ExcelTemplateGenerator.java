package com.github.stupdit1t.excel.core.replace;

import com.github.stupdit1t.excel.core.ExcelHelper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel模板生成器 - 生成可视化的Excel模板文件
 */
public class ExcelTemplateGenerator {
    
    public static void main(String[] args) {
        try {
            // 生成包含原始模板和替换结果的Excel文件
            generateComparisonExcel();
            System.out.println("Excel模板对比文件已生成: excel_template_comparison.xlsx");
        } catch (Exception e) {
            System.err.println("生成Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成包含原始模板和替换结果的对比Excel文件
     */
    private static void generateComparisonExcel() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        
        // 创建原始模板工作表
        Sheet originalSheet = workbook.createSheet("原始模板");
        createOriginalTemplate(originalSheet);
        
        // 创建替换结果工作表
        Sheet resultSheet = workbook.createSheet("替换结果");
        createReplacedResult(resultSheet);
        
        // 创建说明工作表
        Sheet instructionSheet = workbook.createSheet("使用说明");
        createInstructionSheet(instructionSheet);
        
        // 保存文件
        try (FileOutputStream fileOut = new FileOutputStream("excel_template_comparison.xlsx")) {
            workbook.write(fileOut);
        }
        
        workbook.close();
    }
    
    /**
     * 创建原始模板
     */
    private static void createOriginalTemplate(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        
        // 创建标题样式
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleStyle.setFont(titleFont);
        
        // 创建变量样式
        CellStyle variableStyle = sheet.getWorkbook().createCellStyle();
        Font variableFont = sheet.getWorkbook().createFont();
        variableFont.setColor(IndexedColors.BLUE.getIndex());
        variableStyle.setFont(variableFont);
        
        // 创建循环标记样式
        CellStyle loopStyle = sheet.getWorkbook().createCellStyle();
        Font loopFont = sheet.getWorkbook().createFont();
        loopFont.setColor(IndexedColors.RED.getIndex());
        loopFont.setBold(true);
        loopStyle.setFont(loopFont);
        
        int rowIndex = 0;
        
        // 第0行：公司名称
        Row companyRow = sheet.createRow(rowIndex++);
        Cell companyCell = companyRow.createCell(0);
        companyCell.setCellValue("${companyName}");
        companyCell.setCellStyle(variableStyle);
        
        // 第1行：报表日期
        Row dateRow = sheet.createRow(rowIndex++);
        Cell dateCell = dateRow.createCell(0);
        dateCell.setCellValue("报表日期: ${reportDate}");
        dateCell.setCellStyle(variableStyle);
        
        // 第2行：空行
        sheet.createRow(rowIndex++);
        
        // 第3行：表头
        Row headerRow = sheet.createRow(rowIndex++);
        headerRow.createCell(0).setCellValue("员工编号");
        headerRow.createCell(1).setCellValue("姓名");
        headerRow.createCell(2).setCellValue("部门");
        headerRow.createCell(3).setCellValue("薪资");
        
        // 第4行：循环开始
        Row startRow = sheet.createRow(rowIndex++);
        Cell startCell = startRow.createCell(0);
        startCell.setCellValue("${#foreach employees}");
        startCell.setCellStyle(loopStyle);
        
        // 第5行：循环体
        Row bodyRow = sheet.createRow(rowIndex++);
        Cell empNoCell = bodyRow.createCell(0);
        empNoCell.setCellValue("${item.empNo}");
        empNoCell.setCellStyle(variableStyle);
        
        Cell nameCell = bodyRow.createCell(1);
        nameCell.setCellValue("${item.name}");
        nameCell.setCellStyle(variableStyle);
        
        Cell deptCell = bodyRow.createCell(2);
        deptCell.setCellValue("${item.department}");
        deptCell.setCellStyle(variableStyle);
        
        Cell salaryCell = bodyRow.createCell(3);
        salaryCell.setCellValue("${item.salary}");
        salaryCell.setCellStyle(variableStyle);
        
        // 第6行：循环结束
        Row endRow = sheet.createRow(rowIndex++);
        Cell endCell = endRow.createCell(0);
        endCell.setCellValue("${/foreach}");
        endCell.setCellStyle(loopStyle);
        
        // 第7行：空行
        sheet.createRow(rowIndex++);
        
        // 第8行：总计
        Row totalRow = sheet.createRow(rowIndex++);
        Cell totalCell = totalRow.createCell(0);
        totalCell.setCellValue("总计: ${totalCount} 人");
        totalCell.setCellStyle(variableStyle);
    }
    
    /**
     * 创建替换结果示例
     */
    private static void createReplacedResult(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        
        int rowIndex = 0;
        
        // 第0行：公司名称（已替换）
        Row companyRow = sheet.createRow(rowIndex++);
        companyRow.createCell(0).setCellValue("科技有限公司");
        
        // 第1行：报表日期（已替换）
        Row dateRow = sheet.createRow(rowIndex++);
        dateRow.createCell(0).setCellValue("报表日期: 2024-01-15");
        
        // 第2行：空行
        sheet.createRow(rowIndex++);
        
        // 第3行：表头
        Row headerRow = sheet.createRow(rowIndex++);
        headerRow.createCell(0).setCellValue("员工编号");
        headerRow.createCell(1).setCellValue("姓名");
        headerRow.createCell(2).setCellValue("部门");
        headerRow.createCell(3).setCellValue("薪资");
        
        // 第4-6行：员工数据（循环展开后的结果）
        String[][] employees = {
            {"E001", "张三", "开发部", "8000.0"},
            {"E002", "李四", "产品部", "7000.0"},
            {"E003", "王五", "测试部", "6500.0"}
        };
        
        for (String[] employee : employees) {
            Row empRow = sheet.createRow(rowIndex++);
            empRow.createCell(0).setCellValue(employee[0]);
            empRow.createCell(1).setCellValue(employee[1]);
            empRow.createCell(2).setCellValue(employee[2]);
            empRow.createCell(3).setCellValue(employee[3]);
        }
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 总计（已替换）
        Row totalRow = sheet.createRow(rowIndex++);
        totalRow.createCell(0).setCellValue("总计: 3 人");
    }
    
    /**
     * 创建使用说明工作表
     */
    private static void createInstructionSheet(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 8000);
        sheet.setColumnWidth(1, 12000);
        
        // 创建标题样式
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        
        // 创建子标题样式
        CellStyle subTitleStyle = sheet.getWorkbook().createCellStyle();
        Font subTitleFont = sheet.getWorkbook().createFont();
        subTitleFont.setBold(true);
        subTitleFont.setFontHeightInPoints((short) 12);
        subTitleStyle.setFont(subTitleFont);
        
        int rowIndex = 0;
        
        // 标题
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("Excel模板循环功能使用说明");
        titleCell.setCellStyle(titleStyle);
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 功能介绍
        Row introRow = sheet.createRow(rowIndex++);
        Cell introCell = introRow.createCell(0);
        introCell.setCellValue("功能介绍:");
        introCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("本功能支持在Excel模板中使用循环语法，可以根据数据列表动态生成多行内容。");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 语法说明
        Row syntaxRow = sheet.createRow(rowIndex++);
        Cell syntaxCell = syntaxRow.createCell(0);
        syntaxCell.setCellValue("语法说明:");
        syntaxCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("1. 普通变量: ${variableName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("2. 循环开始: ${#foreach listName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("3. 循环变量: ${item.propertyName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("4. 循环结束: ${/foreach}");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 使用示例
        Row exampleRow = sheet.createRow(rowIndex++);
        Cell exampleCell = exampleRow.createCell(0);
        exampleCell.setCellValue("Java代码示例:");
        exampleCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("ExcelHelper.opsReplace()");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .from(inputStream)");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"companyName\", \"科技有限公司\")");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"reportDate\", \"2024-01-15\")");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .loop(\"employees\", employeeList)");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"totalCount\", employeeList.size())");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .replaceTo(outputStream);");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 注意事项
        Row noteRow = sheet.createRow(rowIndex++);
        Cell noteCell = noteRow.createCell(0);
        noteCell.setCellValue("注意事项:");
        noteCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("1. 循环标记必须独占一行");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("2. 循环体中的变量使用item.属性名格式");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("3. 支持嵌套对象属性访问");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("4. 循环会自动复制模板行并替换变量");
    }
}