package com.lg.dao.core;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 分页对象
 */
@Data
public class Page<T> {
    private int pageNum;        // 当前页码
    private int pageSize;       // 每页大小
    private long total;         // 总记录数
    private int pages;          // 总页数
    private List<T> records;    // 当前页数据
    private boolean hasNext;    // 是否有下一页
    private boolean hasPrev;    // 是否有上一页
    
    public Page() {
        this.pageNum = 1;
        this.pageSize = 10;
        this.total = 0;
        this.pages = 0;
        this.records = Collections.emptyList();
        this.hasNext = false;
        this.hasPrev = false;
    }

    public Page(int pageNum, int pageSize) {
        this.pageNum = Math.max(pageNum, 1);
        this.pageSize = Math.max(pageSize, 1);
        this.records = Collections.emptyList();
    }

    public void setTotal(long total) {
        this.total = total;
        // 确保pageSize不为0，避免除零错误
        if (pageSize > 0) {
            this.pages = (int) ((total + pageSize - 1) / pageSize);
        } else {
            this.pages = 0;
        }
        this.hasNext = pageNum < pages;
        this.hasPrev = pageNum > 1;
    }

    public int getOffset() {
        // 确保pageSize不为0，避免计算错误
        if (pageSize <= 0) {
            return 0;
        }
        return (pageNum - 1) * pageSize;
    }
} 