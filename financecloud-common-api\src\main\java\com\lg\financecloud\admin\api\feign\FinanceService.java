package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.dto.AddAccountVo;
import com.lg.financecloud.admin.api.dto.FcDeptVo;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-18
 * <p>
 * 远程FinanceService调用接口
 */
@FeignClient(contextId = "financeService", value = ServiceNameConstants.FINANCE_SERVICE)
public interface FinanceService {

	/**
	 * 添加app应用
	 *
	 * @param tenantId
	 * @param from
	 * @return
	 */
	@PostMapping("/fcenterpriseaccount/addAccountInner")
	R addAccountInner(@RequestBody AddAccountVo accountVo, @RequestHeader(SecurityConstants.FROM) String from);
	/**
	 * 新增财务辅助核算部门
	 * */
	@PostMapping("/fcdept")
	R addFcdept(@RequestBody FcDeptVo accountVo, @RequestHeader(SecurityConstants.FROM) String from);
	/**
	 * 删除财务辅助核算部门
	 * */
	@DeleteMapping("/fcdept/removeByid")
	R removeById(@RequestParam("ids") List<Long> ids, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 更改财务辅助核算部门
	 * */
	@PutMapping("/fcdept")
	R updateFcdept(@RequestBody FcDeptVo accountVo, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 发票上传
	 * */
	@PostMapping(value ="/invoiceUploadTask/uploadInvoiceFileInner",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	R uploadInvoiceFileInner(@RequestPart("file") MultipartFile[] files,
							 @RequestParam("type") Integer type,
							 @RequestParam("accountId") Long accountId,
							 @RequestParam("tenantId") Integer tenantId,
							 @RequestParam("ids") Long []ids,
							 @RequestParam("jsonStr")String jsonStr );


	/**
	 * 机器人导出发票清单
	 * @param file
	 * @param type
	 * @param templateType
	 * @param taskId
	 * @return
	 */
	@PostMapping(value ="/fcinputinvoice/importInvoice2Inner",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public R importInvoice2Inner(MultipartFile file,
								 @RequestParam(value = "type", required = false, defaultValue = "1") String type,
								 @RequestParam(value = "templateType", required = false, defaultValue = "4") Integer templateType,
								 @RequestParam(value = "taskId") Long taskId,
								 @RequestParam(value = "accountId") Long accountId,
								 @RequestParam(value = "tenantId") Integer tenantId);
}
