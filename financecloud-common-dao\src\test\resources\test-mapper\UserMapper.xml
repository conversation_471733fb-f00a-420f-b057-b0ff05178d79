<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lg.dao.test.UserMapper">

    <select id="findById" resultType="com.lg.dao.mybatis.MybatisNativeParserTest$TestUser">
        SELECT id, name, age, email FROM t_user WHERE id = #{id}
    </select>

    <select id="findByCondition" resultType="com.lg.dao.mybatis.MybatisNativeParserTest$TestUser">
        SELECT id, name, age, email FROM t_user
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="age != null">
                AND age = #{age}
            </if>
        </where>
    </select>

    <select id="findByIds" resultType="com.lg.dao.mybatis.MybatisNativeParserTest$TestUser">
        SELECT id, name, age, email FROM t_user WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <insert id="insertUser">
        INSERT INTO t_user (id, name, age, email) VALUES (#{id}, #{name}, #{age}, #{email})
    </insert>

    <update id="updateUser">
        UPDATE t_user SET name = #{name}, age = #{age} WHERE id = #{id}
    </update>

    <delete id="deleteUser">
        DELETE FROM t_user WHERE id = #{id}
    </delete>

    <select id="findUsersWithComplexCondition" resultType="com.lg.dao.mybatis.MybatisNativeParserTest$TestUser">
        SELECT id, name, age, email FROM t_user
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="minAge != null and maxAge != null">
                AND age BETWEEN #{minAge} AND #{maxAge}
            </if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="XXXXXX" resultType="com.lg.dao.mybatis.MybatisNativeParserTest$TestUser">

        SELECT
        wft.id AS id,
        wft.tenant_id AS tenantId,
        wft.instance_id AS instanceId,
        wft.business_key AS businessKey,
        wft.node_id AS nodeId,
        wft.node_name AS nodeName,
        wft.assignee_id AS assigneeId,
        wft.assignee_name AS assigneeName,
        wft.`STATUS` AS `status`,
        wft.action AS action,
        wft.`COMMENT` AS `comment`,
        wft.form_data AS formData,
        wft.`VARIABLES` AS `variables`,
        wft.priority AS priority,
        wft.due_date AS dueDate,
        wft.claim_time AS claimTime,
        wft.complete_time AS completeTime,
        wft.create_time AS createTime,
        wft.update_time AS updateTime,
        wfi.title,
        wfd.name processName,
        wfd.category,
        wfc.category_code as categoryCode,
        wfc.category_name as categoryName,
        wfd.pc_form_url pcFormUrl,
        wfd.mobile_form_url mobileFormUrl,
        wfi.status AS instanceStatus
        FROM
        workflow_task wft
        INNER JOIN workflow_instance wfi ON (wfi.id = wft.instance_id)
        INNER JOIN workflow_definition wfd ON (wfd.id = wfi.definition_id)
        LEFT JOIN workflow_category wfc ON (wfd.category = wfc.id)
        WHERE
        wft.assignee_id = #{userId}
        AND wft.node_type = #{nodeType}
        <if test="instanceStatus != null">
            AND wfi.status = #{instanceStatus}
        </if>
        <if test="title != null">
            AND wfi.title LIKE #{title}
        </if>
        <if test="category != null">
            AND wfd.category = #{category}
        </if>
        <if test="startTime != null">
            AND wft.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND wft.create_time <![CDATA[= #{endTime}
        ]]></if>
        ORDER BY
        wft.create_time DESC
    </select>

</mapper>
