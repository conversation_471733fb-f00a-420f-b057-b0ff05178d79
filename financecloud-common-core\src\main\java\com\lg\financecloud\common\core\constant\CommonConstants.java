/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.common.core.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017/10/29
 */
public interface CommonConstants {

    /**
     * header 中租户ID
     */
    String TENANT_ID = "TENANT-ID";

    /****
     * 账套ID
     */
    String ACCOUNT_ID = "ACCOUNT-ID";

    /**
     * header 中版本信息
     */
    String VERSION = "VERSION";

    /**
     * 租户ID
     */
    Integer TENANT_ID_1 = 1;

    /**
     * 删除
     */
    String STATUS_DEL = "1";

    /**
     * 正常
     */
    String STATUS_NORMAL = "0";

    /**
     * 锁定
     */
    String STATUS_LOCK = "9";

    /**
     * 菜单树根节点
     */
    Integer MENU_TREE_ROOT_ID = -1;

    /**
     * 编码
     */
    String UTF8 = "UTF-8";

    /**
     * 前端工程名
     */
    String FRONT_END_PROJECT = "cloudx-ui";

    /**
     * 后端工程名
     */
    String BACK_END_PROJECT = "cloudx";

    /**
     * 公共参数
     */
    String PIG_PUBLIC_PARAM_KEY = "PIG_PUBLIC_PARAM_KEY";

    /**
     * 成功标记
     */
    Integer SUCCESS = 0;

    /**
     * 失败标记
     */
    Integer FAIL = 1;

    Integer AUTH_FAIL = 401;

    /**
     * 默认存储bucket
     */
    String BUCKET_NAME = "cloudx";

    /**
     * 滑块验证码
     */
    String IMAGE_CODE_TYPE = "blockPuzzle";

    /**
     * 凭证类型
     */
    String REMEMBER = "记";
    /**
     * 科目编码规则(小企业)
     */
    String accountSystemCode = "4-3-3-3-3-3";
    /**
    /**
     * 科目编码规则(企业准则)
     */
    String accountSystemCodeYb = "4-3-3-3-3-3";
    /**
     * 科目编码规则(民非)
     */
    String accountSystemCodeMf = "4-3-3-2-2-2";

    /**
     * 科目编码规则(村集体)
     */
    String accountSystemCodeCjt = "3-2-2-2-2-2";


    String subject_cate_by_system_ = "subject_cate_by_system_";
    /**
     * 横杠
     */
    String horizontalBar = "-";
    /**
     * 逗号
     */
    String comma = ",";
    /**
     * 空格
     */
    String space = " ";
    /**
     * 点
     */
    String spot = ".";

    String SUBJECT_SECOND_CATE_PIFIX = "subject_seconds_cate_";

    String VOUCHER_RULE_CATEGORY = "origin_voucher_rule_category";


    String VOUCHER_WORD_JI = "记";

    String JIAN_CHU_COST = "5000";

    String VOUCHER_RULE_ITEM_JTGZ = "jtgz";

    String VOUCHER_RULE_ITEM_FFGZ = "ffgz";

    String VOUCHER_RULE_ITEM_SYJZ = "syzj";

    String ACCOUNTING_PROFIT_LOSS_CATE_CONFIG_PIFIX = "ACCOUNTING_PROFIT_LOSS_CATE_CONFIG_";

    String ASSETS_SUBJECT_DEFAULT_CONFIG_PIFIX = "ASSETS_SUBJECT_DEFAULT_CONFIG_";

    String account_system = "account_system";

    String TENANT_ROLE_CODE = "ROLE_ADMIN";

    String INDUSTRY = "INDUSTRY";

    String TAX_POLICY = "TAX_POLICY";


    String ERP_ENABLE="ERP_ENABLE";
    String ERP_ENABLE_DEFAULT_VAL="true";





}
