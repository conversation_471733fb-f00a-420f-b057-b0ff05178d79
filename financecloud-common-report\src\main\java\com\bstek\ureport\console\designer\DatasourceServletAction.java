/*******************************************************************************
 * Copyright 2017 Bstek
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.console.designer;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;

import com.bstek.ureport.definition.dataset.SqlDatasetDefinition;
import com.zaxxer.hikari.pool.HikariProxyConnection;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.lg.financecloud.common.core.util.SpringContextHolder;
import okhttp3.*;
import org.ssssssss.magicapi.core.model.JsonBean;
import org.ssssssss.magicapi.core.service.MagicAPIService;
import java.util.concurrent.TimeUnit;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.*;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterUtils;
import org.springframework.jdbc.core.namedparam.ParsedSql;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.jdbc.support.JdbcUtils;

import com.bstek.ureport.Utils;
import com.bstek.ureport.build.Context;
import com.bstek.ureport.console.RenderPageServletAction;
import com.bstek.ureport.console.exception.ReportDesignException;
import com.bstek.ureport.definition.dataset.Field;
import com.bstek.ureport.definition.datasource.BuildinDatasource;
import com.bstek.ureport.definition.datasource.DataType;
import com.bstek.ureport.expression.ExpressionUtils;
import com.bstek.ureport.expression.model.Expression;
import com.bstek.ureport.expression.model.data.ExpressionData;
import com.bstek.ureport.expression.model.data.ObjectExpressionData;
import com.bstek.ureport.utils.ProcedureUtils;

/**
 * <AUTHOR>
 * @since 2017年2月6日
 */
public class DatasourceServletAction extends RenderPageServletAction {

	@Override
	public void execute(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String method=retriveMethod(req);
		if(method!=null){
			invokeMethod(method, req, resp);
		}
	}

	public void loadBuildinDatasources(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		List<String> datasources=new ArrayList<String>();
		for(BuildinDatasource datasource:Utils.getBuildinDatasources()){
			datasources.add(datasource.name());
		}
		writeObjectToJson(resp, datasources);
	}

	public void loadMethods(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String beanId=req.getParameter("beanId");
		Object obj=applicationContext.getBean(beanId);
		Class<?> clazz=obj.getClass();
		Method[] methods=clazz.getMethods();
		List<String> result=new ArrayList<String>();
		for(Method method:methods){
			Class<?>[] types=method.getParameterTypes();
			if(types.length!=3){
				continue;
			}
			Class<?> typeClass1=types[0];
			Class<?> typeClass2=types[1];
			Class<?> typeClass3=types[2];
			if(!String.class.isAssignableFrom(typeClass1)){
				continue;
			}
			if(!String.class.isAssignableFrom(typeClass2)){
				continue;
			}
			if(!Map.class.isAssignableFrom(typeClass3)){
				continue;
			}
			result.add(method.getName());
		}
		writeObjectToJson(resp, result);
	}

	public void buildClass(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String clazz=req.getParameter("clazz");
		List<Field> result=new ArrayList<Field>();
		try{
			Class<?> targetClass=Class.forName(clazz);
			PropertyDescriptor[] propertyDescriptors=PropertyUtils.getPropertyDescriptors(targetClass);
			for(PropertyDescriptor pd:propertyDescriptors){
				String name=pd.getName();
				if("class".equals(name)){
					continue;
				}
				result.add(new Field(name));
			}
			writeObjectToJson(resp, result);
		}catch(Exception ex){
			throw new ReportDesignException(ex);
		}
	}

	public void buildDatabaseTables(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		Connection conn=null;
		ResultSet rs = null;
		try{
			//
			conn=buildConnection(req);
			DatabaseMetaData metaData = conn.getMetaData();
			String url = metaData.getURL();
			String schema = null;
			if (url.toLowerCase().contains("oracle")) {
				schema = metaData.getUserName();
			}else{
				schema= conn.getCatalog();
			}

			List<Map<String,String>> tables = new ArrayList<Map<String,String>>();
			rs = metaData.getTables(schema, schema, "%", new String[] { "TABLE","VIEW" });
			while (rs.next()) {
				Map<String,String> table = new HashMap<String,String>();
				table.put("name",rs.getString("TABLE_NAME"));
				table.put("type",rs.getString("TABLE_TYPE"));
				tables.add(table);
			}
			writeObjectToJson(resp, tables);
		}catch(Exception ex){
			throw new ServletException(ex);
		}finally{
			JdbcUtils.closeResultSet(rs);
			JdbcUtils.closeConnection(conn);
		}
	}

	public void buildFields(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String sql=req.getParameter("sql");
		String parameters=req.getParameter("parameters");
		Connection conn=null;
		final List<Field> fields=new ArrayList<Field>();
		try{
			conn=buildConnection(req);
			Map<String, Object> map = buildParameters(parameters);
			sql=parseSql(sql, map);
			SqlDatasetDefinition sqlDatasetDefinition = new SqlDatasetDefinition();
			sqlDatasetDefinition.setSql(sql);
			sql = sqlDatasetDefinition.parseSql(map);
			if(ProcedureUtils.isProcedure(sql)){
				List<Field> fieldsList = ProcedureUtils.procedureColumnsQuery(sql, map, conn);
				fields.addAll(fieldsList);
			}else{
				DataSource dataSource=new SingleConnectionDataSource(conn,false);
				JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
				jdbcTemplate.query(sql, sqlDatasetDefinition.getParams(),new ResultSetExtractor<List<Map<String, Object>>>() {
					@Override
					public List<Map<String, Object>> extractData(ResultSet rs) throws SQLException, DataAccessException {
						List<Map<String, Object>> list = new LinkedList<Map<String, Object>>();
						ResultSetMetaData metadata = rs.getMetaData();
						int count = metadata.getColumnCount();
						for (int i = 1; i <= count; i++) {
							String columnName = metadata.getColumnLabel(i);
							fields.add(new Field(columnName));
						}
						return null;
					}
				});
			}
			writeObjectToJson(resp, fields);
		}catch(Exception ex){
			throw new ReportDesignException(ex);
		}finally{
			JdbcUtils.closeConnection(conn);
		}
	}

	protected PreparedStatementCreator getPreparedStatementCreator(String sql, SqlParameterSource paramSource) {
		ParsedSql parsedSql = NamedParameterUtils.parseSqlStatement(sql);
		String sqlToUse = NamedParameterUtils.substituteNamedParameters(parsedSql, paramSource);
		Object[] params = NamedParameterUtils.buildValueArray(parsedSql, paramSource, null);
		List<SqlParameter> declaredParameters = NamedParameterUtils.buildSqlParameterList(parsedSql, paramSource);
		PreparedStatementCreatorFactory pscf = new PreparedStatementCreatorFactory(sqlToUse, declaredParameters);
		return pscf.newPreparedStatementCreator(params);
	}

	public void previewData(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String sql=req.getParameter("sql");
		String parameters=req.getParameter("parameters");
		Map<String, Object> map = buildParameters(parameters);
		SqlDatasetDefinition sqlDatasetDefinition = new SqlDatasetDefinition();
		sqlDatasetDefinition.setSql(sql);
		sql = sqlDatasetDefinition.parseSql(map);
		Connection conn=null;
		int row = 100;
		List<String> fields=new ArrayList<String>();
		try{
			conn=buildConnection(req);
			List<Map<String,Object>> list=null;
			if(ProcedureUtils.isProcedure(sql)){
				list=ProcedureUtils.procedureQuery(sql, map, conn);
			}else{
				DataSource dataSource=new SingleConnectionDataSource(conn,false);
				JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
//				list=jdbc.queryForList(sql, map);

				list = jdbcTemplate.query(sql, sqlDatasetDefinition.getParams(),new ResultSetExtractor<List<Map<String, Object>>>() {
					@Override
					public List<Map<String, Object>> extractData(ResultSet rs) throws SQLException, DataAccessException {
						List<Map<String, Object>> list = new LinkedList<Map<String, Object>>();
						ResultSetMetaData metadata = rs.getMetaData();
						int count = metadata.getColumnCount();
						for (int i = 1; i <= count; i++) {
							String columnName = metadata.getColumnLabel(i);
							fields.add(columnName);
						}
						int index = 0;
						while (rs.next() && index < row) {
							int initialCapacity = (int) ((float) fields.size() / 0.75F + 1.0F);
							HashMap<String, Object> map = new HashMap<String, Object>(initialCapacity);
							int i = 1;
							for (String field : fields) {
								Object value = JdbcUtils.getResultSetValue(rs, i);
								map.put(field, value);
								i++;
							}
							index++;
							list.add(map);
						}
						return list;
					}
				});
			}
			int size=list.size();
			int currentTotal=size;
			if(currentTotal>500){
				currentTotal=500;
			}
			List<Map<String,Object>> ls=new ArrayList<Map<String,Object>>();
			for(int i=0;i<currentTotal;i++){
				ls.add(list.get(i));
			}
			DataResult result=new DataResult();
//			List<String> fields=new ArrayList<String>();
//			if(size>0){
//				Map<String,Object> item=list.get(0);
//				for(String name:item.keySet()){
//					fields.add(name);
//				}
//			}
			result.setFields(fields);
			result.setCurrentTotal(currentTotal);
			result.setData(ls);
			result.setTotal(size);
			writeObjectToJson(resp, result);
		}catch(Exception ex){
			throw new ServletException(ex);
		}finally{
			if(conn!=null){
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}

	private String parseSql(String sql,Map<String,Object> parameters){
		sql=sql.trim();
		Context context=new Context(applicationContext, parameters);
		if(sql.startsWith(ExpressionUtils.EXPR_PREFIX) && sql.endsWith(ExpressionUtils.EXPR_SUFFIX)){
			sql=sql.substring(2, sql.length()-1);
			Expression expr=ExpressionUtils.parseExpression(sql);
			sql=executeSqlExpr(expr,context);
			return sql;
		}else{
			String sqlForUse=sql;
			Pattern pattern=Pattern.compile("\\$\\{.*?\\}");
			Matcher matcher=pattern.matcher(sqlForUse);
			while(matcher.find()){
				String substr=matcher.group();
				String sqlExpr=substr.substring(2,substr.length()-1);
				Expression expr=ExpressionUtils.parseExpression(sqlExpr);
				String result=executeSqlExpr(expr, context);
				sqlForUse=sqlForUse.replace(substr, result);
			}
			Utils.logToConsole("DESIGN SQL:"+sqlForUse);
			return sqlForUse;
		}
	}

	private String executeSqlExpr(Expression sqlExpr,Context context){
		String sqlForUse=null;
		ExpressionData<?> exprData=sqlExpr.execute(null, null, context);
		if(exprData instanceof ObjectExpressionData){
			ObjectExpressionData data=(ObjectExpressionData)exprData;
			Object obj=data.getData();
			if(obj!=null){
				String s=obj.toString();
				s=s.replaceAll("\\\\", "");
				sqlForUse=s;
			}
		}
		return sqlForUse;
	}

	public void testConnection(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String username=req.getParameter("username");
		String password=req.getParameter("password");
		String driver=req.getParameter("driver");
		String url=req.getParameter("url");
		Connection conn=null;
		Map<String,Object> map=new HashMap<String,Object>();
		try{
			Class.forName(driver);
			conn=DriverManager.getConnection(url, username, password);
			map.put("result", true);
		}catch(Exception ex){
			map.put("error", ex.toString());
			map.put("result", false);
		}finally{
			if(conn!=null){
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		writeObjectToJson(resp, map);
	}

	@SuppressWarnings("unchecked")
	private Map<String, Object> buildParameters(String parameters) throws IOException, JsonParseException, JsonMappingException {
		Map<String,Object> map=new HashMap<String,Object>();
		if(StringUtils.isBlank(parameters)){
			return map;
		}
		ObjectMapper mapper=new ObjectMapper();
		List<Map<String,Object>> list=mapper.readValue(parameters, ArrayList.class);
		for(Map<String,Object> param:list){
			String name=param.get("name").toString();
			DataType type=DataType.valueOf(param.get("type").toString());
			String defaultValue=(String)param.get("defaultValue");
			if(defaultValue==null || defaultValue.equals("")){
				switch(type){
					case Boolean:
						map.put(name, false);
					case Date:
						map.put(name, new Date());
					case Float:
						map.put(name, new Float(0));
					case Integer:
						map.put(name, 0);
					case String:
						if(defaultValue!=null && defaultValue.equals("")){
							map.put(name, "");
						}else{
							map.put(name, "null");
						}
						break;
					case List:
						map.put(name, new ArrayList<Object>());
				}
			}else{
				map.put(name, type.parse(defaultValue));
			}
		}
		return map;
	}

	private Connection buildConnection(HttpServletRequest req) throws Exception{
		String type=req.getParameter("type");
		if(type.equals("jdbc")){
			String username=req.getParameter("username");
			String password=req.getParameter("password");
			String driver=req.getParameter("driver");
			String url=req.getParameter("url");

			Class.forName(driver);
			Connection conn=DriverManager.getConnection(url, username, password);
			return conn;
		}else{
			String name=req.getParameter("name");
			Connection conn=Utils.getBuildinConnection(name);
			if(conn==null){
				throw new ReportDesignException("Buildin datasource ["+name+"] not exist.");
			}
			return conn;
		}
	}

	/**
	 * 测试MagicApi数据源连接
	 */
	public void testMagicApiConnection(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String name = req.getParameter("name");
		String baseUrl = req.getParameter("baseUrl");
		String apiPath = req.getParameter("apiPath");
		String headers = req.getParameter("headers");

		Map<String, Object> result = new HashMap<>();

		try {
			// 解析请求头
			Map<String, String> headerMap = new HashMap<>();
			if (headers != null && !headers.trim().isEmpty()) {
				headerMap = JSON.parseObject(headers, Map.class);
			}

			// 获取MagicApi服务
			MagicAPIService magicAPIService = SpringContextHolder.getBean(MagicAPIService.class);
			if (magicAPIService == null) {
				result.put("success", false);
				result.put("message", "MagicApi服务未配置");
				writeObjectToJson(resp, result);
				return;
			}

			// 测试连接 - 调用一个简单的测试接口
			String testApiPath = apiPath + "/test";
			JsonBean testResult = magicAPIService.call("get", testApiPath, new HashMap<>());

			if (testResult != null) {
				result.put("success", true);
				result.put("message", "连接测试成功");
			} else {
				result.put("success", false);
				result.put("message", "连接测试失败：无响应");
			}

		} catch (Exception e) {
			result.put("success", false);
			result.put("message", "连接测试失败：" + e.getMessage());
		}

		writeObjectToJson(resp, result);
	}

	/**
	 * 测试RestApi数据源连接
	 */
	public void testRestApiConnection(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String name = req.getParameter("name");
		String baseUrl = req.getParameter("baseUrl");
		String authType = req.getParameter("authType");
		String authToken = req.getParameter("authToken");
		String timeoutStr = req.getParameter("timeout");
		String headers = req.getParameter("headers");

		int timeout = 30000;
		if (timeoutStr != null && !timeoutStr.trim().isEmpty()) {
			timeout = Integer.parseInt(timeoutStr);
		}

		Map<String, Object> result = new HashMap<>();

		try {
			// 解析请求头
			Map<String, String> headerMap = new HashMap<>();
			if (headers != null && !headers.trim().isEmpty()) {
				headerMap = JSON.parseObject(headers, Map.class);
			}

			// 构建请求头
			Map<String, String> requestHeaders = buildRequestHeaders(authType, authToken, headerMap);

			// 创建HTTP客户端
			OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(timeout, TimeUnit.MILLISECONDS)
				.readTimeout(timeout, TimeUnit.MILLISECONDS)
				.writeTimeout(timeout, TimeUnit.MILLISECONDS)
				.build();

			// 构建测试请求 - 通常测试根路径或健康检查端点
			String testUrl = baseUrl;
			if (!testUrl.endsWith("/")) {
				testUrl += "/";
			}

			Request.Builder requestBuilder = new Request.Builder().url(testUrl);

			// 添加请求头
			for (Map.Entry<String, String> entry : requestHeaders.entrySet()) {
				requestBuilder.addHeader(entry.getKey(), entry.getValue());
			}

			Request request = requestBuilder.get().build();

			// 执行请求
			try (Response response = client.newCall(request).execute()) {
				if (response.isSuccessful()) {
					result.put("success", true);
					result.put("message", "连接测试成功");
				} else {
					result.put("success", false);
					result.put("message", "连接测试失败：HTTP " + response.code() + " " + response.message());
				}
			}

		} catch (Exception e) {
			result.put("success", false);
			result.put("message", "连接测试失败：" + e.getMessage());
		}

		writeObjectToJson(resp, result);
	}

	/**
	 * 测试MagicApi数据集
	 */
	public void testMagicApiDataset(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String baseUrl = req.getParameter("baseUrl");
		String apiPath = req.getParameter("apiPath");
		String apiMethod = req.getParameter("apiMethod");
		String httpMethod = req.getParameter("httpMethod");
		String headers = req.getParameter("headers");

		if (httpMethod == null || httpMethod.trim().isEmpty()) {
			httpMethod = "GET";
		}

		Map<String, Object> result = new HashMap<>();

		try {
			// 解析请求头
			Map<String, String> headerMap = new HashMap<>();
			if (headers != null && !headers.trim().isEmpty()) {
				headerMap = JSON.parseObject(headers, Map.class);
			}

			// 获取MagicApi服务
			MagicAPIService magicAPIService = SpringContextHolder.getBean(MagicAPIService.class);
			if (magicAPIService == null) {
				result.put("success", false);
				result.put("message", "MagicApi服务未配置");
				writeObjectToJson(resp, result);
				return;
			}

			// 构建完整的API路径
			String fullApiPath = apiPath + apiMethod;

			// 调用MagicApi
			JsonBean apiResult = magicAPIService.call(httpMethod.toLowerCase(), fullApiPath, new HashMap<>());

			if (apiResult == null || apiResult.getCode() != 0) {
				result.put("success", false);
				result.put("message", "调用MagicApi失败: " +
					(apiResult != null ? apiResult.getMessage() : "返回结果为空"));
				writeObjectToJson(resp, result);
				return;
			}

			// 获取数据
			Object data = apiResult.getData();
			List<Object> list;

			if (data instanceof List) {
				list = (List<Object>) data;
			} else {
				// 如果不是列表，包装成单元素列表
				list = new java.util.ArrayList<>();
				list.add(data);
			}

			// 解析字段信息
			List<Map<String, Object>> fields = extractFieldsFromData(list);

			result.put("success", true);
			result.put("message", "数据集测试成功");
			result.put("data", list);
			result.put("fields", fields);

		} catch (Exception e) {
			result.put("success", false);
			result.put("message", "数据集测试失败：" + e.getMessage());
		}

		writeObjectToJson(resp, result);
	}

	/**
	 * 测试RestApi数据集
	 */
	public void testRestApiDataset(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String baseUrl = req.getParameter("baseUrl");
		String authType = req.getParameter("authType");
		String authToken = req.getParameter("authToken");
		String timeoutStr = req.getParameter("timeout");
		String endpoint = req.getParameter("endpoint");
		String httpMethod = req.getParameter("httpMethod");
		String requestBody = req.getParameter("requestBody");
		String dataPath = req.getParameter("dataPath");
		String headers = req.getParameter("headers");

		int timeout = 30000;
		if (timeoutStr != null && !timeoutStr.trim().isEmpty()) {
			timeout = Integer.parseInt(timeoutStr);
		}

		if (httpMethod == null || httpMethod.trim().isEmpty()) {
			httpMethod = "GET";
		}

		if (dataPath == null || dataPath.trim().isEmpty()) {
			dataPath = "$";
		}

		Map<String, Object> result = new HashMap<>();

		try {
			// 解析请求头
			Map<String, String> headerMap = new HashMap<>();
			if (headers != null && !headers.trim().isEmpty()) {
				headerMap = JSON.parseObject(headers, Map.class);
			}

			// 构建请求头
			Map<String, String> requestHeaders = buildRequestHeaders(authType, authToken, headerMap);

			// 构建完整URL
			String fullUrl = baseUrl;
			if (!fullUrl.endsWith("/") && !endpoint.startsWith("/")) {
				fullUrl += "/";
			}
			fullUrl += endpoint;

			// 创建HTTP客户端
			OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(timeout, TimeUnit.MILLISECONDS)
				.readTimeout(timeout, TimeUnit.MILLISECONDS)
				.writeTimeout(timeout, TimeUnit.MILLISECONDS)
				.build();

			// 构建请求
			Request request = buildHttpRequest(fullUrl, httpMethod, requestBody, requestHeaders);

			// 执行请求
			try (Response response = client.newCall(request).execute()) {
				if (!response.isSuccessful()) {
					result.put("success", false);
					result.put("message", "HTTP请求失败: " + response.code() + " " + response.message());
					writeObjectToJson(resp, result);
					return;
				}

				String responseBody = response.body().string();

				// 解析响应数据
				Object data = parseResponseData(responseBody, dataPath);

				// 转换为列表
				List<Object> list = convertToList(data);

				// 解析字段信息
				List<Map<String, Object>> fields = extractFieldsFromData(list);

				result.put("success", true);
				result.put("message", "数据集测试成功");
				result.put("data", list);
				result.put("fields", fields);
			}

		} catch (Exception e) {
			result.put("success", false);
			result.put("message", "数据集测试失败：" + e.getMessage());
		}

		writeObjectToJson(resp, result);
	}

	/**
	 * 构建请求头
	 */
	private Map<String, String> buildRequestHeaders(String authType, String authToken, Map<String, String> headers) {
		Map<String, String> requestHeaders = new HashMap<>();

		// 添加默认请求头
		if (headers != null) {
			requestHeaders.putAll(headers);
		}

		// 添加Content-Type
		if (!requestHeaders.containsKey("Content-Type")) {
			requestHeaders.put("Content-Type", "application/json");
		}

		// 添加认证头
		if (authType != null && authToken != null && !authToken.trim().isEmpty()) {
			switch (authType.toLowerCase()) {
				case "bearer":
					requestHeaders.put("Authorization", "Bearer " + authToken);
					break;
				case "basic":
					requestHeaders.put("Authorization", "Basic " + authToken);
					break;
				case "apikey":
					requestHeaders.put("X-API-Key", authToken);
					break;
				default:
					// 无认证或其他类型
					break;
			}
		}

		return requestHeaders;
	}

	/**
	 * 构建HTTP请求
	 */
	private Request buildHttpRequest(String url, String httpMethod, String requestBody, Map<String, String> headers) {
		Request.Builder builder = new Request.Builder().url(url);

		// 添加请求头
		if (headers != null) {
			for (Map.Entry<String, String> entry : headers.entrySet()) {
				builder.addHeader(entry.getKey(), entry.getValue());
			}
		}

		// 根据HTTP方法构建请求体
		RequestBody body = null;
		if (!"GET".equalsIgnoreCase(httpMethod)) {
			if (requestBody != null && !requestBody.trim().isEmpty()) {
				body = RequestBody.create(requestBody, MediaType.parse("application/json"));
			} else {
				body = RequestBody.create("", MediaType.parse("application/json"));
			}
		}

		// 设置HTTP方法
		switch (httpMethod.toUpperCase()) {
			case "POST":
				builder.post(body);
				break;
			case "PUT":
				builder.put(body);
				break;
			case "DELETE":
				if (body != null) {
					builder.delete(body);
				} else {
					builder.delete();
				}
				break;
			case "PATCH":
				builder.patch(body);
				break;
			default:
				builder.get();
				break;
		}

		return builder.build();
	}

	/**
	 * 解析响应数据
	 */
	private Object parseResponseData(String responseBody, String dataPath) {
		try {
			Object jsonData = JSON.parse(responseBody);

			// 使用JSONPath提取数据
			if (dataPath != null && !dataPath.equals("$")) {
				return JSONPath.eval(jsonData, dataPath);
			}

			return jsonData;
		} catch (Exception e) {
			throw new RuntimeException("解析响应数据失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 转换为列表
	 */
	@SuppressWarnings("unchecked")
	private List<Object> convertToList(Object data) {
		if (data instanceof List) {
			return (List<Object>) data;
		} else {
			// 如果不是列表，包装成单元素列表
			List<Object> list = new java.util.ArrayList<>();
			list.add(data);
			return list;
		}
	}

	/**
	 * 从数据中提取字段信息
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> extractFieldsFromData(List<Object> dataList) {
		List<Map<String, Object>> fields = new ArrayList<>();

		if (dataList == null || dataList.isEmpty()) {
			return fields;
		}

		// 使用第一条记录来分析字段结构
		Object firstRecord = dataList.get(0);
		if (firstRecord instanceof Map) {
			Map<String, Object> recordMap = (Map<String, Object>) firstRecord;

			for (Map.Entry<String, Object> entry : recordMap.entrySet()) {
				String fieldName = entry.getKey();
				Object fieldValue = entry.getValue();

				Map<String, Object> field = new HashMap<>();
				field.put("name", fieldName);
				field.put("displayName", fieldName); // 默认别名和字段名一样
				field.put("type", determineFieldType(fieldValue));

				fields.add(field);
			}
		}

		return fields;
	}

	/**
	 * 判断字段类型
	 */
	private String determineFieldType(Object value) {
		if (value == null) {
			return "String";
		}

		if (value instanceof String) {
			return "String";
		} else if (value instanceof Integer || value instanceof Long) {
			return "Integer";
		} else if (value instanceof Float || value instanceof Double) {
			return "Float";
		} else if (value instanceof Boolean) {
			return "Boolean";
		} else if (value instanceof java.util.Date) {
			return "Date";
		} else if (value instanceof List) {
			return "List";
		} else {
			return "String"; // 默认为字符串类型
		}
	}

	@Override
	public String url() {
		return "/datasource";
	}
}