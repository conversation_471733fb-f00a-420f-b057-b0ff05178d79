package com.lg.dao.core.interceptor;

import lombok.Data;
import org.springframework.util.StopWatch;

/**
 * SQL拦截器上下文
 */
@Data
public class SqlInterceptorContext {

    /**
     * SQL类型
     */
    private SqlType sqlType;

    /**
     * 执行计时器
     */
    private StopWatch stopWatch;

    /**
     * SQL类型枚举
     */
    public enum SqlType {
        /**
         * 查询
         */
        SELECT,

        /**
         * 插入
         */
        INSERT,

        /**
         * 更新
         */
        UPDATE,

        /**
         * 删除
         */
        DELETE,

        /**
         * 其他
         */
        OTHER
    }
}