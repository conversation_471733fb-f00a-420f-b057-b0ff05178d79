package com.lg.financecloud.common.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * AI模型配置属性类
 * 用于统一管理AI模型的配置信息
 */
@Data
//@ConfigurationProperties(prefix = "ai.model")
public class AIModelProperties {

    /**
     * 默认模型类型
     */
    private String defaultModel = "openai";

    /**
     * 模型配置映射
     * key: 模型类型
     * value: 模型特定配置
     */
    private Map<String, ModelConfig> models;

    /**
     * 模型配置类
     */
    @Data
    public static class ModelConfig {
        /**
         * 模型服务地址
         */
        private String endpoint;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 超时时间（毫秒）
         */
        private long timeout = 30000;

        /**
         * 重试次数
         */
        private int maxRetries = 3;

        /**
         * 其他配置参数
         */
        private Map<String, String> parameters;
    }
}