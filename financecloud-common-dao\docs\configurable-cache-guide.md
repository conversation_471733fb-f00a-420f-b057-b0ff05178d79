# 可配置缓存使用指南

## 优化背景

原有的缓存配置存在以下问题：
- ❌ 硬编码配置，无法根据环境调整
- ❌ 缓存条目数过大（5000、3000等）
- ❌ 过期时间过长（7200秒、14400秒等）
- ❌ 资源浪费，不适合小型应用

## 优化成果

✅ **完全配置化** - 所有缓存参数都可通过配置文件调整  
✅ **合理默认值** - 条目数 30-200，过期时间 150-1800秒  
✅ **环境适配** - 支持开发、测试、生产环境不同配置  
✅ **资源节省** - 大幅减少内存占用  

## 默认配置对比

### 优化前（硬编码）
```java
DAO_CACHE("dao", 5000, 7200, 3600),           // 5000条目，2小时过期
ENTITY_CACHE("entity", 3000, 3600, 1800),     // 3000条目，1小时过期
PERMISSION_CACHE("permission", 1000, 7200, 3600), // 1000条目，2小时过期
SQL_TEMPLATE_CACHE("sql_template", 2000, 14400, 7200), // 2000条目，4小时过期
```

### 优化后（可配置）
```java
DAO_CACHE("dao", 100, 300, 150),              // 100条目，5分钟过期
ENTITY_CACHE("entity", 200, 1800, 600),       // 200条目，30分钟过期
PERMISSION_CACHE("permission", 50, 1800, 600), // 50条目，30分钟过期
SQL_TEMPLATE_CACHE("sql_template", 50, 3600, 1800), // 50条目，1小时过期
```

## 配置方式

### 1. 基本配置

```yaml
light:
  orm:
    cache:
      enable: true  # 启用缓存
      unified:
        # 实体信息缓存
        entity:
          max-size: 200           # 最大条目数
          expire-after-write: 1800 # 写入后过期时间（秒）
          expire-after-access: 600 # 访问后过期时间（秒）
        
        # DAO操作缓存
        dao:
          max-size: 100
          expire-after-write: 300
          expire-after-access: 150
```

### 2. 环境特定配置

#### 开发环境（较小缓存）
```yaml
spring:
  profiles: dev
  
light:
  orm:
    cache:
      unified:
        entity:
          max-size: 100
          expire-after-write: 900
          expire-after-access: 300
```

#### 生产环境（较大缓存）
```yaml
spring:
  profiles: prod
  
light:
  orm:
    cache:
      unified:
        entity:
          max-size: 500
          expire-after-write: 3600
          expire-after-access: 1800
```

#### 测试环境（禁用缓存）
```yaml
spring:
  profiles: test
  
light:
  orm:
    cache:
      enable: false  # 测试环境禁用缓存
```

## 缓存类型说明

### 1. DAO_CACHE
- **用途**: 通用DAO操作缓存
- **推荐配置**: 小容量，短过期时间
- **默认值**: 100条目，5分钟过期

### 2. ENTITY_CACHE
- **用途**: 实体信息缓存（EntityInfo）
- **推荐配置**: 中等容量，较长过期时间
- **默认值**: 200条目，30分钟过期

### 3. PERMISSION_CACHE
- **用途**: 权限信息缓存
- **推荐配置**: 小容量，较长过期时间
- **默认值**: 50条目，30分钟过期

### 4. SQL_TEMPLATE_CACHE
- **用途**: SQL模板缓存
- **推荐配置**: 小容量，长过期时间
- **默认值**: 50条目，1小时过期

### 5. METADATA_CACHE
- **用途**: 元数据缓存
- **推荐配置**: 小容量，长过期时间
- **默认值**: 30条目，1小时过期

### 6. MYBATIS_PROXY_CACHE
- **用途**: MyBatis代理缓存
- **推荐配置**: 小容量，中等过期时间
- **默认值**: 50条目，30分钟过期

## 配置建议

### 1. 应用规模配置

#### 小型应用（< 1000用户）
```yaml
light:
  orm:
    cache:
      unified:
        dao: { max-size: 50, expire-after-write: 180, expire-after-access: 90 }
        entity: { max-size: 100, expire-after-write: 900, expire-after-access: 300 }
        permission: { max-size: 20, expire-after-write: 900, expire-after-access: 300 }
```

#### 中型应用（1000-10000用户）
```yaml
light:
  orm:
    cache:
      unified:
        dao: { max-size: 100, expire-after-write: 300, expire-after-access: 150 }
        entity: { max-size: 200, expire-after-write: 1800, expire-after-access: 600 }
        permission: { max-size: 50, expire-after-write: 1800, expire-after-access: 600 }
```

#### 大型应用（> 10000用户）
```yaml
light:
  orm:
    cache:
      unified:
        dao: { max-size: 200, expire-after-write: 600, expire-after-access: 300 }
        entity: { max-size: 500, expire-after-write: 3600, expire-after-access: 1800 }
        permission: { max-size: 100, expire-after-write: 3600, expire-after-access: 1800 }
```

### 2. 性能优化建议

1. **监控缓存命中率**: 定期查看缓存统计，调整配置
2. **合理设置过期时间**: 平衡性能和数据一致性
3. **根据业务特点调整**: 频繁变更的数据使用较短过期时间
4. **内存使用监控**: 避免缓存占用过多内存

## 使用示例

### 1. 编程式配置

```java
@Configuration
public class CacheConfig {
    
    @Bean
    public UnifiedCacheManager customCacheManager() {
        CacheProperties properties = new CacheProperties();
        
        // 自定义实体缓存配置
        CacheProperties.CacheTypeConfig entityConfig = 
            new CacheProperties.CacheTypeConfig(300, 2400, 1200);
        properties.getUnified().setEntity(entityConfig);
        
        return new UnifiedCacheManager(properties);
    }
}
```

### 2. 缓存监控

```java
@Service
public class CacheMonitorService {
    
    @Autowired
    private UnifiedCacheManager cacheManager;
    
    @Scheduled(fixedRate = 300000) // 每5分钟
    public void printCacheStats() {
        cacheManager.printStats();
    }
}
```

## 迁移指南

### 从硬编码配置迁移

1. **添加配置文件**: 在 application.yml 中添加缓存配置
2. **调整默认值**: 根据应用规模调整缓存大小
3. **测试验证**: 验证缓存功能正常工作
4. **性能监控**: 观察缓存命中率和内存使用

### 配置验证

```java
@Test
public void testCacheConfiguration() {
    // 验证配置是否生效
    String result = cacheManager.get(
        UnifiedCacheManager.CacheType.ENTITY_CACHE,
        "test-key",
        () -> "test-value"
    );
    assertEquals("test-value", result);
}
```

## 总结

通过配置化优化，缓存系统变得更加：
- **灵活**: 可根据环境和需求调整
- **高效**: 合理的默认值，避免资源浪费
- **可维护**: 配置清晰，易于管理
- **可扩展**: 支持新的缓存类型和策略
