/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;


import com.lg.financecloud.admin.api.dto.AttachFileVo;
import com.lg.financecloud.admin.api.dto.MergeAttachFileGroupDto;
import com.lg.financecloud.admin.api.entity.BaseAttachFile;
import com.lg.financecloud.admin.api.entity.BaseAttachFileGroup;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import com.lg.financecloud.common.feign.MultipartSupportConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteBaseAttachService", value = ServiceNameConstants.UPMS_SERVICE,configuration = MultipartSupportConfig.class)
public interface RemoteBaseAttachService {


//    @PostMapping("/baseattachfilegroup/getAttachFilesByGroupIds")
//    R<List<BaseAttachFileGroup>> getAttachFilesByGroupIds(@RequestParam("groups") List<Long> groups, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 更新附件组启用状态
     *
     * @return
     */

    @PostMapping("/baseattachfilegroup/formalAttachFile")
    R formalAttachFile(@RequestParam("groupId") Long groupId, @RequestParam("attachFileIds") List<Long> attachFiles,
                       @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 更新附件组启用状态
     *
     * @return
     */

    @PostMapping("/baseattachfilegroup/formalAttachFileGroup")
    R formalAttachFileGroup(@RequestParam("groupId") Long groupId,
                            @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 更新附件组启用状态
     *
     * @return
     */

    @PostMapping("/baseattachfilegroup/formalAttachFileGroups")
    R formalAttachFileGroups(@RequestParam("groupIds") List<Long> groupIds,
                             @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 更新附件未启用状态并且删除附件组
     * @param groupIds
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/invalidAttachmentFileGroups")
    R invalidAttachmentFileGroups(@RequestParam("groupIds") List<Long> groupIds,
                             @RequestHeader(SecurityConstants.FROM) String from);

    /****
     * 删除附件 根据ID
     * @param attachFileId
     * @param from
     * @return
     */
    @GetMapping("/baseattachfilegroup/removeAttachFile/{attachFileId}")
    R removeAttachFile(@PathVariable(value = "attachFileId") Long attachFileId, @RequestHeader(SecurityConstants.FROM) String from);

    /****
     * 删除附件 根据ID
     * @param attachGroup
     * @param from
     * @return
     */
    @GetMapping("/baseattachfilegroup/removeAttachGroup/{attachGroup}")
    R removeAttachGroup(@PathVariable(value = "attachGroup") Long attachGroup, @RequestHeader(SecurityConstants.FROM) String from);


    /****
     * 批量删除附件组
     * @param groups
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/removeAttachGroups")
    R removeAttachGroups(@RequestParam("groups") List<Long> groups, @RequestHeader(SecurityConstants.FROM) String from);

    /****
     * 合并附件组
     * @param mergeAttachFileGroupDtos
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/mergeAttachFileGroup")
    R<List<BaseAttachFileGroup>> mergeAttachFileGroup(@RequestBody List<MergeAttachFileGroupDto> mergeAttachFileGroupDtos, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * @return R
     * 返回 附件组id 以及附件明细
     */
    @PostMapping("/baseattachfilegroup/appendAttachFilesInner")
    R<Map<String, Object>> appendAttachFilesInner(@RequestBody BaseAttachFileGroup baseAttachFileGroup, @RequestHeader(SecurityConstants.FROM) String from);


    /****
     * 批量添加附件组
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/batchAddAttachFileGroupInner")
    R batchAddAttachFileGroup(@RequestBody List<BaseAttachFileGroup> groups, @RequestHeader(SecurityConstants.FROM) String from);
    /****
     * 获取附件组信息
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/getAttachFileGroupList")
    R<List<BaseAttachFileGroup>> getAttachFileGroupList(@RequestBody List<BaseAttachFileGroup> groups, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 查询附件信息
     * @param id
     * @param from
     * @return
     */
    @GetMapping("/baseattachfilegroup/getAttachFilesByGroupIdInner/{id}")
    R getAttachFilesByGroupIdInner(@PathVariable(value = "id") Long id, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 查询附件信息
     * @param id
     * @param from
     * @return
     */
    @GetMapping("/baseattachfilegroup/getAttachFilesByGroupIdInner/{id}")
    R<BaseAttachFileGroup> getAttachFilesByGroupIdInnerV1(@PathVariable(value = "id") Long id, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/baseattachfilegroup/uploadAttachFile4Erp")
     R uploadAttachFile4Erp(@RequestBody List<AttachFileVo> files,
                  @RequestParam(value = "acttachType",required = true) String acttachType,
                  @RequestParam(value = "attachFileGroupId",required = false)Long attachFileGroupId);

    @PostMapping(value = "/baseattachfilegroup/uploadAttachFile",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<BaseAttachFileGroup> uploadAttachFile(@RequestPart("multipartFiles") MultipartFile[] files,
                                            @RequestParam(value = "acttachType",required = true) String acttachType,
                                            @RequestParam(value = "attachFileGroupId",required = false)Long attachFileGroupId) ;

    /****
     * 根据附件组id和附件名称，移出组内文件
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/batchRemoveAttachFileByList")
    R batchRemoveAttachFileByList(@RequestBody List<BaseAttachFile> files, @RequestHeader(SecurityConstants.FROM) String from);
    /****
     * 根据附件组ids查询附件明细信息
     * @param from
     * @return
     */
    @PostMapping("/baseattachfilegroup/getAttachFilesByGroupIds")
    R<List<BaseAttachFileGroup>> getAttachFilesByGroupIds(@RequestBody List<Long> groupIds,@RequestHeader(SecurityConstants.FROM)String from);
}
