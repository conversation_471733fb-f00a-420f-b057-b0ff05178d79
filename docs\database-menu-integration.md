# 低代码平台数据库菜单集成方案

## 📋 概述

基于现有系统的 `sys_menu` 表结构，低代码平台直接操作数据库表来实现菜单注册和管理，避免了复杂的HTTP接口调用，提高了集成的可靠性和性能。

## 🗃️ 数据库表结构

### sys_menu 表结构
```sql
CREATE TABLE `sys_menu` (
  `menu_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `permission` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `path` varchar(300) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL COMMENT '父菜单ID',
  `icon` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sort` int(11) DEFAULT '1' COMMENT '排序值',
  `keep_alive` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0',
  `type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0',
  `tenant_id` int(11) unsigned DEFAULT NULL COMMENT '租户ID',
  `state` int(1) unsigned zerofill DEFAULT '0' COMMENT '普通租户(0可看,1不可看)',
  `source` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单来源 0 自身，1 第三方同步',
  PRIMARY KEY (`menu_id`) USING BTREE,
  KEY `IDX_sys_menu_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单权限表';
```

### 字段说明

| 字段名 | 类型 | 说明 | 低代码平台使用 |
|--------|------|------|----------------|
| menu_id | int(11) | 菜单ID，主键 | 自动生成 |
| name | varchar(32) | 菜单名称 | 页面名称 |
| permission | varchar(100) | 权限标识 | lowcode:page:{pageCode} |
| path | varchar(300) | 菜单路径 | /lowcode/runtime/{pageId} |
| parent_id | int(11) | 父菜单ID | 低代码平台分组ID |
| icon | varchar(32) | 菜单图标 | 页面图标或默认图标 |
| sort | int(11) | 排序值 | 页面排序或999 |
| keep_alive | char(1) | 是否缓存 | 固定为'0' |
| type | char(1) | 菜单类型 | '0'-目录，'1'-菜单，'2'-按钮 |
| create_time | datetime | 创建时间 | 自动设置 |
| update_time | datetime | 更新时间 | 自动更新 |
| del_flag | char(1) | 删除标志 | '0'-正常，'1'-删除 |
| tenant_id | int(11) | 租户ID | 配置的默认租户ID |
| state | int(1) | 状态 | 0-可见，1-不可见 |
| source | char(1) | 菜单来源 | '1'-第三方同步 |

## 🔧 技术实现

### 1. 实体类设计

```java
@Data
@TableName("sys_menu")
public class SysMenu {
    @TableId(value = "menu_id", type = IdType.AUTO)
    private Integer menuId;
    
    @TableField("name")
    private String name;
    
    @TableField("permission")
    private String permission;
    
    @TableField("path")
    private String path;
    
    @TableField("parent_id")
    private Integer parentId;
    
    @TableField("icon")
    private String icon;
    
    @TableField("sort")
    private Integer sort;
    
    @TableField("type")
    private String type;
    
    @TableField("source")
    private String source;
    
    @TableField("tenant_id")
    private Integer tenantId;
    
    // 其他字段...
}
```

### 2. DAO操作（使用自研框架）

```java
// 查询菜单
SysMenu menu = DaoHelper.dao(SysMenu.class).lambdaQuery()
    .eq(SysMenu::getPath, path)
    .eq(SysMenu::getTenantId, tenantId)
    .eq(SysMenu::getDelFlag, "0")
    .one();

// 插入菜单
DaoHelper.dao(SysMenu.class).insert(menu);

// 更新菜单
DaoHelper.dao(SysMenu.class).lambdaUpdate()
    .set(SysMenu::getName, newName)
    .set(SysMenu::getUpdateTime, LocalDateTime.now())
    .eq(SysMenu::getMenuId, menuId)
    .update();

// 逻辑删除菜单
DaoHelper.dao(SysMenu.class).lambdaUpdate()
    .set(SysMenu::getDelFlag, "1")
    .set(SysMenu::getUpdateTime, LocalDateTime.now())
    .eq(SysMenu::getMenuId, menuId)
    .update();
```

### 3. 菜单注册服务

```java
@Service
public class MenuRegistrationServiceImpl implements MenuRegistrationService {
    
    @Value("${financecloud.lowcode.system.default-tenant-id:1}")
    private Integer defaultTenantId;
    
    @Override
    @Transactional
    public Map<String, Object> registerPageMenu(PageMenuRegistrationDTO pageMenuDTO) {
        // 获取或创建低代码平台父菜单
        Integer parentId = getOrCreateLowcodePlatformParent();
        
        // 创建页面菜单
        SysMenu pageMenu = createPageMenu(pageMenuDTO, parentId);
        DaoHelper.dao(SysMenu.class).insert(pageMenu);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("menuId", pageMenu.getMenuId());
        return result;
    }
}
```

## 🚀 自动化集成

### 1. 页面发布时自动注册菜单

```java
@Component
public class PagePublishListener {
    
    @Async
    @EventListener
    public void handlePagePublishEvent(PagePublishEvent event) {
        LcPage page = event.getPage();
        
        // 构建页面菜单注册信息
        PageMenuRegistrationDTO pageMenuDTO = buildPageMenuDTO(page);
        
        // 注册页面菜单
        menuRegistrationService.registerPageMenu(pageMenuDTO);
    }
}
```

### 2. 页面服务中发布事件

```java
@Service
public class LcPageServiceImpl implements LcPageService {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Override
    @Transactional
    public void publishPage(String id) {
        // 更新页面状态
        // ...
        
        // 发布页面发布事件
        LcPage updatedPage = getPageById(id);
        eventPublisher.publishEvent(new PagePublishListener.PagePublishEvent(updatedPage));
    }
}
```

## 📊 菜单层级结构

### 默认菜单结构
```
低代码平台 (menu_id: auto, type: '0', source: '1')
├── 页面管理 (type: '1', path: '/lowcode/pages')
├── 数据模型 (type: '1', path: '/lowcode/models')  
├── 组件管理 (type: '1', path: '/lowcode/components')
├── 模板市场 (type: '1', path: '/lowcode/templates')
└── 动态页面菜单 (type: '1', path: '/lowcode/runtime/{pageId}')
    ├── 用户管理页面
    ├── 订单管理页面
    └── 其他业务页面...
```

### 菜单标识规则

| 菜单类型 | 权限标识格式 | 路径格式 | 示例 |
|----------|-------------|----------|------|
| 平台管理菜单 | lowcode:{module}:view | /lowcode/{module} | lowcode:page:view |
| 动态页面菜单 | lowcode:page:{pageCode} | /lowcode/runtime/{pageId} | lowcode:page:user_mgmt |

## 🔄 API接口

### 1. 菜单注册接口

```http
POST /api/lc/menu-registration/register-page
Content-Type: application/json

{
  "pageId": "page123",
  "pageCode": "user_mgmt",
  "pageName": "用户管理",
  "icon": "el-icon-user",
  "permission": "lowcode:page:user_mgmt",
  "sortOrder": 1,
  "enabled": true
}
```

### 2. 菜单同步接口

```http
POST /api/lc/menu-registration/sync-published-pages
```

### 3. 菜单清理接口

```http
DELETE /api/lc/menu-registration/cleanup
```

## 🧪 测试接口

### 1. 初始化平台菜单

```http
POST /api/lc/menu-test/init-platform-menu
```

### 2. 查看菜单列表

```http
GET /api/lc/menu-test/list-lowcode-menus
```

### 3. 创建测试菜单

```http
POST /api/lc/menu-test/create-test-menu?menuName=测试页面
```

### 4. 清理测试菜单

```http
DELETE /api/lc/menu-test/cleanup-test-menus
```

## ⚙️ 配置说明

### application.yml 配置

```yaml
financecloud:
  lowcode:
    system:
      default-tenant-id: 1  # 默认租户ID
    menu:
      auto-sync: true       # 是否启用自动同步
      default-parent-code: lowcode_pages  # 默认父菜单编码
```

### 环境变量配置

```bash
# 默认租户ID
LOWCODE_DEFAULT_TENANT_ID=1

# 是否启用菜单自动同步
LOWCODE_MENU_AUTO_SYNC=true
```

## 🎯 使用流程

### 1. 初始化阶段

1. **创建低代码平台根菜单**
   ```http
   POST /api/lc/menu-test/init-platform-menu
   ```

2. **验证菜单创建**
   ```http
   GET /api/lc/menu-test/list-lowcode-menus
   ```

### 2. 页面发布阶段

1. **在低代码平台发布页面**
2. **系统自动注册页面菜单**
3. **在cloudx-ui中查看新增菜单**

### 3. 菜单管理阶段

1. **查看注册状态**
   ```http
   GET /api/lc/menu-registration/status
   ```

2. **手动同步菜单**
   ```http
   POST /api/lc/menu-registration/sync-published-pages
   ```

3. **清理无效菜单**
   ```http
   DELETE /api/lc/menu-registration/cleanup
   ```

## 🚨 注意事项

### 1. 数据一致性
- 所有菜单操作都在事务中执行
- 使用逻辑删除，避免数据丢失
- 定期清理无效的菜单数据

### 2. 权限控制
- 菜单权限标识遵循统一规范
- 支持租户隔离
- 区分菜单来源（自身/第三方同步）

### 3. 性能优化
- 使用缓存减少数据库查询
- 批量操作提高效率
- 异步处理菜单同步

### 4. 错误处理
- 完善的异常处理机制
- 详细的错误日志记录
- 友好的错误提示信息

## 📞 技术支持

如有问题或建议，请联系开发团队：
- 数据库设计: [DBA联系方式]
- 后端开发: [后端开发联系方式]
- 前端集成: [前端开发联系方式]
