package com.lg.dao.core.filter;

import cn.hutool.core.collection.ListUtil;
import com.lg.dao.core.interceptor.InterceptorExclusionManager;
import com.lg.dao.core.interceptor.LightOrmSqlInterceptor;
import com.lg.dao.core.interceptor.SqlInterceptorContext;
import com.lg.financecloud.admin.api.dto.SessionUser;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据权限拦截器
 * 实现LightOrmSqlInterceptor接口，用于在SQL执行前应用数据权限规则
 * 支持SoulTable格式的筛选条件
 *
 * <AUTHOR>
 */
@Slf4j
public class DataPermissionInterceptor implements LightOrmSqlInterceptor {

    @Autowired
    @Lazy
    private DataPermissionService dataPermissionService;

    @Autowired
    private JsonFilterParser jsonFilterParser;
    
    // 用于提取SQL中的表名的正则表达式
    private static final Pattern FROM_PATTERN = Pattern.compile("\\s+FROM\\s+([\\w\\.]+)", Pattern.CASE_INSENSITIVE);
    
    /**
     * 递归深度控制，防止死循环
     */
    private static final ThreadLocal<Integer> RECURSION_DEPTH = ThreadLocal.withInitial(() -> 0);
    private static final int MAX_RECURSION_DEPTH = 3;
    
    /**
     * 线程本地变量，用于存储筛选条件
     */
    private static final ThreadLocal<FilterConfig> THREAD_LOCAL_FILTER = new ThreadLocal<>();
    
    /**
     * 线程本地变量，用于存储当前执行的方法
     */
    private static final ThreadLocal<Method> THREAD_LOCAL_METHOD = new ThreadLocal<>();
    
    /**
     * 设置筛选条件
     *
     * @param filterConfig 筛选条件配置
     */
    public static void setFilterConfig(FilterConfig filterConfig) {
        THREAD_LOCAL_FILTER.set(filterConfig);
    }
    
    /**
     * 设置筛选条件
     *
     * @param jsonFilter JSON格式的筛选条件
     */
    public static void setFilterConfig(String jsonFilter) {
        try {
            JsonFilterParser jsonFilterParser = new JsonFilterParser();
            FilterConfig filterConfig = jsonFilterParser.parse(jsonFilter);
            THREAD_LOCAL_FILTER.set(filterConfig);
        } catch (Exception e) {
            log.error("解析JSON筛选条件失败", e);
            clearFilterConfig();
        }
    }
    
    /**
     * 清除筛选条件
     */
    public static void clearFilterConfig() {
        THREAD_LOCAL_FILTER.remove();
    }
    
    /**
     * 设置当前执行的方法
     *
     * @param method 当前执行的方法
     */
    public static void setCurrentMethod(Method method) {
        THREAD_LOCAL_METHOD.set(method);
    }
    
    /**
     * 清除当前执行的方法
     */
    public static void clearCurrentMethod() {
        THREAD_LOCAL_METHOD.remove();
    }
    
    /**
     * 清除所有线程本地变量
     */
    public static void clearAllThreadLocals() {
        RECURSION_DEPTH.remove();
        THREAD_LOCAL_FILTER.remove();
        THREAD_LOCAL_METHOD.remove();
    }
    
    @Override
    public String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
        // 检查是否排除数据权限拦截器
        if (InterceptorExclusionManager.isExcluded(InterceptorExclusionManager.InterceptorType.DATA_PERMISSION)) {
            return sql;
        }

        // 检查递归深度，防止死循环
        int depth = RECURSION_DEPTH.get();
        if (depth >= MAX_RECURSION_DEPTH) {
            log.warn("数据权限拦截器递归深度超限，跳过处理: depth={}, sql={}", depth, sql);
            return sql;
        }
        RECURSION_DEPTH.set(depth + 1);

        try {
            // 检查当前方法是否有ExcludeDataFilter注解
            Method currentMethod = THREAD_LOCAL_METHOD.get();
            if (currentMethod != null && currentMethod.isAnnotationPresent(ExcludeDataFilter.class)) {
                return sql;
            }

            // 只处理SELECT语句
            if (context.getSqlType() != SqlInterceptorContext.SqlType.SELECT) {
                return sql;
            }
            
            // 获取当前用户信息
            String userId = getCurrentUserId();
            if (StringUtils.isEmpty(userId)) {
                log.debug("当前用户ID为空，跳过数据权限处理");
                return sql;
            }
            
            List<String> roleIds = getCurrentUserRoleIds();
            if (roleIds == null || roleIds.isEmpty()) {
                log.debug("当前用户角色为空，跳过数据权限处理");
                return sql;
            }
            
            // 提取表名
            String tableName = extractTableName(sql);
            if (StringUtils.isEmpty(tableName)) {
                log.debug("无法从SQL中提取表名，跳过数据权限处理: {}", sql);
                return sql;
            }
            
            // 应用筛选条件
            String filteredSql = applyFilterCondition(sql, currentMethod);
            
            try {
                // 获取数据权限条件
                String condition = dataPermissionService.getDataPermissionCondition(tableName, userId, roleIds);
                if (StringUtils.isEmpty(condition)) {
                    return filteredSql;
                }
                
                // 应用数据权限条件
                return applyDataPermissionCondition(filteredSql, condition);
            } catch (Exception e) {
                log.error("应用数据权限条件失败: {}", tableName, e);
                return filteredSql;
            }
        } catch (Exception e) {
            log.error("数据权限处理异常", e);
            return sql;
        } finally {
            // 清理递归深度计数
            int currentDepth = RECURSION_DEPTH.get();
            if (currentDepth > 0) {
                RECURSION_DEPTH.set(currentDepth - 1);
            } else {
                RECURSION_DEPTH.remove();
            }
            
            // 如果递归结束，清除所有线程本地变量
            if (currentDepth <= 1) {
                clearAllThreadLocals();
            }
        }
    }
    
    /**
     * 应用筛选条件
     *
     * @param sql 原始SQL
     * @param method 当前执行的方法
     * @return 应用筛选条件后的SQL
     */
    private String applyFilterCondition(String sql, Method method) {
        try {
            // 优先使用线程本地变量中的筛选条件
            FilterConfig filterConfig = THREAD_LOCAL_FILTER.get();
            
            // 如果线程本地变量中没有筛选条件，尝试从请求参数中获取
            if (filterConfig == null) {
                filterConfig = getFilterConfigFromRequest();
            }
            
            // 如果请求参数中没有筛选条件，尝试从注解中获取
            if (filterConfig == null && method != null && method.isAnnotationPresent(DynamicDataFilter.class)) {
                filterConfig = getFilterConfigFromAnnotation(method);
            }
            
            if (filterConfig == null) {
                return sql;
            }
            
            SqlFilter sqlFilter = new DefaultSqlFilter(dataPermissionService);
            return sqlFilter.apply(sql, filterConfig);
        } catch (Exception e) {
            log.error("应用筛选条件失败", e);
            return sql;
        }
    }
    
    /**
     * 从请求参数中获取筛选条件
     *
     * @return 筛选条件配置
     */
    private FilterConfig getFilterConfigFromRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return null;
            }
            
            HttpServletRequest request = attributes.getRequest();
            String filterSos = request.getParameter("filterSos");
            String field = request.getParameter("field");
            String order = request.getParameter("order");
            String tableCode = request.getParameter("tableCode");
            
            if (StringUtils.isEmpty(filterSos) && StringUtils.isEmpty(field) && StringUtils.isEmpty(order)) {
                return null;
            }
            
            Map<String, Object> params = new HashMap<>();
            if (!StringUtils.isEmpty(filterSos)) {
                params.put("filterSos", filterSos);
            }
            if (!StringUtils.isEmpty(field)) {
                params.put("field", field);
            }
            if (!StringUtils.isEmpty(order)) {
                params.put("order", order);
            }
            if (!StringUtils.isEmpty(tableCode)) {
                params.put("tableCode", tableCode);
            }
            
            return jsonFilterParser.parse(params.toString());
        } catch (Exception e) {
            log.error("从请求参数中获取筛选条件失败", e);
            return null;
        }
    }
    
    /**
     * 从注解中获取筛选条件
     *
     * @param method 当前执行的方法
     * @return 筛选条件配置
     */
    private FilterConfig getFilterConfigFromAnnotation(Method method) {
        try {
            DynamicDataFilter annotation = method.getAnnotation(DynamicDataFilter.class);
            if (annotation == null) {
                return null;
            }
            
            // 获取表编码
            String tableCode = annotation.tableCode();
            
            // 优先使用JSON格式的过滤条件
            String filterJson = annotation.value();
            if (!StringUtils.isEmpty(filterJson)) {
                // 如果JSON字符串中没有tableCode，但注解中有，则添加到JSON中
                if (!filterJson.contains("tableCode") && !StringUtils.isEmpty(tableCode)) {
                    // 移除可能的结尾大括号，添加tableCode，然后重新添加结尾大括号
                    if (filterJson.endsWith("}")) {
                        filterJson = filterJson.substring(0, filterJson.length() - 1) + 
                                    ", \"tableCode\": \"" + tableCode + "\"}";
                    } else {
                        filterJson = "{\"tableCode\": \"" + tableCode + "\", " + 
                                    (filterJson.startsWith("{") ? filterJson.substring(1) : filterJson);
                    }
                }
                return jsonFilterParser.parse(filterJson);
            }
            
            // 如果没有JSON格式的过滤条件，尝试使用字段配置
            FilterField[] filterFields = annotation.fields();
            if (filterFields == null || filterFields.length == 0) {
                return null;
            }
            
            FilterConfig filterConfig = new FilterConfig();
            List<FilterConfig.Filter> filters = new ArrayList<>();
            
            for (FilterField filterField : filterFields) {
                FilterConfig.Filter filter = new FilterConfig.Filter();
                filter.setField(filterField.field());
                filter.setOperator(filterField.operator());
                filter.setValue(filterField.value());
                filters.add(filter);
            }
            
            filterConfig.setFilters(filters);
            return filterConfig;
        } catch (Exception e) {
            log.error("从注解中获取筛选条件失败", e);
            return null;
        }
    }
    
    /**
     * 从SQL中提取表名
     *
     * @param sql SQL语句
     * @return 表名
     */
    private String extractTableName(String sql) {
        Matcher matcher = FROM_PATTERN.matcher(sql);
        if (matcher.find()) {
            String tableName = matcher.group(1);
            // 处理表别名
            int spaceIndex = tableName.indexOf(' ');
            if (spaceIndex > 0) {
                tableName = tableName.substring(0, spaceIndex);
            }
            return tableName;
        }
        return null;
    }
    
    /**
     * 应用数据权限条件
     *
     * @param sql 原始SQL
     * @param condition 数据权限条件
     * @return 应用数据权限条件后的SQL
     */
    private String applyDataPermissionCondition(String sql, String condition) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT * FROM (");
        builder.append(sql);
        builder.append(") DP_TEMP");
        
        // 检查原始SQL是否已经有WHERE子句
        if (sql.toUpperCase().contains(" WHERE ")) {
            builder.append(" AND ");
        } else {
            builder.append(" WHERE ");
        }
        
        builder.append(condition);
        
        return builder.toString();
    }
    
    @Override
    public int getOrder() {
        // 设置拦截器优先级，数值越小优先级越高
        return -90;
    }
    
    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    private String getCurrentUserId() {
        SessionUser sessionUser = (SessionUser) TenantContextHolder.getCurrentSessionUser();
        if (sessionUser != null) {
          return sessionUser.getId()+"";
        }
        return null;
    }
    
    /**
     * 获取当前用户的角色ID列表
     *
     * @return 角色ID列表
     */
    private List<String> getCurrentUserRoleIds() {
       SessionUser sessionUser = (SessionUser) TenantContextHolder.getCurrentSessionUser();
       if (sessionUser != null) {
         // 转化成 Li<String>
         Integer[] roleIds =  sessionUser.getRoleIds();
           return   ListUtil.of(roleIds).stream()
               .map(String::valueOf)
               .collect(Collectors.toList());
       }
        
        return new ArrayList<>();
    }
    
    /**
     * 用户详情接口
     * 实际项目中应该使用已有的用户详情接口
     */
    private interface UserDetails {
        String getId();
        List<String> getRoleIds();
    }
} 