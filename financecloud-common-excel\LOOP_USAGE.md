# Excel组件循环功能使用说明

## 概述

Excel组件现已支持模板循环功能，可以在Excel模板中使用循环语法动态生成重复行，适用于报表、清单等场景。

## 循环语法

### 基本语法

```
${#foreach listName}
循环体内容
${/foreach}
```

- `listName`: 循环数据的变量名
- 循环体: 在`${#foreach}`和`${/foreach}`之间的所有行都会被重复生成
- `${item.property}`: 在循环体中使用`item`访问当前循环项的属性

### 支持的数据类型

- `List<Map<String, Object>>`: 推荐使用，支持复杂对象
- `List<Object>`: 支持简单对象列表
- `Map[]`: 支持Map数组

## 使用示例

### 1. 简单列表循环

**Excel模板:**
```
员工信息表
${#foreach users}
${item.name}    ${item.age}    ${item.position}
${/foreach}
```

**Java代码:**
```java
// 准备数据
List<Map<String, Object>> users = Arrays.asList(
    createUser("张三", 25, "开发工程师"),
    createUser("李四", 30, "产品经理"),
    createUser("王五", 28, "测试工程师")
);

// 使用循环功能
ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .var("title", "员工信息表")
    .loop("users", users)  // 设置循环数据
    .replaceTo("output.xlsx");

private Map<String, Object> createUser(String name, int age, String position) {
    Map<String, Object> user = new HashMap<>();
    user.put("name", name);
    user.put("age", age);
    user.put("position", position);
    return user;
}
```

**生成结果:**
```
员工信息表
张三    25    开发工程师
李四    30    产品经理
王五    28    测试工程师
```

### 2. 多行循环模板

**Excel模板:**
```
${#foreach products}
产品编号: ${item.code}
产品名称: ${item.name}    价格: ${item.price}
${/foreach}
```

**Java代码:**
```java
List<Map<String, Object>> products = Arrays.asList(
    createProduct("P001", "笔记本电脑", 5999.00),
    createProduct("P002", "智能手机", 2999.00)
);

ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .loop("products", products)
    .replaceTo("output.xlsx");
```

**生成结果:**
```
产品编号: P001
产品名称: 笔记本电脑    价格: 5999.0
产品编号: P002
产品名称: 智能手机    价格: 2999.0
```

### 3. 嵌套对象循环

**Excel模板:**
```
${#foreach orders}
${item.orderNo}    ${item.customer.name}    ${item.customer.address}
${/foreach}
```

**Java代码:**
```java
List<Map<String, Object>> orders = Arrays.asList(
    createOrder("ORD001", "张三", "北京市朝阳区"),
    createOrder("ORD002", "李四", "上海市浦东新区")
);

ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .loop("orders", orders)
    .replaceTo("output.xlsx");

private Map<String, Object> createOrder(String orderNo, String customerName, String address) {
    Map<String, Object> order = new HashMap<>();
    order.put("orderNo", orderNo);
    
    Map<String, Object> customer = new HashMap<>();
    customer.put("name", customerName);
    customer.put("address", address);
    order.put("customer", customer);
    
    return order;
}
```

### 4. 混合使用循环和普通变量

**Excel模板:**
```
${companyName}
报表日期: ${reportDate}

${#foreach employees}
${item.empNo}    ${item.name}    ${item.department}    ${item.salary}
${/foreach}

总计: ${totalCount} 人
```

**Java代码:**
```java
List<Map<String, Object>> employees = Arrays.asList(
    createEmployee("E001", "张三", "开发部", 8000.00),
    createEmployee("E002", "李四", "产品部", 7000.00),
    createEmployee("E003", "王五", "测试部", 6500.00)
);

ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .var("companyName", "科技有限公司")
    .var("reportDate", "2024-01-15")
    .loop("employees", employees)  // 设置循环数据
    .var("totalCount", employees.size())
    .replaceTo("output.xlsx");
```

## API说明

### OpsReplace类新增方法

#### loop(String name, List<?> data)
设置单个循环数据

**参数:**
- `name`: 循环变量名，对应模板中的`${#foreach name}`
- `data`: 循环数据列表

**返回:** `OpsReplace` 实例，支持链式调用

#### loops(Map<String, List<?>> loopData)
批量设置多个循环数据

**参数:**
- `loopData`: 包含多个循环数据的Map，key为循环变量名，value为数据列表

**返回:** `OpsReplace` 实例，支持链式调用

### 使用示例

```java
// 方式1: 单个循环
ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .loop("users", userList)
    .replaceTo("output.xlsx");

// 方式2: 多个循环
Map<String, List<?>> loops = new HashMap<>();
loops.put("users", userList);
loops.put("products", productList);

ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .loops(loops)
    .replaceTo("output.xlsx");

// 方式3: 混合使用
ExcelHelper.opsReplace()
    .fromPath("template.xlsx")
    .var("title", "报表标题")
    .loop("users", userList)
    .loop("products", productList)
    .var("date", new Date())
    .replaceTo("output.xlsx");
```

## 注意事项

1. **循环标记必须独占一行**: `${#foreach listName}` 和 `${/foreach}` 必须各自独占一行

2. **循环体可以包含多行**: 在循环开始和结束标记之间的所有行都会被重复

3. **支持嵌套属性访问**: 使用点号访问嵌套对象属性，如 `${item.customer.name}`

4. **空列表处理**: 如果循环数据为空列表，循环区域会被完全删除

5. **数据类型**: 循环数据建议使用 `List<Map<String, Object>>` 格式，确保最佳兼容性

6. **变量命名**: 在循环体内，当前项固定使用 `item` 访问，如 `${item.property}`

7. **处理顺序**: 循环处理在普通变量替换之前执行，确保循环生成的内容也能进行变量替换

## 错误处理

- 如果循环数据为 `null`，会被当作空列表处理
- 如果模板中的循环语法不匹配（缺少开始或结束标记），会抛出异常
- 如果访问不存在的属性，会显示为空字符串

## 性能建议

- 对于大量数据的循环，建议分批处理或考虑使用分页导出功能
- 循环模板设计时，尽量减少不必要的空行和复杂格式
- 嵌套对象层级不宜过深，建议不超过3层