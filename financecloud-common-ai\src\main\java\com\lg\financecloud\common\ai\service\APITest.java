package com.lg.financecloud.common.ai.service;

import cn.hutool.json.JSONUtil;
import com.lg.financecloud.common.ai.exception.AIServiceException;
import com.lg.financecloud.common.ai.factory.AIServiceFactory;
import com.lg.financecloud.common.ai.model.*;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static com.lg.financecloud.common.ai.service.AbstractModelService.*;

public class APITest {


    // 4. 添加测试方法
    private static void testFileUpload() {
        File file = new File("d://test.jsonl");
        FileUploadResponse response = AIServiceFactory.getService()
                .uploadFile(file, "batch","", "sk-7259c9ce068c4ab19cfe0fa8093ab2a8");
        System.out.println("上传成功，文件ID: " + response.getId());
    }





    public static void testRawStream() {
        try (CloseableHttpAsyncClient httpClient = HttpAsyncClients.createDefault()) {
            httpClient.start();

            String apiKey = "sk-7259c9ce068c4ab19cfe0fa8093ab2a8";
            String endpoint = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
            String question = "我要提一个复杂的问题，数学的起源";

            HttpPost httpPost = new HttpPost(endpoint);
            httpPost.setHeader(AUTHORIZATION_HEADER, BEARER_PREFIX + apiKey);
            httpPost.setHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON);
            httpPost.setHeader(ACCEPT_HEADER, TEXT_EVENT_STREAM);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put(MODEL_KEY, "qwen-plus");
            requestBody.put(STREAM_KEY, true);
            requestBody.put(MESSAGES_KEY, Collections.singletonList(
                    new HashMap<String, String>() {{
                        put(ROLE_KEY, "user");
                        put(CONTENT_KEY, question);
                    }}
            ));
            httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(requestBody), StandardCharsets.UTF_8));

            CountDownLatch latch = new CountDownLatch(1);

            httpClient.execute(httpPost, new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.getEntity().getContent()))) {
                        String line = null;
                        while ((line = reader.readLine()) != null) {
                            System.err.println("接收到服务器响应: " + line);
                        }


                    } catch (IOException e) {
                        System.err.println("响应处理错误: " + e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                }

                @Override
                public void failed(Exception ex) {
                    System.err.println("请求失败: " + ex.getMessage());
                    latch.countDown();
                }

                @Override
                public void cancelled() {
                    System.err.println("请求被取消");
                    latch.countDown();
                }
            });

            try {
                if (!latch.await(REQUEST_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    System.err.println("Request timed out after " + REQUEST_TIMEOUT_SECONDS + " seconds");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        } catch (IOException e) {
            System.err.println("关闭客户端失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        testRawStream();
    }

    private static void testVlModel() {

        AIRequest request = AIRequest.multimodalBuilder("sk-7259c9ce068c4ab19cfe0fa8093ab2a8").model("qwen-vl-plus")
                .addUserImage(new File("d://testaaa.png")).addUserMessage("结构化成json")
                .build();
        request.setRequestUrl("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions");

        AIResponse response = AIServiceFactory.getService().process(request);
        System.out.println(response);
    }

    private static void testTextModel() {

        AIRequest request = AIRequest.textBuilder("sk-7259c9ce068c4ab19cfe0fa8093ab2a8")
                .model("qwen-turbo")
                .addUserMessage("你是什么大模型")
                .build();
        request.setRequestUrl("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions");

        AIResponse response = AIServiceFactory.getService().process(request);
        System.out.println(response);
    }

    // 5. 添加测试方法
    private static void testBatchTask() {
        try {
            // 先上传文件
            File file = new File("test.jsonl");
            FileUploadResponse uploadRes = AIServiceFactory.getService()
                    .uploadFile(file, "batch", null, "sk-your-key");

            // 创建Batch任务
            BatchRequest request = new AIRequest.BatchBuilder("sk-your-key")
                    .inputFileId(uploadRes.getId())
                    .endpoint("/v1/chat/completions")
                    .completionWindow("24h")
                    .metadata("测试任务", "API集成测试")
                    .build();

            BatchTaskResult response = AIServiceFactory.getService().createBatchTask(request, null, "");
            System.out.println("任务创建成功，ID: " + response.getId());
        } catch (AIServiceException e) {
            System.err.println("任务创建失败: " + e.getMessage());
        }
    }
}
