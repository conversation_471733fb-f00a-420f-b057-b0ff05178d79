package com.lg.dao.core.annotation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 注解解析器
 */
@Slf4j
public class AnnotationParser {
    
    /**
     * 解析实体类的所有字段注解信息
     */
    public static Map<String, ColumnInfo> parseEntityFields(Class<?> entityClass) {
        Map<String, ColumnInfo> columnMap = new HashMap<>();
        
        // 获取所有字段（包括父类字段）
        Class<?> currentClass = entityClass;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                ColumnInfo columnInfo = parseField(field);
                if (columnInfo != null) {
                    columnMap.put(field.getName(), columnInfo);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        
        return columnMap;
    }

    /**
     * 解析单个字段的注解信息
     */
    private static ColumnInfo parseField(Field field) {
        ColumnInfo columnInfo = new ColumnInfo();
        columnInfo.setFieldName(field.getName());
        columnInfo.setFieldType(field.getType());
        
        // 默认列名与字段名相同（驼峰转下划线）
        columnInfo.setColumnName(camelToUnderline(field.getName()));
        
        // 解析JPA注解
        if (parseJpaAnnotations(field, columnInfo)) {
            return columnInfo;
        }
        
        // 解析MyBatis-Plus注解
        if (parseMybatisAnnotations(field, columnInfo)) {
            return columnInfo;
        }
        
        return columnInfo;
    }

    /**
     * 解析JPA注解
     */
    private static boolean parseJpaAnnotations(Field field, ColumnInfo columnInfo) {
        boolean hasJpaAnnotation = false;
        
        // 解析@Id注解
        Id id = field.getAnnotation(Id.class);
        if (id != null) {
            columnInfo.setPrimaryKey(true);
            hasJpaAnnotation = true;
        }
        
        // 解析@Column注解
        Column column = field.getAnnotation(Column.class);
        if (column != null) {
            if (!column.name().isEmpty()) {
                columnInfo.setColumnName(column.name());
            }
            hasJpaAnnotation = true;
        }
        
        // 解析@GeneratedValue注解
        GeneratedValue generatedValue = field.getAnnotation(GeneratedValue.class);
        if (generatedValue != null) {
            columnInfo.setGenerated(true);
            hasJpaAnnotation = true;
        }
        
        return hasJpaAnnotation;
    }

    /**
     * 解析MyBatis-Plus注解
     */
    private static boolean parseMybatisAnnotations(Field field, ColumnInfo columnInfo) {
        boolean hasMybatisAnnotation = false;
        
        // 解析@TableId注解
        TableId tableId = field.getAnnotation(TableId.class);
        if (tableId != null) {
            columnInfo.setPrimaryKey(true);
            if (!tableId.value().isEmpty()) {
                columnInfo.setColumnName(tableId.value());
            }
            columnInfo.setGenerated(tableId.type() == IdType.AUTO);
            hasMybatisAnnotation = true;
        }
        
        // 解析@TableField注解
        TableField tableField = field.getAnnotation(TableField.class);
        if (tableField != null) {
            if (!tableField.value().isEmpty()) {
                columnInfo.setColumnName(tableField.value());
            }
            hasMybatisAnnotation = true;
        }
        
        return hasMybatisAnnotation;
    }

    /**
     * 驼峰命名转下划线命名
     */
    private static String camelToUnderline(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }
} 