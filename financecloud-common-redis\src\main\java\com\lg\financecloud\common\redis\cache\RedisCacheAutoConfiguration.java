/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.redis.cache;

import com.lg.financecloud.common.redis.cache.memory.MemoryCacheManager;
import com.lg.financecloud.common.redis.cache.properties.CacheProperties;
import com.lg.financecloud.common.redis.cache.twolevel.TwoLevelCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 扩展redis-cache支持注解cacheName添加超时时间
 * 支持纯内存、纯Redis和二级缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@AutoConfigureAfter({ RedisAutoConfiguration.class })
@ConditionalOnBean({ RedisConnectionFactory.class })
@ConditionalOnMissingBean({ CacheManager.class })
@EnableConfigurationProperties({org.springframework.boot.autoconfigure.cache.CacheProperties.class, CacheProperties.class})
public class RedisCacheAutoConfiguration {

	private final org.springframework.boot.autoconfigure.cache.CacheProperties springCacheProperties;
	private final CacheProperties financeCloudCacheProperties;
	private final CacheManagerCustomizers customizerInvoker;

	@Nullable
	private final RedisCacheConfiguration redisCacheConfiguration;

	RedisCacheAutoConfiguration(org.springframework.boot.autoconfigure.cache.CacheProperties springCacheProperties,
			CacheProperties financeCloudCacheProperties,
			CacheManagerCustomizers customizerInvoker,
			ObjectProvider<RedisCacheConfiguration> redisCacheConfiguration) {
		this.springCacheProperties = springCacheProperties;
		this.financeCloudCacheProperties = financeCloudCacheProperties;
		this.customizerInvoker = customizerInvoker;
		this.redisCacheConfiguration = redisCacheConfiguration.getIfAvailable();
		log.info("初始化缓存配置: 缓存模式={}", financeCloudCacheProperties.getCacheMode());
	}

	@Bean
	public CacheManager cacheManager(RedisConnectionFactory connectionFactory, ResourceLoader resourceLoader) {
		DefaultRedisCacheWriter redisCacheWriter = new DefaultRedisCacheWriter(connectionFactory);
		RedisCacheConfiguration cacheConfiguration = this.determineConfiguration(resourceLoader.getClassLoader());

		List<String> cacheNames = this.springCacheProperties.getCacheNames();
		Map<String, RedisCacheConfiguration> initialCaches = new LinkedHashMap<>();
		if (!cacheNames.isEmpty()) {
			Map<String, RedisCacheConfiguration> cacheConfigMap = new LinkedHashMap<>(cacheNames.size());
			cacheNames.forEach(it -> cacheConfigMap.put(it, cacheConfiguration));
			initialCaches.putAll(cacheConfigMap);
		}
		
		// 总是创建RedisCacheManager，因为它在TWO_LEVEL模式中也会被使用
		RedisAutoCacheManager redisCacheManager = new RedisAutoCacheManager(redisCacheWriter, cacheConfiguration,
				initialCaches, true);
		redisCacheManager.setTransactionAware(false);
		
		// 基于缓存模式，返回相应的缓存管理器
		switch (financeCloudCacheProperties.getCacheMode()) {
			case MEMORY:
				// 纯内存缓存模式
				log.info("使用纯内存缓存模式 (Caffeine)");
				return customizerInvoker.customize(new MemoryCacheManager(
						financeCloudCacheProperties.getMaxSize(),
						financeCloudCacheProperties.getTimeToLiveSeconds(),
						financeCloudCacheProperties.getTimeToIdleSeconds()));
				
			case TWO_LEVEL:
				// 二级缓存模式（内存+Redis）
				log.info("使用二级缓存模式 (Caffeine + Redis)");
				return customizerInvoker.customize(new TwoLevelCacheManager(
						redisCacheManager,
						redisCacheWriter,
						cacheConfiguration,
						financeCloudCacheProperties.getMaxSize(),
						financeCloudCacheProperties.getTimeToLiveSeconds(),
						financeCloudCacheProperties.getTimeToIdleSeconds()));
				
			case REDIS:
			default:
				// 纯Redis缓存模式（默认行为）
				log.info("使用纯Redis缓存模式");
				return this.customizerInvoker.customize(redisCacheManager);
		}
	}

	private RedisCacheConfiguration determineConfiguration(ClassLoader classLoader) {
		if (this.redisCacheConfiguration != null) {
			return this.redisCacheConfiguration;
		}
		else {
			org.springframework.boot.autoconfigure.cache.CacheProperties.Redis redisProperties = this.springCacheProperties.getRedis();
			redisProperties.setTimeToLive(Duration.ofHours(12));
			RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig();
			config = config.serializeValuesWith(RedisSerializationContext.SerializationPair
					.fromSerializer(new FSTSerializer()));
			if (redisProperties.getTimeToLive() != null) {
				config = config.entryTtl(redisProperties.getTimeToLive());
			}

			if (redisProperties.getKeyPrefix() != null) {
				config = config.prefixKeysWith(redisProperties.getKeyPrefix());
			}

			if (!redisProperties.isCacheNullValues()) {
				config = config.disableCachingNullValues();
			}

			if (!redisProperties.isUseKeyPrefix()) {
				config = config.disableKeyPrefix();
			}

			return config;
		}
	}
}
