package com.lg.dao.config;

import com.lg.dao.config.properties.*;
import com.lg.dao.core.SqlTemplateManager;
import com.lg.dao.core.batch.BatchOperationService;
import com.lg.dao.core.builder.SqlBuilderService;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.executor.DefaultLightOrmSqlExecutor;
import com.lg.dao.core.executor.LightOrmSqlExecutor;
import com.lg.dao.core.fill.DefaultMetaObjectHandler;
import com.lg.dao.core.fill.MetaObjectHandler;
import com.lg.dao.core.interceptor.LightOrmSqlInterceptorChain;
import com.lg.dao.core.sequence.SequenceDao;
import com.lg.dao.core.sequence.SequenceManager;
import com.lg.dao.core.sql.SqlPrintInterceptor;
import com.lg.dao.core.tenant.TenantSqlInterceptor;
import com.lg.dao.core.transaction.TransactionHelper;
import com.lg.dao.factory.DaoFactory;
import com.lg.dao.factory.EnhancedDaoFactory;
import com.lg.dao.helper.DaoHelper;

import com.lg.dao.mybatis.MybatisNativeXmlParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Light ORM自动装配类
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({
    BasicProperties.class,
    SqlPrintProperties.class,
    TenantProperties.class,
    CacheProperties.class, MybatisProxyProperties.class
})
@Import({CacheAutoConfiguration.class, EntityInfoManagerAutoConfiguration.class})
public class LightORMAutoConfiguration {

    /**
     * 配置SQL拦截器链
     */
    @Bean
    @ConditionalOnMissingBean
    public LightOrmSqlInterceptorChain sqlInterceptorChain() {
        return new LightOrmSqlInterceptorChain();
    }

    /**
     * 配置SQL执行器
     */
    @Bean
    @ConditionalOnMissingBean
    public LightOrmSqlExecutor lightOrmSqlExecutor(JdbcTemplate jdbcTemplate, LightOrmSqlInterceptorChain interceptorChain,UnifiedCacheManager unifiedCacheManager) {
        return new DefaultLightOrmSqlExecutor(jdbcTemplate, interceptorChain,unifiedCacheManager);
    }

    /**
     * 配置SQL构建服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SqlBuilderService sqlBuilderService(MetaObjectHandler metaObjectHandler) {
        return new SqlBuilderService(metaObjectHandler);
    }

    /**
     * 配置批量操作服务
     */
    @Bean
    @ConditionalOnMissingBean
    public BatchOperationService batchOperationService(JdbcTemplate jdbcTemplate, MetaObjectHandler metaObjectHandler) {
        return new BatchOperationService(jdbcTemplate, metaObjectHandler);
    }

    @Bean
    @ConditionalOnMissingBean(PlatformTransactionManager.class)
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 配置事务助手
     */
    @Bean
    @ConditionalOnMissingBean
    public TransactionHelper transactionHelper(PlatformTransactionManager transactionManager) {
        return new TransactionHelper(transactionManager);
    }

    /**
     * 配置SQL打印拦截器
     */
    @Bean
    @ConditionalOnProperty(prefix = "light.orm.sql-print", name = "enable", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    public SqlPrintInterceptor sqlPrintInterceptor(SqlPrintProperties properties) {
        return new SqlPrintInterceptor(
            properties.isShowParams(),
            properties.isFormat(),
            properties.isShowExecuteTime(),
            properties.getSlowSqlThreshold()
        );
    }

    /**
     * 配置多租户SQL拦截器
     */
    @Bean
    @ConditionalOnProperty(prefix = "light.orm.tenant", name = "enable", havingValue = "true", matchIfMissing = false)
    @ConditionalOnMissingBean
    public TenantSqlInterceptor tenantSqlInterceptor(TenantProperties properties) {
        return new TenantSqlInterceptor(
            properties.getMode(),
            properties.getTenantField(),
            properties.getSchemaPrefix(),
            properties.getIgnoreTables()
        );
    }

    /**
     * 配置统一缓存管理器
     */
    @Bean
    @ConditionalOnProperty(prefix = "light.orm.cache", name = "enable", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    public UnifiedCacheManager unifiedCacheManager(CacheProperties cacheProperties) {
        log.info("配置统一缓存管理器 UnifiedCacheManager");
        return new UnifiedCacheManager(cacheProperties);
    }

    /**
     * 配置默认元对象处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public MetaObjectHandler metaObjectHandler() {
        return new DefaultMetaObjectHandler();
    }

    /**
     * 配置SQL模板管理器（非优化版本，当没有统一缓存管理器时使用）
     */
    @Bean
    @ConditionalOnMissingBean(name = {"sqlTemplateManager"})
    public SqlTemplateManager sqlTemplateManager(BasicProperties properties, UnifiedCacheManager unifiedCacheManager) {
        log.info("配置标准SQL模板管理器（启用统一缓存）");
        return new SqlTemplateManager(properties,unifiedCacheManager);
    }

    /**
     * 配置增强版DAO工厂（优先使用）
     */
    @Bean
    @ConditionalOnMissingBean(name = "daoFactory")
    public EnhancedDaoFactory enhancedDaoFactory() {
        return new EnhancedDaoFactory();
    }
    
    /**
     * 配置基础DAO工厂（备用）
     */
    @Bean
    @ConditionalOnMissingBean
    public DaoFactory daoFactory() {
        return new DaoFactory();
    }
    
    /**
     * 配置DAO助手
     */
    @Bean
    @ConditionalOnMissingBean
    public DaoHelper daoHelper(@Autowired(required = false) UnifiedCacheManager unifiedCacheManager) {
        DaoHelper helper = new DaoHelper();
        if (unifiedCacheManager != null) {
            helper.setUnifiedCacheManager(unifiedCacheManager);
            log.info("DAO助手已配置统一缓存管理器");
        } else {
            log.info("DAO助手未配置统一缓存管理器（缓存启用）");
        }
        return helper;
    }

    /**
     * 配置序列DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public SequenceDao sequenceDao() {
        return new SequenceDao();
    }

    /**
     * 配置序列管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public SequenceManager sequenceManager(SequenceDao sequenceDao) {
        return new SequenceManager(sequenceDao);
    }

    /**
     * 配置MyBatis原生XML解析器（非优化版本）
     */
    @Bean
    @ConditionalOnProperty(prefix = "light.orm.mybatis", name = "enable", havingValue = "true")
    @ConditionalOnMissingBean(name = {"mybatisNativeXmlParser"})
    public MybatisNativeXmlParser mybatisNativeXmlParser(UnifiedCacheManager unifiedCacheManager) {
        log.info("配置标准MyBatis原生XML解析器（启用统一缓存）");
        return new MybatisNativeXmlParser(unifiedCacheManager);
    }


}