package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.common.redis.mq.config.MqProperties;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 任务恢复服务
 * 用于恢复长时间处理中的任务和失败的任务
 *
 * <AUTHOR>
 */
@Slf4j
public class TaskRecoveryService {

    private final MqProperties properties;
    private final RedissonClient redissonClient;

    // 恢复统计
    private final AtomicInteger recoveredTaskCount = new AtomicInteger(0);
    private final AtomicInteger failedRecoveryCount = new AtomicInteger(0);

    // 分布式锁key
    private static final String RECOVERY_LOCK_KEY = "task:recovery:lock";

    /**
     * 构造函数
     */
    public TaskRecoveryService(MqProperties properties, RedissonClient redissonClient) {
        this.properties = properties;
        this.redissonClient = redissonClient;
        log.info("任务恢复服务已初始化，配置：{}", properties.getRecovery());
    }

    /**
     * 定时执行任务恢复
     */
    @Scheduled(fixedRateString = "#{${light.mq.recovery.interval-minutes:5} * 60 * 1000}")
    public void scheduledRecovery() {
        // 检查是否启用恢复功能
        if (!properties.getRecovery().isEnabled()) {
            log.debug("任务恢复功能已禁用，跳过定时恢复");
            return;
        }

        log.debug("开始定时任务恢复");

        // 使用分布式锁确保只有一个实例执行恢复
        if (redissonClient != null) {
            RLock lock = redissonClient.getLock(RECOVERY_LOCK_KEY);
            try {
                // 尝试获取锁，最多等待1分钟，锁定时间为配置的超时时间
                int lockTimeoutMinutes = properties.getRecovery().getLockTimeoutMinutes();
                if (lock.tryLock(1, lockTimeoutMinutes, TimeUnit.MINUTES)) {
                    try {
                        performRecoveryWithLock();
                    } finally {
                        lock.unlock();
                    }
                } else {
                    log.debug("其他实例正在执行任务恢复，跳过本次执行");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("任务恢复被中断", e);
            } catch (Exception e) {
                log.error("定时任务恢复失败", e);
            }
        } else {
            // 如果没有Redis客户端，直接执行（单实例模式）
            try {
                performRecoveryWithLock();
            } catch (Exception e) {
                log.error("定时任务恢复失败", e);
            }
        }
    }
    
    /**
     * 执行任务恢复（带锁保护）
     */
    private void performRecoveryWithLock() {
        long startTime = System.currentTimeMillis();
        int totalRecovered = 0;
        int totalFailed = 0;

        try {
            MqProperties.Recovery recoveryConfig = properties.getRecovery();
            log.info("开始执行任务恢复，配置参数: batchSize={}, maxProcessingHours={}, waitingTimeoutMinutes={}, maxRetryAttempts={}",
                recoveryConfig.getBatchSize(), recoveryConfig.getMaxProcessingHours(),
                recoveryConfig.getWaitingTimeoutMinutes(), recoveryConfig.getMaxRetryAttempts());

            // 1. 恢复长时间处理中的任务
            int longRunningRecovered = recoverLongRunningTasks();
            totalRecovered += longRunningRecovered;

            // 2. 恢复失败的任务（重试）
            int failedTasksRecovered = recoverFailedTasks();
            totalRecovered += failedTasksRecovered;

            // 3. 恢复长时间等待的任务
            int waitingTasksRecovered = recoverWaitingTasks();
            totalRecovered += waitingTasksRecovered;

            // 更新统计信息
            recoveredTaskCount.addAndGet(totalRecovered);

            long duration = System.currentTimeMillis() - startTime;
            log.info("任务恢复完成，耗时: {}ms, 恢复任务数: {}, 累计恢复: {}, 累计失败: {}",
                duration, totalRecovered, recoveredTaskCount.get(), failedRecoveryCount.get());

        } catch (Exception e) {
            failedRecoveryCount.incrementAndGet();
            log.error("任务恢复过程中发生错误", e);
        }
    }

    /**
     * 执行任务恢复（公共接口）
     */
    public void performRecovery() {
        performRecoveryWithLock();
    }
    
    /**
     * 恢复长时间处理中的任务
     */
    private int recoverLongRunningTasks() {
        int recoveredCount = 0;
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);

            // 查找处理中超过指定时间的任务
            MqProperties.Recovery recoveryConfig = properties.getRecovery();
            Date cutoffTime = DateUtil.offsetHour(new Date(), -recoveryConfig.getMaxProcessingHours());

            List<JobMateMationDto> longRunningTasks = dao.lambdaQuery()
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING)
                .lt(JobMateMationDto::getCreateTime, DateUtil.formatDateTime(cutoffTime))
                .limit(recoveryConfig.getBatchSize())
                .list();

            for (JobMateMationDto taskDto : longRunningTasks) {
                try {
                    if (recoverLongRunningTask(taskDto)) {
                        recoveredCount++;
                    }
                } catch (Exception e) {
                    log.error("恢复长时间运行任务失败: {}", taskDto.getJobId(), e);
                }
            }

            if (recoveredCount > 0) {
                log.info("恢复长时间运行任务数量: {}", recoveredCount);
            }

        } catch (Exception e) {
            log.error("恢复长时间运行任务过程中发生错误", e);
        }
        return recoveredCount;
    }
    
    /**
     * 恢复失败的任务（重试机制）
     */
    private int recoverFailedTasks() {
        int recoveredCount = 0;
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);

            // 查找失败但可以重试的任务，并且距离上次失败有一定间隔（避免频繁重试）
            Date retryAfterTime = DateUtil.offsetMinute(new Date(), -5); // 5分钟后才重试

            MqProperties.Recovery recoveryConfig = properties.getRecovery();
            List<JobMateMationDto> failedTasks = dao.lambdaQuery()
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL)
                .lt(JobMateMationDto::getRetrys, recoveryConfig.getMaxRetryAttempts())
                .lt(JobMateMationDto::getComplateTime, DateUtil.formatDateTime(retryAfterTime))
                .limit(recoveryConfig.getBatchSize())
                .list();

            for (JobMateMationDto taskDto : failedTasks) {
                try {
                    if (recoverFailedTask(taskDto)) {
                        recoveredCount++;
                    }
                } catch (Exception e) {
                    log.error("重试失败任务失败: {}", taskDto.getJobId(), e);
                }
            }

            if (recoveredCount > 0) {
                log.info("重试失败任务数量: {}", recoveredCount);
            }

        } catch (Exception e) {
            log.error("重试失败任务过程中发生错误", e);
        }
        return recoveredCount;
    }
    
    /**
     * 恢复长时间等待的任务
     */
    private int recoverWaitingTasks() {
        int recoveredCount = 0;
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);

            // 查找等待状态超过一定时间的任务
            MqProperties.Recovery recoveryConfig = properties.getRecovery();
            Date cutoffTime = DateUtil.offsetMinute(new Date(), -recoveryConfig.getWaitingTimeoutMinutes());

            List<JobMateMationDto> waitingTasks = dao.lambdaQuery()
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT)
                .lt(JobMateMationDto::getCreateTime, DateUtil.formatDateTime(cutoffTime))
                .limit(recoveryConfig.getBatchSize())
                .list();

            for (JobMateMationDto taskDto : waitingTasks) {
                try {
                    if (recoverWaitingTask(taskDto)) {
                        recoveredCount++;
                    }
                } catch (Exception e) {
                    log.error("恢复等待任务失败: {}", taskDto.getJobId(), e);
                }
            }

            if (recoveredCount > 0) {
                log.info("恢复等待任务数量: {}", recoveredCount);
            }

        } catch (Exception e) {
            log.error("恢复等待任务过程中发生错误", e);
        }
        return recoveredCount;
    }
    
    /**
     * 恢复单个长时间运行的任务
     */
    private boolean recoverLongRunningTask(JobMateMationDto taskDto) {
        try {
            log.warn("发现长时间运行任务，将其标记为失败: {}", taskDto.getJobId());

            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            int updateCount = dao.lambdaUpdate()
                .eq(JobMateMationDto::getJobId, taskDto.getJobId())
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING) // 确保状态未被其他线程修改
                .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL)
                .set(JobMateMationDto::getResponseBody, "任务执行超时，被恢复服务标记为失败")
                .set(JobMateMationDto::getComplateTime, new Date())
                .update();

            return updateCount > 0;
        } catch (Exception e) {
            log.error("恢复长时间运行任务失败: {}", taskDto.getJobId(), e);
            return false;
        }
    }
    
    /**
     * 重试失败的任务
     */
    private boolean recoverFailedTask(JobMateMationDto taskDto) {
        try {
            // 增加重试次数
            int newRetryCount = (taskDto.getRetrys() != null ? taskDto.getRetrys() : 0) + 1;

            log.info("重试失败任务: {}, 重试次数: {}", taskDto.getJobId(), newRetryCount);

            // 解析任务数据
            BaseTask task = JSONUtil.toBean(taskDto.getRequestBody(), BaseTask.class);
            task.setTaskIdentity(taskDto.getJobId());

            // 更新重试次数和状态（使用乐观锁确保并发安全）
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            int updateCount = dao.lambdaUpdate()
                .eq(JobMateMationDto::getJobId, taskDto.getJobId())
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL) // 确保状态未被其他线程修改
                .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT)
                .set(JobMateMationDto::getRetrys, newRetryCount)
                .set(JobMateMationDto::getResponseBody, "任务重试中，重试次数: " + newRetryCount)
                .set(JobMateMationDto::getComplateTime, null) // 清空完成时间
                .update();

            if (updateCount > 0) {
                // 尝试重新提交任务到队列
                try {
                    resubmitTaskToQueue(task, taskDto);
                    log.info("任务重新提交成功: {}", taskDto.getJobId());
                    return true;
                } catch (Exception e) {
                    log.warn("任务重新提交失败，但已标记为等待状态: {}", taskDto.getJobId(), e);
                    // 即使重新提交失败，任务状态已经改为等待，可以被其他机制处理
                    return true;
                }
            } else {
                log.warn("任务状态更新失败，可能已被其他线程处理: {}", taskDto.getJobId());
                return false;
            }

        } catch (Exception e) {
            log.error("重试任务失败: {}", taskDto.getJobId(), e);
            return false;
        }
    }

    /**
     * 重新提交任务到队列
     */
    private void resubmitTaskToQueue(BaseTask task, JobMateMationDto taskDto) {
        try {
            // 从 requestBody 的 JSON 中提取提交类型
            String submitType = extractSubmitTypeFromRequestBody(taskDto);

            if ("JOBCONSUMER".equals(submitType)) {
                // JobConsumer 方式提交的任务恢复
                resubmitJobConsumerTask(task, taskDto);
            } else {
                // 默认的监听器方式任务恢复
                resubmitListenerTask(task, taskDto);
            }

        } catch (Exception e) {
            log.error("重新提交任务到队列失败: {}", task.getTaskIdentity(), e);
            throw e;
        }
    }

    /**
     * 恢复监听器方式提交的任务
     */
    private void resubmitListenerTask(BaseTask task, JobMateMationDto taskDto) {
        // 尝试从任务类型信息中获取监听器类名
        String listenerClassName = extractListenerClassName(taskDto);
        if (StrUtil.isNotBlank(listenerClassName)) {
            // 创建监听器实例
            RedisAmqListener listener = createListenerInstance(listenerClassName);
            if (listener != null) {
                // 重新提交任务
                RedisJob.submitJob(task, listener);
                log.info("使用监听器重新提交任务成功: {}, 监听器: {}", task.getTaskIdentity(), listenerClassName);
                return;
            }
        }

        // 如果无法获取监听器信息，尝试使用通用的重试机制
        log.warn("无法获取监听器信息，任务已标记为等待状态，需要业务模块自行处理: {}", task.getTaskIdentity());
    }

    /**
     * 恢复 JobConsumer 方式提交的任务
     */
    private void resubmitJobConsumerTask(BaseTask task, JobMateMationDto taskDto) {
        // 尝试从任务数据中获取监听器类名
        String listenerClassName = extractListenerClassName(taskDto);
        if (StrUtil.isNotBlank(listenerClassName)) {
            try {
                // 使用 getJobConsumer 方法重新创建 JobConsumer
                JobConsumer jobConsumer = RedisJob.getJobConsumer(task, listenerClassName);
                RedisJob.submitJob(jobConsumer);
                log.info("使用 JobConsumer 重新提交任务成功: {}, 监听器: {}", task.getTaskIdentity(), listenerClassName);
                return;
            } catch (Exception e) {
                log.warn("使用 JobConsumer 方式恢复任务失败，尝试降级为监听器方式: {}", task.getTaskIdentity(), e);
                // 降级为监听器方式
                resubmitListenerTask(task, taskDto);
                return;
            }
        }

        log.warn("JobConsumer 任务无法获取监听器信息，无法恢复: {}", task.getTaskIdentity());
    }

    /**
     * 从任务信息中提取监听器类名
     */
    private String extractListenerClassName(JobMateMationDto taskDto) {
        try {
            // 方法1: 从任务的 requestBody 中解析 BaseTask，查看是否有 listenerClass 字段
            String listenerClass = extractFromTaskData(taskDto);
            if (StrUtil.isNotBlank(listenerClass)) {
                log.debug("从任务数据中提取到监听器类名: {}", listenerClass);
                return listenerClass;
            }

            // 方法2: 根据队列代码和模块名称推断监听器类名
            String inferredClass = inferListenerClassFromQueueCode(taskDto);
            if (StrUtil.isNotBlank(inferredClass)) {
                log.debug("根据队列代码推断监听器类名: {}", inferredClass);
                return inferredClass;
            }

            log.debug("无法提取监听器类名: {}", taskDto.getJobId());
            return null;

        } catch (Exception e) {
            log.debug("提取监听器类名失败: {}", taskDto.getJobId(), e);
            return null;
        }
    }

    /**
     * 从任务数据中提取监听器类名
     */
    private String extractFromTaskData(JobMateMationDto taskDto) {
        try {
            if (StrUtil.isBlank(taskDto.getRequestBody())) {
                return null;
            }

            // 解析 requestBody 中的 JSON 数据
            cn.hutool.json.JSONObject taskJson = JSONUtil.parseObj(taskDto.getRequestBody());

            // 尝试获取 listenerClass 字段（新版本任务框架）
            String listenerClass = taskJson.getStr("listenerClass");
            if (StrUtil.isNotBlank(listenerClass)) {
                return listenerClass;
            }

            // 尝试获取 classPath 字段（兼容旧版本）
            String classPath = taskJson.getStr("classPath");
            if (StrUtil.isNotBlank(classPath)) {
                return classPath;
            }

            // 尝试从 taskData 中获取监听器信息（内部字段）
            cn.hutool.json.JSONObject taskData = taskJson.getJSONObject("taskData");
            if (taskData != null) {
                // 优先使用新的内部字段
                String listenerFromTaskData = taskData.getStr("_internal_listenerClass");
                if (StrUtil.isNotBlank(listenerFromTaskData)) {
                    return listenerFromTaskData;
                }

                // 兼容旧的字段名
                String legacyListener = taskData.getStr("_listenerClass");
                if (StrUtil.isNotBlank(legacyListener)) {
                    return legacyListener;
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("从任务数据中提取监听器类名失败: {}", taskDto.getJobId(), e);
            return null;
        }
    }

    /**
     * 根据队列代码推断监听器类名
     */
    private String inferListenerClassFromQueueCode(JobMateMationDto taskDto) {
        try {
            if (taskDto == null) {
                return null;
            }
            String requestBody = taskDto.getRequestBody();
            if (StrUtil.isBlank(requestBody)) {
                return null;
            }
            BaseTask task = JSONUtil.toBean(requestBody, BaseTask.class);
            if (task == null) {
                return null;
            }
            String queueCode = task.getQueueCode();
            String moduleName = task.getModuleName();

            // 根据常见的命名规范推断监听器类名
            // 例如: BaseSendTaskQueue -> BaseSendTaskListener
            if (queueCode.endsWith("Queue")) {
                String baseName = queueCode.substring(0, queueCode.length() - 5); // 移除 "Queue"

                // 尝试几种常见的监听器命名模式
                String[] patterns = {
                    baseName + "Listener",
                    baseName + "Handler",
                    baseName + "Processor",
                    baseName + "Consumer"
                };

                for (String pattern : patterns) {
                    String fullClassName = buildFullClassName(pattern, moduleName);
                    if (isValidListenerClass(fullClassName)) {
                        return fullClassName;
                    }
                }
            }

            // 尝试直接使用队列代码作为类名
            String directClassName = buildFullClassName(queueCode + "Listener", moduleName);
            if (isValidListenerClass(directClassName)) {
                return directClassName;
            }

            return null;
        } catch (Exception e) {
            log.debug("根据队列代码推断监听器类名失败: {}", taskDto.getJobId(), e);
            return null;
        }
    }

    /**
     * 构建完整的类名
     */
    private String buildFullClassName(String className, String moduleName) {
        if (StrUtil.isBlank(moduleName)) {
            // 如果没有模块名，尝试常见的包路径
            String[] commonPackages = {
                "com.lg.financecloud.admin.listener.",
                "com.lg.financecloud.common.redis.mq.listener.",
                "com.lg.financecloud." + (moduleName != null ? moduleName : "admin") + ".listener."
            };

            for (String packagePath : commonPackages) {
                String fullName = packagePath + className;
                if (isValidListenerClass(fullName)) {
                    return fullName;
                }
            }
            return className; // 返回简单类名，让后续处理
        }

        // 根据模块名构建包路径
        return "com.lg.financecloud." + moduleName + ".listener." + className;
    }



    /**
     * 验证监听器类是否有效
     */
    private boolean isValidListenerClass(String className) {
        try {
            if (StrUtil.isBlank(className)) {
                return false;
            }

            // 尝试加载类
            Class<?> clazz = Class.forName(className);

            // 检查是否是 RedisAmqListener 的子类
            return RedisAmqListener.class.isAssignableFrom(clazz);

        } catch (ClassNotFoundException e) {
            log.debug("监听器类不存在: {}", className);
            return false;
        } catch (Exception e) {
            log.debug("验证监听器类失败: {}", className, e);
            return false;
        }
    }

    /**
     * 创建监听器实例
     */
    private RedisAmqListener createListenerInstance(String className) {
        try {
            // 首先尝试从Spring容器获取
            try {
                return SpringUtil.getBean(className, RedisAmqListener.class);
            } catch (Exception e) {
                log.debug("从Spring容器获取监听器失败: {}", className);
            }

            // 尝试通过反射创建
            Class<?> clazz = Class.forName(className);
            if (RedisAmqListener.class.isAssignableFrom(clazz)) {
                return (RedisAmqListener) clazz.newInstance();
            }

            return null;
        } catch (Exception e) {
            log.error("创建监听器实例失败: {}", className, e);
            return null;
        }
    }
    
    /**
     * 恢复等待中的任务
     */
    private boolean recoverWaitingTask(JobMateMationDto taskDto) {
        try {
            log.warn("发现长时间等待任务，将其标记为失败: {}", taskDto.getJobId());

            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            int updateCount = dao.lambdaUpdate()
                .eq(JobMateMationDto::getJobId, taskDto.getJobId())
                .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT) // 确保状态未被其他线程修改
                .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL)
                .set(JobMateMationDto::getResponseBody, "任务等待超时，被恢复服务标记为失败")
                .set(JobMateMationDto::getComplateTime, new Date())
                .update();

            return updateCount > 0;
        } catch (Exception e) {
            log.error("恢复等待任务失败: {}", taskDto.getJobId(), e);
            return false;
        }
    }
    
    /**
     * 手动触发恢复
     */
    public void manualRecovery() {
        log.info("手动触发任务恢复");
        performRecovery();
    }

    /**
     * 获取恢复统计信息
     */
    public RecoveryStats getRecoveryStats() {
        MqProperties.Recovery recoveryConfig = properties.getRecovery();
        return new RecoveryStats(
            recoveredTaskCount.get(),
            failedRecoveryCount.get(),
            recoveryConfig.getBatchSize(),
            recoveryConfig.getMaxProcessingHours(),
            recoveryConfig.getWaitingTimeoutMinutes(),
            recoveryConfig.getMaxRetryAttempts()
        );
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        recoveredTaskCount.set(0);
        failedRecoveryCount.set(0);
        log.info("恢复统计信息已重置");
    }

    /**
     * 获取当前配置信息
     */
    public String getConfigInfo() {
        MqProperties.Recovery recoveryConfig = properties.getRecovery();
        return String.format(
            "TaskRecoveryService配置: batchSize=%d, maxProcessingHours=%d, waitingTimeoutMinutes=%d, maxRetryAttempts=%d, lockTimeoutMinutes=%d",
            recoveryConfig.getBatchSize(), recoveryConfig.getMaxProcessingHours(),
            recoveryConfig.getWaitingTimeoutMinutes(), recoveryConfig.getMaxRetryAttempts(),
            recoveryConfig.getLockTimeoutMinutes()
        );
    }

    /**
     * 恢复统计信息类
     */
    public static class RecoveryStats {
        private final int totalRecovered;
        private final int totalFailed;
        private final int batchSize;
        private final int maxProcessingHours;
        private final int waitingTimeoutMinutes;
        private final int maxRetryAttempts;

        public RecoveryStats(int totalRecovered, int totalFailed, int batchSize,
                           int maxProcessingHours, int waitingTimeoutMinutes, int maxRetryAttempts) {
            this.totalRecovered = totalRecovered;
            this.totalFailed = totalFailed;
            this.batchSize = batchSize;
            this.maxProcessingHours = maxProcessingHours;
            this.waitingTimeoutMinutes = waitingTimeoutMinutes;
            this.maxRetryAttempts = maxRetryAttempts;
        }

        public int getTotalRecovered() { return totalRecovered; }
        public int getTotalFailed() { return totalFailed; }
        public int getBatchSize() { return batchSize; }
        public int getMaxProcessingHours() { return maxProcessingHours; }
        public int getWaitingTimeoutMinutes() { return waitingTimeoutMinutes; }
        public int getMaxRetryAttempts() { return maxRetryAttempts; }

        @Override
        public String toString() {
            return String.format(
                "RecoveryStats{totalRecovered=%d, totalFailed=%d, batchSize=%d, maxProcessingHours=%d, waitingTimeoutMinutes=%d, maxRetryAttempts=%d}",
                totalRecovered, totalFailed, batchSize, maxProcessingHours, waitingTimeoutMinutes, maxRetryAttempts
            );
        }
    }

    /**
     * 从 requestBody 的 JSON 中提取提交类型
     * @param taskDto 任务数据传输对象
     * @return 提交类型：LISTENER 或 JOBCONSUMER，默认为 LISTENER
     */
    private String extractSubmitTypeFromRequestBody(JobMateMationDto taskDto) {
        try {
            String requestBody = taskDto.getRequestBody();
            if (StrUtil.isNotBlank(requestBody)) {
                JSONObject requestJson = JSONUtil.parseObj(requestBody);
                String submitType = requestJson.getStr("_submitType");
                if (StrUtil.isNotBlank(submitType)) {
                    log.debug("从 requestBody 中提取到提交类型: {} -> {}", taskDto.getJobId(), submitType);
                    return submitType;
                }
            }
        } catch (Exception e) {
            log.warn("从 requestBody 中提取提交类型失败: {}", taskDto.getJobId(), e);
        }

        // 默认返回 LISTENER 类型
        log.debug("未找到提交类型信息，默认使用 LISTENER 方式: {}", taskDto.getJobId());
        return "LISTENER";
    }
}
