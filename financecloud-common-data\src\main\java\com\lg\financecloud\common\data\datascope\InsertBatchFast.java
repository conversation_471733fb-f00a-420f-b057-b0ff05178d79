package com.lg.financecloud.common.data.datascope;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;


@Slf4j
public class InsertBatchFast extends AbstractMethod {

	protected InsertBatchFast() {
		super("insertBatchFast");
	}

//	@Override
//	public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
//		final String sql = "<script>#{sql}</script>";
//		SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
//		System.err.println(sql);
//		return this.addInsertMappedStatement(mapperClass, modelClass, "insertBatchFast", sqlSource, new NoKeyGenerator(), null, null);
//	}

	/**
	 * insert into user(id, name, age) values (1, "a", 17), (2, "b", 18);
	 <script>
	 insert into user(id, name, age) values
	 <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
	 #{item.id}, #{item.name}, #{item.age}
	 </foreach>
	 </script>
	 */
	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
		final String sql = "<script>${sql}</script>";
//		final String fieldSql = prepareFieldSql(tableInfo);
//		final String valueSql = prepareValuesSql(tableInfo);
//		final String sqlResult = String.format(sql, tableInfo.getTableName(), fieldSql, valueSql);
		SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
		// 第三个参数必须和RootMapper的自定义方法名一致
		return this.addInsertMappedStatement(mapperClass, modelClass, "insertBatchFast", sqlSource, new NoKeyGenerator(), null, null);
	}

	private String prepareFieldSql(TableInfo tableInfo) {
		StringBuilder fieldSql = new StringBuilder();
		fieldSql.append(tableInfo.getKeyColumn()).append(",");
		tableInfo.getFieldList().forEach(x -> {
			fieldSql.append(x.getColumn()).append(",");
		});
		fieldSql.delete(fieldSql.length() - 1, fieldSql.length());
		fieldSql.insert(0, "(");
		fieldSql.append(")");
		return fieldSql.toString();
	}

	private String prepareValuesSql(TableInfo tableInfo) {
		final StringBuilder valueSql = new StringBuilder();
		valueSql.append("<foreach collection=\"list\" item=\"item\" index=\"index\" open=\"(\" separator=\"),(\" close=\")\">");
		valueSql.append("#{item.").append(tableInfo.getKeyProperty()).append("},");
		tableInfo.getFieldList().forEach(x -> valueSql.append("#{item.").append(x.getProperty()).append("},"));
		valueSql.delete(valueSql.length() - 1, valueSql.length());
		valueSql.append("</foreach>");
		return valueSql.toString();
	}


}
