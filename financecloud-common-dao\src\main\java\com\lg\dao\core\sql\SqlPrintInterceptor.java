package com.lg.dao.core.sql;

import cn.hutool.db.sql.SqlUtil;
import com.lg.dao.config.properties.SqlPrintProperties;
import com.lg.dao.core.interceptor.LightOrmSqlInterceptor;
import com.lg.dao.core.interceptor.SqlInterceptorContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.util.StopWatch;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SQL打印拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlPrintInterceptor implements LightOrmSqlInterceptor {
    private final SqlPrintProperties properties;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public SqlPrintInterceptor(boolean showParams, boolean format, boolean showExecuteTime, long slowSqlThreshold) {
        this.properties = new SqlPrintProperties();
        this.properties.setShowParams(showParams);
        this.properties.setFormat(format);
        this.properties.setShowExecuteTime(showExecuteTime);
        this.properties.setSlowSqlThreshold(slowSqlThreshold);
    }

    @Override
    public String beforeExecute(String sql, List<Object> params, SqlInterceptorContext context) {
        // 开始计时
        if (properties.isShowExecuteTime()) {
            StopWatch stopWatch = new StopWatch("SQL_EXECUTION");
            stopWatch.start();
            context.setStopWatch(stopWatch);
        }

        // 格式化SQL并替换参数
        String executableSql = formatSqlWithParams(sql, params);
        String formattedSql = properties.isFormat() ? SqlUtil.formatSql(executableSql) : executableSql;

        // 打印SQL
        StringBuilder logBuilder = new StringBuilder("\n======= SQL Statement =======\n");
        logBuilder.append(formattedSql);

        // 打印原始参数
        if (properties.isShowParams() && params != null && !params.isEmpty()) {
            logBuilder.append("\n======= Original Parameters =======\n");
            logBuilder.append(formatParams(params));
        }

        log.info(logBuilder.toString());
        return sql;
    }

    @Override
    public void afterExecute(String sql, List<Object> params, Object result, SqlInterceptorContext context) {
        if (!properties.isShowExecuteTime()) {
            return;
        }

        StopWatch stopWatch = (StopWatch) context.getStopWatch();
        if (stopWatch != null && stopWatch.isRunning()) {
            stopWatch.stop();
            long executionTime = stopWatch.getTotalTimeMillis();
            StringBuilder logBuilder = new StringBuilder("\n======= Execution Time =======\n");
            logBuilder.append(executionTime).append("ms");

            // 检查是否是慢SQL
            if (executionTime > properties.getSlowSqlThreshold()) {
                logBuilder.append(" [SLOW SQL]");
                log.warn(logBuilder.toString());
            } else {
                log.info(logBuilder.toString());
            }
        }
    }

    @Override
    public int getOrder() {
        // 确保在租户处理之后执行，所以order值要比租户的大
        return Ordered.LOWEST_PRECEDENCE - 50; // 比租户拦截器的 -100 大，所以会在租户处理后执行
    }

    /**
     * 格式化SQL并替换参数
     */
    private String formatSqlWithParams(String sql, List<Object> params) {
        if (params == null || params.isEmpty()) {
            return sql;
        }

        StringBuilder result = new StringBuilder(sql);
        int paramIndex = 0;
        int questionMarkIndex;
        
        // 从前往后依次替换问号
        while ((questionMarkIndex = result.indexOf("?")) != -1 && paramIndex < params.size()) {
            Object param = params.get(paramIndex++);
            String paramStr = formatParamValue(param);
            result.replace(questionMarkIndex, questionMarkIndex + 1, paramStr);
        }

        return result.toString();
    }

    /**
     * 格式化参数值
     */
    private String formatParamValue(Object param) {
        if (param == null) {
            return "NULL";
        }
        if (param instanceof String) {
            return "'" + escapeString((String) param) + "'";
        }
        if (param instanceof Date) {
            return "'" + DATE_FORMAT.format((Date) param) + "'";
        }
        if (param instanceof Boolean) {
            return ((Boolean) param) ? "1" : "0";
        }
        if (param instanceof Number) {
            return param.toString();
        }
        // 其他类型都用字符串形式
        return "'" + escapeString(param.toString()) + "'";
    }

    /**
     * 转义字符串中的特殊字符（修复换行问题）
     */
    private String escapeString(String str) {
        return str.replace("'", "''")
                .replace("\\", "\\\\")
                .replaceAll("\\s+", " "); // 新增：合并所有空白符为单个空格
    }

    /**
     * 格式化SQL
     */
    private String formatSql(String sql) {
        String[] keywords = {"SELECT", "FROM", "WHERE", "AND", "OR", "ORDER BY", "GROUP BY", "HAVING", "JOIN", "LEFT JOIN", "RIGHT JOIN", "INNER JOIN", "OUTER JOIN", "ON", "INSERT INTO", "VALUES", "UPDATE", "SET", "DELETE FROM"};
        
        String formattedSql = sql.replaceAll("\\s+", " ").trim();
        
        for (String keyword : keywords) {
            formattedSql = formattedSql.replaceAll("(?i)\\s*" + keyword + "\\s*", "\n" + keyword + " ");
        }
        
        return formattedSql.trim();
    }

    /**
     * 格式化参数列表
     */
    private String formatParams(List<Object> params) {
        return params.stream()
                .map(param -> param == null ? "null" : param.toString())
                .collect(Collectors.joining(", "));
    }
} 