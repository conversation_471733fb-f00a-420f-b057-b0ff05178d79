package com.lg.dao.core;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.annotation.ColumnInfo;
import com.lg.dao.core.cache.UnifiedCacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 实体映射缓存测试
 * 验证独立缓存类型的正确性和高效性
 */
public class EntityMappingCacheTest {

    private CacheProperties cacheProperties;
    private UnifiedCacheManager cacheManager;
    
    @Mock
    private ResultSet resultSet;
    
    @Mock
    private ResultSetMetaData metaData;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);

        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();

        // 初始化 EntityInfoManager 的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    /**
     * 测试实体类
     */
    @Table(name = "test_user")
    public static class TestUser {
        @Id
        @Column(name = "user_id")
        private Long id;
        
        @Column(name = "user_name")
        private String name;
        
        @Column(name = "user_email")
        private String email;
        
        private Integer age; // 没有注解，使用默认映射
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }



    /**
     * 测试 EntityRowMapper 缓存功能（使用 ENTITY_CACHE 和 METADATA_CACHE）
     */
    @Test
    public void testEntityRowMapperCache() throws Exception {
        // 创建带缓存的 EntityRowMapper
        EntityRowMapper<TestUser> mapperWithCache = new EntityRowMapper<>(TestUser.class, cacheManager);
        
        // 创建不带缓存的 EntityRowMapper
        EntityRowMapper<TestUser> mapperWithoutCache = new EntityRowMapper<>(TestUser.class);
        
        // 模拟 ResultSet
        when(resultSet.getMetaData()).thenReturn(metaData);
        when(metaData.getColumnCount()).thenReturn(4);
        when(metaData.getColumnLabel(1)).thenReturn("user_id");
        when(metaData.getColumnLabel(2)).thenReturn("user_name");
        when(metaData.getColumnLabel(3)).thenReturn("user_email");
        when(metaData.getColumnLabel(4)).thenReturn("age");
        
        when(resultSet.getObject(1)).thenReturn(1L);
        when(resultSet.getObject(2)).thenReturn("张三");
        when(resultSet.getObject(3)).thenReturn("<EMAIL>");
        when(resultSet.getObject(4)).thenReturn(25);
        
        // 测试映射功能
        TestUser userWithCache = mapperWithCache.mapRow(resultSet, 1);
        TestUser userWithoutCache = mapperWithoutCache.mapRow(resultSet, 1);
        
        assertNotNull(userWithCache, "带缓存的映射结果不应为空");
        assertNotNull(userWithoutCache, "不带缓存的映射结果不应为空");
        
        assertEquals(userWithCache.getId(), userWithoutCache.getId(), "ID应该一致");
        assertEquals(userWithCache.getName(), userWithoutCache.getName(), "姓名应该一致");
        assertEquals(userWithCache.getEmail(), userWithoutCache.getEmail(), "邮箱应该一致");
        assertEquals(userWithCache.getAge(), userWithoutCache.getAge(), "年龄应该一致");
        
        System.out.println("EntityRowMapper 缓存测试通过");
    }

    /**
     * 测试实体映射性能对比
     */




    /**
     * 测试实体信息缓存一致性
     */
    @Test
    public void testEntityInfoCacheConsistency() throws Exception {
        // 创建多个 EntityRowMapper 实例
        EntityRowMapper<TestUser> mapper1 = new EntityRowMapper<>(TestUser.class, cacheManager);
        EntityRowMapper<TestUser> mapper2 = new EntityRowMapper<>(TestUser.class, cacheManager);
        
        // 模拟 ResultSet
        when(resultSet.getMetaData()).thenReturn(metaData);
        when(metaData.getColumnCount()).thenReturn(2);
        when(metaData.getColumnLabel(1)).thenReturn("user_id");
        when(metaData.getColumnLabel(2)).thenReturn("user_name");
        
        when(resultSet.getObject(1)).thenReturn(1L);
        when(resultSet.getObject(2)).thenReturn("测试用户");
        
        // 两个实例应该使用相同的缓存
        TestUser user1 = mapper1.mapRow(resultSet, 1);
        TestUser user2 = mapper2.mapRow(resultSet, 1);
        
        assertNotNull(user1, "第一个映射器结果不应为空");
        assertNotNull(user2, "第二个映射器结果不应为空");
        assertEquals(user1.getId(), user2.getId(), "两个映射器的结果应该一致");
        assertEquals(user1.getName(), user2.getName(), "两个映射器的结果应该一致");
        
        System.out.println("实体信息缓存一致性测试通过");
    }
}
