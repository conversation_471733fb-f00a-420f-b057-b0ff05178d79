package com.lg.dao.example;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.lg.dao.core.BaseDao;
import com.lg.dao.helper.DaoHelper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON查询示例类
 * 演示如何使用BaseDao进行JSON查询，支持字段自动驼峰命名转换
 */
@Component
public class JsonQueryExample {

    /**
     * 示例1：基本JSON查询（默认驼峰命名）
     * 查询用户信息，字段自动转换为驼峰命名
     */
    public JSONArray example1_BasicJsonQuery() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 执行查询，字段会自动从 user_name, create_time 转换为 userName, createTime
        String sql = "SELECT user_id, user_name, email, create_time FROM users WHERE status = ?";
        JSONArray result = baseDao.selectJsonArray(sql, "ACTIVE");
        
        System.out.println("基本JSON查询结果：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
    
    /**
     * 示例2：禁用驼峰命名转换
     * 保持原始字段名不变
     */
    public JSONArray example2_NoToCamelCase() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 不转换驼峰命名，保持原始字段名
        String sql = "SELECT user_id, user_name, email FROM users LIMIT 5";
        JSONArray result = baseDao.selectJsonArray(sql, new Object[]{}, false);
        
        System.out.println("禁用驼峰命名转换结果：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
    
    /**
     * 示例3：使用参数列表进行JSON查询
     * 支持多个参数
     */
    public JSONArray example3_JsonQueryWithParamList() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        String sql = "SELECT order_id, user_id, order_amount, order_status, create_time " +
                    "FROM orders WHERE user_id = ? AND order_status IN (?, ?) ORDER BY create_time DESC";
        
        List<Object> params = ListUtil.of(1001L, "PENDING", "COMPLETED");
        JSONArray result = baseDao.selectJsonArray(sql, params);
        
        System.out.println("参数列表JSON查询结果：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
    
    /**
     * 示例4：使用模板进行JSON查询
     * 支持SQL模板和参数绑定
     */
    public JSONArray example4_JsonQueryByTemplate() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        // 假设有一个名为 "getUserOrders" 的SQL模板
        String templateName = "getUserOrders";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", 1001L);
        params.put("startDate", "2024-01-01");
        params.put("endDate", "2024-12-31");
        
        JSONArray result = baseDao.selectJsonArrayByTemplate(templateName, params);
        
        System.out.println("模板JSON查询结果：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
    
    /**
     * 示例5：复杂聚合查询返回JSON
     * 统计分析类查询
     */
    public JSONArray example5_ComplexAggregationJson() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        String sql = "" +
                " SELECT \n" +
                "                DATE_FORMAT(create_time, '%Y-%m') as order_month,\n" +
                "                COUNT(*) as order_count,\n" +
                "                SUM(order_amount) as total_amount,\n" +
                "                AVG(order_amount) as avg_amount,\n" +
                "                MAX(order_amount) as max_amount,\n" +
                "                MIN(order_amount) as min_amount\n" +
                "            FROM orders \n" +
                "            WHERE create_time >= ? AND create_time < ?\n" +
                "            GROUP BY DATE_FORMAT(create_time, '%Y-%m')\n" +
                "            ORDER BY order_month DESC";
        
        JSONArray result = baseDao.selectJsonArray(sql, "2024-01-01", "2025-01-01");
        
        System.out.println("复杂聚合JSON查询结果：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
    
    /**
     * 示例6：处理JSON查询结果
     * 演示如何操作返回的JSONArray
     */
    public void example6_ProcessJsonResult() {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        String sql = "SELECT user_id, user_name, email, balance FROM users WHERE balance > ?";
        JSONArray result = baseDao.selectJsonArray(sql, 1000.0);
        
        System.out.println("处理JSON查询结果：");
        System.out.println("总记录数：" + result.size());
        
        // 遍历结果
        for (int i = 0; i < result.size(); i++) {
            JSONObject user = result.getJSONObject(i);
            Long userId = user.getLong("userId");
            String userName = user.getStr("userName");
            String email = user.getStr("email");
            Double balance = user.getDouble("balance");
            
            System.out.printf("用户ID: %d, 用户名: %s, 邮箱: %s, 余额: %.2f%n", 
                            userId, userName, email, balance);
        }
        
        // 计算总余额
        double totalBalance = result.stream()
            .mapToDouble(obj -> ((JSONObject) obj).getDouble("balance"))
            .sum();
        
        System.out.printf("所有用户总余额：%.2f%n", totalBalance);
    }
    
    /**
     * 示例7：动态字段查询
     * 根据条件动态选择字段
     */
    public JSONArray example7_DynamicFieldQuery(boolean includePrivateInfo) {
        BaseDao baseDao = DaoHelper.getBaseDao();
        
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT user_id, user_name, email");
        
        if (includePrivateInfo) {
            sqlBuilder.append(", phone_number, id_card, address");
        }
        
        sqlBuilder.append(", create_time FROM users WHERE status = ?");
        
        JSONArray result = baseDao.selectJsonArray(sqlBuilder.toString(), "ACTIVE");
        
        System.out.println("动态字段查询结果（包含私密信息：" + includePrivateInfo + "）：");
        System.out.println(result.toStringPretty());
        
        return result;
    }
}