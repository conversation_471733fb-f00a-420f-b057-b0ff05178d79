/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.entity.SysTenant;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/19
 * <p>
 * 租户接口
 */
@FeignClient(contextId = "remoteTenantService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteTenantService {

	/**
	 * 查询全部有效租户
	 * @param from 内部标志
	 * @return
	 */
	@GetMapping("/tenant/list")
	R<List<SysTenant>> list(@RequestHeader(SecurityConstants.FROM) String from);

	@PostMapping("/tenant/getTenantListByTaxCodes")
	R<List<SysTenant>> getTenantListByTaxCodes(@RequestBody List<String> taxCodes,@RequestHeader(SecurityConstants.FROM) String from);

	// 新增租户
	@PostMapping("/tenant/addtenant")
	Integer addTenantInfo(@RequestBody SysTenant sysTenant, @RequestParam("phone") String phone , @RequestParam("nickname") String nickname ,@RequestHeader(SecurityConstants.FROM) String from);

	// 删除租户
	@DeleteMapping("/tenant/deltenant/{id}")
	R delTenant(@PathVariable("id") Integer id, @RequestHeader(SecurityConstants.FROM) String from);
	// 根据id查询
	@GetMapping("/tenant/getById/{tenantId}")
	R<SysTenant> getById(@PathVariable("tenantId") Integer id, @RequestHeader(SecurityConstants.FROM) String from);

	// 修改租户
	@PutMapping("/tenant/updatetenant")
	R updateTenant(@RequestBody SysTenant sysTenant,@RequestHeader(SecurityConstants.FROM) String from);
}
