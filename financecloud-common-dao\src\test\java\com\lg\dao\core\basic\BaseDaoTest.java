package com.lg.dao.core.basic;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.EntityRowMapper;
import com.lg.dao.core.Page;
import com.lg.dao.core.sql.SqlBuilder;
import com.lg.dao.helper.DaoHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest()
@ActiveProfiles("test")
@Transactional
class BaseDaoTest {

    private BaseDao baseDao;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        baseDao=  DaoHelper.getBaseDao();
        // 清理测试数据
        jdbcTemplate.execute("DELETE FROM t_user");
    }

    @Test
    void testInsert() {
        // 准备测试数据
        User user = new User();
        user.setUserName("测试用户");
        user.setAge(25);
        user.setTenantId("1001");
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 执行插入
        int result = baseDao.insert(user);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证数据库中的数据
        Long count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_user WHERE user_name = ?", Long.class, "测试用户");
        assertEquals(1L, count);
    }

    @Test
    void testUpdate() {
        // 先插入一条数据
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "原始用户", 20, "1001", LocalDateTime.now(), LocalDateTime.now());
        
        // 查询插入的数据
        User user = jdbcTemplate.queryForObject(
                "SELECT * FROM t_user WHERE user_name = ?",
                new EntityRowMapper<>(User.class),
                "原始用户"
        );
        
        // 修改数据
        user.setUserName("修改后用户");
        user.setAge(30);
        user.setUpdateTime(LocalDateTime.now());
        
        // 执行更新
        int result = baseDao.update(user);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证数据库中的数据
        String updatedName = jdbcTemplate.queryForObject(
                "SELECT user_name FROM t_user WHERE id = ?", String.class, user.getId());
        assertEquals("修改后用户", updatedName);
    }

    @Test
    void testDelete() {
        // 先插入一条数据
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "待删除用户", 25, "1001", LocalDateTime.now(), LocalDateTime.now());
        
        // 查询插入的数据
        User user = jdbcTemplate.queryForObject(
                "SELECT * FROM t_user WHERE user_name = ?",
                new EntityRowMapper<>(User.class),
                "待删除用户"
        );
        
        // 执行删除
        int result = baseDao.delete(user);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证数据库中的数据已被删除
        Long count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_user WHERE id = ?", Long.class, user.getId());
        assertEquals(0L, count);
    }

    @Test
    void testSelectOne() {
        // 先插入测试数据
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "查询用户", 28, "1001", LocalDateTime.now(), LocalDateTime.now());
        
        // 构建查询条件
        SqlBuilder builder = new SqlBuilder()
                .SELECT()
                .FROM("t_user")
                .WHERE()
                .AND().append("user_name = ?", "查询用户");
        
        // 执行查询
        User user = baseDao.selectOne(User.class, builder);
        
        // 验证结果
        assertNotNull(user);
        assertEquals("查询用户", user.getUserName());
        assertEquals(28, user.getAge());
        assertEquals("1001", user.getTenantId());
    }

    @Test
    void testSelectList() {
        // 先插入多条测试数据
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "用户1", 25, "1001", LocalDateTime.now(), LocalDateTime.now());
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "用户2", 30, "1001", LocalDateTime.now(), LocalDateTime.now());
        jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                "用户3", 35, "1002", LocalDateTime.now(), LocalDateTime.now());
        
        // 构建查询条件
        SqlBuilder builder = new SqlBuilder()
                .SELECT()
                .FROM("t_user")
                .WHERE()
                .AND().EQ("tenant_id", "1001")
                .ORDER_BY("age ASC");
        
        // 执行查询
        List<User> users = baseDao.selectList(User.class, builder);
        
        // 验证结果
        assertNotNull(users);
        assertEquals(2, users.size());
        assertEquals("用户1", users.get(0).getUserName());
        assertEquals("用户2", users.get(1).getUserName());
    }

    @Test
    void testSelectPage() {
        // 先插入多条测试数据
        for (int i = 1; i <= 10; i++) {
            jdbcTemplate.update("INSERT INTO t_user (user_name, age, tenant_id, create_time, update_time) VALUES (?, ?, ?, ?, ?)",
                    "用户" + i, 20 + i, "1001", LocalDateTime.now(), LocalDateTime.now());
        }
        
        // 构建查询条件
        SqlBuilder builder = new SqlBuilder()
                .SELECT()
                .FROM("t_user")
                .WHERE().EQ("tenant_id", "1001")

                .ORDER_BY("age ASC");
        
        // 执行分页查询
        Page<User> page = baseDao.selectPage(User.class, builder, 2, 3);
        
        // 验证结果
        assertNotNull(page);
        assertEquals(2, page.getPageNum());
        assertEquals(3, page.getPageSize());
        assertEquals(10L, page.getTotal());
        assertEquals(4, page.getPages());
        assertEquals(3, page.getRecords().size());
        assertTrue(page.isHasNext());
        assertTrue(page.isHasPrev());
    }
}