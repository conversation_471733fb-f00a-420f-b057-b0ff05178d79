//package com.lg.financecloud.common.data.dao;
//
//import com.github.foxnic.commons.log.Logger;
//import com.github.foxnic.dao.filter.SQLFilter;
//import com.github.foxnic.dao.filter.SQLFilterObject;
//import com.github.foxnic.dao.spec.DAO;
//import com.github.foxnic.dao.spec.DAOBuilder;
//import com.github.foxnic.sql.meta.DBDataType;
//import com.github.foxnic.sql.treaty.DBTreaty;
//import com.lg.financecloud.admin.api.dto.SessionUser;
//import com.lg.financecloud.common.data.tenant.SwithDbSqlUtil;
//import com.lg.financecloud.common.data.tenant.TenantContextHolder;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
//@Configuration
//@ConditionalOnBean(DataSource.class)
//@Slf4j
//public class DAOConfig {
//
//
//	private Boolean printSQL =true;
//
//	private  Boolean startRelationMonitor=false;
//
//	@Bean
//	@Primary
//	public DAO primaryDAO (DataSource dataSource, DataSourceTransactionManager transactionManager) {
//		try {
//			if(printSQL==null) printSQL=false;
//			DAO dao= (new DAOBuilder().datasource(dataSource)).build();
////			if(log.isDebugEnabled()) {
////				dao.setPrintSQL(printSQL);
////				dao.setPrintSQLSimple(printSQL);
////			}
//			dao.setDBTreaty(getDBTreaty());
//
//			//设置序列相关的配置
//			dao.setSequenceTable("sys_sequence");
//			dao.setSequenceProcedure("NEXT_VAL");
//			dao.setTransactionManager(transactionManager);
//			// 注册拦截器 做租户切换
//			dao.getSQLFilterChain().addFilter(new SQLFilter("db_switch",9) {
//
//				@Override
//				public SQLFilterObject doSelectFilter(SQLFilterObject sqlobj) {
//					String sql  = sqlobj.getSql();
//					String swithDbSql4SqlParse = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
//					sqlobj.setSql(swithDbSql4SqlParse, sqlobj.getParams());
//
//					return super.doSelectFilter(sqlobj);
//				}
//
//				@Override
//				public SQLFilterObject doStatementFilter(SQLFilterObject sqlobj) {
//					String sql  = sqlobj.getSql();
//					String swithDbSql4SqlParse = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
//					sqlobj.setSql(swithDbSql4SqlParse, sqlobj.getParams());
//					return super.doStatementFilter(sqlobj);
//				}
//
//				@Override
//				public SQLFilterObject doInsertFilter(SQLFilterObject sqlobj) {
//					String sql  = sqlobj.getSql();
//					String swithDbSql4SqlParse = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
//					sqlobj.setSql(swithDbSql4SqlParse, sqlobj.getParams());
//					return super.doInsertFilter(sqlobj);
//				}
//
//				@Override
//				public SQLFilterObject doUpdateFilter(SQLFilterObject sqlobj) {
//					String sql  = sqlobj.getSql();
//					String swithDbSql4SqlParse = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
//					sqlobj.setSql(swithDbSql4SqlParse, sqlobj.getParams());
//					return super.doUpdateFilter(sqlobj);
//				}
//
//				@Override
//				public SQLFilterObject doDeleteFilter(SQLFilterObject sqlobj) {
//					String sql  = sqlobj.getSql();
//					String swithDbSql4SqlParse = SwithDbSqlUtil.getSwithDbSql4SqlParse(sql);
//					sqlobj.setSql(swithDbSql4SqlParse, sqlobj.getParams());
//					return super.doDeleteFilter(sqlobj);
//				}
//			});
//
//
//
//
//			return dao;
//		} catch (Exception e) {
//			Logger.error("创建DAO失败",e);
//			return null;
//		}
//	}
//
//	public DBTreaty getDBTreaty() {
//		DBTreaty dbTreaty=new DBTreaty();
//		dbTreaty.setAllowDeleteWithoutWhere(false);
//		dbTreaty.setAllowUpdateWithoutWhere(false);
//		dbTreaty.setUserIdDataType(DBDataType.STRING);
//		dbTreaty.setTenantIdField("tenant_id");
//		dbTreaty.setCreateTimeField("create_time");
//		dbTreaty.setUpdateTimeField("update_time");
//		dbTreaty.setDeletedField("del_flag");
//		dbTreaty.setAutoCastLogicField(false);
//		//
//		dbTreaty.setFalseValue(0);
//		dbTreaty.setTrueValue(1);
//		dbTreaty.setTenantIdHandler(()->{
//			return TenantContextHolder.getTenantId()+"";
//		});
//
//		//
//		return dbTreaty;
//	}
//
//}
