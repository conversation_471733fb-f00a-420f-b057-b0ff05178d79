# FinanceCloud DAO 框架代码规范

## 📋 JDK 兼容性要求

### 🎯 JDK 版本要求
- **必须兼容 JDK 1.8**
- 不得使用 JDK 9+ 的新特性
- 所有代码必须能在 JDK 1.8 环境下编译和运行

### ❌ 禁止使用的 JDK 9+ 特性

#### 1. var 关键字 (JDK 10+)
```java
// ❌ 错误 - 使用 var
var list = new ArrayList<String>();
var map = new HashMap<String, Object>();

// ✅ 正确 - 明确类型声明
List<String> list = new ArrayList<>();
Map<String, Object> map = new HashMap<>();
```

#### 2. 集合工厂方法 (JDK 9+)
```java
// ❌ 错误 - List.of(), Set.of(), Map.of()
List<String> list = List.of("a", "b", "c");
Set<String> set = Set.of("a", "b", "c");
Map<String, String> map = Map.of("key1", "value1", "key2", "value2");

// ✅ 正确 - 传统方式
List<String> list = Arrays.asList("a", "b", "c");
Set<String> set = new HashSet<>(Arrays.asList("a", "b", "c"));
Map<String, String> map = new HashMap<>();
map.put("key1", "value1");
map.put("key2", "value2");
```

#### 3. Optional 新方法 (JDK 9+)
```java
// ❌ 错误 - Optional.or(), ifPresentOrElse()
optional.or(() -> Optional.of("default"));
optional.ifPresentOrElse(System.out::println, () -> System.out.println("empty"));

// ✅ 正确 - JDK 8 兼容方式
Optional<String> result = optional.isPresent() ? optional : Optional.of("default");
if (optional.isPresent()) {
    System.out.println(optional.get());
} else {
    System.out.println("empty");
}
```

#### 4. Stream 新方法 (JDK 9+)
```java
// ❌ 错误 - takeWhile(), dropWhile()
stream.takeWhile(x -> x < 5);
stream.dropWhile(x -> x < 5);

// ✅ 正确 - 使用 filter 等 JDK 8 方法
stream.filter(x -> x < 5);
```

#### 5. 接口私有方法 (JDK 9+)
```java
// ❌ 错误 - 接口中的私有方法
public interface MyInterface {
    private void helper() { }  // JDK 9+
}

// ✅ 正确 - 使用默认方法或抽象类
public interface MyInterface {
    default void helper() { }  // JDK 8
}
```

## 📝 代码风格规范

### 1. 类型声明
```java
// ✅ 推荐 - 使用钻石操作符
Map<String, List<EntityInfo>> cache = new HashMap<>();
List<String> names = new ArrayList<>();

// ✅ 可接受 - 完整类型声明
Map<String, List<EntityInfo>> cache = new HashMap<String, List<EntityInfo>>();
```

### 2. 集合初始化
```java
// ✅ 推荐 - 使用 Arrays.asList
List<String> list = Arrays.asList("a", "b", "c");

// ✅ 推荐 - 使用构造器
Set<String> set = new HashSet<>(Arrays.asList("a", "b", "c"));

// ✅ 推荐 - 逐个添加
Map<String, String> map = new HashMap<>();
map.put("key1", "value1");
map.put("key2", "value2");
```

### 3. 异常处理
```java
// ✅ 推荐 - 明确异常类型
try {
    // 操作
} catch (IllegalArgumentException e) {
    log.warn("参数错误", e);
} catch (Exception e) {
    log.error("未知错误", e);
}
```

### 4. 日志记录
```java
// ✅ 推荐 - 使用 Slf4j
@Slf4j
public class MyClass {
    public void method() {
        log.debug("调试信息: {}", param);
        log.info("操作完成");
        log.warn("警告信息: {}", message);
        log.error("错误信息", exception);
    }
}
```

### 5. 空值检查
```java
// ✅ 推荐 - 明确的空值检查
if (value != null && !value.isEmpty()) {
    // 处理
}

// ✅ 推荐 - 使用 Optional (JDK 8)
Optional<String> optional = Optional.ofNullable(getValue());
if (optional.isPresent()) {
    // 处理
}
```

## 🔧 缓存相关规范

### 1. 缓存键命名
```java
// ✅ 推荐 - 统一的缓存键格式
String cacheKey = entityClass.getName();
String cacheKey = "entity_info:" + entityClass.getName();
String cacheKey = "column_mapping:" + entityClass.getName() + ":" + column;
```

### 2. 缓存类型使用
```java
// ✅ 推荐 - 明确的缓存类型
UnifiedCacheManager.CacheType.ENTITY_CACHE      // 实体信息
UnifiedCacheManager.CacheType.METADATA_CACHE    // 元数据映射
UnifiedCacheManager.CacheType.SQL_TEMPLATE_CACHE // SQL模板
```

### 3. 缓存操作模式
```java
// ✅ 推荐 - 统一的缓存获取模式
if (unifiedCacheManager != null) {
    return unifiedCacheManager.get(cacheType, cacheKey, supplier);
}
return localCache.computeIfAbsent(key, function);

// ✅ 推荐 - 正确的缓存清除方法
unifiedCacheManager.remove(cacheType, cacheKey);  // 清除单个缓存项
unifiedCacheManager.clear(cacheType);             // 清除整个缓存类型
unifiedCacheManager.clearAll();                   // 清除所有缓存

// ❌ 错误 - 不存在的方法
unifiedCacheManager.evict(cacheType, cacheKey);   // 方法不存在
```

## 📊 性能优化规范

### 1. 避免重复创建
```java
// ❌ 错误 - 重复创建
EntityInfo info1 = EntityInfo.of(entityClass);
EntityInfo info2 = EntityInfo.of(entityClass);

// ✅ 正确 - 统一管理
EntityInfo info = EntityInfoManager.getInstance().getEntityInfo(entityClass);
```

### 2. 合理使用缓存
```java
// ✅ 推荐 - 检查集合参数，避免缓存陷阱
if (!containsCollectionParam(params)) {
    return cache.get(key, supplier);
}
return supplier.get();
```

### 3. 资源管理
```java
// ✅ 推荐 - 使用 try-with-resources
try (Connection conn = dataSource.getConnection()) {
    // 数据库操作
}
```

## 🧪 测试规范

### 1. 测试方法命名
```java
// ✅ 推荐 - 描述性的测试方法名
@Test
public void testEntityInfoCacheWithUnifiedManager() { }

@Test
public void testCacheConsistencyAcrossMultipleInstances() { }
```

### 2. 断言使用
```java
// ✅ 推荐 - 使用 JUnit 5 断言
assertNotNull(result, "结果不应为空");
assertEquals(expected, actual, "值应该相等");
assertTrue(condition, "条件应该为真");
```

## 📚 文档规范

### 1. 类注释
```java
/**
 * 实体信息统一管理器
 * 解决 EntityInfo 重复创建和缓存不统一的问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class EntityInfoManager {
}
```

### 2. 方法注释
```java
/**
 * 统一获取实体信息的方法
 * 所有组件都应该通过这个方法获取 EntityInfo
 * 
 * @param entityClass 实体类，不能为 null
 * @return 实体信息
 * @throws IllegalArgumentException 如果 entityClass 为 null
 */
public EntityInfo getEntityInfo(Class<?> entityClass) {
}
```

## 🚨 常见错误检查清单

### 编译时检查
- [ ] 是否使用了 `var` 关键字
- [ ] 是否使用了 `List.of()`, `Set.of()`, `Map.of()`
- [ ] 是否使用了 JDK 9+ 的新方法
- [ ] 是否有未处理的泛型警告
- [ ] 是否调用了不存在的方法（如 `evict` 应该是 `remove`）
- [ ] 是否有类型不匹配的问题（如 `Map<String, CacheStats>` vs `Map<String, Object>`）

### 运行时检查
- [ ] 是否有空指针异常风险
- [ ] 是否有资源泄漏风险
- [ ] 是否有并发安全问题
- [ ] 是否有性能瓶颈

### 缓存检查
- [ ] 是否避免了重复缓存
- [ ] 是否使用了统一的缓存键格式
- [ ] 是否检查了集合参数缓存陷阱
- [ ] 是否有缓存一致性问题

## 📋 代码审查要点

1. **JDK 兼容性**：确保所有代码兼容 JDK 1.8
2. **缓存统一性**：确保使用统一的缓存管理策略
3. **性能优化**：避免重复创建和不必要的操作
4. **异常处理**：合理的异常处理和日志记录
5. **测试覆盖**：充分的单元测试和集成测试
6. **文档完整**：清晰的注释和使用文档

遵循这些规范可以确保代码质量、性能和可维护性。
