package com.lg.financecloud.common.redis.mq;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.security.SqlSecurityUtils;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class MqDbDao  {

    public MqDbDao() {

    }

    public void insertMqLog(BaseTask task) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        dao.insert(convertToDto(task));
    }

    /**
     * 插入 JobConsumer 方式提交的任务日志
     */
    public void insertMqLogForJobConsumer(BaseTask task) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        dao.insert(convertToDto(task, "JOBCONSUMER"));
    }

    public void insertMqLog(List<BaseTask> tasks) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        dao.batchInsert(tasks.stream().map(this::convertToDto).collect(Collectors.toList()));
    }

    public int updateMqLogState2processing(BaseTask task) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaUpdate()
            .eq(JobMateMationDto::getJobId, task.getTaskIdentity())
            .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING)
            .set(JobMateMationDto::getComplateTime, null)
            .update();
    }

    public int updateMqLogState2retry(BaseTask task) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaUpdate()
            .eq(JobMateMationDto::getJobId, task.getTaskIdentity())
            .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING)
            .set(JobMateMationDto::getComplateTime, null)
            .update();
    }

    public int updateMqLogState(String taskIdentity, String requestMsg, String state) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaUpdate()
            .eq(JobMateMationDto::getJobId, taskIdentity)
            .set(JobMateMationDto::getStatus, state)
            .set(JobMateMationDto::getRequestBody, requestMsg)
            .update();
    }

    public int updateMqLogState2complate(String taskIdentity, String state, String errorMsg) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaUpdate()
            .eq(JobMateMationDto::getJobId, taskIdentity)
            .eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING)
            .set(JobMateMationDto::getStatus, state)
            .set(JobMateMationDto::getComplateTime, new Date())
            .set(JobMateMationDto::getResponseBody, errorMsg)
            .update();
    }



    public JobMateMationDto getQueueLogById(String taskIdentity) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.getById(taskIdentity);
    }

    /**
     * 增加任务重试次数
     */
    public int incrementRetryCount(String taskIdentity) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        JobMateMationDto task = dao.getById(taskIdentity);
        if (task != null) {
            int newRetryCount = (task.getRetrys() != null ? task.getRetrys() : 0) + 1;
            return dao.lambdaUpdate()
                .eq(JobMateMationDto::getJobId, taskIdentity)
                .set(JobMateMationDto::getRetrys, newRetryCount)
                .update();
        }
        return 0;
    }

    /**
     * 重置任务状态为等待
     */
    public int resetTaskToWaiting(String taskIdentity, String reason) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaUpdate()
            .eq(JobMateMationDto::getJobId, taskIdentity)
            .set(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT)
            .set(JobMateMationDto::getResponseBody, reason)
            .set(JobMateMationDto::getComplateTime, null)
            .update();
    }

    /**
     * 获取需要恢复的任务列表
     */
    public List<JobMateMationDto> getTasksForRecovery(String status, Date beforeTime, int limit) {
        GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
        return dao.lambdaQuery()
            .eq(JobMateMationDto::getStatus, status)
            .lt(JobMateMationDto::getCreateTime, DateUtil.formatDateTime(beforeTime))
            .limit(limit)
            .list();
    }

    private JobMateMationDto convertToDto(BaseTask task) {
        return convertToDto(task, "LISTENER"); // 默认为监听器方式
    }

    private JobMateMationDto convertToDto(BaseTask task, String submitType) {
        JobMateMationDto dto = new JobMateMationDto();
        dto.setJobId(task.getTaskIdentity());
        dto.setTitle(task.getJobDesc());
        dto.setJobType(task.getQueueCode());
        dto.setBigType(task.getType().toString());
        // 注意：不能使用 type 字段来标记提交方式，该字段有其他用途
        // dto.setType(submitType); // 移除：type 字段有其他用途

        // 在 requestBody 的 JSON 中添加提交类型信息
        JSONObject taskJson = JSONUtil.parseObj(task);
        taskJson.put("_submitType", submitType); // 在 JSON 中标记提交方式：LISTENER 或 JOBCONSUMER
        dto.setRequestBody(taskJson.toString());
        dto.setStatus(BaseTask.TASK_STATE_WAIT);

        if (StrUtil.isNotEmpty(task.getCreateBy())){
            dto.setCreateId(task.getCreateBy());
        }else {
            if (TenantContextHolder.getCurrentSessionUser() !=null && TenantContextHolder.getCurrentSessionUser().getId()!=null){
                dto.setCreateId(TenantContextHolder.getCurrentSessionUser().getId().toString());
            }else {
                //使用系统管理用户 TODO
                dto.setCreateId("12312313");
            }
        }

        dto.setCreateIp(task.getHostname());
        dto.setCreateTime(DateUtil.formatDateTime(new Date()));
        dto.setTenantId(task.getTenantId());
         dto.setBizKey(task.getBizKey());
        return dto;
    }
}
