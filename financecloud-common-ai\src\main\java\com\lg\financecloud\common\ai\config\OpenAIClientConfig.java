//package com.lg.financecloud.common.ai.config;
//
//import com.theokanning.openai.service.OpenAiService;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.time.Duration;
//
///**
// * OpenAI客户端配置类
// */
//@Configuration
//public class OpenAIClientConfig {
//
//    @Bean
//    public OpenAiService openAiService(AIModelProperties properties) {
//        AIModelProperties.ModelConfig config = properties.getModels().get("openai");
//        if (config == null || !config.isEnabled()) {
//            return null;
//        }
//
//        return new OpenAiService(
//            config.getApiKey(),
//            Duration.ofMillis(config.getTimeout())
//        );
//    }
//}