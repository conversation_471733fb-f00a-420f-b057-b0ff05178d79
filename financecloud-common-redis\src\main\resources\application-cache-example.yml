# 不同环境的缓存配置示例

# 默认配置 (Redis模式)
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000  # 1小时 (毫秒)
      cache-null-values: true
      use-key-prefix: true
  redis:
    host: localhost
    port: 6379
    database: 0

# Finance Cloud 缓存配置
light:
  cache:
    # 缓存模式: REDIS, MEMORY, 或 TWO_LEVEL (默认: REDIS)
    # - REDIS: 仅使用Redis缓存
    # - MEMORY: 仅使用本地内存缓存
    # - TWO_LEVEL: 同时使用内存(L1)和Redis(L2)缓存
    cache-mode: TWO_LEVEL
    
    # 内存缓存的最大条目数 (对于 TWO_LEVEL 或 MEMORY 模式)
    max-size: 1000
    
    # 内存缓存条目的存活时间(秒) (默认: 3600 = 1小时)
    time-to-live-seconds: 3600
    
    # 内存缓存条目的空闲失效时间(秒) (默认: 1800 = 30分钟)
    time-to-idle-seconds: 1800

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  cache:
    type: redis

financecloud:
  cache:
    # 在开发环境使用内存模式，避免Redis依赖
    cache-mode: MEMORY
    max-size: 1000
    time-to-live-seconds: 300  # 5分钟

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  cache:
    type: redis
    redis:
      time-to-live: 7200000  # 2小时 (毫秒)

light:
  cache:
    # 在生产环境使用二级缓存，提高性能同时保持分布式一致性
    cache-mode: TWO_LEVEL
    max-size: 10000  # 生产环境更大的缓存大小
    time-to-live-seconds: 600  # L1缓存10分钟
    time-to-idle-seconds: 300  # 5分钟空闲时间