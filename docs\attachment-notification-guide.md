# 附件管理与消息通知系统使用指南

## 📋 概述

本文档介绍低代码平台的两个核心基础能力：
- **统一附件管理系统**：支持单附件、附件组，多种存储方式
- **多渠道消息通知系统**：支持系统消息、邮件、短信、微信、钉钉等

## 🗂️ 附件管理系统

### 功能特性

#### 1. **多存储支持**
- ✅ 本地存储
- ✅ 阿里云OSS
- ✅ 腾讯云COS  
- ✅ 七牛云存储
- ✅ 自定义存储扩展

#### 2. **灵活组织**
- 📁 附件组管理
- 🏷️ 标签分类
- 🔍 全文搜索
- 📊 统计分析

#### 3. **安全控制**
- 🔒 权限控制
- ⏰ 过期管理
- 🛡️ 文件类型限制
- 📏 大小限制

### 快速开始

#### 1. 上传单个附件

```bash
# API调用示例
curl -X POST "http://localhost:8080/api/lc/attachment/upload" \
  -F "file=@example.pdf" \
  -F "businessType=DASHBOARD" \
  -F "businessId=dashboard_001" \
  -F "description=数据看板附件"
```

```javascript
// 前端调用示例
const formData = new FormData();
formData.append('file', file);
formData.append('businessType', 'DASHBOARD');
formData.append('businessId', 'dashboard_001');
formData.append('description', '数据看板附件');

const response = await fetch('/api/lc/attachment/upload', {
  method: 'POST',
  body: formData
});
```

#### 2. 创建附件组

```javascript
// 创建附件组
const groupData = {
  groupName: '报表附件',
  groupCode: 'report_files',
  businessType: 'REPORT',
  businessId: 'report_001',
  maxCount: 10,
  maxSize: 104857600, // 100MB
  allowedTypes: 'pdf,doc,docx,xls,xlsx'
};

const response = await fetch('/api/lc/attachment/group', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(groupData)
});
```

#### 3. 批量上传附件

```javascript
// 批量上传
const formData = new FormData();
files.forEach(file => formData.append('files', file));
formData.append('businessType', 'PAGE');
formData.append('businessId', 'page_001');
formData.append('groupId', 'group_001');

const response = await fetch('/api/lc/attachment/upload/batch', {
  method: 'POST',
  body: formData
});
```

### 存储配置

#### 1. 本地存储配置

```yaml
# application.yml
financecloud:
  lowcode:
    attachment:
      default-storage: local_storage
      max-file-size: 100
      allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
```

#### 2. 阿里云OSS配置

```sql
-- 插入OSS存储配置
INSERT INTO lc_storage_config (
  id, config_name, config_code, storage_type, config_json, is_enabled
) VALUES (
  'aliyun_oss', '阿里云OSS', 'aliyun_oss', 'ALIYUN_OSS',
  '{
    "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
    "bucketName": "your-bucket",
    "accessKeyId": "your-access-key",
    "accessKeySecret": "your-secret-key",
    "urlPrefix": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com"
  }',
  1
);
```

#### 3. 腾讯云COS配置

```sql
-- 插入COS存储配置
INSERT INTO lc_storage_config (
  id, config_name, config_code, storage_type, config_json, is_enabled
) VALUES (
  'tencent_cos', '腾讯云COS', 'tencent_cos', 'TENCENT_COS',
  '{
    "region": "ap-guangzhou",
    "bucketName": "your-bucket-1234567890",
    "secretId": "your-secret-id",
    "secretKey": "your-secret-key",
    "urlPrefix": "https://your-bucket-1234567890.cos.ap-guangzhou.myqcloud.com"
  }',
  1
);
```

### 使用场景

#### 1. 数据看板附件

```javascript
// 在数据看板中使用附件
const dashboardAttachments = await fetch(
  `/api/lc/attachment/list?businessType=DASHBOARD&businessId=${dashboardId}`
);

// 显示附件列表
attachments.forEach(attachment => {
  const link = document.createElement('a');
  link.href = `/api/lc/attachment/${attachment.id}/download`;
  link.textContent = attachment.originalName;
  link.download = attachment.originalName;
});
```

#### 2. 报表组件附件

```javascript
// 报表中的附件管理
class ReportAttachmentManager {
  async uploadReportFile(reportId, file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('businessType', 'REPORT');
    formData.append('businessId', reportId);
    
    return await fetch('/api/lc/attachment/upload', {
      method: 'POST',
      body: formData
    });
  }
  
  async getReportAttachments(reportId) {
    return await fetch(
      `/api/lc/attachment/list?businessType=REPORT&businessId=${reportId}`
    );
  }
}
```

#### 3. 页面设计器附件

```javascript
// 页面设计器中的图片上传
class PageDesignerAttachment {
  async uploadImage(pageId, imageFile) {
    const formData = new FormData();
    formData.append('file', imageFile);
    formData.append('businessType', 'PAGE');
    formData.append('businessId', pageId);
    formData.append('groupId', 'page_images');
    
    const response = await fetch('/api/lc/attachment/upload', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    return result.data.fileUrl; // 返回图片URL
  }
}
```

## 📢 消息通知系统

### 功能特性

#### 1. **多渠道支持**
- 💬 系统消息
- 📧 邮件通知
- 📱 短信通知
- 💼 企业微信
- 🔔 钉钉通知
- 🌐 WebSocket推送

#### 2. **模板管理**
- 📝 富文本模板
- 🔧 变量替换
- 🎨 多格式支持
- 📊 使用统计

#### 3. **智能发送**
- ⚡ 异步发送
- 🔄 自动重试
- 📈 批量发送
- 🎯 多渠道发送

### 快速开始

#### 1. 发送系统消息

```javascript
// 发送系统消息
const messageData = {
  channelType: 'SYSTEM',
  receiver: 'user_001',
  subject: '任务提醒',
  content: '您有一个新的待办任务需要处理',
  variables: {
    taskName: '数据看板审核',
    deadline: '2024-01-15 18:00'
  }
};

const response = await fetch('/api/lc/notification/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(messageData)
});
```

#### 2. 使用模板发送通知

```javascript
// 使用模板发送
const templateData = {
  templateCode: 'WORKFLOW_TASK_ASSIGN',
  channelType: 'EMAIL',
  receiver: '<EMAIL>',
  variables: {
    taskName: '报表审核任务',
    taskDescription: '请审核Q4季度财务报表',
    deadline: '2024-01-20 17:00'
  }
};

const response = await fetch('/api/lc/notification/send/template', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(templateData)
});
```

#### 3. 批量发送通知

```javascript
// 批量发送
const batchData = {
  channelType: 'SYSTEM',
  receivers: ['user_001', 'user_002', 'user_003'],
  subject: '系统维护通知',
  content: '系统将于今晚22:00-24:00进行维护，请提前保存工作。',
  variables: {
    maintenanceTime: '2024-01-15 22:00-24:00'
  }
};

const response = await fetch('/api/lc/notification/send/batch', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(batchData)
});
```

#### 4. 多渠道发送

```javascript
// 多渠道发送
const multiChannelData = {
  channelTypes: ['SYSTEM', 'EMAIL', 'WECHAT'],
  receiver: 'user_001',
  subject: '重要通知',
  content: '您的账户安全设置已更新，如非本人操作请及时联系管理员。',
  variables: {
    updateTime: '2024-01-15 14:30',
    ipAddress: '*************'
  }
};

const response = await fetch('/api/lc/notification/send/multi-channel', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(multiChannelData)
});
```

### 渠道配置

#### 1. 邮件渠道配置

```sql
-- 更新邮件渠道配置
UPDATE lc_notification_channel 
SET config_json = '{
  "host": "smtp.qq.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "your-auth-code",
  "from": "<EMAIL>",
  "ssl": true,
  "contentType": "text/html"
}',
is_enabled = 1
WHERE channel_code = 'email';
```

#### 2. 短信渠道配置

```sql
-- 配置阿里云短信
UPDATE lc_notification_channel 
SET config_json = '{
  "accessKeyId": "your-access-key",
  "accessKeySecret": "your-secret",
  "signName": "your-sign-name",
  "regionId": "cn-hangzhou"
}',
is_enabled = 1
WHERE channel_code = 'sms_aliyun';
```

#### 3. 企业微信配置

```sql
-- 配置企业微信
UPDATE lc_notification_channel 
SET config_json = '{
  "corpId": "your-corp-id",
  "agentId": "your-agent-id",
  "secret": "your-secret"
}',
is_enabled = 1
WHERE channel_code = 'wechat_work';
```

### 模板管理

#### 1. 创建通知模板

```javascript
// 创建模板
const templateData = {
  templateName: '数据告警通知',
  templateCode: 'DATA_ALERT',
  templateType: 'ALERT',
  channelType: 'EMAIL',
  subject: '数据告警：${alertTitle}',
  content: `
    <h3>数据告警通知</h3>
    <p><strong>告警标题：</strong>${alertTitle}</p>
    <p><strong>告警内容：</strong>${alertContent}</p>
    <p><strong>告警时间：</strong>${alertTime}</p>
    <p><strong>告警级别：</strong>${alertLevel}</p>
    <p>请及时处理相关问题。</p>
  `,
  contentType: 'HTML',
  variables: {
    alertTitle: '告警标题',
    alertContent: '告警内容',
    alertTime: '告警时间',
    alertLevel: '告警级别'
  },
  category: 'ALERT'
};

const response = await fetch('/api/lc/notification/template', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(templateData)
});
```

### 业务集成

#### 1. 数据看板告警通知

```javascript
// 数据看板告警
class DashboardAlertNotifier {
  async sendAlert(dashboardId, alertData) {
    const notificationData = {
      businessType: 'DASHBOARD',
      businessId: dashboardId,
      templateCode: 'DASHBOARD_ALERT',
      channelType: 'EMAIL',
      receiver: alertData.receiver,
      variables: {
        alertTitle: alertData.title,
        alertContent: alertData.content,
        alertTime: new Date().toLocaleString(),
        alertLevel: alertData.level
      }
    };
    
    return await fetch('/api/lc/notification/send/business', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    });
  }
}
```

#### 2. 报表生成通知

```javascript
// 报表生成完成通知
class ReportNotifier {
  async notifyReportGenerated(reportId, userId, reportUrl) {
    const notificationData = {
      businessType: 'REPORT',
      businessId: reportId,
      templateCode: 'REPORT_GENERATE',
      channelType: 'EMAIL',
      receiver: userId,
      variables: {
        reportName: '月度财务报表',
        generateTime: new Date().toLocaleString(),
        reportUrl: reportUrl
      }
    };
    
    return await fetch('/api/lc/notification/send/business', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    });
  }
}
```

#### 3. 工作流通知

```javascript
// 工作流任务通知
class WorkflowNotifier {
  async notifyTaskAssigned(taskId, assigneeId, taskData) {
    const notificationData = {
      businessType: 'WORKFLOW',
      businessId: taskId,
      templateCode: 'WORKFLOW_TASK_ASSIGN',
      channelType: 'SYSTEM',
      receiver: assigneeId,
      variables: {
        taskName: taskData.name,
        taskDescription: taskData.description,
        deadline: taskData.deadline
      }
    };
    
    return await fetch('/api/lc/notification/send/business', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    });
  }
}
```

## 🔧 高级功能

### 附件管理高级功能

#### 1. 文件去重

```javascript
// 基于MD5的文件去重
const checkDuplicate = async (file) => {
  const md5 = await calculateMD5(file);
  const response = await fetch(`/api/lc/attachment/check-duplicate?md5=${md5}`);
  return response.json();
};
```

#### 2. 缩略图生成

```javascript
// 图片缩略图自动生成
const uploadImageWithThumbnail = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('generateThumbnail', 'true');
  formData.append('thumbnailWidth', '200');
  formData.append('thumbnailHeight', '200');
  
  return await fetch('/api/lc/attachment/upload', {
    method: 'POST',
    body: formData
  });
};
```

#### 3. 批量操作

```javascript
// 批量删除附件
const batchDelete = async (attachmentIds) => {
  return await fetch('/api/lc/attachment/batch', {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(attachmentIds)
  });
};

// 批量移动附件
const batchMove = async (attachmentIds, targetGroupId) => {
  return await fetch(`/api/lc/attachment/batch/move?targetGroupId=${targetGroupId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(attachmentIds)
  });
};
```

### 通知系统高级功能

#### 1. 用户通知设置

```javascript
// 获取用户通知设置
const getUserSettings = async (userId) => {
  return await fetch(`/api/lc/notification/settings/${userId}`);
};

// 更新用户通知设置
const updateUserSettings = async (userId, settings) => {
  return await fetch(`/api/lc/notification/settings/${userId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(settings)
  });
};
```

#### 2. 消息统计分析

```javascript
// 获取发送统计
const getSendStats = async (channelType, startDate, endDate) => {
  const params = new URLSearchParams({
    channelType,
    startDate,
    endDate
  });
  
  return await fetch(`/api/lc/notification/statistics/send?${params}`);
};

// 获取模板使用统计
const getTemplateStats = async (templateCode) => {
  return await fetch(`/api/lc/notification/statistics/template?templateCode=${templateCode}`);
};
```

## 🚨 注意事项

### 安全注意事项

1. **文件上传安全**
   - 严格限制文件类型和大小
   - 对上传文件进行病毒扫描
   - 使用安全的文件存储路径

2. **通知安全**
   - 验证接收者权限
   - 防止消息轰炸
   - 敏感信息加密传输

### 性能优化

1. **附件管理**
   - 使用CDN加速文件访问
   - 定期清理过期和孤立文件
   - 合理设置缓存策略

2. **消息通知**
   - 使用异步发送提高性能
   - 合理设置重试机制
   - 监控发送成功率

### 运维建议

1. **监控告警**
   - 监控存储空间使用情况
   - 监控消息发送成功率
   - 设置异常告警

2. **定期维护**
   - 定期清理过期附件
   - 清理失败的消息记录
   - 更新渠道配置

通过以上配置和使用，您的低代码平台将具备完整的附件管理和消息通知能力，为各种业务场景提供强大的基础支撑！
