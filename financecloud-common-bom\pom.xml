<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.lg</groupId>
    <artifactId>financecloud-common-bom</artifactId>
    <version>3.10.1</version>
    <packaging>pom</packaging>
    
    <description>financecloud 依赖版本管理 BOM (Bill of Materials)</description>

    <properties>
        <!-- 从父pom复制所有必要的版本属性 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.version>3.8.1</maven.compiler.version>
        
        <!-- 核心依赖版本 -->
        <lombok.version>1.18.30</lombok.version>
        <guava.version>31.1-jre</guava.version>
        <caffeine.version>2.9.3</caffeine.version>
        <feign.version>10.10.1</feign.version>
        <fst.version>2.57</fst.version>
        <jakarta-persistence.version>2.2.3</jakarta-persistence.version>
        <poi-excel.version>poi-412.7</poi-excel.version>
        
        <!-- Spring 相关版本 -->
        <spring-boot.version>2.3.3.RELEASE</spring-boot.version>
        <spring-cloud.version>Hoxton.SR8</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.2.RELEASE</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.3.0</spring-boot-admin.version>
        
        <!-- 工具类版本 -->
        <hutool.version>5.8.5</hutool.version>
        <swagger.fox.version>2.9.2</swagger.fox.version>
        <knife4j.version>2.0.4</knife4j.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <druid.version>1.2.8</druid.version>
        <redisson.version>3.17.6</redisson.version>
        <satoken.version>1.31.0</satoken.version>
        <dynamic-ds.version>3.2.0</dynamic-ds.version>
        <mysql.connector.version>8.0.21</mysql.connector.version>
        <swagger.core.version>1.5.22</swagger.core.version>
        <mp.weixin.version>3.9.0</mp.weixin.version>
        <ijpay.version>2.7.0</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <security.oauth.version>2.3.6.RELEASE</security.oauth.version>
        <fastjson.version>1.2.73</fastjson.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <ttl.version>2.11.4</ttl.version>
        <aws.version>1.11.543</aws.version>
        <xxl.job.version>2.2.0</xxl.job.version>
        <activiti.version>5.22.0</activiti.version>
        <aliyun-sdk.version>3.8.1</aliyun-sdk.version>
        <lcn.version>4.1.0</lcn.version>
        
        <!-- Apache工具类版本 -->
        <commons-lang3.version>3.7</commons-lang3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-fileupload.version>1.4</commons-fileupload.version>
        <commons-cli.version>1.2</commons-cli.version>
        <commons-net.version>3.6</commons-net.version>
        
        <!-- 文档处理 -->
        <poi.version>4.1.2</poi.version>
        <velocity.version>1.7</velocity.version>
        <itext.version>2.1.7</itext.version>
        <pdfbox.version>2.0.26</pdfbox.version>
        <xdocreport.version>1.0.6</xdocreport.version>
        <xstream.version>1.4.19</xstream.version>
        <junrar.version>7.4.1</junrar.version>
        <jodconverter.version>4.4.5</jodconverter.version>
        
        <!-- 其他工具版本 -->
        <sevenzipjbinding.version>16.02-2.01</sevenzipjbinding.version>
        <jchardet.version>1.0</jchardet.version>
        <antlr.version>2.7.7</antlr.version>
        <concurrentlinkedhashmap.version>1.4.2</concurrentlinkedhashmap.version>
        <rocksdb.version>5.17.2</rocksdb.version>
        <jai-imageio.version>1.4.0</jai-imageio.version>
        <jbig2-imageio.version>3.0.4</jbig2-imageio.version>
        <galimatias.version>0.2.1</galimatias.version>
        <bytedeco.version>1.5.2</bytedeco.version>
        <opencv.version>4.1.2-1.5.2</opencv.version>
        <openblas.version>0.3.6-1.5.1</openblas.version>
        <ffmpeg.version>4.2.1-1.5.2</ffmpeg.version>
        <httpclient.version>3.1</httpclient.version>
        
        <!-- 阿里云SDK -->
        <aliyun-sdk-oss.version>3.15.0</aliyun-sdk-oss.version>
        <aliyun-java-sdk-core.version>4.5.3</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-sts.version>3.0.0</aliyun-java-sdk-sts.version>
        
        <!-- 测试相关 -->
        <junit-jupiter.version>5.8.2</junit-jupiter.version>
        <mockito.version>4.6.1</mockito.version>
        <h2.version>2.1.214</h2.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-httpclient</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-core</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>de.ruedigermoeller</groupId>
                <artifactId>fst</artifactId>
                <version>${fst.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>${jakarta-persistence.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <!-- Spring Boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Boot Admin -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-dependencies</artifactId>
                <version>${spring-boot-admin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-datasource</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-feign</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-gateway</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-gray</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-sentinel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-oss-ext</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-report</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-ai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-workflow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lg</groupId>
                <artifactId>financecloud-common-task</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- 工具类依赖 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            
            <!-- 数据库相关 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>test</scope>
            </dependency>
            
            <!-- 缓存相关 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            
            <!-- 安全认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            
            <!-- API文档 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            
            <!-- Apache工具类 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>

            


            
            <!-- 文档处理 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            

            
            <!-- 其他依赖 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            
            <!-- 测试相关 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- POI相关依赖 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.stupdit1t</groupId>
                <artifactId>poi-excel</artifactId>
                <version>${poi-excel.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


</project>