package com.lg.dao.factory;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ReflectUtil;
import com.lg.dao.DaoMode;
import com.lg.dao.core.BaseDao;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.DDLHelper;
import com.lg.dao.core.sequence.SequenceManager;
import com.lg.dao.factory.DaoFactory;
import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.EntityInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabase;
import org.springframework.stereotype.Component;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;
import java.lang.reflect.Field;
import javax.persistence.*;

/**
 * 增强版DAO工厂 - 支持编程式模式控制
 */
@Component
@ConditionalOnProperty(name = "light.orm.enhanced.enable", havingValue = "true", matchIfMissing = true)
@Slf4j
public class EnhancedDaoFactory extends DaoFactory {

    private static final ConcurrentHashMap<String, BaseDao> BASE_DAO_CACHE = new ConcurrentHashMap<>(2) ;
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    private DataSource memoryDataSource;

    @Autowired(required = false)
    private DataSource primaryDataSource;

    // 内存DAO实例缓存
    private static final ConcurrentHashMap<String, GenericDao<?, ?>> MEMORY_DAO_CACHE = new ConcurrentHashMap<>(64);

    // 数据库DAO实例缓存
    private static final ConcurrentHashMap<String, GenericDao<?, ?>> DATABASE_DAO_CACHE = new ConcurrentHashMap<>(128);
    
    // 自定义DAO缓存
    private static final ConcurrentHashMap<String, GenericDao<?, ?>> CUSTOM_DAO_CACHE = new ConcurrentHashMap<>(32);

    // DDL Helper 缓存
    private static final ConcurrentHashMap<DataSource, DDLHelper> DDL_HELPER_CACHE = new ConcurrentHashMap<>();
    
    // 数据源类型缓存，避免重复判断
    private static final ConcurrentHashMap<DataSource, Boolean> DATASOURCE_TYPE_CACHE = new ConcurrentHashMap<>();
    
    // 数据源可用性缓存，避免重复检查
    private static final ConcurrentHashMap<DataSource, Boolean> DATASOURCE_AVAILABILITY_CACHE = new ConcurrentHashMap<>();

    // 当前模式
    private volatile DaoMode mode = DaoMode.AUTO;
    
    // 模式判断结果缓存
    private volatile DaoMode effectiveMode = null;

    /**
     * 设置使用模式
     */
    public void setMode(DaoMode mode) {
        if (this.mode != mode) {
            this.mode = mode;
            this.effectiveMode = null; // 清除缓存的模式判断结果
        }
    }

    /**
     * 获取当前模式
     */
    public DaoMode getMode() {
        return mode;
    }
    
    /**
     * 获取实际使用的模式（考虑数据源可用性）
     */
    public DaoMode getEffectiveMode() {
        if (effectiveMode != null) {
            return effectiveMode;
        }
        
        synchronized (this) {
            if (effectiveMode != null) {
                return effectiveMode;
            }
            
            switch (mode) {
                case MEMORY:
                    effectiveMode = DaoMode.MEMORY;
                    break;
                case DATABASE:
                    effectiveMode = DaoMode.DATABASE;
                    break;
                case AUTO:
                default:
                    // 自动模式：优先使用真实数据库，不可用时降级到内存数据库
                    if (primaryDataSource != null && isDatabaseAvailable(primaryDataSource)) {
                        // 判断数据源实例类型 是内存数据源
                        if (isMemoryDataSource(primaryDataSource)) {
                            effectiveMode = DaoMode.MEMORY;
                        } else {
                            effectiveMode = DaoMode.DATABASE;
                        }
                    } else if (memoryDataSource != null) {
                        effectiveMode = DaoMode.MEMORY;
                    } else {
                        throw new IllegalStateException("No DataSource available. Please configure a DataSource or enable memory mode.");
                    }
            }
            
            return effectiveMode;
        }
    }

    @Override
    public <T, K> GenericDao<T, K> getDao(Class<T> entityClass, Class<K> keyClass) {
        DaoMode effective = getEffectiveMode();
        
        switch (effective) {
            case MEMORY:
                return getMemoryDao(entityClass, keyClass);
            case DATABASE:
                return getDatabaseDao(entityClass, keyClass);
            default:
                throw new IllegalStateException("Unexpected mode: " + effective);
        }
    }

    /**
     * 判断数据源是否为内存数据源
     */
    private boolean isMemoryDataSource(DataSource dataSource) {
        return DATASOURCE_TYPE_CACHE.computeIfAbsent(dataSource, ds -> {
            try {
                // 通过连接URL判断
                Connection connection = ds.getConnection();
                String url = connection.getMetaData().getURL();
                connection.close();

                // 检查是否为内存数据库URL
                return url != null && (
                        url.startsWith("jdbc:h2:mem:") ||
                                url.startsWith("jdbc:hsqldb:mem:") ||
                                url.startsWith("jdbc:derby:memory:") ||
                                url.contains(":memory:")
                );
            } catch (Exception e) {
                return false;
            }
        });
    }

    /**
     * 获取内存数据库DAO实例
     */
    @SuppressWarnings("unchecked")
    public <T, K> GenericDao<T, K> getMemoryDao(Class<T> entityClass, Class<K> keyClass) {
        if (memoryDataSource == null) {
            throw new IllegalStateException("Memory DataSource not available. Please enable memory mode.");
        }

        // 使用类的hashCode作为缓存键，减少字符串拼接开销
        String cacheKey = entityClass.getName().hashCode() + "_" + keyClass.getName().hashCode();

        return (GenericDao<T, K>) MEMORY_DAO_CACHE.computeIfAbsent(cacheKey, key -> {
            GenericDao<T, K> dao = createDaoInstance(entityClass, keyClass, memoryDataSource);
            // 增强的自动建表逻辑
            autoCreateTableEnhanced(entityClass, memoryDataSource);
            return dao;
        });
    }

    /**
     * 获取真实数据库DAO实例
     */
    @SuppressWarnings("unchecked")
    public <T, K> GenericDao<T, K> getDatabaseDao(Class<T> entityClass, Class<K> keyClass) {
        if (primaryDataSource == null) {
            throw new IllegalStateException("Primary DataSource not available. Please configure a DataSource.");
        }

        // 使用类的hashCode作为缓存键，减少字符串拼接开销
        String cacheKey = entityClass.getName().hashCode() + "_" + keyClass.getName().hashCode();

        return (GenericDao<T, K>) DATABASE_DAO_CACHE.computeIfAbsent(cacheKey, key -> {
            return createDaoInstance(entityClass, keyClass, primaryDataSource);
        });
    }

    /**
     * 创建DAO实例（集成DDLHelper和SequenceManager）
     */
    private <T, K> GenericDao<T, K> createDaoInstance(Class<T> entityClass, Class<K> keyClass, DataSource dataSource) {
        // 使用带参数的构造函数，避免泛型类型解析问题
        GenericDao<T, K> dao = new GenericDao<T, K>(entityClass) {};
        
        applicationContext.getAutowireCapableBeanFactory().autowireBean(dao);
        return dao;
    }

    /**
     * 增强的自动建表逻辑
     */
    private <T> void autoCreateTableEnhanced(Class<T> entityClass, DataSource dataSource) {
        try {
            DDLHelper ddlHelper = DDL_HELPER_CACHE.computeIfAbsent(dataSource, ds -> 
                new DDLHelper(new JdbcTemplate(ds)));
            EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(entityClass);
            // 获取表名
            String tableName = entityInfo.getTableName();
            
            // 检查表是否已存在
            if (ddlHelper.tableExists(tableName)) {
                log.info("Table already exists: " + tableName);
                return;
            }

            // 解析实体类，生成表结构
            // 创建表
            ddlHelper.createTableFromEntity(entityInfo);
            log.info("Auto created table: " + tableName + " for entity: " + entityClass.getSimpleName());

        } catch (Exception e) {
            log.error("Error auto creating table: " + e.getMessage(), e);
            throw new RuntimeException("Error auto creating table: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取表名
     */
    private <T> String getTableName(Class<T> entityClass) {
        Table tableAnnotation = entityClass.getAnnotation(Table.class);
        if (tableAnnotation != null && StringUtils.hasText(tableAnnotation.name())) {
            return tableAnnotation.name();
        }
        // 默认使用类名转下划线
        return camelToUnderscore(entityClass.getSimpleName());
    }
    
    /**
     * 解析实体类字段，生成列定义
     */
    private <T> List<DDLHelper.TableColumn> parseEntityColumns(Class<T> entityClass) {
        List<DDLHelper.TableColumn> columns = new ArrayList<>();
        
        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            // 跳过静态字段和被@Transient标记的字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || 
                field.isAnnotationPresent(Transient.class)) {
                continue;
            }
            
            DDLHelper.TableColumn column = parseFieldToColumn(field);
            if (column != null) {
                columns.add(column);
            }
        }
        
        return columns;
    }
    
    /**
     * 解析字段为列定义
     */
    private DDLHelper.TableColumn parseFieldToColumn(Field field) {
        Column columnAnnotation = field.getAnnotation(Column.class);
        
        String columnName = (columnAnnotation != null && StringUtils.hasText(columnAnnotation.name())) 
            ? columnAnnotation.name() 
            : camelToUnderscore(field.getName());
            
        String columnType = mapJavaTypeToSqlType(field.getType());
        
        DDLHelper.TableColumn.TableColumnBuilder builder = DDLHelper.TableColumn.builder()
            .name(columnName)
            .type(columnType)
            .nullable(columnAnnotation == null || columnAnnotation.nullable());
            
        // 只为需要长度的类型设置长度
        if (needsLength(columnType) && columnAnnotation != null && columnAnnotation.length() > 0) {
            builder.length(columnAnnotation.length());
        }
        
        // 设置精度和小数位数（仅适用于 DECIMAL 等类型）
        if (needsPrecision(columnType) && columnAnnotation != null && columnAnnotation.precision() > 0) {
            builder.precision(columnAnnotation.precision());
            if (columnAnnotation.scale() > 0) {
                builder.scale(columnAnnotation.scale());
            }
        }
        
        return builder.build();
    }
    
    /**
     * 判断类型是否需要长度参数
     */
    private boolean needsLength(String sqlType) {
        return "VARCHAR".equals(sqlType) || "CHAR".equals(sqlType);
    }
    
    /**
     * 判断类型是否需要精度参数
     */
    private boolean needsPrecision(String sqlType) {
        return "DECIMAL".equals(sqlType) || "NUMERIC".equals(sqlType);
    }
    
    /**
     * 获取主键列
     */
    private <T> String getPrimaryKeyColumns(Class<T> entityClass) {
        List<String> primaryKeys = new ArrayList<>();
        
        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Id.class)) {
                Column columnAnnotation = field.getAnnotation(Column.class);
                String columnName = (columnAnnotation != null && StringUtils.hasText(columnAnnotation.name())) 
                    ? columnAnnotation.name() 
                    : camelToUnderscore(field.getName());
                primaryKeys.add(columnName);
            }
        }
        
        return String.join(", ", primaryKeys);
    }
    
    /**
     * 获取索引列
     */
    private <T> String[] getIndexColumns(Class<T> entityClass) {
        // 这里可以根据需要扩展，解析@Index注解等
        return new String[0];
    }
    
    /**
     * Java类型映射到SQL类型
     */
    private String mapJavaTypeToSqlType(Class<?> javaType) {
        if (javaType == String.class) {
            return "VARCHAR";
        } else if (javaType == Integer.class || javaType == int.class) {
            return "INT";
        } else if (javaType == Long.class || javaType == long.class) {
            return "BIGINT";
        } else if (javaType == Double.class || javaType == double.class) {
            return "DOUBLE";
        } else if (javaType == Float.class || javaType == float.class) {
            return "FLOAT";
        } else if (javaType == Boolean.class || javaType == boolean.class) {
            return "BOOLEAN";
        } else if (javaType == java.util.Date.class || javaType == java.sql.Date.class) {
            return "DATE";
        } else if (javaType == java.sql.Timestamp.class || javaType == java.time.LocalDateTime.class) {
            return "TIMESTAMP";
        } else if (javaType == java.time.LocalDate.class) {
            return "DATE";
        } else if (javaType == java.time.LocalTime.class) {
            return "TIME";
        } else if (javaType == java.math.BigDecimal.class) {
            return "DECIMAL";
        } else {
            return "VARCHAR"; // 默认类型
        }
    }
    
    /**
     * 驼峰转下划线
     */
    private String camelToUnderscore(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 判断数据源是否可用
     */
    private boolean isDatabaseAvailable(DataSource dataSource) {
        return DATASOURCE_AVAILABILITY_CACHE.computeIfAbsent(dataSource, ds -> {
            try {
                Connection connection = ds.getConnection();
                boolean valid = connection.isValid(2); // 2秒超时
                connection.close();
                return valid;
            } catch (Exception e) {
                return false;
            }
        });
    }

    /**
     * 创建自定义DAO实例
     */
    @SuppressWarnings("unchecked")
    public <T, K, D extends GenericDao<T, K>> D createCustomDaoInstance(Class<D> daoClass, Class<T> entityClass, Class<K> keyClass, DataSource dataSource) {
        try {
            // 创建自定义DAO实例
            D dao = daoClass.getDeclaredConstructor().newInstance();
            ReflectUtil.setFieldValue(dao, "entityClass", entityClass);
            // 进行Spring依赖注入
            applicationContext.getAutowireCapableBeanFactory().autowireBean(dao);

            // 自动模式：优先使用真实数据库，不可用时降级到内存数据库
            if(getMode() == DaoMode.AUTO || getMode() == DaoMode.MEMORY) {
                // 判断数据源实例类型 是内存数据源
                if (isMemoryDataSource(primaryDataSource)) {
                    autoCreateTableEnhanced(entityClass, memoryDataSource);
                }
            }

            return dao;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create custom DAO instance: " + daoClass.getName(), e);
        }
    }

    /**
     * 获取自定义DAO实例
     */
    @SuppressWarnings("unchecked")
    public <T, K, D extends GenericDao<T, K>> D getCustomDao(Class<D> daoClass, Class<T> entityClass, Class<K> keyClass) {
        // 使用类的hashCode作为缓存键，减少字符串拼接开销
        String cacheKey = daoClass.getName().hashCode() + "_" + 
                          entityClass.getName().hashCode() + "_" + 
                          keyClass.getName().hashCode();
        
        return (D) CUSTOM_DAO_CACHE.computeIfAbsent(cacheKey, key -> {
            DaoMode effective = getEffectiveMode();
            DataSource dataSource;
            
            if (effective == DaoMode.MEMORY) {
                dataSource = memoryDataSource;
                // 自动建表
                autoCreateTableEnhanced(entityClass, memoryDataSource);
            } else {
                dataSource = primaryDataSource;
            }
            
            return createCustomDaoInstance(daoClass, entityClass, keyClass, dataSource);
        });
    }
    
    /**
     * 重写父类方法，获取BaseDao实例，用于无实体查询
     * @return BaseDao实例
     */
    @Override
    public BaseDao getBaseDao() {
        // 根据当前模式选择数据源
        String cacheKey = "BaseDao_" +getMode();
        
        return (BaseDao) BASE_DAO_CACHE.computeIfAbsent(cacheKey, key -> {
            BaseDao dao = new BaseDao();
            // 手动注入Spring依赖
            applicationContext.getAutowireCapableBeanFactory().autowireBean(dao);
            return dao;
        });
    }

    /**
     * 清除所有缓存
     * 在需要重新加载DAO实例时调用
     */
    public void clearCache() {
        super.clearCache();
        MEMORY_DAO_CACHE.clear();
        DATABASE_DAO_CACHE.clear();
        CUSTOM_DAO_CACHE.clear();
        DATASOURCE_TYPE_CACHE.clear();
        DATASOURCE_AVAILABILITY_CACHE.clear();
        effectiveMode = null;
    }
    
    /**
     * 重置数据源缓存
     * 在数据源配置变更时调用
     */
    public void resetDataSourceCache() {
        DATASOURCE_TYPE_CACHE.clear();
        DATASOURCE_AVAILABILITY_CACHE.clear();
        effectiveMode = null;
    }

}
