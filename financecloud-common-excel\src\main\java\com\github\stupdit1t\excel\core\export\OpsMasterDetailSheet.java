package com.github.stupdit1t.excel.core.export;

import com.github.stupdit1t.excel.core.AbsParent;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.function.Function;

/**
 * 主从表导出规则定义
 * 支持头表（主表）和行表（明细表）的循环导出功能
 * 
 * @param <M> 主表数据类型
 * @param <D> 明细表数据类型
 */
public class OpsMasterDetailSheet<M, D> extends AbsParent<OpsExport> implements OpsFinish {

    /**
     * 主表数据
     */
    List<M> masterData;

    /**
     * 明细数据提取器
     */
    Function<M, List<D>> detailDataExtractor;

    /**
     * sheet名字
     */
    String sheetName;

    /**
     * 主表表头定义
     */
    OpsHeader<M> masterHeader;

    /**
     * 主表数据列定义
     */
    OpsColumn<M> masterColumn;

    /**
     * 明细表表头定义
     */
    OpsHeader<D> detailHeader;

    /**
     * 明细表数据列定义
     */
    OpsColumn<D> detailColumn;

    /**
     * 主从表之间的间隔行数
     */
    int spacingRows = 1;

    /**
     * 是否显示明细表头
     */
    boolean showDetailHeader = true;

    /**
     * 是否为每个主表记录重复明细表头
     */
    boolean repeatDetailHeaderForEachMaster = false;

    /**
     * 构造函数
     *
     * @param opsExport 父级导出对象
     * @param masterData 主表数据
     * @param detailDataExtractor 明细数据提取器
     */
    OpsMasterDetailSheet(OpsExport opsExport, List<M> masterData, Function<M, List<D>> detailDataExtractor) {
        super(opsExport);
        this.masterData = masterData;
        this.detailDataExtractor = detailDataExtractor;
    }

    /**
     * 设置sheet名称
     *
     * @param sheetName sheet名称
     * @return OpsMasterDetailSheet
     */
    public OpsMasterDetailSheet<M, D> sheetName(String sheetName) {
        this.sheetName = sheetName;
        return this;
    }

    /**
     * 设置主从表之间的间隔行数
     *
     * @param spacingRows 间隔行数
     * @return OpsMasterDetailSheet
     */
    public OpsMasterDetailSheet<M, D> spacingRows(int spacingRows) {
        this.spacingRows = spacingRows;
        return this;
    }

    /**
     * 设置是否显示明细表头
     *
     * @param showDetailHeader 是否显示明细表头
     * @return OpsMasterDetailSheet
     */
    public OpsMasterDetailSheet<M, D> showDetailHeader(boolean showDetailHeader) {
        this.showDetailHeader = showDetailHeader;
        return this;
    }

    /**
     * 设置是否为每个主表记录重复明细表头
     *
     * @param repeatDetailHeaderForEachMaster 是否重复明细表头
     * @return OpsMasterDetailSheet
     */
    public OpsMasterDetailSheet<M, D> repeatDetailHeaderForEachMaster(boolean repeatDetailHeaderForEachMaster) {
        this.repeatDetailHeaderForEachMaster = repeatDetailHeaderForEachMaster;
        return this;
    }

    /**
     * 主表表头设置
     *
     * @return OpsHeader<M>
     */
    public OpsHeader<M> opsMasterHeader() {
        if (masterHeader == null) {
            // 创建一个临时的OpsSheet来包装主表数据
            OpsSheet<M> tempMasterSheet = new OpsSheet<>(parent);
            tempMasterSheet.data = masterData;
            masterHeader = new OpsHeader<>(tempMasterSheet);
        }
        return masterHeader;
    }

    /**
     * 主表数据列设置
     *
     * @return OpsMasterColumn<M>
     */
    public OpsMasterColumn<M> opsMasterColumn() {
        if (masterColumn == null) {
            // 创建一个临时的OpsSheet来包装主表数据
            OpsSheet<M> tempMasterSheet = new OpsSheet<>(parent);
            tempMasterSheet.data = masterData;
            masterColumn = new OpsColumn<>(tempMasterSheet);
        }
        return new OpsMasterColumn<>(masterColumn, this);
    }

    /**
     * 明细表表头设置
     *
     * @return OpsHeader<D>
     */
    public OpsDetailHeader<D> opsDetailHeader() {
        if (detailHeader == null) {
            // 创建一个临时的OpsSheet来包装明细数据
            OpsSheet<D> tempDetailSheet = new OpsSheet<>(parent);
            detailHeader = new OpsHeader<>(tempDetailSheet);
        }
        return new OpsDetailHeader<>(detailHeader, this);
    }

    /**
     * 明细表数据列设置
     *
     * @return OpsColumn<D>
     */
    public OpsDetailColumn<D> opsDetailColumn() {
        if (detailColumn == null) {
            // 创建一个临时的OpsSheet来包装明细数据
            OpsSheet<D> tempDetailSheet = new OpsSheet<>(parent);
            detailColumn = new OpsColumn<>(tempDetailSheet);
        }
        return new OpsDetailColumn<>(detailColumn, this);
    }

    /**
     * 明细表头包装类，用于链式调用
     */
    public static class OpsDetailHeader<D> extends AbsParent<OpsMasterDetailSheet<?, D>> {
        private final OpsHeader<D> detailHeader;

        OpsDetailHeader(OpsHeader<D> detailHeader, OpsMasterDetailSheet<?, D> parent) {
            super(parent);
            this.detailHeader = detailHeader;
        }

        public OpsHeader.SimpleHeader<D> simple() {
            return detailHeader.simple();
        }

        public OpsHeader.ComplexHeader<D> complex() {
            return detailHeader.complex();
        }

        public OpsDetailHeader<D> noFreeze() {
            detailHeader.noFreeze();
            return this;
        }
    }

    /**
     * 主表列包装类，用于链式调用
     */
    public static class OpsMasterColumn<M> extends AbsParent<OpsMasterDetailSheet<M, ?>> {
        private final OpsColumn<M> masterColumn;

        OpsMasterColumn(OpsColumn<M> masterColumn, OpsMasterDetailSheet<M, ?> parent) {
            super(parent);
            this.masterColumn = masterColumn;
        }

        public OutColumn<M> field(String field) {
            return masterColumn.field(field);
        }

        public OpsMasterColumn<M> fields(String... fields) {
            masterColumn.fields(fields);
            return this;
        }
    }

    /**
     * 明细表列包装类，用于链式调用
     */
    public static class OpsDetailColumn<D> extends AbsParent<OpsMasterDetailSheet<?, D>> {
        private final OpsColumn<D> detailColumn;

        OpsDetailColumn(OpsColumn<D> detailColumn, OpsMasterDetailSheet<?, D> parent) {
            super(parent);
            this.detailColumn = detailColumn;
        }

        public OutColumn<D> field(String field) {
            return detailColumn.field(field);
        }

        public OpsDetailColumn<D> fields(String... fields) {
            detailColumn.fields(fields);
            return this;
        }
    }

    // 获取器方法
    public List<M> getMasterData() {
        return masterData;
    }

    public Function<M, List<D>> getDetailDataExtractor() {
        return detailDataExtractor;
    }

    public String getSheetName() {
        return sheetName;
    }

    public OpsHeader<M> getMasterHeader() {
        return masterHeader;
    }

    public OpsColumn<M> getMasterColumn() {
        return masterColumn;
    }

    public OpsHeader<D> getDetailHeader() {
        return detailHeader;
    }

    public OpsColumn<D> getDetailColumn() {
        return detailColumn;
    }

    public int getSpacingRows() {
        return spacingRows;
    }

    public boolean isShowDetailHeader() {
        return showDetailHeader;
    }

    public boolean isRepeatDetailHeaderForEachMaster() {
        return repeatDetailHeaderForEachMaster;
    }

    // 实现OpsFinish接口的方法
    @Override
    public void export(String toPath) {
        parent.export(toPath);
    }

    @Override
    public void export(OutputStream toStream) {
        parent.export(toStream);
    }

    @Override
    public void export(HttpServletResponse toResponse, String fileName) {
        parent.export(toResponse, fileName);
    }

    @Override
    public void export(Workbook workbook) {
        parent.export(workbook);
    }
}
