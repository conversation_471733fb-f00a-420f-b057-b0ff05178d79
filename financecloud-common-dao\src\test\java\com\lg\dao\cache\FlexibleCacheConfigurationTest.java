package com.lg.dao.cache;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.cache.UnifiedCacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 灵活缓存配置测试
 */
public class FlexibleCacheConfigurationTest {

    private CacheProperties cacheProperties;
    private UnifiedCacheManager cacheManager;

    @BeforeEach
    public void setUp() {
        cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);
    }

    /**
     * 测试默认配置
     */
    @Test
    public void testDefaultConfiguration() {
        // 不设置任何自定义配置，使用默认值
        CacheProperties.CacheConfig daoConfig = cacheProperties.getConfig("dao");
        assertEquals(500, daoConfig.getMaxSize(), "DAO缓存默认容量应为500");
        assertEquals(3600, daoConfig.getExpireSeconds(), "DAO缓存默认过期时间应为3600秒");
        assertEquals("DAO操作缓存，用于缓存数据库查询结果", daoConfig.getDescription());

        CacheProperties.CacheConfig entityConfig = cacheProperties.getConfig("entity");
        assertEquals(2000, entityConfig.getMaxSize(), "实体缓存默认容量应为2000");
        assertEquals(7200, entityConfig.getExpireSeconds(), "实体缓存默认过期时间应为7200秒");

        CacheProperties.CacheConfig permissionConfig = cacheProperties.getConfig("permission");
        assertEquals(1000, permissionConfig.getMaxSize(), "权限缓存默认容量应为1000");
        assertEquals(1800, permissionConfig.getExpireSeconds(), "权限缓存默认过期时间应为1800秒");

        CacheProperties.CacheConfig sqlTemplateConfig = cacheProperties.getConfig("sql_template");
        assertEquals(1500, sqlTemplateConfig.getMaxSize(), "SQL模板缓存默认容量应为1500");
        assertEquals(3600, sqlTemplateConfig.getExpireSeconds(), "SQL模板缓存默认过期时间应为3600秒");

        CacheProperties.CacheConfig metadataConfig = cacheProperties.getConfig("metadata");
        assertEquals(300, metadataConfig.getMaxSize(), "元数据缓存默认容量应为300");
        assertEquals(7200, metadataConfig.getExpireSeconds(), "元数据缓存默认过期时间应为7200秒");

        CacheProperties.CacheConfig mybatisProxyConfig = cacheProperties.getConfig("mybatis_proxy");
        assertEquals(200, mybatisProxyConfig.getMaxSize(), "MyBatis代理缓存默认容量应为200");
        assertEquals(3600, mybatisProxyConfig.getExpireSeconds(), "MyBatis代理缓存默认过期时间应为3600秒");

        CacheProperties.CacheConfig mybatisSqlParseConfig = cacheProperties.getConfig("mybatis_sql_parse");
        assertEquals(800, mybatisSqlParseConfig.getMaxSize(), "MyBatis SQL解析缓存默认容量应为800");
        assertEquals(1800, mybatisSqlParseConfig.getExpireSeconds(), "MyBatis SQL解析缓存默认过期时间应为1800秒");

        CacheProperties.CacheConfig queryResultConfig = cacheProperties.getConfig("query_result");
        assertEquals(1000, queryResultConfig.getMaxSize(), "查询结果缓存默认容量应为1000");
        assertEquals(600, queryResultConfig.getExpireSeconds(), "查询结果缓存默认过期时间应为600秒");
    }

    /**
     * 测试自定义配置
     */
    @Test
    public void testCustomConfiguration() {
        // 设置自定义配置
        CacheProperties.CacheConfig customDaoConfig = new CacheProperties.CacheConfig(1000, 7200, "自定义DAO缓存");
        cacheProperties.getTypes().put("dao", customDaoConfig);

        CacheProperties.CacheConfig customEntityConfig = new CacheProperties.CacheConfig(5000, 14400, "自定义实体缓存");
        cacheProperties.getTypes().put("entity", customEntityConfig);

        // 验证自定义配置生效
        CacheProperties.CacheConfig daoConfig = cacheProperties.getConfig("dao");
        assertEquals(1000, daoConfig.getMaxSize(), "自定义DAO缓存容量应为1000");
        assertEquals(7200, daoConfig.getExpireSeconds(), "自定义DAO缓存过期时间应为7200秒");
        assertEquals("自定义DAO缓存", daoConfig.getDescription());

        CacheProperties.CacheConfig entityConfig = cacheProperties.getConfig("entity");
        assertEquals(5000, entityConfig.getMaxSize(), "自定义实体缓存容量应为5000");
        assertEquals(14400, entityConfig.getExpireSeconds(), "自定义实体缓存过期时间应为14400秒");
        assertEquals("自定义实体缓存", entityConfig.getDescription());

        // 未自定义的配置应该使用默认值
        CacheProperties.CacheConfig permissionConfig = cacheProperties.getConfig("permission");
        assertEquals(1000, permissionConfig.getMaxSize(), "权限缓存应使用默认容量1000");
        assertEquals(1800, permissionConfig.getExpireSeconds(), "权限缓存应使用默认过期时间1800秒");
    }

    /**
     * 测试混合配置（部分自定义，部分默认）
     */
    @Test
    public void testMixedConfiguration() {
        // 只自定义部分缓存类型
        CacheProperties.CacheConfig customPermissionConfig = new CacheProperties.CacheConfig(2000, 900);
        cacheProperties.getTypes().put("permission", customPermissionConfig);

        CacheProperties.CacheConfig customSqlTemplateConfig = new CacheProperties.CacheConfig(3000, 7200);
        cacheProperties.getTypes().put("sql_template", customSqlTemplateConfig);

        // 验证自定义的配置
        CacheProperties.CacheConfig permissionConfig = cacheProperties.getConfig("permission");
        assertEquals(2000, permissionConfig.getMaxSize());
        assertEquals(900, permissionConfig.getExpireSeconds());

        CacheProperties.CacheConfig sqlTemplateConfig = cacheProperties.getConfig("sql_template");
        assertEquals(3000, sqlTemplateConfig.getMaxSize());
        assertEquals(7200, sqlTemplateConfig.getExpireSeconds());

        // 验证未自定义的配置使用默认值
        CacheProperties.CacheConfig daoConfig = cacheProperties.getConfig("dao");
        assertEquals(500, daoConfig.getMaxSize(), "DAO缓存应使用默认容量");
        assertEquals(3600, daoConfig.getExpireSeconds(), "DAO缓存应使用默认过期时间");

        CacheProperties.CacheConfig entityConfig = cacheProperties.getConfig("entity");
        assertEquals(2000, entityConfig.getMaxSize(), "实体缓存应使用默认容量");
        assertEquals(7200, entityConfig.getExpireSeconds(), "实体缓存应使用默认过期时间");
    }

    /**
     * 测试未知缓存类型的默认配置
     */
    @Test
    public void testUnknownCacheTypeDefaultConfiguration() {
        CacheProperties.CacheConfig unknownConfig = cacheProperties.getConfig("unknown_cache_type");
        assertEquals(100, unknownConfig.getMaxSize(), "未知缓存类型应使用默认容量100");
        assertEquals(1800, unknownConfig.getExpireSeconds(), "未知缓存类型应使用默认过期时间1800秒");
        assertEquals("默认缓存配置", unknownConfig.getDescription());
    }

    /**
     * 测试获取所有默认配置
     */
    @Test
    public void testGetAllDefaultConfigs() {
        Map<String, CacheProperties.CacheConfig> allDefaults = cacheProperties.getAllDefaultConfigs();
        
        assertEquals(8, allDefaults.size(), "应该有8种缓存类型的默认配置");
        
        assertTrue(allDefaults.containsKey("dao"), "应该包含DAO缓存配置");
        assertTrue(allDefaults.containsKey("entity"), "应该包含实体缓存配置");
        assertTrue(allDefaults.containsKey("permission"), "应该包含权限缓存配置");
        assertTrue(allDefaults.containsKey("sql_template"), "应该包含SQL模板缓存配置");
        assertTrue(allDefaults.containsKey("metadata"), "应该包含元数据缓存配置");
        assertTrue(allDefaults.containsKey("mybatis_proxy"), "应该包含MyBatis代理缓存配置");
        assertTrue(allDefaults.containsKey("mybatis_sql_parse"), "应该包含MyBatis SQL解析缓存配置");
        assertTrue(allDefaults.containsKey("query_result"), "应该包含查询结果缓存配置");
        
        // 验证每个配置都有描述信息
        for (Map.Entry<String, CacheProperties.CacheConfig> entry : allDefaults.entrySet()) {
            assertNotNull(entry.getValue().getDescription(), 
                         "缓存类型 " + entry.getKey() + " 应该有描述信息");
            assertTrue(entry.getValue().getMaxSize() > 0, 
                      "缓存类型 " + entry.getKey() + " 的容量应该大于0");
            assertTrue(entry.getValue().getExpireSeconds() > 0, 
                      "缓存类型 " + entry.getKey() + " 的过期时间应该大于0");
        }
    }

    /**
     * 测试缓存禁用状态
     */
    @Test
    public void testCacheDisabled() throws Exception {
        cacheProperties.setEnable(false);
        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();

        // 验证缓存被禁用时的行为
        String result = cacheManager.get(
            UnifiedCacheManager.CacheType.DAO_CACHE,
            "test-key",
            () -> "test-value"
        );
        
        assertEquals("test-value", result, "缓存禁用时应该直接执行加载器");
    }

    /**
     * 测试缓存启用状态
     */
    @Test
    public void testCacheEnabled() throws Exception {
        cacheProperties.setEnable(true);
        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();

        // 验证缓存启用时的行为
        String result1 = cacheManager.get(
            UnifiedCacheManager.CacheType.DAO_CACHE,
            "test-key",
            () -> "test-value"
        );
        
        String result2 = cacheManager.get(
            UnifiedCacheManager.CacheType.DAO_CACHE,
            "test-key",
            () -> "different-value"
        );
        
        assertEquals("test-value", result1, "第一次调用应该执行加载器");
        assertEquals("test-value", result2, "第二次调用应该从缓存获取");
    }
}
