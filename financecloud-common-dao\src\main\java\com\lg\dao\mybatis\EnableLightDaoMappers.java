package com.lg.dao.mybatis;

import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 启用LightDao替换特定的MyBatis Mapper
 * 使用方式：
 * <pre>
 * @EnableLightDaoMappers(
 *     basePackages = "com.example.mapper",
 *     mapperClasses = {UserMapper.class, OrderMapper.class},
 *     mapperLocations = "classpath*:mapper/**\/*.xml",
 *     lazyInit = true
 * )
 * </pre>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(LightDaoMapperRegistrar.class)
public @interface EnableLightDaoMappers {
    
    /**
     * 需要替换的Mapper接口所在的包
     * 如果指定了basePackages，将扫描包中的所有接口
     */
    String[] basePackages() default {};
    
    /**
     * 需要替换的特定Mapper接口类
     * 可以直接指定特定的Mapper接口，而不必替换整个包
     */
    Class<?>[] mapperClasses() default {};
    
    /**
     * Mapper XML文件位置
     * 默认为classpath*:mapper/**\/*.xml
     */
    String mapperLocations() default "classpath*:mapper/**/*.xml";
    
    /**
     * 是否启用延迟初始化
     * 如果为true，只有在实际使用Mapper接口时才会创建代理
     * 默认为true
     */
    boolean lazyInit() default true;
    
    /**
     * 是否替换已存在的Mapper Bean
     * 如果为true，会覆盖已存在的同名Bean
     * 默认为false
     */
    boolean replaceExisting() default false;
} 