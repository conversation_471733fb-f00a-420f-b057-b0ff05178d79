# EntityInfo 统一缓存管理策略

## 📋 设计理念

**采用统一缓存管理，保持框架一致性，同时确保避免重复解析**

## 🎯 核心目标

1. **统一缓存管理**：与其他组件保持一致，使用 UnifiedCacheManager
2. **避免重复解析**：确保每个实体类只解析一次
3. **高性能访问**：缓存命中时提供极快的访问速度
4. **框架一致性**：与 MybatisNativeXmlParser、MybatisTemplateEngine 等保持一致

## 🔧 实现策略

### 1. 双层缓存架构

```java
public class EntityInfoManager {
    private static volatile UnifiedCacheManager globalCacheManager;
    private static final Map<Class<?>, EntityInfo> LOCAL_CACHE = new ConcurrentHashMap<>();
    
    public EntityInfo getEntityInfo(Class<?> entityClass) {
        // 优先使用统一缓存管理器
        if (globalCacheManager != null) {
            String cacheKey = entityClass.getName();
            return globalCacheManager.get(
                UnifiedCacheManager.CacheType.ENTITY_CACHE,
                cacheKey,
                () -> EntityInfo.of(entityClass)  // 只在缓存未命中时解析
            );
        }
        
        // 回退到本地缓存
        return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
    }
}
```

### 2. 缓存层次说明

#### 主缓存：UnifiedCacheManager
- **缓存类型**：`ENTITY_CACHE`
- **缓存键**：`entityClass.getName()`
- **特性**：
  - 支持配置化管理
  - 支持统计和监控
  - 支持过期策略
  - 与其他组件一致

#### 备用缓存：ConcurrentHashMap
- **使用场景**：统一缓存未启用时
- **特性**：
  - 高性能本地缓存
  - 线程安全
  - 永不过期

### 3. 避免重复解析的保证

#### UnifiedCacheManager 的 get 方法
```java
// UnifiedCacheManager.get() 方法确保：
// 1. 如果缓存存在，直接返回
// 2. 如果缓存不存在，调用 supplier 创建并缓存
// 3. 多线程环境下，同一个 key 只会调用一次 supplier

return globalCacheManager.get(
    UnifiedCacheManager.CacheType.ENTITY_CACHE,
    cacheKey,
    () -> EntityInfo.of(entityClass)  // 只在真正需要时调用
);
```

#### ConcurrentHashMap 的 computeIfAbsent 方法
```java
// computeIfAbsent 确保：
// 1. 如果 key 存在，直接返回 value
// 2. 如果 key 不存在，调用 function 计算并存储
// 3. 原子操作，线程安全

return LOCAL_CACHE.computeIfAbsent(entityClass, EntityInfo::of);
```

## 📊 性能分析

### 1. 缓存命中场景
```
第一次调用: entityClass -> 解析 -> 缓存 -> 返回 EntityInfo
后续调用: entityClass -> 缓存命中 -> 直接返回 EntityInfo (同一个对象)
```

### 2. 性能对比

#### 统一缓存 vs 重复解析
- **统一缓存**：缓存查找 + 可能的序列化开销
- **重复解析**：反射 + 注解解析 + 对象创建
- **预期结果**：统一缓存应该显著快于重复解析

#### 统一缓存 vs 本地缓存
- **统一缓存**：Caffeine 缓存查找 + 配置管理开销
- **本地缓存**：ConcurrentHashMap 直接查找
- **预期结果**：本地缓存可能更快，但统一缓存提供更多功能

### 3. 测试策略

```java
@Test
public void testPerformanceComparison() {
    EntityInfoManager manager = EntityInfoManager.getInstance();
    
    // 测试避免重复解析
    EntityInfo result1 = manager.getEntityInfo(TestUser.class);
    EntityInfo result2 = manager.getEntityInfo(TestUser.class);
    assertSame(result1, result2); // 验证是同一个对象
    
    // 性能对比测试
    // 统一缓存 vs 重复解析
}
```

## 🚀 优势分析

### 1. 框架一致性
- **统一管理**：与其他组件使用相同的缓存管理器
- **统一配置**：可以通过配置文件统一管理所有缓存
- **统一监控**：可以统一监控所有缓存的使用情况

### 2. 功能完整性
- **配置化**：支持缓存大小、过期时间等配置
- **统计功能**：支持缓存命中率、请求数等统计
- **监控功能**：支持缓存状态监控
- **管理功能**：支持缓存清除、重置等操作

### 3. 避免重复解析
- **对象复用**：多次调用返回同一个 EntityInfo 对象
- **性能提升**：避免重复的反射和注解解析
- **内存节省**：不会创建重复的 EntityInfo 实例

## 📝 配置示例

### 1. 缓存配置
```yaml
light:
  orm:
    cache:
      enable: true
      types:
        entity:
          max-size: 1000
          expire-seconds: 3600
          description: "实体信息缓存"
```

### 2. 使用示例
```java
// 获取实体信息（使用统一缓存管理）
EntityInfoManager manager = EntityInfoManager.getInstance();
EntityInfo entityInfo = manager.getEntityInfo(User.class);

// 多次调用返回同一个对象
EntityInfo entityInfo2 = manager.getEntityInfo(User.class);
assert entityInfo == entityInfo2; // 同一个对象，避免重复解析

// 缓存管理
manager.evictEntityInfo(User.class);     // 清除指定实体缓存
manager.evictAllEntityInfo();            // 清除所有缓存
String stats = manager.getCacheStats();  // 获取缓存统计
```

## 🔍 监控和调试

### 1. 缓存统计
```java
EntityInfoManager manager = EntityInfoManager.getInstance();
String stats = manager.getCacheStats();
System.out.println(stats);

// 输出示例：
// EntityInfo 缓存统计:
// 本地缓存大小: 5
// 统一缓存: 已启用 (entity)
// 详细统计请查看日志输出
// 已缓存的实体类:
// - User
// - Order
// - Product
```

### 2. 日志监控
```
INFO  - EntityInfoManager 缓存管理器初始化完成: 统一缓存已启用
DEBUG - 清除实体信息缓存: com.example.User
INFO  - 清除所有实体信息缓存，本地缓存数量: 5
```

## 🎯 最佳实践

### 1. 生产环境
- **启用统一缓存**：获得完整的缓存管理功能
- **合理配置**：根据实际使用情况配置缓存大小和过期时间
- **监控缓存**：定期检查缓存命中率和使用情况

### 2. 开发环境
- **可选择禁用缓存**：便于调试和测试
- **或使用较小的缓存配置**：避免内存占用过大

### 3. 测试环境
- **验证避免重复解析**：确保多次调用返回同一个对象
- **性能测试**：验证缓存性能提升效果

## 📋 总结

通过采用统一缓存管理策略：

1. **保持框架一致性**：与其他组件使用相同的缓存管理方式
2. **确保避免重复解析**：每个实体类只解析一次，返回同一个对象
3. **提供完整功能**：支持配置、统计、监控等企业级功能
4. **保持高性能**：在避免重复解析的基础上提供高性能访问
5. **向后兼容**：API 保持不变，平滑升级

这种策略既满足了统一缓存管理的需求，又确保了避免重复解析的核心目标。
