DROP TABLE IF EXISTS t_user;

CREATE TABLE t_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_name VARCHAR(50),
    real_name VARCHAR(50),
    age INT ,
    tenant_id VARCHAR(50),
    create_time TIMESTAMP,
    update_time TIMESTAMP
);

-- 序列表
CREATE TABLE IF NOT EXISTS sys_sequence (
    tenant_id VARCHAR(32) NOT NULL,
    sequence_name VARCHAR(64) NOT NULL,
    current_value BIGINT NOT NULL DEFAULT 0,
    increment_value INT NOT NULL DEFAULT 1,
    min_value BIGINT NOT NULL DEFAULT 1,
    max_value BIGINT NOT NULL DEFAULT 999999999999,
    cycle_flag BOOLEAN NOT NULL DEFAULT FALSE,
    strategy VARCHAR(32) NOT NULL DEFAULT 'NEVER',
    prefix VARCHAR(32),
    suffix_length INT NOT NULL DEFAULT 4,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_reset_time TIMESTAMP,
    CONSTRAINT uk_tenant_sequence UNIQUE (tenant_id, sequence_name)
    );