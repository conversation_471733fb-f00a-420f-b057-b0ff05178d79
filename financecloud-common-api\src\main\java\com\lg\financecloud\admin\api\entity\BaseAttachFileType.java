/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 附件管理
 *
 * <AUTHOR>
 * @date 2021-07-16 11:21:45
 */
@Data
@TableName("base_attach_file_type")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "附件管理")
public class BaseAttachFileType extends Model<BaseAttachFileType> {
private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id",type = IdType.INPUT)
    @ApiModelProperty(value="编号")
    private Integer id;
    /**
     * 附件类型
     */
    @ApiModelProperty(value="附件类型")
    private String attachType;
    /**
     * 存储目录
     */
    @ApiModelProperty(value="存储目录")
    private String storageDir;
    /**
     * 0 私有  1 公有
     */
    @ApiModelProperty(value="0 私有  1 公有")
    private String isPub;
    /**
     * 允许上传的文件类型 jpg,png,txt,doc,docx,xls,xlsx......
     */
    @ApiModelProperty(value="允许上传的文件类型 jpg,png,txt,doc,docx,xls,xlsx......")
    private String fileType;
    /**
     * 允许上传文件单个文件大小
     */
    @ApiModelProperty(value="允许上传文件单个文件大小")
    private Long fileSize;
    /**
     * 
     */
    @ApiModelProperty(value="")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;
    /**
     * 允许上传的文件的限额
     */
    @ApiModelProperty(value="允许上传的文件的限额")
    private Integer alowUploadLimit;
    }
