/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ServiceSequenceKeyEnum {

	/**
	 *系统业务取号key
	 */
	fzhsType( "$accountId_fzhs_type","辅助核算项类型","1"),
	fzhsTypeItem( "$accountId_fzhs_$fzhsType","辅助核算项","1"),
	stockBillIn( "$accountId_stock_bill_in","存货入库单据","1"),
	stockBillOut( "$accountId_stock_bill_out","存货出库单据","1"),
	supplierDeliveryBill( "$accountId_supplier_delivery_bill","供应商送货单","2"),
	selfDeliveryBill( "$accountId_self_delivery_bill","己方送货单","1"),
	materialBill( "$accountId_material_bill","领料单","3"),
	fixedAssetsCardNum("$accountId_fixedAssetsCode","固定资产","1"),
	virtualAssetsCardNum("$accountId_virtualAssetsCode","无形资产","1"),
	shareAssetsCardNum("$accountId_shareAssetsCode","长期摊销","1"),
	documentNo("$tenantId_$code","文案号","1"),


	;


    /**
	 * 描述
	 */
	private String code;
	private String name;
	private String num;


	private static final Map<String, ServiceSequenceKeyEnum> lookup = new HashMap<String, ServiceSequenceKeyEnum>();


    static {
        for (ServiceSequenceKeyEnum s : EnumSet.allOf(ServiceSequenceKeyEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

	public static ServiceSequenceKeyEnum lookup(String code) {
		return lookup.get(code);
	}


}
