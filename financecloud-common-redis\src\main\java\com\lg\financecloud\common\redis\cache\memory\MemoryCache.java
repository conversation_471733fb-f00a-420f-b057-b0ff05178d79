package com.lg.financecloud.common.redis.cache.memory;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.support.AbstractValueAdaptingCache;
import org.springframework.lang.Nullable;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * 使用Caffeine实现的高性能内存缓存
 * 
 * <AUTHOR>
 */
@Slf4j
public class MemoryCache extends AbstractValueAdaptingCache {
    
    private final String name;
    private final Cache<Object, Object> caffeineCache;
    
    /**
     * 创建一个Caffeine缓存
     *
     * @param name 缓存名称
     * @param maxSize 最大缓存条目数
     * @param timeToLiveSeconds 存活时间（秒）
     * @param timeToIdleSeconds 空闲时间（秒）
     */
    public MemoryCache(String name, int maxSize, long timeToLiveSeconds, long timeToIdleSeconds) {
        super(true); // 允许缓存null值
        this.name = name;
        
        Caffeine<Object, Object> cacheBuilder = Caffeine.newBuilder();
        
        if (maxSize > 0) {
            cacheBuilder.maximumSize(maxSize);
        }
        
        if (timeToLiveSeconds > 0) {
            cacheBuilder.expireAfterWrite(timeToLiveSeconds, TimeUnit.SECONDS);
        }
        
        if (timeToIdleSeconds > 0) {
            cacheBuilder.expireAfterAccess(timeToIdleSeconds, TimeUnit.SECONDS);
        }
        
        // 记录缓存的统计信息
        cacheBuilder.recordStats();
        
        this.caffeineCache = cacheBuilder.build();
    }
    
    @Override
    public String getName() {
        return this.name;
    }
    
    @Override
    public Object getNativeCache() {
        return this.caffeineCache;
    }
    
    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        Object value = caffeineCache.get(key, k -> {
            try {
                return toStoreValue(valueLoader.call());
            }
            catch (Exception e) {
                throw new ValueRetrievalException(key, valueLoader, e);
            }
        });
        
        return (T) fromStoreValue(value);
    }
    
    @Override
    protected Object lookup(Object key) {
        return caffeineCache.getIfPresent(key);
    }
    
    @Override
    public void put(Object key, @Nullable Object value) {
        caffeineCache.put(key, toStoreValue(value));
    }
    
    @Override
    public void evict(Object key) {
        caffeineCache.invalidate(key);
    }
    
    @Override
    public void clear() {
        caffeineCache.invalidateAll();
    }
    
    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public com.github.benmanes.caffeine.cache.stats.CacheStats stats() {
        return caffeineCache.stats();
    }
} 