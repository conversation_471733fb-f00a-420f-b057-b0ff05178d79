/*
 *
 *      Copyright (c) 2018-2025, ebag All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;


import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR> @date 2018/6/22
 */
@FeignClient(contextId = "remoteMobileService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteMobileService {
	/**
	 * 通过用户名查询用户、角色信息
	 *
	 * @param from 用户名
	 * @param mobile     调用标志
	 * @return R
	 */
	@GetMapping("/mobile/{mobile}")
	public R sendSmsCode(@PathVariable("mobile") String mobile, @RequestHeader(SecurityConstants.FROM) String from);

}
