package com.lg.financecloud.common.data.dbcache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.lg.dao.core.GenericDao;
import com.lg.dao.core.condition.ConditionStrategy;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class DbCacheUtil {



    /****
     *
     * @param catalog
     * @param area
     * @param userId
     * @param value
     */
    public static void saveDbCache(String catalog,String  area,String ownType,String ownId,String value){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache dbCache = new DbCache();
        dbCache.setArea(area);
        dbCache.setCatalog(catalog);
        dbCache.setOwnerType(ownType);
        dbCache.setOwnerId(ownId);
        DbCache existsDbCache = dao.lambdaQuery().allEq(dbCache).one();
        dbCache.setValue(value);
        if(existsDbCache==null){
            dbCache.setId(IdUtil.getSnowflakeNextIdStr());
            dao.insert(dbCache);
        }else{
            dbCache.setId(existsDbCache.getId());
            dao.lambdaUpdate().allEq(dbCache).update();

        }

    }
    /****
     *
     * @param dbCache

     */
    public static void saveDbCache(DbCache dbCache){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        String value = dbCache.getValue();
        String remark = dbCache.getRemark();
        dbCache.setValue(null);
        dbCache.setId(null);
        dbCache.setRemark(null);
        DbCache existsDbCache = dao.lambdaQuery().eq(DbCache::getCatalog,dbCache.getCatalog())
                .eq(DbCache::getArea,dbCache.getArea())
                .eq(DbCache::getOwnerId,dbCache.getOwnerId())
                .eq(DbCache::getOwnerType,dbCache.getOwnerType())
                .one();
        if(existsDbCache==null){
            dbCache.setId(IdUtil.getSnowflakeNextIdStr());
            dbCache.setValue(value);
            dbCache.setRemark(remark);
            dbCache.setCreateTime(new Date());
            dbCache.setUpdateTime(new Date());
            dao.insert(dbCache);
        }else{
            dbCache.setId(existsDbCache.getId());
            dbCache.setValue(value);
            dbCache.setUpdateTime(new Date());
            dao.updateById(dbCache);

        }

    }

    public static void batchSaveDbCache(List<DbCache> dbCaches){
        if(CollectionUtil.isEmpty(dbCaches)){
            return;
        }
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        dao.batchInsert(dbCaches);

   }


    /****
     *
     * @param catalog
     * @param area
     * @param userId
     * @param value
     */
    public static void saveUserDbCache(String catalog, String  area, Long userId, String value){
        saveDbCache(catalog,area ,DbCache.OWN_TYPT_USER , userId.toString(), value);
    }

    /****
     *
     * @param catalog
     * @param area
     * @param userId
     * @param value
     */
    public static void saveAccountDbCache(String catalog, String  area, Long accountId, String value){
        saveDbCache(catalog,area ,DbCache.OWN_TYPT_ACCOUNT , accountId.toString(), value);
    }
    /****
     *
     * @param catalog
     * @param area
     * @param userId
     * @param value
     */
    public static void saveTenantDbCache(String catalog, String  area, Integer tenantId, String value){
        saveDbCache(catalog,area ,DbCache.OWN_TYPT_TENANT , tenantId.toString(), value);
    }

    public static void deleteUserDbCache(String catalog, String key, Long userId) {
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache userDbCache = getUserDbCache(catalog, key, userId);
        if(userDbCache!=null){
            dao.deleteById(userDbCache.getId());
        }
    }

    public static DbCache getDbCacheById(String id){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        return dao.getById(id );
    }

    public static List<DbCache> getDbCacheByIds(String []ids){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        return  dao.lambdaQuery().inIfNotEmpty(DbCache::getId,ids).list();
    }

    public static DbCache queryDbCache(String catalog, String area, String ownType, String ownId ){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache dbCache  = new DbCache();
        dbCache.setOwnerId(ownId);
        dbCache.setOwnerType(ownType);
        dbCache.setCatalog(catalog);
        dbCache.setArea(area);
        return  dao.lambdaQuery().allEq(dbCache).one();
    }



    public static List<DbCache> getDbCaches(String catalog,  String ownType, String ownId,String [] keys ) {
        List<DbCache> dbCaches = getDbCaches(catalog, ownType, ownId);
        if(CollectionUtil.isNotEmpty(dbCaches)){
          return  dbCaches.stream().filter(dbCache -> {
                for (String key : keys) {
                    if(dbCache.getArea().equals(key)){
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }
        return ListUtil.empty();
    }


    public static List<DbCache> getDbCaches(String catalog,  String ownType, String ownId ) {
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache dbCache = new DbCache();
        dbCache.setOwnerId(ownId);
        dbCache.setOwnerType(ownType);
        dbCache.setCatalog(catalog);
        return  dao.lambdaQuery().allEq(dbCache).list();
    }

    public static DbCache getUserDbCache(String catalog,String area,Long userId ){
        return queryDbCache(catalog, area, DbCache.OWN_TYPT_USER, userId.toString());
    }

    public static DbCache getTenantDbCache(String catalog,String area,Integer tenantId ){
        return queryDbCache(catalog, area, DbCache.OWN_TYPT_TENANT, tenantId.toString());
    }


    public  static  DbCache getAccountDbCache(String catalog,String area,Long account ){
        return queryDbCache(catalog, area, DbCache.OWN_TYPT_ACCOUNT, account.toString());
    }
    public  static  List<DbCache> getSystemDbCaches(String catalog ){
        return getDbCaches(catalog, DbCache.OWN_TYPT_SYSTEM,"system");
    }

    public  static  DbCache getSystemDbCache(String catalog ,String key){
        return queryDbCache(catalog, key,DbCache.OWN_TYPT_SYSTEM,"system");
    }

    public static Boolean deleteDbCacheById(String id){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        return dao.deleteById(id)>0;
    }


    public  static  DbCache getTenantDbCacheIfNull(String catalog,String area,Integer tenantId ){
        DbCache tenantDbCache = DbCacheUtil.getTenantDbCache(catalog, area, tenantId);
        if(tenantDbCache==null){
            tenantDbCache = DbCacheUtil.getSystemDbCache(catalog,area);
        }
        return tenantDbCache;
    }
    public  static  DbCache getUserDbCacheIfNull(String catalog,String area,Long userId ){
        DbCache userDbCache = DbCacheUtil.getUserDbCache(catalog, area, userId);
        if(userDbCache==null){
            userDbCache = DbCacheUtil.getSystemDbCache(catalog,area);
        }
        return userDbCache;
    }


    public static com.lg.dao.core.Page<DbCache> getDbCachePage(Page page, DbCache dbCache){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        dbCache.setOwnerType("4");
       return   dao.lambdaQuery().page(page.getCurrent(), page.getSize());

    }




    public static List<DbCache> getDbCacheByArea(String area){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        return dao.lambdaQuery().eq(DbCache::getArea,area).list();
    }

    public static List<DbCache> queryDbCache(DbCache dbCache){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        return dao.lambdaQuery().allEq(dbCache).list();
    }




    public static void saveDbCache( JSONObject configs){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache dbCache = new DbCache();
        dbCache.setArea(configs.getStr("area"));
        dbCache.setCatalog(configs.getStr("catalog"));
        dbCache.setOwnerType(configs.getStr("ownerType"));
        dbCache.setOwnerId("system");
        DbCache existsDbCache = dao.lambdaQuery().allEq(dbCache).one();
        dbCache.setValue(configs.getStr("value"));
        if(!configs.containsKey("rowId")){
            dbCache.setId(IdUtil.getSnowflakeNextIdStr());
            dao.insert(dbCache);
        }else{
            dbCache.setId(configs.getStr("rowId"));
            dao.lambdaUpdate().allEq(dbCache).update();
        }
    }



    public static void delDbCacheById(String id){
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        dao.deleteById(id);

    }


    public static DbCache getDbCacheByCatalogAndArea(String catalog, String area) {
        GenericDao<DbCache, String> dao = DaoHelper.daoString(DbCache.class);
        DbCache dbCache=new DbCache();
        dbCache.setCatalog(catalog);
        dbCache.setArea(area);
        dbCache.setOwnerType(DbCache.OWN_TYPT_SYSTEM);
        dbCache.setOwnerId(DbCache.OWN_ID_SYSTEM);
        List<DbCache> dbCaches = dao.lambdaQuery().allEq(dbCache).list();
        if (CollectionUtil.isEmpty(dbCaches)){
            return null;
        }
        DbCache system = dbCaches.get(0);

        if (DbCache.OWN_TYPT_USER.equals(system.getScope())){
            dbCache.setOwnerType(DbCache.OWN_TYPT_USER);
           dbCache.setOwnerId(TenantContextHolder.getUserId().toString());
        }
        if (DbCache.OWN_TYPT_ACCOUNT.equals(system.getScope())){
            dbCache.setOwnerType(DbCache.OWN_TYPT_ACCOUNT);
           dbCache.setOwnerId(TenantContextHolder.getAccountId().toString());
        }
        if (DbCache.OWN_TYPT_TENANT.equals(system.getScope())){
            dbCache.setOwnerType(DbCache.OWN_TYPT_TENANT);
            dbCache.setOwnerId(TenantContextHolder.getTenantId().toString());
        }
       return  dao.lambdaQuery().allEq(dbCache).one();
    }

    public static void main(String[] args) {
        System.out.println(getDbCacheByCatalogAndArea("test","test"));
    }

}
