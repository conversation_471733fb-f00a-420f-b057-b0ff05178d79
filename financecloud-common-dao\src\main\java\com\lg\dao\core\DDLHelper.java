package com.lg.dao.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DDL操作工具类
 */
@Slf4j
public class DDLHelper {
    private final JdbcTemplate jdbcTemplate;

    public DDLHelper(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 创建表
     */
    public void createTable(String tableName, List<TableColumn> columns, String primaryKey, String... indexes) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (");

        // 添加列定义
        List<String> columnDefs = new ArrayList<>();
        for (TableColumn column : columns) {
            columnDefs.add(column.toSql());
        }

        // 添加主键
        if (StringUtils.hasText(primaryKey)) {
            columnDefs.add("PRIMARY KEY (" + primaryKey + ")");
        }

        sql.append(String.join(", ", columnDefs));
        sql.append(")");

        jdbcTemplate.execute(sql.toString());
        log.info("Created table script: {}", sql);

        // 创建索引
        for (String index : indexes) {
            if (StringUtils.hasText(index)) {
                String indexName = "idx_" + tableName + "_" + index.replace(",", "_");
                String indexSql = "CREATE INDEX IF NOT EXISTS " + indexName + " ON " + tableName + "(" + index + ")";
                jdbcTemplate.execute(indexSql);
                log.info("Created index: {}", indexName);
            }
        }
    }

    /**
     * 根据实体信息自动创建表结构
     */
    public void createTableFromEntity(EntityInfo entityInfo) {
        List<TableColumn> columns = new ArrayList<>();
        
        // 转换字段信息为TableColumn
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            TableColumn column = TableColumn.builder()
                .name(fieldInfo.getColumn())
                .type(fieldInfo.getDbType())
                .nullable(true) // 默认可空
                .build();
            columns.add(column);
        }
        
        // 获取主键字段
        String primaryKey = entityInfo.getIdFields().stream()
            .map(EntityInfo.FieldInfo::getColumn)
            .collect(Collectors.joining(","));
        
        // 创建表
        createTable(entityInfo.getTableName(), columns, primaryKey);
        
        // 创建索引
        createIndexesFromEntity(entityInfo);
    }
    
    /**
     * 根据实体信息创建索引
     */
    private void createIndexesFromEntity(EntityInfo entityInfo) {
        String tableName = entityInfo.getTableName();
        
        // 创建索引
        for (EntityInfo.IndexInfo indexInfo : entityInfo.getIndexes()) {
            String indexName = indexInfo.getName();
            String columns = String.join(",", indexInfo.getColumnNames());
            
            if (indexInfo.isUnique()) {
                createUniqueIndex(tableName, indexName, columns);
            } else {
                addIndex(tableName, indexName, columns);
            }
        }
    }

    /**
     * 添加列
     */
    public void addColumn(String tableName, TableColumn column) {
        String sql = "ALTER TABLE " + tableName + " ADD COLUMN " + column.toSql();
        jdbcTemplate.execute(sql);
    }

    /**
     * 修改列
     */
    public void modifyColumn(String tableName, TableColumn column) {
        String sql = "ALTER TABLE " + tableName + " MODIFY COLUMN " + column.toSql();
        jdbcTemplate.execute(sql);
    }

    /**
     * 删除列
     */
    public void dropColumn(String tableName, String columnName) {
        String sql = "ALTER TABLE " + tableName + " DROP COLUMN " + columnName;
        jdbcTemplate.execute(sql);
    }

    /**
     * 添加索引
     */
    public void addIndex(String tableName, String indexName, String columns) {
        String sql = "CREATE INDEX IF NOT EXISTS " + indexName + " ON " + tableName + "(" + columns + ")";
        jdbcTemplate.execute(sql);
        log.info("Created index: {}", indexName);
    }
    
    /**
     * 添加唯一索引
     */
    public void createUniqueIndex(String tableName, String indexName, String columns) {
        String sql = "CREATE UNIQUE INDEX IF NOT EXISTS " + indexName + " ON " + tableName + "(" + columns + ")";
        jdbcTemplate.execute(sql);
        log.info("Created unique index: {}", indexName);
    }

    /**
     * 删除索引
     */
    public void dropIndex(String tableName, String indexName) {
        String sql = "DROP INDEX IF EXISTS " + indexName + " ON " + tableName;
        jdbcTemplate.execute(sql);
    }

    /**
     * 检查表是否存在
     */
    public boolean tableExists(String tableName) {
        try {
            jdbcTemplate.queryForObject("SELECT 1 FROM " + tableName + " WHERE 1=0", Integer.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取表结构信息
     */
    public List<Map<String, Object>> getTableInfo(String tableName) {
        String sql = "SHOW COLUMNS FROM " + tableName;
        return jdbcTemplate.queryForList(sql);
    }

    public String  getCreateTable(String tableName) {
        String sql = "SHOW CREATE TABLE " + tableName;
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 表列定义
     */
    @lombok.Data
    @lombok.Builder
    public static class TableColumn {
        private String name;            // 列名
        private String type;            // 数据类型
        private Integer length;         // 长度
        private Integer precision;      // 精度
        private Integer scale;          // 小数位数
        private boolean nullable;       // 是否可空
        private String defaultValue;    // 默认值
        private String comment;         // 注释

        public String toSql() {
            StringBuilder sql = new StringBuilder(name + " " + type);

            // 添加长度/精度
            if (length != null) {
                sql.append("(").append(length).append(")");
            } else if (precision != null) {
                sql.append("(").append(precision);
                if (scale != null) {
                    sql.append(",").append(scale);
                }
                sql.append(")");
            }

            // 是否可空
            sql.append(nullable ? " NULL" : " NOT NULL");

            // 默认值
            if (StringUtils.hasText(defaultValue)) {
                sql.append(" DEFAULT ").append(defaultValue);
            }

            // 注释
            if (StringUtils.hasText(comment)) {
                sql.append(" COMMENT '").append(comment).append("'");
            }

            return sql.toString();
        }
    }
}