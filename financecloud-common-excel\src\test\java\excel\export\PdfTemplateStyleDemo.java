package excel.export;

import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.pdf.PdfTemplateConfig;

import java.util.*;

/**
 * PDF模板样式继承演示程序
 * 展示完整的样式继承功能
 */
public class PdfTemplateStyleDemo {

    public static void main(String[] args) {
        System.out.println("=== PDF模板样式继承功能演示 ===");
        
        try {
            // 检查PDF依赖
            try {
                Class.forName("com.itextpdf.text.Document");
                System.out.println("✓ PDF依赖检查通过");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠ 警告: 未找到iText依赖，无法进行PDF导出演示");
                System.out.println("请在pom.xml中添加以下依赖:");
                System.out.println("<dependency>");
                System.out.println("    <groupId>com.itextpdf</groupId>");
                System.out.println("    <artifactId>itextpdf</artifactId>");
                System.out.println("    <version>5.5.13.3</version>");
                System.out.println("</dependency>");
                System.out.println("<dependency>");
                System.out.println("    <groupId>com.itextpdf</groupId>");
                System.out.println("    <artifactId>itext-asian</artifactId>");
                System.out.println("    <version>5.2.0</version>");
                System.out.println("</dependency>");
                return;
            }
            
            PdfTemplateStyleDemo demo = new PdfTemplateStyleDemo();
            demo.demonstrateStyleInheritance();
            
            System.out.println("\n=== 演示完成 ===");
            System.out.println("✓ PDF模板样式继承功能已成功实现");
            System.out.println("✓ 支持Excel模板的完整样式继承，包括：");
            System.out.println("  - 字体样式（大小、粗体、斜体、颜色）");
            System.out.println("  - 单元格背景色");
            System.out.println("  - 边框样式");
            System.out.println("  - 文本对齐方式");
            System.out.println("  - 单元格合并");
            System.out.println("  - 行高列宽");
            System.out.println("✓ 变量替换和循环数据处理");
            System.out.println("✓ 中文字体支持");
            
        } catch (Exception e) {
            System.err.println("✗ 演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 演示样式继承功能
     */
    private void demonstrateStyleInheritance() throws Exception {
        System.out.println("\n--- 样式继承功能演示 ---");
        
        // 检查是否已有样式模板文件
        String templatePath = "src/test/java/excel/export/excel/styled_template.xlsx";
        java.io.File templateFile = new java.io.File(templatePath);
        
        if (!templateFile.exists()) {
            System.out.println("⚠ 样式模板文件不存在，请先运行 StyledPdfTemplateTest 创建模板");
            return;
        }
        
        System.out.println("✓ 找到样式模板文件: " + templatePath);
        
        // 准备演示数据
        Map<String, Object> variables = createDemoVariables();
        List<Map<String, Object>> employees = createDemoEmployees();
        
        System.out.println("✓ 演示数据准备完成");
        System.out.println("  - 变量数量: " + variables.size());
        System.out.println("  - 员工数据: " + employees.size() + " 条记录");
        
        // 执行PDF模板导出（带完整样式继承）
        String outputPath = "src/test/java/excel/export/excel/style_inheritance_demo.pdf";
        
        System.out.println("✓ 开始PDF模板导出...");
        
        ExcelHelper.opsPdfTemplate(templatePath)
                .vars(variables)
                .loop("employees", employees)
                .fontSize(10f)  // 基础字体大小，实际会使用Excel模板中的字体大小
                .enableChineseFont(true)  // 启用中文字体支持
                .orientation(PdfTemplateConfig.PageOrientation.PORTRAIT)
                .pageSize(PdfTemplateConfig.PageSize.A4)
                .margins(20f)
                .showGridLines(false)  // 不显示默认网格线，使用Excel样式的边框
                .preserveTemplateFormat(true)  // 保留模板格式
                .exportTo(outputPath);
        
        // 验证输出结果
        java.io.File outputFile = new java.io.File(outputPath);
        if (outputFile.exists() && outputFile.length() > 0) {
            System.out.println("✓ PDF导出成功！");
            System.out.println("  输出文件: " + outputPath);
            System.out.println("  文件大小: " + formatFileSize(outputFile.length()));
            
            // 显示样式继承详情
            System.out.println("\n--- 样式继承详情 ---");
            System.out.println("✓ 标题样式: 18pt粗体蓝色字体，浅蓝背景，粗边框");
            System.out.println("✓ 副标题样式: 14pt粗体绿色字体，左对齐");
            System.out.println("✓ 信息样式: 10pt斜体灰色字体，右对齐");
            System.out.println("✓ 表头样式: 12pt粗体白色字体，深蓝背景，中等边框");
            System.out.println("✓ 数据样式: 10pt普通字体，细边框，左对齐");
            System.out.println("✓ 货币样式: 10pt粗体红色字体，浅黄背景，右对齐");
            System.out.println("✓ 汇总样式: 11pt粗体蓝色字体，浅蓝背景，中等边框");
            System.out.println("✓ 单元格合并: 标题行跨5列合并");
            System.out.println("✓ 列宽设置: 根据Excel模板自动调整");
            
        } else {
            System.out.println("⚠ PDF文件生成失败或为空");
        }
    }

    /**
     * 创建演示变量
     */
    private Map<String, Object> createDemoVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("companyName", "金融云科技有限公司");
        variables.put("reportTitle", "员工薪资报表（样式继承演示）");
        variables.put("reportDate", "2025年08月21日");
        variables.put("totalEmployees", "8");
        variables.put("avgSalary", "¥13,500");
        return variables;
    }

    /**
     * 创建演示员工数据
     */
    private List<Map<String, Object>> createDemoEmployees() {
        List<Map<String, Object>> employees = new ArrayList<>();
        
        String[][] employeeData = {
            {"EMP001", "张三", "技术部", "高级工程师", "¥18,000"},
            {"EMP002", "李四", "销售部", "销售经理", "¥15,000"},
            {"EMP003", "王五", "财务部", "财务专员", "¥12,000"},
            {"EMP004", "赵六", "人事部", "HR专员", "¥11,000"},
            {"EMP005", "钱七", "市场部", "市场专员", "¥13,000"},
            {"EMP006", "孙八", "技术部", "软件工程师", "¥16,000"},
            {"EMP007", "周九", "运营部", "运营经理", "¥14,000"},
            {"EMP008", "吴十", "客服部", "客服主管", "¥9,000"}
        };
        
        for (String[] data : employeeData) {
            Map<String, Object> employee = new HashMap<>();
            employee.put("employeeId", data[0]);
            employee.put("name", data[1]);
            employee.put("department", data[2]);
            employee.put("position", data[3]);
            employee.put("salary", data[4]);
            employees.add(employee);
        }
        
        return employees;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
}
