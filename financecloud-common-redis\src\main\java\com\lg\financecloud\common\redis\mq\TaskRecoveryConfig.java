package com.lg.financecloud.common.redis.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 任务恢复配置类
 * 启用定时任务调度，用于任务恢复服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "light.mq.recovery", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TaskRecoveryConfig {
    
    public TaskRecoveryConfig() {
        log.info("任务恢复配置已启用");
    }
}
