/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.console.cache;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.bstek.ureport.cache.CacheUtils;
import com.bstek.ureport.console.RequestHolder;
import com.lg.financecloud.admin.api.dto.SessionUser;
import com.lg.financecloud.common.data.RedisUtils;


/**
 * <AUTHOR>
 * @since 2017年9月6日
 */
public class TempObjectCache{
	public static String UREPORT_TMP_PREFIX="gl:ureport_tmp:";
	public static Object getObject(String key){
		HttpServletRequest req=RequestHolder.getRequest();
		String id = req.getSession().getId();
		return RedisUtils.getCacheObject(UREPORT_TMP_PREFIX+ id+":"+key);
	}
	public static void putObject(String key,Object obj){
		HttpServletRequest req=RequestHolder.getRequest();
		String id = req.getSession().getId();
		RedisUtils.setCacheObject(UREPORT_TMP_PREFIX+id+":"+key,obj, Duration.ofMinutes(10));
	}
	
	public static void removeObject(String key){
		HttpServletRequest req=RequestHolder.getRequest();
		String id = req.getSession().getId();
		RedisUtils.deleteObject(UREPORT_TMP_PREFIX+id +":"+key );
	}
	

	



}
