/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.entity;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
@ApiModel(value = "用户")
public class SysUser implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "user_id", type = IdType.INPUT)
	@ApiModelProperty(value = "主键id")
	private Long userId;

	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String username;


	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "昵称")
	private String nickname;

	/**
	 * 密码
	 */
	@ApiModelProperty(value = "密码")
	private String password;

	/**
	 * 随机盐
	 */
	@JsonIgnore
	@ApiModelProperty(value = "随机盐")
	private String salt;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private LocalDateTime updateTime;

	/**
	 * 0-正常，1-删除
	 */
	@TableLogic
	@ApiModelProperty(value = "删除标记,1:已删除,0:正常")
	private String delFlag;

	/**
	 * 锁定标记
	 */
	@ApiModelProperty(value = "锁定标记")
	private String lockFlag;

	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String phone;

	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像地址")
	private String avatar;

	/**
	 * 部门ID
	 */
	@ApiModelProperty(value = "用户所属部门id")
	private Long deptId;

	/**
	 * 部门ID
	 */
	@ApiModelProperty(value = "用户所属部门")
	@TableField(exist = false)
	private String deptName;

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "用户所属租户id")
	private Integer tenantId;

	/**
	 * 微信openid
	 */
	@ApiModelProperty(value = "微信openid")
	private String wxOpenid;

	/**
	 * 微信小程序openId
	 */
	@ApiModelProperty(value = "微信小程序openid")
	private String miniOpenid;

	/**
	 * QQ openid
	 */
	@ApiModelProperty(value = "QQ openid")
	private String qqOpenid;

	/**
	 * 码云唯一标识
	 */
	@ApiModelProperty(value = "码云唯一标识")
	private String giteeLogin;

	/**
	 * 开源中国唯一标识
	 */
	@ApiModelProperty(value = "开源中国唯一标识")
	private String oscId;

	/**
	 * 用户类型 0 B端管理用户 1 C 端 家长账号
	 */
	@ApiModelProperty(value = "用户类型 0 B端管理用户 1 C 端 家长账号")
	private String UserType;

	@ApiModelProperty(value="用户来源 0本平台注册,1平安家校")
	private String userSource;

	private String email;


	@TableField(exist = false)
	private BaseUserInfo baseUserInfo;
	@TableField(exist = false)
	private String token;

	@TableField(exist = false)
	private Map<String,String> profile;

	public Map<String, String> getProfile() {
		if(this.profile ==null){
			this.profile =new HashMap<String,String>();
		}
		if(StrUtil.isNotEmpty(oscId)){
			this.profile.putAll( JSONUtil.toBean(oscId, Map.class));
		}
		 return this.profile;
	}

	public void setProfile(Map<String, String> profile) {
		this.profile = profile;
	}

	public  void addProfile(String key,String value){
		this.getProfile().put(key,value);
	}
}
