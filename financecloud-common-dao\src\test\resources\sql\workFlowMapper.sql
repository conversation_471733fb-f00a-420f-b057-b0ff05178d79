#findTasksByUserIdAndStatus
 SELECT
            wft.id AS id,
            wft.tenant_id AS tenantId,
            wft.instance_id AS instanceId,
            wft.business_key AS businessKey,
            wft.node_id AS nodeId,
            wft.node_name AS nodeName,
            wft.node_type AS nodeType,
            wft.assignee_id AS assigneeId,
            wft.assignee_name AS assigneeName,
            wft.`STATUS` AS `status`,
            wft.action AS action,
            wft.`COMMENT` AS `comment`,
            wft.form_data AS formData,
            wft.`VARIABLES` AS `variables`,
            wft.priority AS priority,
            wft.due_date AS dueDate,
            wft.claim_time AS claimTime,
            wft.complete_time AS completeTime,
            wft.create_time AS createTime,
            wft.update_time AS updateTime,
            wfi.title,
            wfd.name processName,
            wfd.category,
            wfc.category_code as categoryCode,
            wfc.category_name as categoryName,
            wfd.pc_form_url pcFormUrl,
            wfd.mobile_form_url mobileFormUrl
        FROM
            workflow_task wft
            INNER JOIN workflow_instance wfi ON ( wfi.id = wft.instance_id )
            INNER JOIN workflow_definition wfd ON (wfd.id = wfi.definition_id)
            left join workflow_category wfc on(wfd.category=wfc.id)
        WHERE
            wft.assignee_id = #{userId}
          AND wft.STATUS in
            <foreach item="item" collection="list" separator="," open="(" close=")">
                #{item}
            </foreach>

#findEmployeesByDepartmentIds
            SELECT
            eus.id AS id,
            CONCAT(eus.user_name,'(',eus.job_number,')') AS employeeName,
            eus.user_name userName,
            eus.job_number jobNumber,
            0 AS isLeave,
            0 AS open
        FROM  sys_eve_user_staff eus
        WHERE eus.department_id IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
#findCCTasksByUserIdAndStatus
SELECT
    wft.id AS id,
    wft.tenant_id AS tenantId,
    wft.instance_id AS instanceId,
    wft.business_key AS businessKey,
    wft.node_id AS nodeId,
    wft.node_name AS nodeName,
    wft.assignee_id AS assigneeId,
    wft.assignee_name AS assigneeName,
    wft.`STATUS` AS `status`,
    wft.action AS action,
    wft.`COMMENT` AS `comment`,
    wft.form_data AS formData,
    wft.`VARIABLES` AS `variables`,
    wft.priority AS priority,
    wft.due_date AS dueDate,
    wft.claim_time AS claimTime,
    wft.complete_time AS completeTime,
    wft.create_time AS createTime,
    wft.update_time AS updateTime,
    wfi.title,
    wfd.name processName,
    wfd.category,
    wfc.category_code as categoryCode,
    wfc.category_name as categoryName,
    wfd.pc_form_url pcFormUrl,
    wfd.mobile_form_url mobileFormUrl,
    wfi.status AS instanceStatus
FROM
    workflow_task wft
    INNER JOIN workflow_instance wfi ON (wfi.id = wft.instance_id)
    INNER JOIN workflow_definition wfd ON (wfd.id = wfi.definition_id)
    LEFT JOIN workflow_category wfc ON (wfd.category = wfc.id)
WHERE
    wft.assignee_id = #{userId}
    AND wft.node_type = #{nodeType}

ORDER BY
    wft.create_time DESC
#getListRoles
  select
  role_code code,
  role_id roleId,
  role_name roleName,
  role_desc roleDesc

  from sys_role  where del_flag = '0' and tenant_id = #{tenantId}

#getAllOrgList
     SELECT
                  sc.id organizationId,
                  sc.name organizationName,
                  sc.parent_id organizationPid,
                  1 AS organizationType,
                  '0' as positionManageFlag


            FROM sys_company sc
            WHERE sc.del_flag = 0
            UNION ALL
            SELECT
                  sd.dept_id AS organizationId,

                  sd.name organizationName,
                 IF(sd.parent_id='0',sd.company_id,sd.parent_id)organizationPid,
                  2 AS organizationType,
                  '0' as positionManageFlag

            FROM sys_dept sd
            WHERE sd.del_flag = 0
            UNION ALL
            SELECT
                  id organizationId,
                  sp.name organizationName,
                  sp.department_id AS organizationPid,
                  3 AS organizationType,
                  sp.manager_flag positionManageFlag

            FROM sys_position sp
            WHERE sp.del_flag = 0


