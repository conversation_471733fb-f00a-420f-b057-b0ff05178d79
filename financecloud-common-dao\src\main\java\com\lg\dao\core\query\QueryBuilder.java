package com.lg.dao.core.query;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.core.sql.SqlBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询构建器
 */
public class QueryBuilder<T> {
    private final GenericDao<T, ?> dao;
    private final Class<T> entityClass;
    private final EntityInfo entityInfo;
    private final SqlBuilder sqlBuilder;
    private final List<String> selectFields = new ArrayList<>();
    private final List<String> orderByFields = new ArrayList<>();
    private boolean distinct = false;
    private Integer limitCount;
    private Integer offsetCount;
    private boolean isFirstCondition = true;

    public QueryBuilder(GenericDao<T, ?> dao, Class<T> entityClass, EntityInfo entityInfo) {
        this.dao = dao;
        this.entityClass = entityClass;
        this.entityInfo = entityInfo;
        this.sqlBuilder = new SqlBuilder();
    }

    /**
     * 选择字段
     */
    public QueryBuilder<T> select(String... fields) {
        for (String field : fields) {
            selectFields.add(field);
        }
        return this;
    }

    /**
     * 去重
     */
    public QueryBuilder<T> distinct() {
        this.distinct = true;
        return this;
    }

    /**
     * 添加OR连接符
     * 用于连接两个条件，如 status = 'ACTIVE' OR status = 'PENDING'
     * @return 当前查询构建器
     */
    public QueryBuilder<T> or() {
        if (!isFirstCondition) {
            sqlBuilder.append(" OR ");
            isFirstCondition = true;
        }
        return this;
    }

    /**
     * 添加AND连接符
     * 用于连接两个条件，如 status = 'ACTIVE' AND role = 'ADMIN'
     * @return 当前查询构建器
     */
    public QueryBuilder<T> and() {
        if (!isFirstCondition) {
            sqlBuilder.append(" AND ");
            isFirstCondition = true;
        }
        return this;
    }

    /**
     * 开始一个AND条件组
     * 用于创建 (condition1 AND condition2 AND...) 形式的查询条件
     */
    public QueryBuilder<T> beginAndGroup() {
        if (!isFirstCondition) {
            sqlBuilder.append(" AND ");
        }
        sqlBuilder.append("(");
        isFirstCondition = true;
        return this;
    }

    /**
     * 开始一个OR条件组
     * 用于创建 (condition1 OR condition2 OR...) 形式的查询条件
     */
    public QueryBuilder<T> beginOrGroup() {
        if (!isFirstCondition) {
            sqlBuilder.append(" OR ");
        }
        sqlBuilder.append("(");
        isFirstCondition = true;
        return this;
    }

    /**
     * 结束一个条件组
     */
    public QueryBuilder<T> endGroup() {
        sqlBuilder.append(")");
        isFirstCondition = false;
        return this;
    }

    /**
     * 添加嵌套查询条件
     * @param nestedBuilder 嵌套的查询构建器
     */
    public QueryBuilder<T> appendNestedCondition(QueryBuilder<T> nestedBuilder) {
        if (!nestedBuilder.sqlBuilder.getSql().isEmpty()) {
            sqlBuilder.append(nestedBuilder.sqlBuilder.getSql());
            sqlBuilder.getParams().addAll(nestedBuilder.sqlBuilder.getParams());
            isFirstCondition = false;
        }
        return this;
    }

    /**
     * 等于条件
     */
    public QueryBuilder<T> eq(String field, Object value) {
        addCondition(field + " = ?", value);
        return this;
    }

    /**
     * 不等于条件
     */
    public QueryBuilder<T> ne(String field, Object value) {
        addCondition(field + " != ?", value);
        return this;
    }

    /**
     * 大于条件
     */
    public QueryBuilder<T> gt(String field, Object value) {
        addCondition(field + " > ?", value);
        return this;
    }

    /**
     * 大于等于条件
     */
    public QueryBuilder<T> ge(String field, Object value) {
        addCondition(field + " >= ?", value);
        return this;
    }

    /**
     * 小于条件
     */
    public QueryBuilder<T> lt(String field, Object value) {
        addCondition(field + " < ?", value);
        return this;
    }

    /**
     * 小于等于条件
     */
    public QueryBuilder<T> le(String field, Object value) {
        addCondition(field + " <= ?", value);
        return this;
    }

    /**
     * LIKE条件
     */
    public QueryBuilder<T> like(String field, String value) {
        addCondition(field + " LIKE ?", "%" + value + "%");
        return this;
    }

    /**
     * IN条件
     */
    public QueryBuilder<T> in(String field, Object... values) {
        if (values.length > 0) {
            String placeholders = String.join(",", java.util.Collections.nCopies(values.length, "?"));
            addCondition(field + " IN (" + placeholders + ")", values);
        }
        return this;
    }

    /**
     * IN条件 - 支持List参数
     */
    public QueryBuilder<T> in(String field, List<?> values) {
        if (values != null && !values.isEmpty()) {
            String placeholders = String.join(",", java.util.Collections.nCopies(values.size(), "?"));
            addCondition(field + " IN (" + placeholders + ")", values.toArray());
        }
        return this;
    }

    /**
     * BETWEEN条件
     */
    public QueryBuilder<T> between(String field, Object start, Object end) {
        addCondition(field + " BETWEEN ? AND ?", start, end);
        return this;
    }

    /**
     * IS NULL条件
     */
    public QueryBuilder<T> isNull(String field) {
        addCondition(field + " IS NULL");
        return this;
    }

    /**
     * IS NOT NULL条件
     */
    public QueryBuilder<T> isNotNull(String field) {
        addCondition(field + " IS NOT NULL");
        return this;
    }

    /**
     * 排序
     */
    public QueryBuilder<T> orderBy(String field) {
        orderByFields.add(field + " ASC");
        return this;
    }

    /**
     * 降序排序
     */
    public QueryBuilder<T> orderByDesc(String field) {
        orderByFields.add(field + " DESC");
        return this;
    }
    /**
     * 降序排序
     */
    public QueryBuilder<T> orderByAsc(String field) {
        orderByFields.add(field + " Asc");
        return this;
    }

    /**
     * 限制数量
     */
    public QueryBuilder<T> limit(int count) {
        this.limitCount = count;
        return this;
    }

    /**
     * 偏移量
     */
    public QueryBuilder<T> offset(int count) {
        this.offsetCount = count;
        return this;
    }

    /**
     * 执行查询返回列表
     */
    public List<T> list() {
        SqlBuilder builder = buildSql();
        return dao.selectList(entityClass, builder);
    }

    /**
     * 执行查询返回单个结果
     */
    public T one() {
        SqlBuilder builder = buildSql();
        return dao.selectOne(entityClass, builder);
    }

    /**
     * 执行分页查询
     */
    public Page<T> page(int pageNum, int pageSize) {
        SqlBuilder builder = buildSql();
        return dao.selectPage(entityClass, builder, pageNum, pageSize);
    }

    /**
     * 统计数量
     */
    public long count() {
        SqlBuilder countBuilder = new SqlBuilder()
                .append("SELECT COUNT(*) FROM " + entityInfo.getTableName());
        
        if (!sqlBuilder.getSql().isEmpty()) {
            countBuilder.append(" WHERE ").append(sqlBuilder.getSql());
            countBuilder.getParams().addAll(sqlBuilder.getParams());
        }
        
        List<Long> result = dao.getSqlExecutor().executeQuery(
            countBuilder.getSql(), 
            countBuilder.getParams(), 
            Long.class
        );
        return result.isEmpty() ? 0L : result.get(0);
    }

    private void addCondition(String condition, Object... params) {
        if (!isFirstCondition) {
            sqlBuilder.append(" AND ");
        } else {
            isFirstCondition = false;
        }
        sqlBuilder.append(condition);
        for (Object param : params) {
            sqlBuilder.addParam(param);
        }
    }

    private SqlBuilder buildSql() {
        SqlBuilder builder = new SqlBuilder();
        
        // SELECT子句
        builder.append("SELECT ");
        if (distinct) {
            builder.append("DISTINCT ");
        }
        
        if (selectFields.isEmpty()) {
            builder.append("*");
        } else {
            builder.append(String.join(", ", selectFields));
        }
        
        // FROM子句
        builder.append(" FROM " + entityInfo.getTableName());
        
        // WHERE子句
        if (!sqlBuilder.getSql().isEmpty()) {
            builder.append(" WHERE ").append(sqlBuilder.getSql());
            builder.getParams().addAll(sqlBuilder.getParams());
        }
        
        // ORDER BY子句
        if (!orderByFields.isEmpty()) {
            builder.append(" ORDER BY ").append(String.join(", ", orderByFields));
        }
        
        // LIMIT和OFFSET子句
        if (limitCount != null) {
            builder.append(" LIMIT ?");
            builder.addParam(limitCount);
            
            if (offsetCount != null) {
                builder.append(" OFFSET ?");
                builder.addParam(offsetCount);
            }
        }
        
        return builder;
    }

    /**
     * 构建SQL - 供外部调用
     */
    public SqlBuilder build() {
        return buildSql();
    }

    /**
     * 构建统计SQL - 供外部调用
     */
    public SqlBuilder buildCount() {
        SqlBuilder countBuilder = new SqlBuilder()
                .append("SELECT COUNT(*) FROM " + entityInfo.getTableName());
        
        if (!sqlBuilder.getSql().isEmpty()) {
            countBuilder.append(" WHERE ").append(sqlBuilder.getSql());
            countBuilder.getParams().addAll(sqlBuilder.getParams());
        }
        
        return countBuilder;
    }
}