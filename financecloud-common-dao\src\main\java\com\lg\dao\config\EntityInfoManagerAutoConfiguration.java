package com.lg.dao.config;

import com.lg.dao.core.EntityInfoManager;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * EntityInfoManager 自动配置
 * 统一管理实体信息，避免重复创建和缓存不一致
 */
@Slf4j
@Configuration
@ConditionalOnClass(EntityInfoManager.class)
public class EntityInfoManagerAutoConfiguration {

    @Autowired(required = false)
    private UnifiedCacheManager unifiedCacheManager;

    /**
     * 配置 EntityInfoManager Bean
     */
    @Bean
    public EntityInfoManager entityInfoManager() {
        // 初始化缓存管理器
        EntityInfoManager.initializeCacheManager(unifiedCacheManager);

        EntityInfoManager manager = EntityInfoManager.getInstance();
        log.info("EntityInfoManager 配置完成，统一缓存管理器: {}",
                unifiedCacheManager != null ? "已启用" : "未启用");
        return manager;
    }

    /**
     * 实体信息预热配置
     * 在应用启动完成后预热常用实体信息
     */
    @Bean
    @ConditionalOnProperty(name = "light.orm.entity-info.preload.enable", havingValue = "true", matchIfMissing = false)
    @Order(1000) // 确保在其他组件初始化后执行
    public ApplicationListener<ApplicationReadyEvent> entityInfoPreloader() {
        return new ApplicationListener<ApplicationReadyEvent>() {
            @Override
            public void onApplicationEvent(ApplicationReadyEvent event) {
                try {
                    EntityInfoManager manager = EntityInfoManager.getInstance();

                    // 这里可以配置需要预热的实体类
                    // 实际使用时可以通过配置文件指定
                    log.info("开始预热实体信息缓存...");

                    // 示例：预热一些常用实体（实际项目中应该通过配置指定）
                    // manager.preloadEntityInfo(User.class, Order.class, Product.class);

                    log.info("实体信息缓存预热完成");
                } catch (Exception e) {
                    log.warn("实体信息缓存预热失败", e);
                }
            }
        };
    }

    /**
     * 缓存监控配置
     * 定期输出缓存统计信息
     */
    @Bean
    @ConditionalOnProperty(name = "light.orm.entity-info.monitor.enable", havingValue = "true", matchIfMissing = false)
    public EntityInfoCacheMonitor entityInfoCacheMonitor() {
        return new EntityInfoCacheMonitor();
    }

    /**
     * 实体信息缓存监控器
     */
    public static class EntityInfoCacheMonitor {
        
        @Autowired(required = false)
        private EntityInfoManager entityInfoManager;
        
        /**
         * 输出缓存统计信息
         */
        public void printCacheStats() {
            if (entityInfoManager != null) {
                String stats = entityInfoManager.getCacheStats();
                log.info("EntityInfo 缓存统计:\n{}", stats);
            }
        }
        
        /**
         * 清除所有缓存
         */
        public void clearAllCache() {
            if (entityInfoManager != null) {
                entityInfoManager.evictAllEntityInfo();
                log.info("已清除所有 EntityInfo 缓存");
            }
        }
        
        /**
         * 清除指定实体缓存
         */
        public void clearEntityCache(Class<?> entityClass) {
            if (entityInfoManager != null && entityClass != null) {
                entityInfoManager.evictEntityInfo(entityClass);
                log.info("已清除实体缓存: {}", entityClass.getName());
            }
        }
    }
}
