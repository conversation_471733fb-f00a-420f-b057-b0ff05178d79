package com.lg.dao.core.filter;

import java.util.List;

/**
 * SQL过滤器接口
 *
 * <AUTHOR>
 */
public interface SqlFilter {
    /**
     * 应用过滤条件
     *
     * @param sql 原始SQL
     * @param filterConfig 过滤条件配置
     * @return 应用过滤条件后的SQL
     */
    String apply(String sql, FilterConfig filterConfig);
    
    /**
     * 应用过滤条件和数据权限
     *
     * @param sql 原始SQL
     * @param filterConfig 过滤条件配置
     * @param tableCode 表编码
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 应用过滤条件和数据权限后的SQL
     */
    String apply(String sql, FilterConfig filterConfig, String tableCode, String userId, List<String> roleIds);
    
    /**
     * 应用数据权限
     *
     * @param sql 原始SQL
     * @param tableCode 表编码
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 应用数据权限后的SQL
     */
    String applyDataPermission(String sql, String tableCode, String userId, List<String> roleIds);
} 