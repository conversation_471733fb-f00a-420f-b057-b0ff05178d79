package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
@Getter
@AllArgsConstructor
public enum AccountingSystem {


    KXQ("1","会小企","利润表"),
    MF("2","民非","业务活动表"),
    KQ("3","会企","利润表"),
    CK("4","村会","收益及收益分配表"),

    ;


    /**
     * 描述
     */
    private String code;
    private String name;
    private String title02;


    private static final Map<String, AccountingSystem> lookup = new HashMap<String, AccountingSystem>();


    static {
        for (AccountingSystem s : EnumSet.allOf(AccountingSystem.class)) {
            lookup.put(s.getCode(), s);
        }
    }

    public static AccountingSystem lookup(String code) {
        return lookup.get(code);
    }

}
