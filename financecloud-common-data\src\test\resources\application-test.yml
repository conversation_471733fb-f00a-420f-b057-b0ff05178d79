spring:
  redis:
    enabled: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=MySQL
    username: sa
    password: 
  h2:
    console:
      enabled: true
#  sql:
#    init:
#      mode: always
#      schema-locations: classpath:schema.sql

# LightORM配置
light:
  orm:
    sql-print:
      enable: true
      show-params: true
      format: true
      show-execute-time: true
    tenant:
      enable: true
      mode: COLUMN
      tenant-field: tenant_id
      schema-prefix: lg_
      ignore-tables:
        - sys_config
        - sys_dict
    sequence:
      enable: true
      type: DB
      db:
        table-name: sys_sequence
        step: 1000
        step-start: 0
        retry-times: 100
      redis:
        host: localhost
        port: 6379
        step: 1000
        step-start: 0
      snowflake:
        datacenter-id: 0
        worker-id: 0
      default-config:
        prefix: ""
        suffix-length: 4
        increment-value: 1
        strategy: NEVER

logging:
  level:
    com.lg.dao: DEBUG
    org.springframework.jdbc.core: DEBUG
