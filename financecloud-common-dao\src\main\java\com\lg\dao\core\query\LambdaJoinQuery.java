package com.lg.dao.core.query;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.core.condition.ConditionStrategy;
import com.lg.dao.core.condition.ConditionStrategyUtils;
import com.lg.dao.core.func.LFunction;
import com.lg.dao.core.sql.JoinType;
import com.lg.dao.core.sql.SqlBuilder;
import com.lg.dao.core.util.LambdaUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Lambda风格的JOIN查询构建器
 * 提供类型安全的多表连接查询
 */
public class LambdaJoinQuery<T> {
    private final GenericDao<T, ?> baseDao;
    private final Class<T> entityClass;
    private final JoinQueryBuilder joinQueryBuilder;
    
    // 存储实体类与表别名的映射关系
    private final Map<Class<?>, String> tableAliasMap = new HashMap<>();
    // 存储实体类与其元数据信息的映射关系
    private final Map<Class<?>, EntityInfo> entityInfoMap = new HashMap<>();

    /**
     * 构造函数
     * @param baseDao 数据访问对象
     * @param entityClass 主查询实体类
     */
    public LambdaJoinQuery(GenericDao<T, ?> baseDao, Class<T> entityClass) {
        this.baseDao = baseDao;
        this.entityClass = entityClass;
        this.joinQueryBuilder = new JoinQueryBuilder(baseDao);
        
        // 获取主表的实体信息
        EntityInfo entityInfo = baseDao.getEntityInfo(entityClass);
        entityInfoMap.put(entityClass, entityInfo);
        
        // 初始化主表，使用类名小写作为别名
        String alias = entityClass.getSimpleName().toLowerCase();
        tableAliasMap.put(entityClass, alias);
        
        // 设置FROM子句
        if (entityInfo != null) {
            joinQueryBuilder.from(entityInfo.getTableName() + " " + alias);
        } else {
            joinQueryBuilder.from(entityClass.getSimpleName() + " " + alias);
        }
    }

    /**
     * 设置查询字段，支持从不同实体类中选择字段
     * 使用方式：select(Order::getId, User::getUsername)
     * 注意：当实体是静态内部类时可能无法使用此方法
     */
    @SafeVarargs
    public final <X> LambdaJoinQuery<T> select(LFunction<?, ?>... columns) {
        if (columns != null && columns.length > 0) {
            for (LFunction<?, ?> column : columns) {
                String columnName = getGenericColumnName(column);
                if (columnName != null) {
                    joinQueryBuilder.select(columnName);
                }
            }
        }
        return this;
    }
    
    /**
     * 设置查询字段（字符串方式）
     */
    public LambdaJoinQuery<T> select(String... columns) {
        if (columns != null && columns.length > 0) {
            joinQueryBuilder.select(columns);
        }
        return this;
    }
    
    /**
     * 通用方法：获取任意实体类的列名
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private <E, R> String getGenericColumnName(LFunction<?, ?> column) {
        // 获取Lambda表达式对应的实体类
        Class<?> entityClass = LambdaUtils.getEntityClass(column);
        if (entityClass == null) {
            return null;
        }
        
        // 获取字段名
        String fieldName = LambdaUtils.getFieldName((LFunction) column);
        if (fieldName == null) {
            return null;
        }
        
        // 获取表别名
        String alias = tableAliasMap.get(entityClass);
        if (alias == null) {
            // 如果没有找到别名，尝试使用类名小写作为别名
            alias = entityClass.getSimpleName().toLowerCase();
            tableAliasMap.put(entityClass, alias);
        }
        
        // 获取列名
        String columnName = getColumnNameByFieldName(entityClass, fieldName);
        
        return alias + "." + columnName;
    }
    
    /**
     * 使用指定别名的实体类字段作为查询字段
     */
    public <E, R> LambdaJoinQuery<T> select(Class<E> entityClass, String alias, LFunction<E, R> column) {
        tableAliasMap.putIfAbsent(entityClass, alias);
        joinQueryBuilder.select(getColumnName(entityClass, alias, column));
        return this;
    }
    
    /**
     * 设置FROM表（带别名）
     */
    public <E> LambdaJoinQuery<T> from(Class<E> entityClass, String alias) {
        EntityInfo entityInfo = getEntityInfo(entityClass);
        tableAliasMap.put(entityClass, alias);
        
        if (entityInfo != null) {
            joinQueryBuilder.from(entityInfo.getTableName() + " " + alias);
        } else {
            joinQueryBuilder.from(entityClass.getSimpleName() + " " + alias);
        }
        return this;
    }

    /**
     * 添加INNER JOIN
     */
    public LambdaJoinQuery<T> innerJoin(String table) {
        joinQueryBuilder.innerJoin(table);
        return this;
    }
    
    /**
     * 添加INNER JOIN（Lambda方式）
     */
    public <E> LambdaJoinQuery<T> innerJoin(Class<E> joinClass, String alias) {
        EntityInfo entityInfo = getEntityInfo(joinClass);
        tableAliasMap.put(joinClass, alias);
        
        if (entityInfo != null) {
            joinQueryBuilder.innerJoin(entityInfo.getTableName() + " " + alias);
        } else {
            joinQueryBuilder.innerJoin(joinClass.getSimpleName() + " " + alias);
        }
        return this;
    }

    /**
     * 添加LEFT JOIN
     */
    public LambdaJoinQuery<T> leftJoin(String table) {
        joinQueryBuilder.leftJoin(table);
        return this;
    }
    
    /**
     * 添加LEFT JOIN（Lambda方式）
     */
    public <E> LambdaJoinQuery<T> leftJoin(Class<E> joinClass, String alias) {
        EntityInfo entityInfo = getEntityInfo(joinClass);
        tableAliasMap.put(joinClass, alias);
        
        if (entityInfo != null) {
            joinQueryBuilder.leftJoin(entityInfo.getTableName() + " " + alias);
        } else {
            joinQueryBuilder.leftJoin(joinClass.getSimpleName() + " " + alias);
        }
        return this;
    }

    /**
     * 添加RIGHT JOIN
     */
    public LambdaJoinQuery<T> rightJoin(String table) {
        joinQueryBuilder.rightJoin(table);
        return this;
    }
    
    /**
     * 添加RIGHT JOIN（Lambda方式）
     */
    public <E> LambdaJoinQuery<T> rightJoin(Class<E> joinClass, String alias) {
        EntityInfo entityInfo = getEntityInfo(joinClass);
        tableAliasMap.put(joinClass, alias);
        
        if (entityInfo != null) {
            joinQueryBuilder.rightJoin(entityInfo.getTableName() + " " + alias);
        } else {
            joinQueryBuilder.rightJoin(joinClass.getSimpleName() + " " + alias);
        }
        return this;
    }

    /**
     * 添加JOIN ON条件
     */
    public LambdaJoinQuery<T> on(String condition) {
        joinQueryBuilder.on(condition);
        return this;
    }
    
    /**
     * 添加JOIN ON条件（Lambda方式）
     */
    public <E1, E2, R1, R2> LambdaJoinQuery<T> on(Class<E1> leftClass, LFunction<E1, R1> leftColumn, 
                                                  Class<E2> rightClass, LFunction<E2, R2> rightColumn) {
        String leftAlias = tableAliasMap.get(leftClass);
        String rightAlias = tableAliasMap.get(rightClass);
        
        if (leftAlias == null || rightAlias == null) {
            throw new IllegalArgumentException("实体类未注册表别名，请先调用from/join方法设置表别名");
        }
        
        String leftColumnName = getColumnNameWithoutAlias(leftClass, leftColumn);
        String rightColumnName = getColumnNameWithoutAlias(rightClass, rightColumn);
        
        joinQueryBuilder.on(leftAlias + "." + leftColumnName + " = " + rightAlias + "." + rightColumnName);
        return this;
    }

    /**
     * 开始WHERE子句
     */
    public LambdaJoinQuery<T> where() {
        joinQueryBuilder.where();
        return this;
    }

    /**
     * 添加AND条件
     */
    public LambdaJoinQuery<T> and() {
        joinQueryBuilder.and();
        return this;
    }

    /**
     * 添加OR条件
     */
    public LambdaJoinQuery<T> or() {
        joinQueryBuilder.or();
        return this;
    }

    /**
     * 添加等于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> eq(LFunction<T, R> column, Object value) {
        joinQueryBuilder.eq(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加等于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> eq(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.eq(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加不等于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> ne(LFunction<T, R> column, Object value) {
        joinQueryBuilder.ne(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加不等于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> ne(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.ne(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加大于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> gt(LFunction<T, R> column, Object value) {
        joinQueryBuilder.gt(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加大于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> gt(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.gt(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加大于等于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> ge(LFunction<T, R> column, Object value) {
        joinQueryBuilder.ge(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加大于等于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> ge(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.ge(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加小于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> lt(LFunction<T, R> column, Object value) {
        joinQueryBuilder.lt(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加小于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> lt(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.lt(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加小于等于条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> le(LFunction<T, R> column, Object value) {
        joinQueryBuilder.le(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加小于等于条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> le(Class<E> entityClass, LFunction<E, R> column, Object value) {
        joinQueryBuilder.le(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加LIKE条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> like(LFunction<T, R> column, String value) {
        joinQueryBuilder.like(getColumnName(entityClass, column), value);
        return this;
    }
    
    /**
     * 添加LIKE条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> like(Class<E> entityClass, LFunction<E, R> column, String value) {
        joinQueryBuilder.like(getColumnName(entityClass, column), value);
        return this;
    }

    /**
     * 添加IN条件（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> in(LFunction<T, R> column, List<?> values) {
        joinQueryBuilder.in(getColumnName(entityClass, column), values);
        return this;
    }

    /**
     * 添加IN条件（主表Lambda方式 - 可变参数）
     */
    @SafeVarargs
    public final <R> LambdaJoinQuery<T> in(LFunction<T, R> column, Object... values) {
        if (values != null && values.length > 0) {
            joinQueryBuilder.in(getColumnName(entityClass, column), Arrays.asList(values));
        }
        return this;
    }

    /**
     * 添加IN条件（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> in(Class<E> entityClass, LFunction<E, R> column, List<?> values) {
        joinQueryBuilder.in(getColumnName(entityClass, column), values);
        return this;
    }

    /**
     * 添加IN条件（指定实体Lambda方式 - 可变参数）
     */
    @SafeVarargs
    public final <E, R> LambdaJoinQuery<T> in(Class<E> entityClass, LFunction<E, R> column, Object... values) {
        if (values != null && values.length > 0) {
            joinQueryBuilder.in(getColumnName(entityClass, column), Arrays.asList(values));
        }
        return this;
    }

    /**
     * 添加排序（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> orderBy(LFunction<T, R> column) {
        joinQueryBuilder.orderBy(getColumnName(entityClass, column));
        return this;
    }
    
    /**
     * 添加排序（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> orderBy(Class<E> entityClass, LFunction<E, R> column) {
        joinQueryBuilder.orderBy(getColumnName(entityClass, column));
        return this;
    }

    /**
     * 降序排序
     */
    public LambdaJoinQuery<T> desc() {
        joinQueryBuilder.desc();
        return this;
    }

    /**
     * 升序排序
     */
    public LambdaJoinQuery<T> asc() {
        joinQueryBuilder.asc();
        return this;
    }

    /**
     * 添加分组（主表Lambda方式）
     */
    public <R> LambdaJoinQuery<T> groupBy(LFunction<T, R> column) {
        joinQueryBuilder.groupBy(getColumnName(entityClass, column));
        return this;
    }
    
    /**
     * 添加分组（指定实体Lambda方式）
     */
    public <E, R> LambdaJoinQuery<T> groupBy(Class<E> entityClass, LFunction<E, R> column) {
        joinQueryBuilder.groupBy(getColumnName(entityClass, column));
        return this;
    }

    /**
     * 开始HAVING子句
     */
    public LambdaJoinQuery<T> having() {
        joinQueryBuilder.having();
        return this;
    }

    /**
     * 设置LIMIT
     */
    public LambdaJoinQuery<T> limit(int limit) {
        joinQueryBuilder.limit(limit);
        return this;
    }

    /**
     * 设置OFFSET
     */
    public LambdaJoinQuery<T> offset(int offset) {
        joinQueryBuilder.offset(offset);
        return this;
    }

    /**
     * 执行查询返回Map列表
     */
    public List<Map<String, Object>> listMap() {
        return joinQueryBuilder.listMap();
    }

    /**
     * 执行查询返回单个Map结果
     */
    public Map<String, Object> oneMap() {
        return joinQueryBuilder.oneMap();
    }

    /**
     * 执行查询返回指定类型列表
     */
    public <E> List<E> list(Class<E> clazz) {
        // 修复泛型类型问题
        List<?> rawList = joinQueryBuilder.list(clazz);
        List<E> result = new ArrayList<>(rawList.size());
        
        // 安全地转换每个元素
        for (Object item : rawList) {
            if (clazz.isInstance(item)) {
                result.add(clazz.cast(item));
            }
        }
        
        return result;
    }

    /**
     * 执行查询返回指定类型单个结果
     */
    public <E> E one(Class<E> clazz) {
        // 修复泛型类型问题
        Object result = joinQueryBuilder.one(clazz);
        if (result == null) {
            return null;
        }
        
        if (clazz.isInstance(result)) {
            return clazz.cast(result);
        }
        
        return null;
    }

    /**
     * 执行查询返回分页结果
     */
    public <E> Page<E> page(Class<E> clazz, int pageNum, int pageSize) {
        // 创建分页对象
        Page<E> page = new Page<>(pageNum, pageSize);
        
        // 使用修复后的list方法获取所有数据，然后手动分页
        List<E> allRecords = list(clazz);
        
        // 计算总记录数
        long total = allRecords.size();
        page.setTotal(total);
        
        // 如果没有数据，直接返回空页
        if (total == 0 || pageSize <= 0) {
            page.setRecords(Collections.emptyList());
            return page;
        }
        
        // 计算开始和结束索引
        int fromIndex = (pageNum - 1) * pageSize;
        // 确保fromIndex在有效范围内
        if (fromIndex >= total) {
            // 如果起始索引超出范围，调整为最后一页
            fromIndex = (int) ((total - 1) / pageSize) * pageSize;
            // 如果仍然超出范围，设置为0
            if (fromIndex < 0) {
                fromIndex = 0;
            }
        }
        
        int toIndex = Math.min(fromIndex + pageSize, (int) total);
        
        // 提取当前页的记录
        List<E> pageRecords = fromIndex < toIndex ? 
                allRecords.subList(fromIndex, toIndex) : 
                Collections.emptyList();
        
        // 设置分页对象的记录
        page.setRecords(pageRecords);
        
        return page;
    }

    /**
     * 执行统计查询
     */
    public long count() {
        return joinQueryBuilder.count();
    }

    // ==================== 支持条件策略的方法 ====================

    /**
     * 根据条件策略动态添加等于条件
     */
    public <R> LambdaJoinQuery<T> eq(LFunction<T, R> column, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return eq(column, value);
        }
        return this;
    }
    
    /**
     * 根据条件策略动态添加等于条件（指定实体）
     */
    public <E, R> LambdaJoinQuery<T> eq(Class<E> entityClass, LFunction<E, R> column, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return eq(entityClass, column, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加不等于条件
     */
    public <R> LambdaJoinQuery<T> ne(LFunction<T, R> column, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return ne(column, value);
        }
        return this;
    }

    /**
     * 快捷方法：当值不为空时添加等于条件
     */
    public <R> LambdaJoinQuery<T> eqIfNotNull(LFunction<T, R> column, R value) {
        return eq(column, value, ConditionStrategy.IS_NOT_NULL);
    }
    
    /**
     * 快捷方法：当值不为空时添加等于条件（指定实体）
     */
    public <E, R> LambdaJoinQuery<T> eqIfNotNull(Class<E> entityClass, LFunction<E, R> column, R value) {
        return eq(entityClass, column, value, ConditionStrategy.IS_NOT_NULL);
    }

    /**
     * 快捷方法：当字符串不为空时添加LIKE条件
     */
    public <R> LambdaJoinQuery<T> likeIfNotEmpty(LFunction<T, R> column, String value) {
        if (value != null && !value.isEmpty()) {
            return like(column, value);
        }
        return this;
    }
    
    /**
     * 快捷方法：当字符串不为空时添加LIKE条件（指定实体）
     */
    public <E, R> LambdaJoinQuery<T> likeIfNotEmpty(Class<E> entityClass, LFunction<E, R> column, String value) {
        if (value != null && !value.isEmpty()) {
            return like(entityClass, column, value);
        }
        return this;
    }

    /**
     * 设置查询字段，支持特定实体类中选择字段
     * 使用方式：selectFields(Order.class, Order::getId, Order::getAmount)
     */
    @SafeVarargs
    public final <E, R> LambdaJoinQuery<T> selectFields(Class<E> entityClass, LFunction<E, R>... columns) {
        if (columns != null && columns.length > 0) {
            for (LFunction<E, R> column : columns) {
                String alias = tableAliasMap.get(entityClass);
                if (alias == null) {
                    // 这里不要自动设置别名，因为别名应该在from/join时明确指定
                    // 避免默认使用类名作为别名，导致与用户指定的别名不一致
                    throw new IllegalArgumentException("实体类 " + entityClass.getSimpleName() + 
                        " 未注册表别名，请先调用from/join方法设置表别名");
                }
                
                String fieldName = LambdaUtils.getFieldName(column);
                String columnName = getColumnNameByFieldName(entityClass, fieldName);
                joinQueryBuilder.select(alias + "." + columnName);
            }
        }
        return this;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取指定实体类的列名（包含表别名）
     */
    private <E, R> String getColumnName(Class<E> entityClass, LFunction<E, R> column) {
        String alias = tableAliasMap.get(entityClass);
        if (alias == null) {
            throw new IllegalArgumentException("实体类未注册表别名，请先调用from/join方法设置表别名");
        }
        return getColumnName(entityClass, alias, column);
    }
    
    /**
     * 获取指定实体类的列名（指定表别名）
     */
    private <E, R> String getColumnName(Class<E> entityClass, String alias, LFunction<E, R> column) {
        String fieldName = LambdaUtils.getFieldName(column);
        String columnName = getColumnNameByFieldName(entityClass, fieldName);
        return alias + "." + columnName;
    }
    
    /**
     * 获取指定实体类的列名（不包含表别名）
     */
    private <E, R> String getColumnNameWithoutAlias(Class<E> entityClass, LFunction<E, R> column) {
        String fieldName = LambdaUtils.getFieldName(column);
        return getColumnNameByFieldName(entityClass, fieldName);
    }
    
    /**
     * 根据字段名获取列名
     */
    private String getColumnNameByFieldName(Class<?> entityClass, String fieldName) {
        EntityInfo entityInfo = entityInfoMap.get(entityClass);
        if (entityInfo != null) {
            return entityInfo.getColumnName(fieldName);
        }
        return fieldName; // 如果没有实体信息，直接使用字段名作为列名
    }
    
    /**
     * 获取实体信息
     */
    private <E> EntityInfo getEntityInfo(Class<E> entityClass) {
        if (entityInfoMap.containsKey(entityClass)) {
            return entityInfoMap.get(entityClass);
        }
        
        EntityInfo entityInfo = baseDao.getEntityInfo(entityClass);
        entityInfoMap.put(entityClass, entityInfo);
        return entityInfo;
    }

    /**
     * 添加等于条件（字符串方式）
     */
    public LambdaJoinQuery<T> eq(String column, Object value) {
        joinQueryBuilder.eq(column, value);
        return this;
    }
}