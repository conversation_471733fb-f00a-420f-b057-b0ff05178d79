package com.github.stupdit1t.excel.annotation;

import java.lang.annotation.*;

/**
 * Excel转PDF导出注解
 * 用于配置PDF导出的相关参数
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelToPdf {

    /**
     * 页面方向
     * PORTRAIT: 纵向, LANDSCAPE: 横向
     */
    String orientation() default "PORTRAIT";

    /**
     * 页面大小
     * A4, A3, A5, LETTER, LEGAL
     */
    String pageSize() default "A4";

    /**
     * 字体大小
     */
    float fontSize() default 10f;

    /**
     * 页边距 - 上
     */
    float marginTop() default 36f;

    /**
     * 页边距 - 下
     */
    float marginBottom() default 36f;

    /**
     * 页边距 - 左
     */
    float marginLeft() default 36f;

    /**
     * 页边距 - 右
     */
    float marginRight() default 36f;

    /**
     * 是否显示网格线
     */
    boolean showGridLines() default true;

    /**
     * 表头背景色 (十六进制格式，如: 0xF0F0F0)
     */
    int headerBackgroundColor() default 0xF0F0F0;

    /**
     * 表头字体颜色 (十六进制格式，如: 0x000000)
     */
    int headerFontColor() default 0x000000;

    /**
     * 数据行字体颜色 (十六进制格式，如: 0x000000)
     */
    int dataFontColor() default 0x000000;

    /**
     * 是否启用中文字体支持
     */
    boolean enableChineseFont() default true;

    /**
     * 自定义字体路径
     */
    String customFontPath() default "";
}
