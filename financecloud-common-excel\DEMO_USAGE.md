# Excel组件增强功能演示

## 快速开始

### 1. 运行完整演示

直接运行主演示类：

```bash
# 进入项目目录
cd financecloud-common-excel

# 编译项目
mvn clean compile test-compile

# 运行演示程序
mvn exec:java -Dexec.mainClass="excel.export.ExcelEnhancementDemo" -Dexec.classpathScope="test"
```

或者在IDE中直接运行 `ExcelEnhancementDemo.main()` 方法。

### 2. 单独运行测试

```bash
# 运行所有测试
mvn test -Dtest=MasterDetailExportTest

# 运行特定测试方法
mvn test -Dtest=MasterDetailExportTest#testBasicExport
mvn test -Dtest=MasterDetailExportTest#testMasterDetailExport
mvn test -Dtest=MasterDetailExportTest#testTemplateReplace
mvn test -Dtest=MasterDetailExportTest#testPdfExport
```

## 功能演示

### 1. 基本导出功能

演示标准的Excel导出功能，生成包含订单信息的Excel文件。

**输出文件**: `src/test/java/excel/export/excel/basicExport.xlsx`

### 2. 主从表导出功能

演示主从表模式的导出功能，将订单主表和明细表数据合并导出。

**特点**:
- 主表数据和明细数据在同一个Sheet中
- 通过数据类型列区分主表和明细数据
- 支持复杂的数据结构展示

**输出文件**: `src/test/java/excel/export/excel/masterDetailExport.xlsx`

### 3. 模板替换功能

演示Excel模板变量替换和循环功能。

**特点**:
- 支持变量替换 `${variableName}`
- 支持循环数据 `${#foreach listName}...${/foreach}`
- 动态生成内容

**输出文件**: 
- 模板文件: `src/test/java/excel/export/excel/template.xlsx`
- 结果文件: `src/test/java/excel/export/excel/templateResult.xlsx`

### 4. PDF导出功能

演示将Excel数据直接导出为PDF格式。

**注意**: 此功能需要iText依赖，如果没有添加依赖会显示相应提示。

**输出文件**: `src/test/java/excel/export/excel/orderExport.pdf`

## 添加PDF支持

如果要启用PDF导出功能，请在pom.xml中添加以下依赖：

```xml
<!-- PDF导出支持 -->
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itextpdf</artifactId>
    <version>5.5.13.3</version>
</dependency>
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext-asian</artifactId>
    <version>5.2.0</version>
</dependency>
```

## 代码示例

### 基本导出

```java
ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsSheet(orders)
    .sheetName("订单列表")
    .opsHeader().simple()
        .texts("订单号", "客户名称", "订单日期", "订单金额", "订单状态")
        .done()
    .opsColumn()
        .fields("orderNo", "customerName", "orderDate", "totalAmount", "status")
        .done()
    .done()
    .export("basicExport.xlsx");
```

### 主从表导出

```java
ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsMasterDetailSheet(orders, OrderMaster::getOrderDetails)
    .sheetName("订单主从表")
    .opsHeader().simple()
        .texts("数据类型", "订单号", "客户名称", "商品编码", "商品名称", "数量")
        .done()
    .opsColumn()
        .field("dataType").map((val, row, style, rowIndex) -> {
            if (row instanceof OrderMaster) return "主表";
            else if (row instanceof OrderDetail) return "明细";
            else return "分隔";
        }).done()
        // ... 其他字段映射
        .done()
    .done()
    .export("masterDetailExport.xlsx");
```

### 模板替换

```java
ExcelHelper.opsReplace()
    .from("template.xlsx")
    .var("companyName", "测试公司")
    .var("reportDate", "2024-01-15")
    .loop("orders", orderMapList)
    .replaceTo("templateResult.xlsx");
```

### PDF导出

```java
PdfExportConfig pdfConfig = new PdfExportConfig()
    .setOrientation(PdfExportConfig.PageOrientation.LANDSCAPE)
    .setPageSize(PdfExportConfig.PageSize.A4)
    .setEnableChineseFont(true);

ExcelHelper.opsExport(PoiWorkbookType.XLSX)
    .opsSheet(orders)
    .opsHeader().simple().texts("订单号", "客户名称", "订单金额").done()
    .opsColumn().fields("orderNo", "customerName", "totalAmount").done()
    .done()
    .exportToPdf("orderExport.pdf", pdfConfig);
```

## 输出文件说明

运行演示后，会在 `src/test/java/excel/export/excel/` 目录下生成以下文件：

1. **basicExport.xlsx** - 基本导出示例
2. **masterDetailExport.xlsx** - 主从表导出示例
3. **template.xlsx** - 自动生成的模板文件
4. **templateResult.xlsx** - 模板替换结果
5. **orderExport.pdf** - PDF导出结果（需要iText依赖）

## 故障排除

### 1. 编译错误
- 确保Java版本为1.8+
- 运行 `mvn clean compile` 重新编译

### 2. 找不到类错误
- 确保所有依赖都已正确添加
- 运行 `mvn dependency:resolve` 检查依赖

### 3. PDF导出失败
- 检查是否添加了iText依赖
- 确保字体文件可用（中文字体支持）

### 4. 文件权限错误
- 确保输出目录有写权限
- 检查文件是否被其他程序占用

## 扩展开发

基于这些演示代码，您可以：

1. **自定义数据结构** - 修改OrderMaster和OrderDetail类
2. **扩展导出格式** - 添加更多字段和样式
3. **集成到Spring Boot** - 将代码集成到Web应用中
4. **添加数据验证** - 在导出前验证数据完整性
5. **性能优化** - 针对大数据量进行优化

## 技术支持

如有问题，请查看：
- [主从表和PDF导出详细文档](README-masterdetail.md)
- [循环功能使用说明](LOOP_USAGE.md)
- [导出功能详细说明](README-export.md)
