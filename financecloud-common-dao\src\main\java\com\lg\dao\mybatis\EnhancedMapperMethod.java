package com.lg.dao.mybatis;

import com.lg.dao.core.BaseDao;
import com.lg.dao.mybatis.annotation.Delete;
import com.lg.dao.mybatis.annotation.Insert;
import com.lg.dao.mybatis.annotation.Select;
import com.lg.dao.mybatis.annotation.Update;
import org.apache.ibatis.mapping.SqlCommandType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * 增强版 Mapper 方法执行器
 * 使用 MyBatis 原生解析器处理 XML 和动态 SQL
 */
public class EnhancedMapperMethod {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedMapperMethod.class);
    
    private final Class<?> mapperInterface;
    private final Method method;
    private final MybatisNativeXmlParser xmlParser;
    private final String statementId;
    
    public EnhancedMapperMethod(Class<?> mapperInterface, Method method, MybatisNativeXmlParser xmlParser) {
        this.mapperInterface = mapperInterface;
        this.method = method;
        this.xmlParser = xmlParser;
        this.statementId = mapperInterface.getName() + "." + method.getName();
    }
    
    /**
     * 执行方法
     */
    public Object execute(BaseDao baseDao, Object[] args) {
        try {
            // 准备参数对象
            Object parameterObject = prepareParameterObject(args);
            
            // 首先尝试从注解获取 SQL
            String annotationSql = getAnnotationSql();
            if (annotationSql != null) {
                return executeWithAnnotationSql(baseDao, annotationSql, parameterObject);
            }
            
            // 尝试从 XML 获取解析后的 SQL
            if (xmlParser != null && xmlParser.hasStatement(statementId)) {
                return executeWithXmlSql(baseDao, parameterObject);
            }
            
            throw new RuntimeException("未找到 SQL 定义: " + statementId);
            
        } catch (Exception e) {
            logger.error("执行方法失败: {}", statementId, e);
            throw new RuntimeException("执行方法失败: " + statementId, e);
        }
    }
    
    /**
     * 使用注解 SQL 执行
     */
    private Object executeWithAnnotationSql(BaseDao baseDao, String sql, Object parameterObject) {
        // 简单的参数处理（注解 SQL 通常比较简单）
        Object[] parameters = extractSimpleParameters(sql, parameterObject);
        SqlCommandType commandType = getAnnotationCommandType();
        
        return executeByCommandType(baseDao, sql, parameters, commandType);
    }
    
    /**
     * 使用 XML SQL 执行
     */
    private Object executeWithXmlSql(BaseDao baseDao, Object parameterObject) {
        // 使用 MyBatis 原生解析器获取解析后的 SQL 和参数
        ParsedSqlInfo sqlInfo = xmlParser.getParsedSql(statementId, parameterObject);
        if (sqlInfo == null) {
            throw new RuntimeException("无法解析 SQL: " + statementId);
        }
        
        return executeByCommandType(baseDao, sqlInfo.getSql(), sqlInfo.getParameters(), sqlInfo.getSqlCommandType());
    }
    
    /**
     * 根据命令类型执行
     */
    private Object executeByCommandType(BaseDao baseDao, String sql, Object[] parameters, SqlCommandType commandType) {
        switch (commandType) {
            case SELECT:
                return executeSelect(baseDao, sql, parameters);
            case INSERT:
                return executeInsert(baseDao, sql, parameters);
            case UPDATE:
                return executeUpdate(baseDao, sql, parameters);
            case DELETE:
                return executeDelete(baseDao, sql, parameters);
            default:
                throw new UnsupportedOperationException("不支持的命令类型: " + commandType);
        }
    }
    
    /**
     * 执行查询
     */
    private Object executeSelect(BaseDao baseDao, String sql, Object[] parameters) {
        Class<?> returnType = method.getReturnType();
        
        // 处理集合返回类型
        if (Collection.class.isAssignableFrom(returnType)) {
            Class<?> elementType = getCollectionElementType();
            return baseDao.selectList(sql, elementType, parameters);
        }
        // 处理单个对象返回类型
        else if (!isPrimitiveOrWrapper(returnType)) {
            return baseDao.selectOne(sql, returnType, parameters);
        }
        // 处理基本类型返回
        else {
            if (returnType == Integer.class || returnType == int.class) {
                return baseDao.queryInteger(sql, parameters);
            } else {
                return baseDao.selectOne(sql, returnType, parameters);
            }
        }
    }
    
    /**
     * 执行插入
     */
    private Object executeInsert(BaseDao baseDao, String sql, Object[] parameters) {
        List<Object> paramList = parameters != null ? Arrays.asList(parameters) : Collections.emptyList();
        int rows = baseDao.getSqlExecutor().executeUpdate(sql, paramList);
        return convertUpdateResult(rows);
    }
    
    /**
     * 执行更新
     */
    private Object executeUpdate(BaseDao baseDao, String sql, Object[] parameters) {
        List<Object> paramList = parameters != null ? Arrays.asList(parameters) : Collections.emptyList();
        int rows = baseDao.getSqlExecutor().executeUpdate(sql, paramList);
        return convertUpdateResult(rows);
    }
    
    /**
     * 执行删除
     */
    private Object executeDelete(BaseDao baseDao, String sql, Object[] parameters) {
        List<Object> paramList = parameters != null ? Arrays.asList(parameters) : Collections.emptyList();
        int rows = baseDao.getSqlExecutor().executeUpdate(sql, paramList);
        return convertUpdateResult(rows);
    }
    
    /**
     * 转换更新结果
     */
    private Object convertUpdateResult(int rows) {
        Class<?> returnType = method.getReturnType();
        if (returnType == Integer.class || returnType == int.class) {
            return rows;
        } else if (returnType == Boolean.class || returnType == boolean.class) {
            return rows > 0;
        } else if (returnType == Long.class || returnType == long.class) {
            return (long) rows;
        } else {
            return null;
        }
    }
    
    /**
     * 准备参数对象
     */
    private Object prepareParameterObject(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        } else if (args.length == 1) {
            return args[0];
        } else {
            // 多个参数时，使用 Map 封装
            Map<String, Object> params = new HashMap<>();
            for (int i = 0; i < args.length; i++) {
                params.put("param" + (i + 1), args[i]);
            }
            return params;
        }
    }
    
    /**
     * 提取简单参数（用于注解 SQL）
     */
    private Object[] extractSimpleParameters(String sql, Object parameterObject) {
        if (parameterObject == null) {
            return new Object[0];
        }

        if (parameterObject instanceof Object[]) {
            return (Object[]) parameterObject;
        } else if (parameterObject instanceof Collection) {
            return ((Collection<?>) parameterObject).toArray();
        } else {
            return new Object[]{parameterObject};
        }
    }
    
    /**
     * 获取注解 SQL
     */
    private String getAnnotationSql() {
        if (method.isAnnotationPresent(Select.class)) {
            return method.getAnnotation(Select.class).value();
        } else if (method.isAnnotationPresent(Insert.class)) {
            return method.getAnnotation(Insert.class).value();
        } else if (method.isAnnotationPresent(Update.class)) {
            return method.getAnnotation(Update.class).value();
        } else if (method.isAnnotationPresent(Delete.class)) {
            return method.getAnnotation(Delete.class).value();
        }
        return null;
    }
    
    /**
     * 获取注解命令类型
     */
    private SqlCommandType getAnnotationCommandType() {
        if (method.isAnnotationPresent(Select.class)) {
            return SqlCommandType.SELECT;
        } else if (method.isAnnotationPresent(Insert.class)) {
            return SqlCommandType.INSERT;
        } else if (method.isAnnotationPresent(Update.class)) {
            return SqlCommandType.UPDATE;
        } else if (method.isAnnotationPresent(Delete.class)) {
            return SqlCommandType.DELETE;
        }
        return SqlCommandType.UNKNOWN;
    }
    
    /**
     * 获取集合元素类型
     */
    private Class<?> getCollectionElementType() {
        Type genericReturnType = method.getGenericReturnType();
        if (genericReturnType instanceof ParameterizedType) {
            Type[] typeArgs = ((ParameterizedType) genericReturnType).getActualTypeArguments();
            if (typeArgs != null && typeArgs.length > 0) {
                if (typeArgs[0] instanceof Class) {
                    return (Class<?>) typeArgs[0];
                }
            }
        }
        return Object.class;
    }
    
    /**
     * 判断是否为基本类型或包装类
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == String.class;
    }
}
