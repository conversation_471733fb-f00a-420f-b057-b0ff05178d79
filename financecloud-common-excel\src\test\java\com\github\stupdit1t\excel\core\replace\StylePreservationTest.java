package com.github.stupdit1t.excel.core.replace;

import com.github.stupdit1t.excel.core.ExcelHelper;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 样式保持测试
 */
public class StylePreservationTest {
    
    @Test
    public void testStylePreservation() throws IOException {
        // 先生成样式测试模板
        StyleTestGenerator.generateStyleTestTemplate();
        
        // 准备测试数据
        List<Employee> employees = new ArrayList<>();
        employees.add(new Employee("E001", "张三", "开发部", 8000.0));
        employees.add(new Employee("E002", "李四", "产品部", 7000.0));
        employees.add(new Employee("E003", "王五", "测试部", 6500.0));
        
        // 执行替换
        try (FileInputStream inputStream = new FileInputStream("style_test_template.xlsx");
             FileOutputStream outputStream = new FileOutputStream("style_test_result.xlsx")) {
            
            ExcelHelper.opsReplace()
                .from(inputStream)
                .var("companyName", "科技有限公司")
                .var("reportDate", "2024-01-15")
                .loop("employees", employees)
                .var("totalCount", employees.size())
                .replaceTo(outputStream);
        }
        
        System.out.println("样式测试完成！");
        System.out.println("原始模板: style_test_template.xlsx");
        System.out.println("替换结果: style_test_result.xlsx");
        System.out.println("请对比两个文件的样式是否一致");
    }
    
    /**
     * 员工实体类
     */
    public static class Employee {
        private String empNo;
        private String name;
        private String department;
        private Double salary;
        
        public Employee(String empNo, String name, String department, Double salary) {
            this.empNo = empNo;
            this.name = name;
            this.department = department;
            this.salary = salary;
        }
        
        // Getters
        public String getEmpNo() { return empNo; }
        public String getName() { return name; }
        public String getDepartment() { return department; }
        public Double getSalary() { return salary; }
        
        // Setters
        public void setEmpNo(String empNo) { this.empNo = empNo; }
        public void setName(String name) { this.name = name; }
        public void setDepartment(String department) { this.department = department; }
        public void setSalary(Double salary) { this.salary = salary; }
    }
}