package com.lg.dao.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * SQL打印配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.orm.sql-print")
public class SqlPrintProperties {
    
    /**
     * 是否启用SQL打印
     */
    private boolean enable = true;

    /**
     * 是否显示参数
     */
    private boolean showParams = true;

    /**
     * 是否格式化SQL
     */
    private boolean format = true;

    /**
     * 是否显示执行时间
     */
    private boolean showExecuteTime = true;

    /**
     * 慢SQL阈值（毫秒）
     */
    private long slowSqlThreshold = 1000;
} 