/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 附件管理
 *
 * <AUTHOR>
 * @date 2021-07-16 11:21:45
 */
@Data
@TableName("base_attach_file_group")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "附件管理")
public class BaseAttachFileGroup extends Model<BaseAttachFileGroup> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 附件用途
     */
    @ApiModelProperty(value = "附件用途")
    private Integer attachType;
    /**
     * 附件用途
     */
    @ApiModelProperty(value = "附件用途")
    @TableField(exist = false)
    private String attachTypeCode;
    /**
     * 附件数量
     */
    @ApiModelProperty(value = "附件数量")
    private Integer attachLimit;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Long createBy;
    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "0未启用 1使用中")
    private String useFlag;
    /**
     *
     */
    @ApiModelProperty(value = "", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "附件组附件明细")
    @TableField(exist = false)
    private List<BaseAttachFile> files;
    @ApiModelProperty(value = "附件组附件明细")
    @TableField(exist = false)
    private List<BaseAttachFile> pdfFiles;

    @ApiModelProperty(value = "附件组业务目录")
    @TableField(exist = false)
    private String storageDir;

    @ApiModelProperty(value = "公有or私有")
    @TableField(exist = false)
    private String isPub;
    /**
     * 用的到这个字段
     */
    @TableField(exist = false)
    private String fileName;

}
