package com.lg.financecloud.common.redis.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 任务恢复使用示例
 * 展示如何使用增强后的MQ功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskRecoveryExample {
    
    @Autowired(required = false)
    private TaskRecoveryService taskRecoveryService;
    
    /**
     * 示例：提交任务
     */
    public void submitTaskExample() {
        // 创建任务
        BaseTask task = new BaseTask();
        task.setQueueCode("example_task");
        task.setJobDesc("示例任务");
        task.setModuleName("example_module");
        
        // 创建监听器
        RedisAmqListener<BaseTask> listener = new RedisAmqListener<BaseTask>() {
            @Override
            protected void execute(BaseTask task) {
                log.info("执行示例任务: {}", task.getTaskIdentity());
                // 业务逻辑
                processTask(task);
            }
        };
        
        // 提交即时任务
        String taskId = RedisJob.submitJob(task, listener);
        log.info("提交即时任务成功: {}", taskId);
        
        // 提交延时任务
        String delayTaskId = RedisJob.submitJob(task, listener, Duration.ofMinutes(5));
        log.info("提交延时任务成功: {}", delayTaskId);
        
        // 提交定时任务
        String scheduleTaskId = RedisJob.submitScheduleJob(task, listener, "0 0 12 * * ?");
        log.info("提交定时任务成功: {}", scheduleTaskId);
    }
    
    /**
     * 示例：重试任务
     */
    public void retryTaskExample(String taskId) {
        // 创建监听器
        RedisAmqListener<BaseTask> listener = new RedisAmqListener<BaseTask>() {
            @Override
            protected void execute(BaseTask task) {
                log.info("重试执行任务: {}", task.getTaskIdentity());
                processTask(task);
            }
        };
        
        // 重试任务
        String newTaskId = RedisJob.retryTask(taskId, listener);
        if (newTaskId != null) {
            log.info("任务重试成功: {}", newTaskId);
        } else {
            log.warn("任务重试失败: {}", taskId);
        }
    }
    
    /**
     * 示例：查询任务状态
     */
    public void checkTaskStatusExample(String taskId) {
        String status = RedisJob.getTaskStatus(taskId);
        log.info("任务状态: {} -> {}", taskId, status);
        
        switch (status) {
            case BaseTask.TASK_STATE_WAIT:
                log.info("任务等待中");
                break;
            case BaseTask.TASK_STATE_PROCESSING:
                log.info("任务处理中");
                break;
            case BaseTask.TASK_STATE_SUCCESS:
                log.info("任务执行成功");
                break;
            case BaseTask.TASK_STATE_FAIL:
                log.info("任务执行失败");
                break;
            default:
                log.info("未知任务状态");
        }
    }
    
    /**
     * 示例：手动触发恢复
     */
    public void manualRecoveryExample() {
        if (taskRecoveryService != null) {
            log.info("手动触发任务恢复");
            taskRecoveryService.manualRecovery();
        } else {
            log.warn("任务恢复服务未启用");
        }
    }
    
    /**
     * 示例：取消任务
     */
    public void cancelTaskExample(String taskId) {
        boolean success = RedisJob.cancelTask(taskId);
        if (success) {
            log.info("任务取消成功: {}", taskId);
        } else {
            log.warn("任务取消失败: {}", taskId);
        }
    }
    
    /**
     * 模拟业务处理
     */
    private void processTask(BaseTask task) {
        try {
            // 模拟业务处理时间
            Thread.sleep(1000);
            log.info("任务处理完成: {}", task.getTaskIdentity());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("任务处理被中断", e);
        }
    }
}
