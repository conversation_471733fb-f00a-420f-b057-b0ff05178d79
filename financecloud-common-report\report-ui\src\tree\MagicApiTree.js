/**
 * MagicApi数据源树组件
 * Created by UReport Team on 2024-01-10.
 */
import uuid from 'node-uuid';
import {alert, confirm} from '../MsgBox.js';
import MagicApiDatasetDialog from '../dialog/MagicApiDatasetDialog.js';
import BaseTree from './BaseTree.js';

export default class MagicApiTree extends BaseTree {
    constructor(container, datasources, ds, context) {
        super();
        this.type = 'magicapi';
        this.datasources = datasources;
        this.datasets = ds.datasets || [];
        this.context = context;
        this.id = uuid.v1();
        this.name = ds.name;
        this.baseUrl = ds.baseUrl;
        this.apiPath = ds.apiPath;
        this.headers = ds.headers || {};
        this.init(container);
    }
    
    init(container) {
        this.treeContainer = $('<div class="tree" style="margin-left: 10px"></div>');
        container.append(this.treeContainer);
        this.ul = $('<ul style="padding-left: 20px;"></ul>');
        this.treeContainer.append(this.ul);
        this.buildDatasource();
        
        for (let dataset of this.datasets) {
            const fieldsUL = this.addDataset(dataset);
            this.buildFields(dataset, fieldsUL);
        }
    }
    
    buildDatasource() {
        this.datasourceLi = $('<li></li>');
        const rootSpan = $(`
            <span id="${this.id}">
                <i class='ureport ureport-minus' style='margin-right:2px'></i>
                <i class="ureport ureport-api"></i> 
                <a href='###' class="ds_name">${this.name}</a>
            </span>
        `);
        
        this.datasourceLi.append(rootSpan);
        this.ul.append(this.datasourceLi);
        this.attachEvent(rootSpan, this.datasourceLi);
        this.datasetUL = $('<ul style="margin-left: -16px;"></ul>');
        this.datasourceLi.append(this.datasetUL);
        
        this.attachContextMenu();
    }
    
    attachContextMenu() {
        const _this = this;
        const datasetDialog = new MagicApiDatasetDialog(this, {parameters: []});
        
        if (window.isAdmin) {
            $.contextMenu({
                selector: '#' + this.id,
                items: {
                    "add": {name: "添加数据集", icon: "add"},
                    "edit": {name: "编辑", icon: "edit"},
                    "delete": {name: "删除", icon: "delete"}
                },
                callback: function(key, options) {
                    if (key === 'add') {
                        datasetDialog.show(function(name, apiMethod, httpMethod, fields, parameters) {
                            const dataset = {name, apiMethod, httpMethod, fields: fields || [], parameters: parameters || []};
                            _this.datasets.push(dataset);
                            const fieldsUL = _this.addDataset(dataset);
                            _this.buildFields(dataset, fieldsUL);
                        });
                    } else if (key === 'edit') {
                        _this.showEditDialog();
                    } else if (key === 'delete') {
                        _this.confirmDelete();
                    }
                }
            });
        }
    }
    
    showEditDialog() {
        // 这里需要引入MagicApiDialog，但为了避免循环依赖，我们通过事件通知父组件
        const event = new CustomEvent('editMagicApiDatasource', {
            detail: {
                datasource: this,
                data: {
                    name: this.name,
                    baseUrl: this.baseUrl,
                    apiPath: this.apiPath,
                    headers: this.headers
                }
            }
        });
        document.dispatchEvent(event);
    }
    
    confirmDelete() {
        const _this = this;
        confirm(`确定要删除数据源[${this.name}]吗？`, function() {
            const datasources = _this.datasources;
            const index = datasources.indexOf(_this);
            datasources.splice(index, 1);
            _this.treeContainer.remove();
        });
    }
    
    addDataset(dataset) {
        const li = $('<li></li>');
        const spanId = uuid.v1();
        const span = $(`
            <span id="${spanId}">
                <i class='ureport ureport-minus' style='margin-right:2px'></i> 
                <i class="ureport ureport-sqlds"></i> 
                <a href='###' class="dataset_name">${dataset.name}</a>
            </span>
        `);
        
        li.append(span);
        this.datasetUL.append(li);
        this.attachEvent(span, li);
        
        const fieldsUL = $('<ul style="padding-left: 22px;"></ul>');
        li.append(fieldsUL);
        
        const _this = this;
        const datasetDialog = new MagicApiDatasetDialog(this, dataset);
        
        if (window.isAdmin) {
            $.contextMenu({
                selector: '#' + spanId,
                items: {
                    "edit": {name: "编辑数据集", icon: "edit"},
                    "delete": {name: "删除数据集", icon: "delete"}
                },
                callback: function(key, options) {
                    if (key === 'edit') {
                        datasetDialog.show(function(name, apiMethod, httpMethod, fields, parameters) {
                            dataset.name = name;
                            dataset.apiMethod = apiMethod;
                            dataset.httpMethod = httpMethod;
                            dataset.fields = fields || [];
                            dataset.parameters = parameters || [];
                            span.find('.dataset_name').html(name);
                            
                            // 重新构建字段
                            fieldsUL.empty();
                            _this.buildFields(dataset, fieldsUL);
                        }, dataset);
                    } else if (key === 'delete') {
                        confirm(`确定要删除数据集[${dataset.name}]吗？`, function() {
                            const index = _this.datasets.indexOf(dataset);
                            _this.datasets.splice(index, 1);
                            li.remove();
                        });
                    }
                }
            });
        }
        
        return fieldsUL;
    }
    
    buildFields(dataset, fieldsUL) {
        if (!dataset.fields) return;
        
        for (let field of dataset.fields) {
            this.addField(field, fieldsUL, dataset);
        }
    }
    
    addField(field, fieldsUL, dataset) {
        const fieldLi = $('<li></li>');
        const fieldSpanId = uuid.v1();
        const fieldSpan = $(`
            <span id="${fieldSpanId}">
                <i class="ureport ureport-field"></i> 
                <a href="###" class="field_name">${field.displayName || field.name}</a>
            </span>
        `);
        
        fieldLi.append(fieldSpan);
        fieldsUL.append(fieldLi);
        
        const _this = this;
        
        // 支持拖拽到单元格
        fieldSpan.draggable({
            helper: 'clone',
            start: function(event, ui) {
                _this.context.dragData = {
                    type: 'field',
                    datasource: _this.name,
                    dataset: dataset.name,
                    field: field.name,
                    displayName: field.displayName || field.name
                };
            }
        });

        // 支持点击添加到单元格
        fieldSpan.click(function() {
            _this._buildClickEvent(dataset, field, _this.context);
        });
    }
    
    attachEvent(span, li) {
        span.click(function (e) {
            let $liChildren = li.find(' > ul > li');
            if ($liChildren.is(":visible")) {
                $liChildren.hide('fast');
                span.children('i:first').addClass('ureport-plus').removeClass('ureport-minus');
            } else {
                $liChildren.show('fast');
                span.children('i:first').addClass('ureport-minus').removeClass('ureport-plus');
            }
            e.stopPropagation();
        });
    }

    toJSON() {
        return {
            type: this.type,
            name: this.name,
            baseUrl: this.baseUrl,
            apiPath: this.apiPath,
            headers: this.headers,
            datasets: this.datasets
        };
    }
}
