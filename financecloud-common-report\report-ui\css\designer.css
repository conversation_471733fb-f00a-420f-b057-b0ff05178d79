.handsontable tr{
    background: transparent;
}
.handsontable td, .handsontable th{
    background: transparent;
}
.handsontable table.htCore{
    border-collapse:collapse
}
.ud-toolbar{
    width: 100%;
    background: #f8f8f8;
}
.ud-property-panel{
    width: 400px;
    border: solid 1px #999;
    border-radius: 5px;
    top: 35px;
    right: 10px;
    z-index: 105;
    position: absolute;
    background: #ffffff;
    box-shadow: 5px 5px 5px #888888;
}

/* iframe环境下的属性面板优化 */
body.iframe-mode .ud-property-panel {
    right: 5px;
    top: 40px;
    width: 380px;
}

/* 检测iframe环境 */
@media screen and (max-width: 1400px) {
    .ud-property-panel {
        right: 5px;
        width: 350px;
    }
}
.ud-select-color{
    background: #000000;
    width: 14px;
    height: 4px;
    display: block;
}
.ud-select-bgcolor{
    background: #fff;
    width: 14px;
    height: 4px;
    display: block;
}

/* 单元格编辑增强样式 */
.handsontable td.editing {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
    box-shadow: 0 0 3px rgba(33, 150, 243, 0.3);
}

.handsontable td:hover {
    background-color: #f5f5f5 !important;
    cursor: text;
}

.handsontable td.simple-cell {
    position: relative;
}

/* 选中状态的视觉反馈 */
.handsontable td.current {
    background-color: #e8f5e8 !important;
}

/* 编辑器样式优化 */
.handsontable .htTextareaEditor {
    border: 2px solid #2196f3;
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}