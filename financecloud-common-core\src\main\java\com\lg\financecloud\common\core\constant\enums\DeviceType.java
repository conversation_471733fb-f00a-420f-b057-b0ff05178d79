package com.lg.financecloud.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户设备类型
 *
 */
@Getter
@AllArgsConstructor
public enum DeviceType {

	/**
	 * pc端
	 */
	PC("PC"),

	/**
	 * robot端
	 */
	REBOT("robot"),

	/**
	 * app 端
	 */
	APP("app");

	private final String device;
	private static final Map<String, DeviceType> lookup = new HashMap<String, DeviceType>();

	static {
		for (DeviceType s : EnumSet.allOf(DeviceType.class)) {
			lookup.put(s.getDevice(), s);
		}
	}

	public static DeviceType lookup(String device) {
		return lookup.get(device);
	}

}
