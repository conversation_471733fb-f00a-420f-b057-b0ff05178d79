package com.lg.dao.cache;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.basic.User;
import com.lg.dao.helper.DaoHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 实体信息缓存测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EntityInfoCacheTest {

    private BaseDao baseDao;
    private UnifiedCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        baseDao = DaoHelper.getBaseDao();
        
        // 创建缓存管理器
        cacheManager = new UnifiedCacheManager();
        try {
            cacheManager.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testEntityInfoCache() {
        // 第一次获取实体信息
        EntityInfo entityInfo1 = baseDao.getEntityInfo(User.class);
        assertNotNull(entityInfo1);
        assertEquals("t_user", entityInfo1.getTableName());
        
        // 第二次获取实体信息，应该从缓存获取
        EntityInfo entityInfo2 = baseDao.getEntityInfo(User.class);
        assertNotNull(entityInfo2);
        assertEquals("t_user", entityInfo2.getTableName());
        
        // 验证是否是同一个对象（如果使用缓存，应该是同一个对象）
        // 注意：这个测试可能因为缓存实现而有所不同
        System.out.println("EntityInfo1: " + entityInfo1);
        System.out.println("EntityInfo2: " + entityInfo2);
        
        System.out.println("✅ 实体信息缓存测试通过");
    }

    @Test
    public void testEntityInfoWithoutCache() {
        // 测试没有统一缓存管理器时的降级处理
        // 这个测试主要验证代码的健壮性
        
        EntityInfo entityInfo = baseDao.getEntityInfo(User.class);
        assertNotNull(entityInfo);
        assertEquals("t_user", entityInfo.getTableName());
        
        System.out.println("✅ 实体信息无缓存降级测试通过");
    }

    @Test
    public void testMultipleEntityTypes() {
        // 测试多种实体类型的缓存
        EntityInfo userInfo1 = baseDao.getEntityInfo(User.class);
        EntityInfo userInfo2 = baseDao.getEntityInfo(User.class);
        
        assertNotNull(userInfo1);
        assertNotNull(userInfo2);
        assertEquals(userInfo1.getTableName(), userInfo2.getTableName());
        
        System.out.println("✅ 多实体类型缓存测试通过");
    }
}
