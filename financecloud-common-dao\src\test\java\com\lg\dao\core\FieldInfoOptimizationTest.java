package com.lg.dao.core;

import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * FieldInfo优化效果测试
 * 验证基于FieldInfo的性能优化是否正常工作
 */
@Slf4j
public class FieldInfoOptimizationTest {

    private UnifiedCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        cacheManager = mock(UnifiedCacheManager.class);

        // 模拟缓存管理器的get方法，让它直接调用supplier
        when(cacheManager.get(any(), any(), any())).thenAnswer(invocation -> {
            java.util.function.Supplier<?> supplier = invocation.getArgument(2);
            return supplier.get();
        });

        // 初始化EntityInfoManager的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    @Data
    @Table(name = "test_entity")
    public static class TestEntity {
        @Id
        @Column(name = "entity_id")
        private Long entityId;
        
        @Column(name = "entity_name")
        private String entityName;
        
        @Column(name = "create_time")
        private java.util.Date createTime;
        
        // 驼峰命名字段
        private String firstName;
        private String lastName;
        private Integer age;
    }

    @Test
    void testFieldInfoBasicFunctionality() throws Exception {
        // 获取EntityInfo
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        assertNotNull(entityInfo);

        // 打印所有字段信息用于调试
        log.info("All fields in TestEntity:");
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("  Field: {} -> Column: {}", field.getPropertyName(), field.getColumn());
        }

        // 测试通过列名查找字段信息
        EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn("entity_name");
        assertNotNull(fieldInfo, "Should find field info for entity_name");
        assertEquals("entityName", fieldInfo.getPropertyName());
        assertEquals("entity_name", fieldInfo.getColumn());
        assertEquals(String.class, fieldInfo.getPropertyType());

        log.info("Basic FieldInfo functionality test passed");
    }

    @Test
    void testFieldInfoPropertyDescriptorCaching() throws Exception {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn("entity_name");
        
        assertNotNull(fieldInfo);
        
        // 第一次获取PropertyDescriptor
        long startTime = System.nanoTime();
        java.beans.PropertyDescriptor pd1 = fieldInfo.getPropertyDescriptor();
        long firstCallTime = System.nanoTime() - startTime;

        // 第二次获取PropertyDescriptor（应该从缓存获取）
        startTime = System.nanoTime();
        java.beans.PropertyDescriptor pd2 = fieldInfo.getPropertyDescriptor();
        long secondCallTime = System.nanoTime() - startTime;
        
        // 验证返回的是同一个对象（缓存生效）
        assertSame(pd1, pd2);
        
        // 第二次调用应该明显更快
        assertTrue(secondCallTime < firstCallTime / 2, 
            "Second call should be much faster due to caching");
        
        log.info("PropertyDescriptor caching test passed. First call: {}ns, Second call: {}ns", 
            firstCallTime, secondCallTime);
    }

    @Test
    void testFieldInfoPropertySetting() throws Exception {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn("entity_name");
        
        assertNotNull(fieldInfo);
        
        TestEntity entity = new TestEntity();
        
        // 测试属性设置
        fieldInfo.setPropertyValue(entity, "Test Entity");
        assertEquals("Test Entity", entity.getEntityName());
        
        // 测试属性获取
        Object value = fieldInfo.getPropertyValue(entity);
        assertEquals("Test Entity", value);
        
        log.info("Property setting/getting test passed");
    }

    @Test
    void testFieldInfoPerformanceComparison() throws Exception {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn("entity_name");
        
        assertNotNull(fieldInfo);
        
        TestEntity entity = new TestEntity();
        int iterations = 10000;
        
        // 测试FieldInfo优化方法的性能
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            fieldInfo.setPropertyValue(entity, "value" + i);
        }
        long optimizedTime = System.nanoTime() - startTime;
        
        // 验证最终值
        assertEquals("value" + (iterations - 1), entity.getEntityName());
        
        double avgOptimizedTime = optimizedTime / (double) iterations / 1_000_000; // 毫秒
        
        log.info("FieldInfo optimized performance test completed:");
        log.info("Total iterations: {}", iterations);
        log.info("Total time: {} ms", optimizedTime / 1_000_000);
        log.info("Average time per operation: {:.6f} ms", avgOptimizedTime);
        
        // 性能应该很好（每次操作小于0.1毫秒）
        assertTrue(avgOptimizedTime < 0.1, 
            "Optimized property setting should be very fast");
    }

    @Test
    void testMappingTableEfficiency() {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        
        // 测试多种列名格式的查找
        String[] columnNames = {
            "entity_id", "ENTITY_ID", "entityId",
            "entity_name", "ENTITY_NAME", "entityName",
            "first_name", "firstName", "FIRST_NAME",
            "create_time", "createTime", "CREATE_TIME"
        };
        
        int foundCount = 0;
        long totalTime = 0;
        
        for (String columnName : columnNames) {
            long startTime = System.nanoTime();
            EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn(columnName);
            long endTime = System.nanoTime();
            
            totalTime += (endTime - startTime);
            
            if (fieldInfo != null) {
                foundCount++;
                log.debug("Found mapping: {} -> {}", columnName, fieldInfo.getPropertyName());
            }
        }
        
        double avgLookupTime = totalTime / (double) columnNames.length / 1_000; // 微秒
        
        log.info("Mapping table efficiency test completed:");
        log.info("Total lookups: {}, Found: {}", columnNames.length, foundCount);
        log.info("Average lookup time: {:.3f} μs", avgLookupTime);
        
        // 应该找到大部分映射
        assertTrue(foundCount >= columnNames.length / 2, 
            "Should find most of the column mappings");
        
        // 查找应该很快（每次查找小于10微秒）
        assertTrue(avgLookupTime < 10.0, 
            "Column lookup should be very fast");
    }

    @Test
    void testCamelCaseConversion() {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestEntity.class);
        
        // 测试下划线到驼峰的转换查找
        EntityInfo.FieldInfo fieldInfo1 = entityInfo.findFieldInfoByColumn("first_name");
        assertNotNull(fieldInfo1);
        assertEquals("firstName", fieldInfo1.getPropertyName());
        
        EntityInfo.FieldInfo fieldInfo2 = entityInfo.findFieldInfoByColumn("last_name");
        assertNotNull(fieldInfo2);
        assertEquals("lastName", fieldInfo2.getPropertyName());
        
        log.info("Camel case conversion test passed");
    }
}
