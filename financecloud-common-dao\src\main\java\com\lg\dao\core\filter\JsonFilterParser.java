package com.lg.dao.core.filter;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * JSON过滤器解析器
 * 用于解析前端传递的JSON格式的过滤条件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JsonFilterParser {

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MetadataService metadataService;
    
    /**
     * 解析JSON格式的过滤条件
     *
     * @param jsonStr JSON字符串
     * @return 过滤配置
     */
    public FilterConfig parse(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return new FilterConfig();
        }
        
        try {
            JSONObject json = JSONUtil.parseObj(jsonStr);
            return parseJson(json);
        } catch (Exception e) {
            log.error("解析JSON过滤条件失败: {}", jsonStr, e);
            return new FilterConfig();
        }
    }
    
    /**
     * 解析JSON对象
     *
     * @param json JSON对象
     * @return 过滤配置
     */
    public FilterConfig parseJson(JSONObject json) {
        FilterConfig filterConfig = new FilterConfig();
        
        try {
            // 解析筛选条件
            String filterSosStr = json.getStr("filterSos");
            if (!StringUtils.isEmpty(filterSosStr)) {
                List<FilterSo> filterSos = parseFilterSos(filterSosStr);
                
                // 尝试从JSON中获取表编码
                String tableCode = json.getStr("tableCode");
                Map<String, String> fieldMapping = getFieldMapping(tableCode);
                
                List<FilterConfig.Filter> filters = convertFilterSoToFilters(filterSos, fieldMapping);
                filterConfig.setFilters(filters);
            }
            
            // 解析排序条件
            String field = json.getStr("field");
            String order = json.getStr("order");
            if (!StringUtils.isEmpty(field) && !StringUtils.isEmpty(order)) {
                FilterConfig.SortCondition sortCondition = new FilterConfig.SortCondition();
                sortCondition.setField(field);
                sortCondition.setDirection(order);
                List<FilterConfig.SortCondition> sorts = new ArrayList<>();
                sorts.add(sortCondition);
                filterConfig.setSorts(sorts);
            }
            
            // 解析分页信息
            Integer page = json.getInt("page");
            Integer limit = json.getInt("limit");
            if (page != null && limit != null) {
                FilterConfig.PageInfo pageInfo = new FilterConfig.PageInfo();
                pageInfo.setPageNum(page);
                pageInfo.setPageSize(limit);
                filterConfig.setPage(pageInfo);
            }
        } catch (Exception e) {
            log.error("解析JSON对象失败", e);
        }
        
        return filterConfig;
    }
    
    /**
     * 获取字段映射
     *
     * @param tableCode 表编码
     * @return 字段映射
     */
    private Map<String, String> getFieldMapping(String tableCode) {
        Map<String, String> fieldMapping = new HashMap<>();
        
        if (StringUtils.isEmpty(tableCode) || metadataService == null) {
            return fieldMapping;
        }
        
        try {
            TableMetadata tableMetadata = metadataService.getTableMetadata(tableCode);
            if (tableMetadata == null) {
                return fieldMapping;
            }
            
            Map<String, TableMetadata.FieldConfig> fieldConfigMap = tableMetadata.getFieldMapping();
            for (Map.Entry<String, TableMetadata.FieldConfig> entry : fieldConfigMap.entrySet()) {
                TableMetadata.FieldConfig fieldConfig = entry.getValue();
                if (fieldConfig != null && !StringUtils.isEmpty(fieldConfig.getFieldMapping())) {
                    fieldMapping.put(entry.getKey(), fieldConfig.getFieldMapping());
                }
            }
        } catch (Exception e) {
            log.error("获取字段映射失败: {}", tableCode, e);
        }
        
        return fieldMapping;
    }
    
    /**
     * 将FilterSo转换为Filter
     *
     * @param filterSos FilterSo列表
     * @param fieldMapping 字段映射
     * @return Filter列表
     */
    private List<FilterConfig.Filter> convertFilterSoToFilters(List<FilterSo> filterSos, Map<String, String> fieldMapping) {
        List<FilterConfig.Filter> filters = new ArrayList<>();
        
        for (FilterSo filterSo : filterSos) {
            String mode = filterSo.getMode();
            
            // 处理组合条件
            if ("condition".equals(mode)) {
                List<FilterSo> children = filterSo.getChildren();
                if (children != null && !children.isEmpty()) {
                    filters.addAll(convertFilterSoToFilters(children, fieldMapping));
                }
                continue;
            }
            
            // 处理单个条件
            String field = filterSo.getField();
            if (StringUtils.isEmpty(field)) {
                continue;
            }
            
            // 如果有字段映射，使用映射后的字段名
            if (fieldMapping.containsKey(field)) {
                field = fieldMapping.get(field);
            }
            
            FilterConfig.Filter filter = new FilterConfig.Filter();
            filter.setField(field);
            setOperatorAndValue(filter, filterSo);
            filters.add(filter);
        }
        
        return filters;
    }
    
    /**
     * 设置操作符和值
     *
     * @param filter Filter对象
     * @param filterSo FilterSo对象
     */
    private void setOperatorAndValue(FilterConfig.Filter filter, FilterSo filterSo) {
        String type = filterSo.getType();
        Object value = filterSo.getValue();
        
        if (value == null) {
            return;
        }
        
        switch (type) {
            case "eq":
                filter.setOperator("eq");
                filter.setValue(value);
                break;
            case "ne":
                filter.setOperator("neq");
                filter.setValue(value);
                break;
            case "like":
                filter.setOperator("like");
                filter.setValue("%" + value + "%");
                break;
            case "likeLeft":
                filter.setOperator("like");
                filter.setValue("%" + value);
                break;
            case "likeRight":
                filter.setOperator("like");
                filter.setValue(value + "%");
                break;
            case "in":
                filter.setOperator("in");
                filter.setValue(value);
                break;
            case "notIn":
                filter.setOperator("not in");
                filter.setValue(value);
                break;
            case "gt":
                filter.setOperator("gt");
                filter.setValue(value);
                break;
            case "ge":
                filter.setOperator("gte");
                filter.setValue(value);
                break;
            case "lt":
                filter.setOperator("lt");
                filter.setValue(value);
                break;
            case "le":
                filter.setOperator("lte");
                filter.setValue(value);
                break;
            case "between":
                filter.setOperator("between");
                filter.setValue(value);
                break;
            default:
                filter.setOperator("eq");
                filter.setValue(value);
                break;
        }
    }
    
    /**
     * 解析FilterSos字符串
     *
     * @param filterSosStr FilterSos字符串
     * @return FilterSo列表
     */
    private List<FilterSo> parseFilterSos(String filterSosStr) {
        List<FilterSo> filterSos = new ArrayList<>();
        
        try {
            JSONArray jsonArray = JSONUtil.parseArray(filterSosStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                FilterSo filterSo = new FilterSo();
                filterSo.setMode(jsonObject.getStr("mode"));
                filterSo.setField(jsonObject.getStr("field"));
                filterSo.setType(jsonObject.getStr("type"));
                filterSo.setValue(jsonObject.get("value"));
                
                // 解析子条件
                if ("condition".equals(filterSo.getMode()) && jsonObject.containsKey("children")) {
                    JSONArray childrenArray = jsonObject.getJSONArray("children");
                    String childrenStr = childrenArray.toString();
                    List<FilterSo> children = parseFilterSos(childrenStr);
                    filterSo.setChildren(children);
                }
                
                filterSos.add(filterSo);
            }
        } catch (Exception e) {
            log.error("解析FilterSos字符串失败: {}", filterSosStr, e);
        }
        
        return filterSos;
    }
    
    /**
     * 筛选条件对象
     */
    @Data
    private static class FilterSo {
        private String mode;
        private String field;
        private String type;
        private Object value;
        private List<FilterSo> children;
    }
} 