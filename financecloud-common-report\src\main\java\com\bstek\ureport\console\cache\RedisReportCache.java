package com.bstek.ureport.console.cache;

import cn.hutool.json.JSONUtil;
import com.bstek.ureport.cache.ReportCache;
import com.lg.financecloud.common.data.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * @Author: summer
 * @Date: 2022/3/5 18:42
 * @Description:
 **/
public class RedisReportCache implements ReportCache {


    @Override
    public Object getObject(String file) {

        return   RedisUtils.getCacheObject(file);
    }

    @Override
    public void storeObject(String file, Object obj) {
        RedisUtils.setCacheObject(file, obj, Duration.ofMinutes(4));

    }

    @Override
    public boolean disabled() {
        return false;
    }
}
