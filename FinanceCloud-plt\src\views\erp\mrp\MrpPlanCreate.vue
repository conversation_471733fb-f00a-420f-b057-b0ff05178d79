<template>
  <div class="conten_body_main">
    <el-container style="background-color: #f0f3f4">

      <el-header class="header-title-button header-position-sticky" style="height:55px;">
        <div class="header_title_text">MRP计划创建</div>
        <div class="header-buttons">
          <el-button
            type="default"
            icon="el-icon-back"
            @click="goBack"
            :size="elSize"
          >
            {{ $t('返回列表') }}
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-check"
            @click="savePlan"
            :loading="saving"
            :size="elSize"
          >
            {{ $t('保存') }}
          </el-button>
<!--          <el-button-->
<!--            type="success"-->
<!--            icon="el-icon-video-play"-->
<!--            @click="submitAndExecute"-->
<!--            :loading="executing"-->
<!--            :size="elSize"-->
<!--          >-->
<!--            {{ $t('保存并执行') }}-->
<!--          </el-button>-->
        </div>
      </el-header>

      <el-main justify="center" align="middle" class="el-main">
        <el-card>
          <vxe-form :data="formData" ref="formRef" class-name="custom_form_cla" size="mini" title-width="160" title-align="right" title-colon :rules="formRules" :prevent-submit="true">
            <vxe-form-item span="24" title-width="240" class-name="form_line">
              <template>
                <div class="custom_form_title">
                  <div style="display:flex;align-items:center;">
                    <i class="el-icon-s-fold"></i>
                    <span>{{ $t('基本信息') }}</span>
                  </div>
                </div>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('计划编码')" field="planCode" :item-render="{}" span="12">
              <template #default>
                <vxe-input v-model="formData.planCode" :placeholder="$t('请输入计划编码')" :disabled="opType != 'add'"></vxe-input>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('计划名称')" field="planName" :item-render="{}" span="12">
              <template #default>
                <vxe-input v-model="formData.planName" :placeholder="$t('请输入计划名称')" :disabled="!isEdit"></vxe-input>
              </template>
            </vxe-form-item>

            <!-- 计算模式字段已移除UI，默认使用标准模式 -->



            <vxe-form-item :title="$t('开始日期')" field="startDate" :item-render="{}" span="8">
              <template #default>
                <vxe-input v-model="formData.startDate" :placeholder="$t('请选择开始日期')" type="date" transfer :disabled="!isEdit"></vxe-input>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('结束日期')" field="endDate" :item-render="{}" span="8">
              <template #default>
                <vxe-input v-model="formData.endDate" :placeholder="$t('请选择结束日期')" type="date" transfer :disabled="!isEdit"></vxe-input>
              </template>
            </vxe-form-item>



            <vxe-form-item :title="$t('创建人')" field="creatorName" :item-render="{}" span="8">
              <template #default>
                <vxe-input v-model="formData.creatorName" disabled></vxe-input>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('创建时间')" field="createTime" :item-render="{}" span="8">
              <template #default>
                <vxe-input v-model="formData.createTime" disabled></vxe-input>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('计划描述')" field="description" :item-render="{}" span="24" align="center">
              <template #default>
                <vxe-textarea v-model="formData.description" rows="2" :placeholder="$t('请输入计划描述')" resize="none" size="mini" maxlength="200" show-word-count :disabled="!isEdit"></vxe-textarea>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-card>

        <el-card style="margin-top: 20px;">
          <vxe-form :data="formData" class-name="custom_form_cla" size="mini" title-width="160" title-align="right" title-colon :prevent-submit="true">
            <vxe-form-item title="" title-width="240" span="24" class-name="form_line">
              <template>
                <div class="custom_form_title">
                  <div style="display:flex;align-items:center;">
                    <i class="el-icon-s-fold"></i>
                    <span>{{ $t('计算参数') }}</span>
                  </div>
                </div>
              </template>
            </vxe-form-item>

            <!-- 基础计算参数 - 所有模式都显示 -->
            <vxe-form-item :title="$t('考虑安全库存')" field="considerSafetyStock" :item-render="{}" span="8">
              <template #default>
                <vxe-switch v-model="formData.considerSafetyStock" :disabled="!isEdit"></vxe-switch>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('考虑在途库存')" field="considerInTransit" :item-render="{}" span="8">
              <template #default>
                <vxe-switch v-model="formData.considerInTransit" :disabled="!isEdit"></vxe-switch>
              </template>
            </vxe-form-item>

            <vxe-form-item :title="$t('考虑已分配')" field="considerAllocated" :item-render="{}" span="8">
              <template #default>
                <vxe-switch v-model="formData.considerAllocated" :disabled="!isEdit"></vxe-switch>
              </template>
            </vxe-form-item>

            <!-- 标准模式和产能约束模式的高级参数 -->
            <template v-if="showAdvancedParams">
              <vxe-form-item :title="$t('批量规则')" field="lotSizingRule" :item-render="{}" span="8">
                <template #default>
                  <DictSelect 
                    v-model="formData.lotSizingRule" 
                    dict-type="MrpLotSizingRuleEnum"
                    :placeholder="$t('请选择批量规则')"
                    :disabled="!isEdit"
                    size="mini"
                    :immediate="false"
                    :batch-mode="true"
                  />
                </template>
              </vxe-form-item>

              <vxe-form-item :title="$t('BOM展开层级')" field="explosionLevel" :item-render="{}" span="8">
                <template #default>
                  <vxe-input v-model="formData.explosionLevel" :placeholder="$t('请输入BOM展开层级')" type="integer" :disabled="!isEdit"></vxe-input>
                </template>
              </vxe-form-item>

              <vxe-form-item :title="$t('提前期偏移')" field="leadTimeOffset" :item-render="{}" span="8">
                <template #default>
                  <vxe-input v-model="formData.leadTimeOffset" type="number" :placeholder="$t('请输入提前期偏移天数')" :disabled="!isEdit"></vxe-input>
                </template>
              </vxe-form-item>
            </template>

 

            <!-- 通用参数 -->
            <vxe-form-item :title="$t('计划时间范围')" field="planningHorizon" :item-render="{}" span="8">
              <template #default>
                <vxe-input v-model="formData.planningHorizon" type="number" :placeholder="$t('请输入计划时间范围(天)')" :disabled="!isEdit"></vxe-input>
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-card>

        <el-card style="margin-top: 20px; min-height: 200px;">
          <vxe-form :data="formData" class-name="custom_form_cla" size="mini" title-width="160" title-align="right" title-colon :prevent-submit="true">
            <vxe-form-item title="" title-width="240" span="24" class-name="form_line">
              <template>
                <div class="custom_form_title">
                  <div style="display:flex;align-items:center;">
                    <i class="el-icon-s-fold"></i>
                    <span>{{ $t('需求来源')}}</span>
                  </div>
                </div>
              </template>
            </vxe-form-item>

            <!-- 需求来源管理组件 -->
            <vxe-form-item span="24">
              <template>
                <MrpDemandSourceManager
                    ref="demandSourceManager"
                    v-model="formData.demandSources"
                    :disabled="!isEdit"
                    @source-change="onDemandSourceChange"
                    @open-source-selector="handleOpenSourceSelector"
                    @open-material-selector="handleOpenMaterialSelector"
                    @batch-select-order="handleBatchSelectOrder"
                />
              </template>
            </vxe-form-item>
          </vxe-form>
        </el-card>


        <!-- 来源单号选择弹窗(行内) -->
        <selectOrderGoods ref="selectOrderGoodsRef" :defaultColumnVisible="false" :searchParamsObject="{custom: this.formData.customId}" :tableTitle="$t('销售订单')" @selectGoodsSuccest="fun_selectGoodsHandel" customApiUrl="/flowable/getChooseSalesOrderItemsByCondition" orderNumberKey="salesOrderNumber" selectType="multipleChoice">
          <template #otherColumn>
            <vxe-column field="operTime" :title="$t('单据日期')" width="130">
              <template #default="{ row }">
                {{ formatTime(row.operTime) }}
              </template>
            </vxe-column>
            <vxe-column field="linkNumber" width="160" :title="$t('单据编号')"></vxe-column>
            <vxe-column field="customName" width="160" :title="$t('客户名称')"></vxe-column>
            <vxe-column field="materialCode" :title="$t('物料编码')" width="130"></vxe-column>
            <vxe-column field="materialName" :title="$t('物料名称')" width="130"></vxe-column>
            <vxe-column field="materialModel" :title="$t('物料型号')" width="130"></vxe-column>
            <vxe-column field="operNumber" :title="$t('需求数量')" width="130"></vxe-column>
            <vxe-column field="unitName" :title="$t('单位')" width="130"></vxe-column>
            <vxe-column field="finishedNumber" :title="$t('已发货数量')" width="130"></vxe-column>
            <vxe-column field="pendingIn" :title="$t('未发货数量')" width="130"></vxe-column>
            <vxe-column field="sendGoodsNumber" :title="$t('待出库数量')" width="130"></vxe-column>
            <vxe-column field="deliveryTime" :title="$t('交货日期')" width="130"></vxe-column>
          </template>
        </selectOrderGoods>

        <!-- 物料选择弹窗 -->
        <SelectGoods
          ref="selectGoodsRef"
          @selectGoodsSuccest="handleMaterialSelect"
          selectType="radioSelect"
        />

      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getMrpPlanDetail, createMrpPlan, updateMrpPlan, getMrpPlanList } from '@/api/erp/mrp';
import { calculateMrpAsync } from '@/api/erp/mrp-calculation';

import SelectOrderGoods from '@/components/selectOrderGoods'; // 批量选择和行内选择
import SelectGoods from '@/components/selectGoods';
import tableColumnBatchInput from '@/components/tableColumnBatchInput';
import selectDepot from '@/components/selectDepot';
import MrpDemandSourceManager from '@/components/mrp/MrpDemandSourceManager';
import DictSelect from '@/components/common/DictSelect';

import dictMixin from '@/mixins/dictMixin';

export default {
  name: 'MrpPlanCreate',
  components: {
    SelectOrderGoods, // 选择商品组件
    tableColumnBatchInput,
    SelectGoods,
    selectDepot,
    MrpDemandSourceManager,
    DictSelect
  },
  mixins: [dictMixin],
  data() {
    return {
      isEdit: true,
      opType: 'add',
      pageFrom: 'MrpPlanCreate',
      dialogVisible: false, // 控制批量销售订单选择弹窗
      currentRowForMaterial: null, // 当前操作物料的行
      currentRowIndex: -1, // 当前操作源单的行索引
      saving: false, // 保存按钮加载状态
      executing: false, // 保存并执行按钮加载状态
      formData: {
        id: '',
        planCode: '',
        planName: '',
        calculationMode: 'STANDARD', // 默认使用标准模式
        status: 'DRAFT',
        priority: 'MEDIUM',
        startDate: '',
        endDate: '',
        description: '',
        // 基础计算参数
        considerSafetyStock: true,
        considerInTransit: true,
        considerAllocated: true,
        planningHorizon: 365,


        // 高级参数（标准模式和产能约束模式）
        lotSizingRule: 'LOT_FOR_LOT',
        explosionLevel: 99,
        leadTimeOffset: 0,
        // 产能约束模式参数（第一期暂不支持）

        // enableCapacityBalancing: false,
        // maxIterations: 10,
        // 仿真模式参数（第一期暂不支持）
        // simulationScenario: 'WHAT_IF',
        // saveSimulationResults: true,
        // simulationNotes: '',
        // 审计字段
        createBy: this.$store.getters.userId || '',
        creatorName: this.$store.getters.name || '系统管理员',
        updateBy: '',
        updateTime: '',
        // 需求来源
        demandSources: []
      },
      formRules: {
        planCode: [
          { required: true, message: '请输入计划编码', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9_-]+$/, message: '计划编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
        ],

        planName: [
          { required: true, message: '请输入计划名称', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        planningHorizon: [
          { required: true, message: '请输入计划时间范围', trigger: 'blur' },
          { type: 'number', min: 1, message: '计划时间范围必须大于0', trigger: 'blur' }
        ],
        // 条件验证规则
        lotSizingRule: [
          {
            validator: (rule, value, callback) => {
              if (this.showAdvancedParams && !value) {
                callback(new Error('请选择批量规则'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        explosionLevel: [
          {
            validator: (rule, value, callback) => {
              if (this.showAdvancedParams && (!value || value < 1 || value > 99)) {
                callback(new Error('BOM展开层级必须在1-99之间'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        leadTimeOffset: [
          {
            validator: (rule, value, callback) => {
              if (this.showAdvancedParams && value < 0) {
                callback(new Error('提前期偏移必须为非负数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],

        maxIterations: [
          {
            validator: (rule, value, callback) => {
              if (this.isCapacityConstrainedMode && (!value || value < 1 || value > 100)) {
                callback(new Error('最大迭代次数必须在1-100之间'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        // 第一期暂不支持仿真模式验证
        // simulationScenario: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if (this.isSimulationMode && !value) {
        //         callback(new Error('请选择仿真场景'));
        //       } else {
        //         callback();
        //       }
        //     },
        //     trigger: 'change'
        //   }
        // ]
      }
    }
  },
  computed: {
    // 动态判断来源单号是否必填
    isSourceNoRequired: function() {
      return this.formData.demandSources.some(function(item) {
        return item.sourceType === 'SALES_ORDER';
      });
    },
    // 是否显示高级参数（默认使用标准模式）
    showAdvancedParams: function() {
      return true; // 默认显示高级参数
    }
  },
  async created() {
    // 预加载页面所需的字典数据
    await this.preloadPageDictionaries();
    
    // 设置默认的计划编码
    if (!this.formData.planCode) {
      this.formData.planCode = this.generatePlanCode();
    }
    this.initData();
  },
  methods: {
    /**
     * 预加载页面所需的字典数据
     */
    async preloadPageDictionaries() {
      const dictTypes = [
        // 基础字典
        'MrpPriorityEnum',           // 优先级
        'MrpStatusEnum',             // MRP状态
        // 需求来源相关字典
        'MrpSourceTypeEnum',   // 需求来源类型（可能对应 MrpSourceTypeEnum）
        'MrpRequirementStatusEnum',    // 需求状态
      ];
      
      try {
        console.log('开始预加载字典数据...', dictTypes);
        await this.preloadDictionaries(dictTypes);
        console.log('字典数据预加载完成');
      } catch (error) {
        console.error('预加载字典数据失败:', error);
        // 不阻塞页面加载，继续执行
      }
    },

    getCurrentTime() {
      return this.getCurrentDateTime();
    },

    // 生成计划编码
    generatePlanCode() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      const second = String(now.getSeconds()).padStart(2, '0');

      return `MRP${year}${month}${day}${hour}${minute}${second}`;
    },

    formatTime(time) {
      if (!time) return '';
      var date = new Date(time);
      return date.toLocaleDateString();
    },
    initData() {
     
      
      if (this.$route.query.id) {
        this.opType = 'edit';
        this.loadPlanData(this.$route.query.id);
      } else {
        // 新建时，确保有默认的需求来源
        if (!this.formData.demandSources || this.formData.demandSources.length === 0) {
          this.addDemandSource();
        }
        // 设置默认的开始和结束日期
        if (!this.formData.startDate) {
          this.formData.startDate = this.getCurrentDate();
        }
        if (!this.formData.endDate) {
          // 默认结束日期为30天后
          const endDate = new Date();
          endDate.setDate(endDate.getDate() + 30);
          this.formData.endDate = this.formatDate(endDate);
        }
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    async loadPlanData(id) {
      try {
        this.$message.info('正在加载计划数据...');
        var res = await getMrpPlanDetail(id);
        console.log(res.data,'--==res.data');
        if (res.data&&res.data.data) {
          // 逐个字段更新formData，确保响应式更新
          Object.keys(res.data.data).forEach(key => {
            if (key !== 'demandSources') {
              this.$set(this.formData, key, res.data.data[key]);
            }
          });
          
          // 特别处理demandSources数组，确保子组件能正确接收到更新
          if (res.data.data.demandSources && res.data.data.demandSources.length > 0) {
            console.log(res.data.data.demandSources,'xxx--------===========');
            this.$set(this.formData, 'demandSources', [...res.data.data.demandSources]);
            
            // 强制更新子组件
            this.$nextTick(() => {
              if (this.$refs.demandSourceManager) {
                this.$refs.demandSourceManager.updateData([...res.data.data.demandSources]);
              }
            });
          } else {
            this.$set(this.formData, 'demandSources', []);
          }
          this.$message.success('计划数据加载成功');
        } else {
          this.$message.error('加载计划数据失败');
        }
      } catch (error) {
        console.error('加载计划数据异常:', error);
        this.$message.error('加载计划数据时发生异常');
      }
    },
    fun_validateForm() {
      // 首先验证表单基本字段
      return this.$refs.formRef.validate().then(() => {
        // 验证计划编码唯一性
        return this.validatePlanCodeUniqueness();
      }).then(() => {
        // 然后验证需求来源数据
        return this.validateDemandSources();
      });
    },

    // 验证计划编码唯一性
    async validatePlanCodeUniqueness() {
      if (!this.formData.planCode) {
        return Promise.resolve(); // 如果没有编码，跳过验证（由表单必填规则处理）
      }

      try {
        // 查询是否存在相同编码的计划
        const response = await getMrpPlanList({
          planCode: this.formData.planCode,
          current: 1,
          size: 1
        });

        if (response && response.data && response.data.records) {
          const existingPlans = response.data.records.filter(plan => {
            // 如果是编辑模式，排除当前计划本身
            return this.opType === 'add' ? true : plan.id !== this.formData.id;
          });

          if (existingPlans.length > 0) {
            this.$message.error('计划编码已存在，请使用其他编码');
            return Promise.reject(new Error('计划编码已存在'));
          }
        }

        return Promise.resolve();
      } catch (error) {
        console.error('验证计划编码失败:', error);
        // 如果验证接口出错，不阻止保存，只记录错误
        return Promise.resolve();
      }
    },

    // 验证需求来源数据
    validateDemandSources() {
      if (!this.formData.demandSources || this.formData.demandSources.length === 0) {
        this.$message.error('请至少添加一条需求来源');
        return Promise.reject(new Error('需求来源不能为空'));
      }

      for (let i = 0; i < this.formData.demandSources.length; i++) {
        const source = this.formData.demandSources[i];
        const rowNum = i + 1;

        // 验证来源类型
        if (!source.sourceType) {
          this.$message.error(`第${rowNum}行：请选择来源类型`);
          return Promise.reject(new Error('来源类型不能为空'));
        }

        // 验证物料信息
        if (!source.materialCode || !source.materialId) {
          this.$message.error(`第${rowNum}行：物料ID为空，请重新选择物料`);
          return Promise.reject(new Error('物料不能为空'));
        }

        // 验证数量
        if (!source.quantity || parseFloat(source.quantity) <= 0) {
          this.$message.error(`第${rowNum}行：请输入有效的需求数量`);
          return Promise.reject(new Error('需求数量必须大于0'));
        }

        // 验证需求日期
        if (!source.demandDate) {
          this.$message.error(`第${rowNum}行：请选择需求日期`);
          return Promise.reject(new Error('需求日期不能为空'));
        }

        // 验证销售订单类型的来源单号
        if (source.sourceType === 'SALES_ORDER' && !source.sourceNo) {
          this.$message.error(`第${rowNum}行：销售订单类型必须选择来源单号`);
          return Promise.reject(new Error('销售订单来源单号不能为空'));
        }
      }

      return Promise.resolve();
    },
    fun_tableValidate(row) {
      // 表格行数据变化时的验证逻辑
      if (row.sourceNo && !row.materialCode) {
        this.$message.warning('请选择物料信息');
      }
    },
    // 单独保存方法
    async savePlan() {
      try {
        this.saving = true;

        // 验证表单
        await this.fun_validateForm();

        this.$message.info('正在保存计划...');

        // 保存计划
        var response;
        if (this.opType === 'add') {
          response = await createMrpPlan(this.formData);
        } else {
          response = await updateMrpPlan(this.formData.id, this.formData);
        }

        if (response && response.data) {
          this.formData.id = response.data.id;
          if (response.data.planCode) {
            this.formData.planCode = response.data.planCode;
          }
          this.$message.success('计划保存成功！');
        } else {
          this.$message.error('保存失败');
        }
      } catch (error) {
        console.error('保存失败:', error);  
        this.$message.error(error.message || '保存失败');
        
      } finally {
        this.saving = false;
      }
    },


    fun_submitSuccess() {
      this.$message.success('保存成功');
      this.goBack();
    },

    goBack() {
      if (this.pageFrom === 'MrpPlanDetail') {
        this.$router.push({ path: '/erp/mrp/MrpPlanDetail', query: { id: this.formData.id } });
      } else {
        this.$router.push({ path: '/erp/mrp/MrpPlanList' });
      }
    },
    insertDemandSource(index) {
      this.formData.demandSources.splice(index, 0, {
        sourceType: '',
        sourceNo: '',
        sourceId: '',
        materialId: '',
        materialCode: '',
        materialName: '',
        materialSpec: '', // 新增：物料型号
        unit: '', // 新增：单位
        unitId: '', // 新增：单位ID
        normsId: '', // 新增：规格ID
        priority: 'MEDIUM',
        quantity: 1,
        demandDate: this.getCurrentDate(),
        customerName: '',
        totalAmount: null,
        status: 'PENDING'
      });
    },
    addDemandSource() {
      this.formData.demandSources.push({
        sourceType: '',
        sourceNo: '',
        sourceId: '',
        materialId: '',
        materialCode: '',
        materialName: '',
        materialSpec: '', // 新增：物料型号
        unit: '', // 新增：单位
        unitId: '', // 新增：单位ID
        normsId: '', // 新增：规格ID
        priority: 'MEDIUM',
        quantity: 1,
        demandDate: this.getCurrentDate(),
        customerName: '',
        totalAmount: null,
        status: 'PENDING'
      });
    },

    // 获取当前日期
    getCurrentDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    removeDemandSource(row, index) {
      this.formData.demandSources.splice(index, 1);
    },
    openMaterialSelect(row) {
      console.log('打开物料选择，当前行:', row);

      this.currentRowForMaterial = row;

      // 使用正确的方式打开SelectGoods弹窗
      if (this.$refs.selectGoodsRef) {
        this.$refs.selectGoodsRef.fun_modalShow();
        console.log('物料选择弹窗已打开');
      } else {
        console.error('selectGoodsRef 引用不存在');
      }
    },
    handleMaterialSelect(selectedList) {
      console.log('物料选择回调，选中的物料:', selectedList);

      if (this.currentRowForMaterial && selectedList && selectedList.length > 0) {
        const selected = selectedList[0]; // 单选模式，取第一个

        this.$set(this.currentRowForMaterial, 'materialId', selected.productId || selected.id);
        this.$set(this.currentRowForMaterial, 'materialCode', selected.materialCode);
        this.$set(this.currentRowForMaterial, 'materialName', selected.productName || selected.materialName);
        // 新增字段回填
        this.$set(this.currentRowForMaterial, 'materialSpec', selected.materialSpec || selected.model || '');
        this.$set(this.currentRowForMaterial, 'unit', selected.unit || selected.unitName || '');
        this.$set(this.currentRowForMaterial, 'unitId', selected.unitId || '');
        this.$set(this.currentRowForMaterial, 'normsId', selected.unitList[0].id|| '');

        console.log('物料信息已回填到行:', this.currentRowForMaterial);
      }

      // 清空当前操作行引用
      this.currentRowForMaterial = null;
    },
    openSelectOrder() {
      // 批量添加 - 从顶部按钮触发
      this.currentRowIndex = -1; // 重置行索引，表示批量选择
      this.$refs.selectOrderGoodsRef.fun_modalShow();
    },
    openSelectOrderForRow(row, rowIndex) {
      // 行内选择 - 从表格行内触发
      if (!this.isEdit) return;

      this.currentRowIndex = rowIndex;
      this.$refs.selectOrderGoodsRef.fun_modalShow();
    },
    // 行内选择回调
    handleSelectOrder(selectedRows) {
      if (selectedRows && selectedRows.length > 0 && this.currentRowIndex !== -1) {
        var selected = selectedRows[0];
        var source = this.formData.demandSources[this.currentRowIndex];

        source.sourceNo = selected.linkNumber; // 来源单号
        source.sourceId = selected.id;
        source.sourceType = 'SALES_ORDER';
        source.materialId = selected.materialId;
        source.materialCode = selected.materialCode;
        source.materialName = selected.materialName;
        // 新增字段回填
        source.materialSpec = selected.materialModel || selected.materialSpec || '';
        source.unit = selected.unitName || selected.unit || '';
        source.unitId = selected.unitId || '';
        source.normsId = selected.normsId || '';
        source.quantity = selected.pendingIn; // 需求数量
        source.demandDate = selected.deliveryDate; // 需求日期
        source.customerName = selected.customerName;

        this.$set(this.formData.demandSources, this.currentRowIndex, source);
        this.currentRowIndex = -1; // Reset index
      }
    },
    // selectOrderGoods组件回调处理方法
    fun_selectGoodsHandel(responseArrayData) {
      if (responseArrayData && responseArrayData.length > 0) {
        // 如果是行内选择（有指定行索引）
        if (this.currentRowIndex !== -1) {
          var selected = responseArrayData[0];

          // 创建新的数据对象
          var newSource = {
            sourceNo: selected.linkNumber || selected.salesOrderNumber, // 来源单号
            sourceType: 'SALES_ORDER',
            materialId: selected.materialId,
            materialCode: selected.materialCode,
            materialName: selected.materialName,
            // 新增字段
            materialSpec: selected.materialModel || selected.materialSpec || '',
            unit: selected.unitName || selected.unit || '',
            normsId: selected.normsId||'',
            quantity: selected.pendingIn || selected.operNumber, // 需求数量
            demandDate: selected.deliveryTime || selected.deliveryDate, // 需求日期
            customerName: selected.customName || selected.customerName,
            totalAmount: selected.totalAmount || (selected.unitPrice * selected.operNumber),
            priority: 'MEDIUM',
            status: 'PENDING'
          };

          // 使用Vue.set确保响应式更新
          this.$set(this.formData.demandSources, this.currentRowIndex, newSource);
          this.currentRowIndex = -1; // Reset index

          this.$message.success('订单数据已回填到当前行');
        } else {
          // 批量选择，替换所有需求来源数据
          var newDemandSources = responseArrayData.map(function(item) {
            return {
              sourceType: 'SALES_ORDER',
              sourceNo: item.linkNumber || item.salesOrderNumber,
              materialId: item.materialId,
              materialCode: item.materialCode,
              materialName: item.materialName,
              // 新增字段
              materialSpec: item.materialModel || item.materialSpec || '',
              unit: item.unitName || item.unit || '',
              unitId: item.unitId || '',
              normsId: item.normsId||'',
              priority: 'MEDIUM',
              quantity: item.pendingIn || item.operNumber,
              demandDate: item.deliveryTime || item.deliveryDate,
              customerName: item.customName || item.customerName,
              totalAmount: item.totalAmount || (item.unitPrice * item.operNumber)
            };
          });

          // 使用Vue.set确保响应式更新
          this.$set(this.formData, 'demandSources', newDemandSources);

          // 强制更新子组件
          this.$nextTick(() => {
            if (this.$refs.demandSourceManager) {
              this.$refs.demandSourceManager.updateData(newDemandSources);
            }
          });

          this.$message.success('成功导入 ' + newDemandSources.length + ' 条销售订单明细');
        }
      }
    },

    // 来源类型变化处理
    onSourceTypeChange(row) {
      // 清空来源单号和相关字段
      row.sourceNo = '';
      row.materialId = '';
      row.materialCode = '';
      row.materialName = '';
      row.quantity = '';
      row.demandDate = '';
      row.customerName = '';

              // 根据类型给出提示
        var self = this;
        this.$nextTick(function() {
          if (row.sourceType === 'SALES_ORDER') {
            self.$message.info('请点击搜索图标选择销售订单');
          } else if (row.sourceType === 'SAFETY_STOCK') {
            self.$message.info('安全库存来源单号为可选项，可输入策略标识');
          }
        });
    },
    // 批量选择回调
    selectData(selection) {
      if (!selection || selection.length === 0) return;

      // 替换现有明细
      var newDemandSources = [];
      for (var i = 0; i < selection.length; i++) {
        var order = selection[i];
        for (var j = 0; j < order.children.length; j++) {
          var item = order.children[j];
          newDemandSources.push({
            sourceType: 'SALES_ORDER',
            sourceNo: order.orderNumber,
            materialId: item.materialId,
            materialCode: item.materialCode,
            materialName: item.productName,
            // 新增字段
            materialSpec: item.materialSpec || item.model || '',
            unit: item.unit || item.unitName || '',
            unitId: item.unitId || '',
            normsId: item.normsId || '',
            quantity: item.needNum,
            demandDate: order.orderDate || new Date(),
            priority: 'MEDIUM',
            customerName: order.customerName
          });
        }
      }

      this.formData.demandSources = newDemandSources;

      if (newDemandSources.length > 0) {
        this.$message.success('成功从 ' + selection.length + ' 个订单中导入 ' + newDemandSources.length + ' 条物料明细');
      } else {
        this.$message.info('选择的订单中没有可导入的物料明细');
      }
    },
    async submitAndExecute() {
      try {
        this.executing = true;

        // 验证表单
        await this.fun_validateForm();
        this.$message.info('正在保存计划...');

        // 保存计划
        var savedPlanResponse;
        if (this.opType === 'add') {
          savedPlanResponse = await createMrpPlan(this.formData);
        } else {
          savedPlanResponse = await updateMrpPlan(this.formData.id, this.formData);
        }

        var planId = savedPlanResponse.data.id;
        if (!planId) {
          this.$message.error('保存计划失败，未能获取到计划ID。');
          return;
        }

        this.formData.id = planId;
        if(savedPlanResponse.data.planCode){
          this.formData.planCode = savedPlanResponse.data.planCode;
        }

        this.$message.info('计划已保存 (ID: ' + planId + ')，正在提交执行...');

        // 使用统一的异步MRP计算接口
        try {
          const { calculateMrpAsync } = await import('@/api/erp/mrp-calculation');
          const calculationResponse = await calculateMrpAsync(planId);
          
          this.$message.success('计划已成功保存并开始执行！正在跳转到结果页面...');
          
          // 跳转到结果页面
          this.$router.push({
            path: '/erp/mrp/MrpPlanResults',
            query: { 
              id: planId,
              from: 'creation'
            }
          });
          
        } catch (error) {
          console.error('MRP计算启动失败:', error);
          this.$message.error(`计算启动失败：${error.message || '未知错误'}`);
          this.goBack();
        }
      } catch (error) {
        console.error('保存并执行失败:', error);
        this.handleSaveError(error);
      } finally {
        this.executing = false;
      }
    },

    // ==================== 新增的需求来源管理组件事件处理方法 ====================

    // 处理需求来源数据变化
    onDemandSourceChange(newData) {
      this.$set(this.formData, 'demandSources', newData);
    },

    // 处理来源选择器打开事件
    handleOpenSourceSelector({ row, rowIndex, sourceType }) {
      if (sourceType === 'SALES_ORDER') {
        this.openSelectOrderForRow(row, rowIndex);
      }
    },

    // 处理物料选择器打开事件
    handleOpenMaterialSelector(row) {
      this.openMaterialSelect(row);
    },

    // 批量选择销售订单
    handleBatchSelectOrder() {
      this.openSelectOrder();
    },





    // 数量变化处理
    onQuantityChange(row) {
      // 可以在这里添加数量变化的业务逻辑
      console.log('需求数量变化:', row);
    },


  }
}
</script>

<style lang="scss" scoped>
.conten_body_main {
  .el-container {
    .el-main {
      padding: 15px;
      .el-card {
        margin-bottom: 20px;
      }
    }
  }
}

.custom_form_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #303133;

  div {
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.row_index_buts {
  display: flex;
  align-items: center;
  justify-content: center;

  .row_add_del_but {
    display: flex;
    flex-direction: column;
    margin-right: 5px;

    i {
      cursor: pointer;
      font-size: 14px;
      margin: 1px 0;

      &.blur-color {
        color: #409EFF;
        &:hover {
          color: #66b1ff;
        }
      }

      &.red-color {
        color: #F56C6C;
        &:hover {
          color: #f78989;
        }
      }
    }
  }

  .row_index {
    font-size: 12px;
    color: #909399;
  }
}

.goodTableClass {
  ::v-deep .vxe-table {
    .vxe-cell--required-icon {
      color: #F56C6C;
    }
  }
}

.header-title-button {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;

  .header_title_text {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.header-position-sticky {
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 来源类型相关样式 */
.source-type-sales {
  border-left: 3px solid #409EFF;
}

.source-type-safety {
  border-left: 3px solid #E6A23C;
}



/* 参数分组样式 */
.param-group {
  border: 1px solid #EBEEF5;
  border-radius: 6px;
  padding: 15px;
  margin: 10px 0;
  background-color: #FAFAFA;

  .param-group-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #E4E7ED;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}



/* 高级参数区域样式 */
.advanced-params {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
  border: 1px solid #E4E7ED;
}

/* 产能约束参数样式 */
.capacity-params {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
  border: 1px solid #E6A23C;
}

/* 第一期暂不支持仿真参数样式 */
/* .simulation-params {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
  border: 1px solid #67C23A;
} */

/* 头部按钮样式 */
.header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-buttons .el-button {
  margin-left: 0;
}

.header-buttons .el-button + .el-button {
  margin-left: 10px;
}
</style>