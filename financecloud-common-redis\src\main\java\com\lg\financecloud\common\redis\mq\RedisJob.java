package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.common.redis.utils.RedisUtils;
import lombok.experimental.UtilityClass;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


/**
 *
 */
@UtilityClass
public   final class RedisJob{
    private static Logger log = LoggerFactory.getLogger(RedisJob.class);

    Map<String,RScheduledExecutorService> executorServiceMap = new HashMap<>();

    private static RScheduledExecutorService scheduledExecutorService;

    public RScheduledExecutorService getScheduledExecutorService() {
        return scheduledExecutorService;
    }


    public RScheduledExecutorService getScheduledExecutorService(String moduleName) {
       return  executorServiceMap.get(moduleName);
    }


//    MqDbDao mqDbDao;
//    public MqDbDao getMqDbDao() {
//        if(mqDbDao==null){
//            DAO dao  = SpringUtil.getBean(DAO.class);
//            MqDbDao mdao = new MqDbDao(dao);
//             mqDbDao = mdao;
//            return mdao;
//        }
//       return mqDbDao;
//
//    }

    /***
     * 跨模块 跨进程调用
     * @param task
     * @param muduleName
     */
    /***
     * 跨模块 跨进程调用
     * @deprecated 不推荐使用，因为直接创建的 JobConsumer 不支持任务恢复。
     *             推荐使用 submitJob(BaseTask, String, String) 方法，内部会自动创建支持恢复的 JobConsumer。
     * @param task JobConsumer 任务
     * @param muduleName 模块名
     */
    @Deprecated
    public void submitJob(JobConsumer task, String muduleName){
        insertJobLog(task.getData(), true);
        RExecutorFuture<?> submit = getScheduledExecutorService(muduleName).submit(task);
    }

    /**
     * 跨模块提交任务并指定监听器类名（支持恢复）- 简化 API
     * 内部组合调用 getJobConsumer + submitJob，避免用户两次调用
     * @param task 基础任务
     * @param listenerClassName 监听器类名，用于任务恢复
     * @param moduleName 模块名
     */
    public void submitJob(BaseTask task, String listenerClassName, String moduleName) {
        JobConsumer consumer = getJobConsumer(task, listenerClassName);
        submitJob(consumer, moduleName);
    }



    /***
     * 提交分布式任务 适用于本模块调用，不需要定义监听类
     * @param jobId
     * @param task
     */
    public void submitJob(String jobId,JobConsumer task) {
        task.getData().setTaskIdentity(jobId);
        insertJobLog(task.getData(), true);
        RExecutorFuture<?> submit = scheduledExecutorService.submit(jobId, task);

    }



    /**
     * 提交 JobConsumer 任务
     * @deprecated 不推荐使用，因为直接创建的 JobConsumer 不支持任务恢复。
     *             推荐使用 submitJob(BaseTask, String) 方法，内部会自动创建支持恢复的 JobConsumer。
     * @param task JobConsumer 任务
     */
    @Deprecated
    public void submitJob(JobConsumer task) {
        insertJobLog(task.getData(), true);
        RExecutorFuture<?> submit = scheduledExecutorService.submit(task);
    }

    /**
     * 提交任务并指定监听器类名（支持恢复）- 简化 API
     * 内部组合调用 getJobConsumer + submitJob，避免用户两次调用
     * @param task 基础任务
     * @param listenerClassName 监听器类名，用于任务恢复
     */
    public void submitJob(BaseTask task, String listenerClassName) {
        JobConsumer consumer = getJobConsumer(task, listenerClassName);
        submitJob(consumer);
    }

    private static void insertJobLog(BaseTask data) {
        insertJobLog(data, false);
    }

    private static void insertJobLog(BaseTask data, boolean isJobConsumer) {
        try {
            // 优化：所有入库任务都补全监听器信息（即使没有listener参数也补全字段，避免恢复时推断失败）
            enhanceTaskWithListenerInfo(data, null);
            if(StrUtil.isEmpty(data.getTaskIdentity())){
                data.setTaskIdentity(IdUtil.getSnowflakeNextIdStr());
            }
            MqDbDao customDao = new MqDbDao();
            if (isJobConsumer) {
                customDao.insertMqLogForJobConsumer(data);
            } else {
                customDao.insertMqLog(data);
            }
        } catch (Exception e) {
           log.error("插入任务日志失败",e);
        }
    }


    /***
     * 提交延迟任务
     * @deprecated 不推荐使用，因为直接创建的 JobConsumer 不支持任务恢复。
     *             推荐使用 submitJob(BaseTask, String, Duration) 方法，内部会自动创建支持恢复的 JobConsumer。
     * @param task JobConsumer 任务
     * @param delay 延迟时间
     */
    @Deprecated
    public void submitJob(JobConsumer task, Duration delay) {
        insertJobLog(task.getData(), true);
        RScheduledFuture<?> schedule = scheduledExecutorService.schedule(task.getData().getTaskIdentity(), task, delay);
    }

    /**
     * 提交延迟任务并指定监听器类名（支持恢复）- 简化 API
     * 内部组合调用 getJobConsumer + submitJob，避免用户两次调用
     * @param task 基础任务
     * @param listenerClassName 监听器类名，用于任务恢复
     * @param delay 延迟时间
     */
    public void submitJob(BaseTask task, String listenerClassName, Duration delay) {
        JobConsumer consumer = getJobConsumer(task, listenerClassName);
        submitJob(consumer, delay);
    }


    /****
     * 提交任务 适用于本模块调用，定义监听类
     * @param task
     * @param listener
     * @return
     */
    public String submitJob(BaseTask task, RedisAmqListener  listener) {
        // 在提交任务前，尝试设置监听器类名信息以便恢复时使用
        enhanceTaskWithListenerInfo(task, listener);
        return submitJob(task,listener,null);
    }

    /****
     * 批量提交延时任务
     *
     * @param tasks
     * @param listener
     * @param duration
     * @throws Exception
     */
    public void batchSubmitJob(List<BaseTask> tasks, RedisAmqListener  listener,Duration duration) throws Exception {
        if(tasks==null || tasks.size()==0){
            return;
        }
        for(BaseTask task:tasks){
            if(StrUtil.isBlank(task.getTaskIdentity())){
                task.setTaskIdentity(IdUtil.getSnowflakeNextIdStr());
            }
        }
        tasks.forEach(task->{
            if(duration!=null) {
                submitJob(task, listener, duration);
            }else{
                submitJob(task, listener);
            }
        });

    }
    public void batchSubmitJob(List<BaseTask> tasks, RedisAmqListener  listener) throws Exception {
        batchSubmitJob(tasks,listener,null);
    }


    public String submitJob(BaseTask task, RedisAmqListener  listener, Duration delay) {
        // 在提交任务前，尝试设置监听器类名信息以便恢复时使用
        enhanceTaskWithListenerInfo(task, listener);

        insertJobLog(task);
        JobConsumer jobConsumer = new JobConsumer(task, x->{
            BaseTask baseTask = (BaseTask) x;
            listener.doExecute(baseTask);
        });
        log.info("【submitJob】开始提交任务，moduleName={}", scheduledExecutorService.getClass().getName());
        if(delay!=null){
            scheduledExecutorService.schedule(task.getTaskIdentity(),jobConsumer,delay);
        }else {
            scheduledExecutorService.submit(task.getTaskIdentity(),jobConsumer);
        }
        return task.getTaskIdentity();
    }

    /**
     * 重试任务
     */
    public String retryTask(String taskIdentity, RedisAmqListener listener) {
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto taskDto = mqDbDao.getQueueLogById(taskIdentity);
            if (taskDto == null) {
                log.warn("重试任务失败，任务不存在: {}", taskIdentity);
                return null;
            }

            // 增加重试次数
            mqDbDao.incrementRetryCount(taskIdentity);

            // 重置任务状态
            mqDbDao.resetTaskToWaiting(taskIdentity, "任务重试中");

            // 解析任务数据
            BaseTask task = JSONUtil.parseObj(taskDto.getRequestBody()).toBean(BaseTask.class);
            task.setTaskIdentity(taskIdentity);

            // 重新提交任务
            return submitJob(task, listener, null);

        } catch (Exception e) {
            log.error("重试任务失败: {}", taskIdentity, e);
            return null;
        }
    }

    public String  submitScheduleJob(BaseTask task, RedisAmqListener  listener, String cron) {
        if(StrUtil.isBlank(task.getTaskIdentity())){
            task.setTaskIdentity(IdUtil.getSnowflakeNextIdStr());
        }

        // 在提交任务前，尝试设置监听器类名信息以便恢复时使用
        enhanceTaskWithListenerInfo(task, listener);

        // 记录定时任务日志
        insertJobLog(task);

        if(scheduledExecutorService.getTaskIds().contains(task.getQueueCode())){
            scheduledExecutorService.cancelTask(task.getQueueCode());
        }
        JobConsumer jobConsumer =new JobConsumer(task,x->{
            BaseTask baseTask = (BaseTask) x;
            listener.doExecute(baseTask);
        });
        scheduledExecutorService.schedule(task.getQueueCode(),jobConsumer,CronSchedule.of(cron));
        return task.getTaskIdentity();
    }

    public void cancelScheduleJob(String jobCode){
        scheduledExecutorService.cancelTask(jobCode);
    }
    public  void batchCancelScheduleJob(List<String> jobCodes){
        jobCodes.forEach(jobCode->{
            scheduledExecutorService.cancelTask(jobCode);
        });
    }



    public static String jobChannel(String moduleName){
        String env = SpringUtil.getProperty("ENV");
        if(StrUtil.equals("local",env)){
            String host = NetUtil.getLocalhostStr();
            return "job-"+ host+"-"+moduleName;
        }else {
            return "job-" + moduleName;
        }
    }
    public static void registerJob(String moduleName){
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        String env = SpringUtil.getProperty("ENV");

        scheduledExecutorService = redissonClient.getExecutorService(jobChannel(moduleName));
        WorkerOptions workerOptions = WorkerOptions.defaults();
        workerOptions.workers(5);
        scheduledExecutorService.registerWorkers(workerOptions);
        executorServiceMap.put(moduleName,scheduledExecutorService);

        //重启所有定时任务
        startAllScheduleJob(moduleName);


    }

    private static void startAllScheduleJob(String moduleName) {
        GenericDao<SysTask, String> dao = DaoHelper.daoString(SysTask.class);
        SysTask query  = new SysTask();
        List<SysTask> sysTasks = dao.lambdaQuery().eq(SysTask::getModule,moduleName).eq(SysTask::getStatus,SysTask.STATUS_NORMAL).list();
        sysTasks.forEach(task->{
            RedisAmqListener listener = newListennerFromClass(task.getClassPath());
            BaseTask baseTask = newTaskInstance(listener);
            baseTask.setQueueCode(task.getTaskCode());
            submitScheduleJob(baseTask,newListennerFromClass(task.getClassPath()),task.getCron());
        });
    }

    public static void registerOtherModuleJobs(List<String> moduleNames){
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        if(CollectionUtil.isNotEmpty(moduleNames)){
            moduleNames.forEach(moduleName->{
             RScheduledExecutorService   scheduledExecutorService =  redissonClient.getExecutorService(jobChannel(moduleName));
                executorServiceMap.put(moduleName,scheduledExecutorService);
            }
            );
        }

    }


    public RedisAmqListener newListennerFromClass(String classPath) {
        Object listenner = ReflectUtil.newInstance(classPath);
        return (RedisAmqListener) listenner;

    }

    public BaseTask newTaskInstance(RedisAmqListener listener) {
            RedisAmqListener redisAmqListener = (RedisAmqListener) listener;
            // 获取redisAmqListener的 泛型
            Class<?> resolveGenericType = resolveGenericType(listener);
           return (BaseTask) ReflectUtil.newInstance(resolveGenericType);
    }

        public static Class<?> resolveGenericType(Object instance) {
            Type genericSuperclass = instance.getClass().getGenericSuperclass();
            if (genericSuperclass instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                if (actualTypeArguments.length > 0) {
                    return resolveClassType(actualTypeArguments[0]);
                }
            }
            return null; // 如果没有找到泛型参数或不是Class类型，则返回null
        }
        
        /**
         * 安全地解析Type为Class类型
         * 处理各种泛型类型情况
         */
        private static Class<?> resolveClassType(Type type) {
            if (type instanceof Class) {
                return (Class<?>) type;
            } else if (type instanceof ParameterizedType) {
                // 对于嵌套泛型，获取原始类型
                ParameterizedType parameterizedType = (ParameterizedType) type;
                Type rawType = parameterizedType.getRawType();
                if (rawType instanceof Class) {
                    return (Class<?>) rawType;
                }
            } else if (type instanceof java.lang.reflect.GenericArrayType) {
                // 处理泛型数组类型
                java.lang.reflect.GenericArrayType arrayType = (java.lang.reflect.GenericArrayType) type;
                Type componentType = arrayType.getGenericComponentType();
                if (componentType instanceof Class) {
                    return (Class<?>) componentType;
                }
            }
            
            // 如果无法解析，返回null
            return null;
        }



    public  JobConsumer getJobConsumer(String json, String consumerClassPath) {
     return getJobConsumer(json,consumerClassPath);
    }


    /**
     * 创建支持恢复的 JobConsumer
     * @deprecated 不推荐直接使用此方法，推荐使用简化的 submitJob(BaseTask, String) 系列方法，
     *             这些方法内部会自动调用 getJobConsumer 并提交任务，避免两次方法调用。
     * @param baseTask 基础任务
     * @param consumerClassPath 监听器类路径
     * @return 支持恢复的 JobConsumer
     */
    @Deprecated
    public  JobConsumer getJobConsumer(BaseTask baseTask, String consumerClassPath) {
        // 为了支持恢复机制，将监听器类名保存到任务数据中
        enhanceTaskWithListenerClassPath(baseTask, consumerClassPath);

        return new JobConsumer(baseTask, t -> {
            // 跨模块提交job , 接收方 在其他模块 这里的 监听类 是在其他模块定义好的
            RedisAmqListener listener = RedisJob.newListennerFromClass(consumerClassPath);
            try {
                Class<?> resolveGenericType = RedisJob.resolveGenericType(listener);
                listener.doExecute(baseTask);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }


    public void registerMqAdminEvent(String module){
        RedisUtils.subscribe(RedisJob.jobChannel(module), String.class, x->{
            JSONObject msg = JSONUtil.parseObj(x);
            String msgType = msg.getStr("msgType");


            if(msgType.equals("batchStartTask")){
                List<SysTask> sysTaskList = msg.getJSONArray("taskList").toList(SysTask.class);
                sysTaskList.forEach(sysTask -> {
                    RedisAmqListener redisAmqListener = RedisJob.newListennerFromClass(sysTask.getClassPath());
                    BaseTask baseTask = RedisJob.newTaskInstance(redisAmqListener);
                    baseTask.setQueueCode(sysTask.getTaskCode());
                    RedisJob.submitScheduleJob(baseTask , redisAmqListener, sysTask.getCron());
                } );

            }
            if(msgType.equals("batchStopTask")){
                List<SysTask> sysTaskList = msg.getJSONArray("taskList").toList(SysTask.class);
                RedisJob.batchCancelScheduleJob(sysTaskList.stream().map(SysTask::getTaskCode).collect(Collectors.toList()));
            }
            if(msgType.equals("scheduleTask")){
               SysTask sysTask = msg.getBean("task",SysTask.class);
                String taskDataJson = StrUtil.emptyToDefault(msg.getStr("taskDataJson"),sysTask.getContextParam());
                RedisAmqListener redisAmqListener = RedisJob.newListennerFromClass(sysTask.getClassPath());
                BaseTask baseTask = RedisJob.newTaskInstance(redisAmqListener);
                baseTask.setTaskData(JSONUtil.parseObj(taskDataJson));
                RedisJob.submitJob( baseTask,redisAmqListener);
            }


        });
    }

    /**
     * 获取任务状态
     */
    public String getTaskStatus(String taskIdentity) {
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto taskDto = mqDbDao.getQueueLogById(taskIdentity);
            return taskDto != null ? taskDto.getStatus() : null;
        } catch (Exception e) {
            log.error("获取任务状态失败: {}", taskIdentity, e);
            return null;
        }
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskIdentity) {
        try {
            MqDbDao mqDbDao = new MqDbDao();
            return mqDbDao.updateMqLogState(taskIdentity, "任务已取消", BaseTask.TASK_STATE_FAIL) > 0;
        } catch (Exception e) {
            log.error("取消任务失败: {}", taskIdentity, e);
            return false;
        }
    }

    /**
     * 批量取消任务
     */
    public void batchCancelTasks(List<String> taskIdentities) {
        if (CollectionUtil.isEmpty(taskIdentities)) {
            return;
        }

        taskIdentities.forEach(taskId -> {
            try {
                cancelTask(taskId);
            } catch (Exception e) {
                log.error("批量取消任务失败: {}", taskId, e);
            }
        });
    }

    /**
     * 增强任务信息，添加监听器类名以便恢复时使用
     * 注意：不在 jobDesc 中添加监听器信息，避免暴露给前端用户
     */
    private static void enhanceTaskWithListenerInfo(BaseTask task, RedisAmqListener listener) {
        try {
            if (listener != null) {
                String listenerClassName = listener.getClass().getName();
                enhanceTaskWithListenerClassPath(task, listenerClassName);
            }
        } catch (Exception e) {
            log.warn("增强任务监听器信息失败: {}", task.getTaskIdentity(), e);
        }
    }

    /**
     * 增强任务信息，添加监听器类路径以便恢复时使用
     * 用于支持 JobConsumer 方式提交的任务恢复
     */
    private static void enhanceTaskWithListenerClassPath(BaseTask task, String listenerClassName) {
        try {
            if (StrUtil.isNotBlank(listenerClassName)) {
                // 方法1: 尝试通过反射设置 listenerClass 字段（如果存在）
                try {
                    java.lang.reflect.Field listenerClassField = task.getClass().getDeclaredField("listenerClass");
                    listenerClassField.setAccessible(true);
                    listenerClassField.set(task, listenerClassName);
                    log.debug("通过反射设置监听器类名: {} -> {}", task.getTaskIdentity(), listenerClassName);
                } catch (NoSuchFieldException e) {
                    // 字段不存在，忽略
                    log.debug("任务类不包含 listenerClass 字段: {}", task.getClass().getName());
                } catch (Exception e) {
                    log.debug("设置监听器类名失败: {}", e.getMessage());
                }

                // 方法2: 将监听器信息添加到 taskData 的内部字段中（前端不可见）
                if (task.getTaskData() == null) {
                    task.setTaskData(new JSONObject());
                }
                task.getTaskData().put("_internal_listenerClass", listenerClassName);
                task.getTaskData().put("_internal_listenerSimpleName", StrUtil.subAfter(listenerClassName, ".", true));
                log.debug("将监听器信息添加到任务数据中: {} -> {}", task.getTaskIdentity(), listenerClassName);
            }
        } catch (Exception e) {
            log.warn("增强任务监听器类路径信息失败: {}", task.getTaskIdentity(), e);
        }
    }



}


