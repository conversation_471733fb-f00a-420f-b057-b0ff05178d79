package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteUreportService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteUreportService {

    @PostMapping("/ureport/ureportExport")
    Response export( @RequestBody String reportParam) ;
    @PostMapping("/ureport/ureportBatchPrint")
    Response batchPrint( @RequestBody String reportParam) ;
}
