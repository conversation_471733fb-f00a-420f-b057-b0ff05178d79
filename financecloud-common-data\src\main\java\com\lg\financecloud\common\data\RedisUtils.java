package com.lg.financecloud.common.data;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.lg.financecloud.common.core.constant.CacheConstants;
import com.lg.financecloud.common.core.util.SpringContextHolder;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.redisson.api.*;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * redis 工具类
 *
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@SuppressWarnings(value = { "unchecked", "rawtypes" })
public class RedisUtils {

	// 新增：监听器注册表（线程安全）
	private static final ConcurrentMap<String, Integer> LISTENER_REGISTRY = new ConcurrentHashMap<>();


	public static NameMapper getNameMapper() {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		Config config = CLIENT.getConfig();
		if (config.isClusterConfig()) {
			return config.useClusterServers().getNameMapper();
		}
		return config.useSingleServer().getNameMapper();
	}

	/**
	 * 限流
	 * @param key 限流key
	 * @param rateType 限流类型
	 * @param rate 速率
	 * @param rateInterval 速率间隔
	 * @return -1 表示失败
	 */
	public static long rateLimiter(String key, RateType rateType, int rate, int rateInterval) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RRateLimiter rateLimiter = CLIENT.getRateLimiter(key);
		rateLimiter.trySetRate(rateType, rate, rateInterval, RateIntervalUnit.SECONDS);
		if (rateLimiter.tryAcquire()) {
			return rateLimiter.availablePermits();
		}
		else {
			return -1L;
		}
	}

	/**
	 * 获取客户端实例
	 */
	public static RedissonClient getClient() {

		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);

		return CLIENT;
	}


	/**
	 * 发布通道消息
	 * @param channelKey 通道key
	 * @param msg 发送数据
	 * @param consumer 自定义处理
	 */
	public static <T> void publish(String channelKey,Class clazz, T msg, Consumer<T> consumer) {

		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RTopic topic = CLIENT.getTopic(channelKey);
		// 检查是否已注册
		if (!LISTENER_REGISTRY.containsKey(channelKey)) {
			int i = topic.addListener(clazz, (channel, recvMsg) -> consumer.accept((T) recvMsg));
			LISTENER_REGISTRY.put(channelKey, i);
		}
		topic.publish(msg);
//		consumer.accept((T) msg);
	}

	public static <T> void publish(String channelKey, T msg) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RTopic topic = CLIENT.getTopic(channelKey);
		topic.publish(msg);
	}

	/**
	 * 订阅通道接收消息
	 * @param channelKey 通道key
	 * @param clazz 消息类型
	 * @param consumer 自定义处理
	 */
	public static <T> void subscribe(String channelKey, Class<T> clazz, Consumer<T> consumer) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RTopic topic = CLIENT.getTopic(channelKey);
		topic.addListener(clazz, (channel, msg) -> consumer.accept(msg));
	}

	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 */
	public static <T> void setCacheObject(  String key, final T value) {
		setCacheObject(key, value, false);
	}

	/**
	 * 缓存基本的对象，保留当前对象 TTL 有效期
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @param isSaveTtl 是否保留TTL有效期(例如: set之前ttl剩余90 set之后还是为90)
	 * @since Redis 6.X 以上使用 setAndKeepTTL 兼容 5.X 方案
	 */
	public static <T> void setCacheObject(  String key, final T value, final boolean isSaveTtl) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBucket<T> bucket = CLIENT.getBucket(key);
		if (isSaveTtl) {
			try {
				bucket.setAndKeepTTL(value);
			}
			catch (Exception e) {
				long timeToLive = bucket.remainTimeToLive();
				setCacheObject(key, value, Duration.ofMillis(timeToLive));
			}
		}
		else {
			bucket.set(value);
		}
	}

	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 * @param key 缓存的键值
	 * @param value 缓存的值
	 * @param duration 时间
	 */
	public static <T> void setCacheObject(  String key, final T value, final Duration duration) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBatch batch = CLIENT.createBatch();
		RBucketAsync<T> bucket = batch.getBucket(key);
		bucket.setAsync(value);
		bucket.expireAsync(duration);
		batch.execute();
	}

	/**
	 * 注册对象监听器
	 * <p>
	 * key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置
	 * @param key 缓存的键值
	 * @param listener 监听器配置
	 */
	public static <T> void addObjectListener(  String key, final ObjectListener listener) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBucket<T> result = CLIENT.getBucket(key);
		result.addListener(listener);
	}

	/**
	 * 设置有效时间
	 * @param key Redis键
	 * @param timeout 超时时间
	 * @return true=设置成功；false=设置失败
	 */
	public static boolean expire(  String key, final long timeout) {
		key = genKey(key);
		return expire(key, Duration.ofSeconds(timeout));
	}

	/**
	 * 设置有效时间
	 * @param key Redis键
	 * @param duration 超时时间
	 * @return true=设置成功；false=设置失败
	 */
	public static boolean expire(  String key, final Duration duration) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBucket rBucket = CLIENT.getBucket(key);
		return rBucket.expire(duration);
	}

	/**
	 * 获得缓存的基本对象。
	 * @param key 缓存键值
	 * @return 缓存键值对应的数据
	 */
	public static <T> T getCacheObject(  String key) {
		return getCacheObject(key,false);
	}
	/**
	 * 获得缓存的基本对象。
	 * @param key 缓存键值
	 * @return 缓存键值对应的数据
	 */
	public static <T> T getCacheObject(  String key,Boolean resetTimeout) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBucket<T> rBucket = CLIENT.getBucket(key);
		if(resetTimeout){
			rBucket.expire(Duration.ofMillis(CLIENT.getBucket(key).remainTimeToLive()));
		}
		return rBucket.get();
	}

	/**
	 * 获得key剩余存活时间
	 * @param key 缓存键值
	 * @return 剩余存活时间
	 */
	public static <T> long getTimeToLive(  String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RBucket<T> rBucket = CLIENT.getBucket(key);
		return rBucket.remainTimeToLive();
	}

	/**
	 * 删除单个对象
	 * @param key 缓存的键值
	 */
	public static boolean deleteObject(  String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		return CLIENT.getBucket(key).delete();
	}

	/**
	 * 删除集合对象
	 * @param collection 多个对象
	 */
	public static void deleteObject(final Collection collection) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RBatch batch = CLIENT.createBatch();
		collection.forEach(t -> {
			String s = genKey(t.toString());
			batch.getBucket(s).deleteAsync();
		});
		batch.execute();
	}

	/**
	 * 缓存List数据
	 * @param key 缓存的键值
	 * @param dataList 待缓存的List数据
	 * @return 缓存的对象
	 */
	public static <T> boolean setCacheList(  String key, final List<T> dataList) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RList<T> rList = CLIENT.getList(key);
		return rList.addAll(dataList);
	}

	/**
	 * 注册List监听器
	 * <p>
	 * key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置
	 * @param key 缓存的键值
	 * @param listener 监听器配置
	 */
	public static <T> void addListListener(  String key, final ObjectListener listener) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RList<T> rList = CLIENT.getList(key);
		rList.addListener(listener);
	}

	/**
	 * 获得缓存的list对象
	 * @param key 缓存的键值
	 * @return 缓存键值对应的数据
	 */
	public static <T> List<T> getCacheList(  String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RList<T> rList = CLIENT.getList(key);
		return rList.readAll();
	}

	/**
	 * 缓存Set
	 * @param key 缓存键值
	 * @param dataSet 缓存的数据
	 * @return 缓存数据的对象
	 */
	public static <T> boolean setCacheSet(  String key, final Set<T> dataSet) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RSet<T> rSet = CLIENT.getSet(key);
		return rSet.addAll(dataSet);
	}

	/**
	 * 注册Set监听器
	 * <p>
	 * key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置
	 * @param key 缓存的键值
	 * @param listener 监听器配置
	 */
	public static <T> void addSetListener(  String key, final ObjectListener listener) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RSet<T> rSet = CLIENT.getSet(key);
		rSet.addListener(listener);
	}

	/**
	 * 获得缓存的set
	 * @param key 缓存的key
	 * @return set对象
	 */
	public static <T> Set<T> getCacheSet(  String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RSet<T> rSet = CLIENT.getSet(key);
		return rSet.readAll();
	}

	/**
	 * 缓存Map
	 * @param key 缓存的键值
	 * @param dataMap 缓存的数据
	 */
	public static <T> void setCacheMap(  String key, final Map<String, T> dataMap) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		if (dataMap != null) {
			RMap<String, T> rMap = CLIENT.getMap(key);
			rMap.putAll(dataMap);
		}
	}

	/**
	 * 注册Map监听器
	 * <p>
	 * key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置
	 * @param key 缓存的键值
	 * @param listener 监听器配置
	 */
	public static <T> void addMapListener(  String key, final ObjectListener listener) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RMap<String, T> rMap = CLIENT.getMap(key);
		rMap.addListener(listener);
	}

	/**
	 * 获得缓存的Map
	 * @param key 缓存的键值
	 * @return map对象
	 */
	public static <T> Map<String, T> getCacheMap(  String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RMap<String, T> rMap = CLIENT.getMap(key);
		return rMap.getAll(rMap.keySet());
	}

	/**
	 * 往Hash中存入数据
	 * @param key Redis键
	 * @param hKey Hash键
	 * @param value 值
	 */
	public static <T> void setCacheMapValue(  String key, final String hKey, final T value) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RMap<String, T> rMap = CLIENT.getMap(key);
		rMap.put(hKey, value);
	}

	/**
	 * 获取Hash中的数据
	 * @param key Redis键
	 * @param hKey Hash键
	 * @return Hash中的对象
	 */
	public static <T> T getCacheMapValue( String key, final String hKey) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RMap<String, T> rMap = CLIENT.getMap(key);
		return rMap.get(hKey);
	}

	/**
	 * 删除Hash中的数据
	 * @param key Redis键
	 * @param hKey Hash键
	 * @return Hash中的对象
	 */
	public static <T> T delCacheMapValue( String key, final String hKey) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RMap<String, T> rMap = CLIENT.getMap(key);
		return rMap.remove(hKey);
	}

	/**
	 * 获取多个Hash中的数据
	 * @param key Redis键
	 * @param hKeys Hash键集合
	 * @return Hash对象集合
	 */
	public static <K, V> Map<K, V> getMultiCacheMapValue(final String key, final Set<K> hKeys) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);

		RMap<K, V> rMap = CLIENT.getMap(key);
		return rMap.getAll(hKeys);
	}

	/**
	 * 设置原子值
	 * @param key Redis键
	 * @param value 值
	 */
	public static void setAtomicValue(String key, long value) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RAtomicLong atomic = CLIENT.getAtomicLong(key);
		atomic.set(value);
	}

	/**
	 * 获取原子值
	 * @param key Redis键
	 * @return 当前值
	 */
	public static long getAtomicValue(String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RAtomicLong atomic = CLIENT.getAtomicLong(key);
		return atomic.get();
	}

	/**
	 * 递增原子值
	 * @param key Redis键
	 * @return 当前值
	 */
	public static long incrAtomicValue(String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RAtomicLong atomic = CLIENT.getAtomicLong(key);
		return atomic.incrementAndGet();
	}

	/**
	 * 递减原子值
	 * @param key Redis键
	 * @return 当前值
	 */
	public static long decrAtomicValue(String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RAtomicLong atomic = CLIENT.getAtomicLong(key);
		return atomic.decrementAndGet();
	}

	/**
	 * 获得缓存的基本对象列表
	 * @param pattern 字符串前缀
	 * @return 对象列表
	 */
	public static Collection<String> keys(final String pattern) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		Stream<String> stream = CLIENT.getKeys().getKeysStreamByPattern(getNameMapper().map(pattern));
		return stream.map(key -> getNameMapper().unmap(key)).collect(Collectors.toList());
	}

	/**
	 * 删除缓存的基本对象列表
	 * @param pattern 字符串前缀
	 */
	public static void deleteKeys(final String pattern) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);

		CLIENT.getKeys().deleteByPattern(getNameMapper().map(pattern));
	}

	/**
	 * 检查redis中是否存在key
	 * @param key 键
	 */
	public static Boolean hasKey(String key) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		key = genKey(key);
		RKeys rKeys = CLIENT.getKeys();
		return rKeys.countExists(getNameMapper().map(key)) > 0;
	}

	public  static String genKey(String key){
//		return key;
		// see https://gitee.wang/pig/cloudx/issues/613
		if (key.startsWith(CacheConstants.GLOBALLY)) {
			return key;
		}
		Integer tenantId = TenantContextHolder.getTenantId();
		tenantId =tenantId==null ? 1 :tenantId;
		return  tenantId+ StrUtil.COLON + key ;
	}


	/**
	 * 尝试获取分布式锁
	 * @param lockKey 锁的键值
	 * @param waitTime 尝试加锁等待时间（单位：秒）
	 * @param leaseTime 上锁后自动释放时间（单位：秒）
	 * @return 是否获取锁成功
	 */
	public static boolean tryLock(String lockKey, long waitTime, long leaseTime) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RLock lock = CLIENT.getLock(genKey(lockKey+"_lock"));
		try {
			return lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			return false;
		}
	}

	/**
	 * 释放分布式锁
	 * @param lockKey 锁的键值
	 */
	public static void unlock(String lockKey) {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RLock lock = CLIENT.getLock(genKey(lockKey+"_lock"));
		lock.unlock();
	}
	public static boolean tryLock(String lockKey, long waitTime)  {
		RedissonClient CLIENT = SpringContextHolder.getBean(RedissonClient.class);
		RLock lock = CLIENT.getLock(genKey(lockKey+"_lock"));
        try {
            return lock.tryLock(waitTime, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
			Thread.currentThread().interrupt();
            return  false;
        }
    }


	public static boolean mutexTryLock(String lockKey, Long waitTime, Long leaseTime)  {
		RedisLock redisLock = new RedisLock(lockKey+"_mutextLock",waitTime.intValue(),leaseTime.intValue());
		try {
			return redisLock.lock();
		}catch (InterruptedException e){
			return false;
		}
	}

	public static void mutexUnLock(String lockKey)  {
		   new RedisLock(lockKey+"_mutextLock").unlock();
	}

	static  final class RedisLock {

		private static Logger logger = LoggerFactory.getLogger(RedisLock.class);

		private RedisTemplate redisTemplate;

		private static final int DEFAULT_ACQUIRY_RESOLUTION_MILLIS = 100;

		/**
		 * Lock key path.
		 */
		private String lockKey;

		/**
		 * 锁超时时间，防止线程在入锁以后，无限的执行等待，默认60秒
		 */
		private int expireMsecs = 60 * 1000;

		/**
		 * 锁等待时间，防止线程饥饿(也就是线程等待时间)，默认10秒
		 */
		private int timeoutMsecs = 10 * 1000;

		private volatile boolean locked = false;

		/**
		 * Detailed constructor with default acquire timeout 10000 msecs and lock expiration of 60000 msecs.
		 *
		 * @param lockKey lock key (ex. account:1, ...)
		 */
		public RedisLock(String lockKey) {
			this.lockKey = lockKey + "_lock";
			redisTemplate = SpringUtil.getBean(RedisTemplate.class);
		}

		/**
		 * Detailed constructor with default lock expiration of 60000 msecs.
		 */
		public RedisLock(String lockKey, int timeoutMsecs) {
			this(lockKey);
			this.timeoutMsecs = timeoutMsecs;
		}

		/**
		 * Detailed constructor.
		 */
		public RedisLock(String lockKey, int timeoutMsecs, int expireMsecs) {
			this(lockKey, timeoutMsecs);
			this.expireMsecs = expireMsecs;
		}

		/**
		 * @return lock key
		 */
		public String getLockKey() {
			return lockKey;
		}

		private String get(final String key) {
			Object obj = null;
			try {
				obj = redisTemplate.execute(new RedisCallback<Object>() {
					@Override
					public Object doInRedis(RedisConnection connection) throws DataAccessException {
						StringRedisSerializer serializer = new StringRedisSerializer();
						byte[] data = connection.get(serializer.serialize(key));
						connection.close();
						if (data == null) {
							return null;
						}
						return serializer.deserialize(data);
					}
				});
				logger.error("get redis success, key is : {}", key);
			} catch (Exception e) {
				logger.error("get redis error, key : {}", key);
			}
			return obj != null ? obj.toString() : null;
		}

		private boolean setNX(final String key, final String value) {
			Object obj = null;
			try {
				obj = redisTemplate.execute(new RedisCallback<Object>() {
					@Override
					public Object doInRedis(RedisConnection connection) throws DataAccessException {
						StringRedisSerializer serializer = new StringRedisSerializer();
						byte[] serialize = serializer.serialize(key);
						Boolean success = connection.setNX(serialize, serializer.serialize(value));
						connection.pExpire(serialize,expireMsecs);
						connection.close();
						return success;
					}
				});
			} catch (Exception e) {
				logger.error("setNX redis error, key : {}", key, e);
			}
			return obj != null ? (Boolean) obj : false;
		}

		private String getSet(final String key, final String value) {
			Object obj = null;
			try {
				obj = redisTemplate.execute(new RedisCallback<Object>() {
					@Override
					public Object doInRedis(RedisConnection connection) throws DataAccessException {
						StringRedisSerializer serializer = new StringRedisSerializer();
						byte[] ret = connection.getSet(serializer.serialize(key), serializer.serialize(value));
						connection.close();
						return serializer.deserialize(ret);
					}
				});
			} catch (Exception e) {
				logger.error("setNX redis error, key : {}", key);
			}
			return obj != null ? (String) obj : null;
		}

		/**
		 * 获得 lock.
		 * 实现思路: 主要是使用了redis 的setnx命令,缓存了锁.
		 * reids缓存的key是锁的key,所有的共享, value是锁的到期时间(注意:这里把过期时间放在value了,没有时间上设置其超时时间)
		 * 执行过程:
		 * 1.通过setnx尝试设置某个key的值,成功(当前没有这个锁)则返回,成功获得锁
		 * 2.锁已经存在则获取锁的到期时间,和当前时间比较,超时的话,则设置新的值
		 *
		 * @return true if lock is acquired, false acquire timeouted
		 * @throws InterruptedException in case of thread interruption
		 */
		public synchronized boolean lock() throws InterruptedException {
			int timeout = timeoutMsecs;
			while (timeout >= 0) {
				long expires = System.currentTimeMillis() + expireMsecs + 1;
				String expiresStr = String.valueOf(expires); // 锁到期时间
				if (this.setNX(lockKey, expiresStr)) {
					// lock acquired
					locked = true;
					return true;
				}

				String currentValueStr = this.get(lockKey); // redis里的时间
				if (currentValueStr != null && Long.parseLong(currentValueStr) < System.currentTimeMillis()) {
					// 判断是否为空，不为空的情况下，如果被其他线程设置了值，则第二个条件判断是过不去的
					// lock is expired

					String oldValueStr = this.getSet(lockKey, expiresStr);
					// 获取上一个锁到期时间，并设置现在的锁到期时间，
					// 只有一个线程才能获取上一个线上的设置时间，因为jedis.getSet是同步的
					if (oldValueStr != null && oldValueStr.equals(currentValueStr)) {
						// 防止误删（覆盖，因为key是相同的）了他人的锁——这里达不到效果，这里值会被覆盖，但是因为什么相差了很少的时间，所以可以接受

						// [分布式的情况下]:如过这个时候，多个线程恰好都到了这里，但是只有一个线程的设置值和当前值相同，他才有权利获取锁
						// lock acquired
						locked = true;
						return true;
					}
				}
				timeout -= DEFAULT_ACQUIRY_RESOLUTION_MILLIS;

            /*
                延迟100 毫秒,  这里使用随机时间可能会好一点,可以防止饥饿进程的出现,即,当同时到达多个进程,
                只会有一个进程获得锁,其他的都用同样的频率进行尝试,后面有来了一些进行,也以同样的频率申请锁,这将可能导致前面来的锁得不到满足.
                使用随机的等待时间可以一定程度上保证公平性
             */
				Thread.sleep(DEFAULT_ACQUIRY_RESOLUTION_MILLIS);
			}
			return false;
		}

		/**
		 * Acqurired lock release.
		 */
		public synchronized void unlock() {
				redisTemplate.delete(lockKey);
		}
	}


}


