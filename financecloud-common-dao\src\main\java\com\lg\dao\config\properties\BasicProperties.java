package com.lg.dao.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * Light ORM基础配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.orm.basic")
public class BasicProperties {
    
    /**
     * 是否启用自动填充功能
     */
    private boolean enableAutoFill = true;

    /**
     * 命名策略
     */
    private NamingStrategy namingStrategy = NamingStrategy.CAMEL_TO_UNDERSCORE;

    /**
     * SQL模板配置
     */
    private SqlTemplate sqlTemplate = new SqlTemplate();

    /**
     * SQL模板位置
     */
    private List<String> sqlLocations = Arrays.asList("classpath:sql/**/*.sql");

    /**
     * 命名策略枚举
     */
    public enum NamingStrategy {
        /**
         * 不做任何转换
         */
        NO_CHANGE,

        /**
         * 驼峰转下划线
         */
        CAMEL_TO_UNDERSCORE,

        /**
         * 下划线转驼峰
         */
        UNDERSCORE_TO_CAMEL
    }

    /**
     * SQL模板配置
     */
    @Data
    public static class SqlTemplate {
        /**
         * SQL模板位置列表
         */
        private List<String> locations = Arrays.asList("classpath:sql/**/*.sql");

        /**
         * 是否启用热更新
         */
        private boolean enableHotReload = true;

        /**
         * 热更新检查间隔（秒）
         */
        private int reloadInterval = 10;

        /**
         * 模板引擎类型
         */
        private TemplateEngine engine = TemplateEngine.MYBATIS;

        /**
         * 是否启用缓存
         */
        private boolean enableCache = true;

        /**
         * 缓存大小
         */
        private int cacheSize = 500;
    }

    /**
     * 模板引擎类型
     */
    public enum TemplateEngine {
        /**
         * Velocity模板引擎
         */
        VELOCITY,

        /**
         * Freemarker模板引擎
         */
        FREEMARKER,
        
        /**
         * MyBatis模板引擎
         */
        MYBATIS
    }
} 