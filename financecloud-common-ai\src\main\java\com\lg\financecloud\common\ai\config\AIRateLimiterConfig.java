//package com.lg.financecloud.common.ai.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import java.util.Map;
//
///**
// * AI模型限流配置类
// * 用于管理各个AI模型的调用限制
// */
//@Data
//@ConfigurationProperties(prefix = "ai.limiter")
//public class AIRateLimiterConfig {
//
//    /**
//     * 是否启用限流
//     */
//    private boolean enabled = true;
//
//    /**
//     * 默认限流配置
//     */
//    private LimiterConfig defaultConfig = new LimiterConfig();
//
//    /**
//     * 各模型的限流配置
//     * key: 模型类型
//     * value: 限流配置
//     */
//    private Map<String, LimiterConfig> models;
//
//    /**
//     * 限流器配置
//     */
//    @Data
//    public static class LimiterConfig {
//        /**
//         * 每秒最大请求数
//         */
//        private int requestsPerSecond = 10;
//
//        /**
//         * 最大等待时间（毫秒）
//         */
//        private long maxWaitMs = 500;
//
//        /**
//         * 是否启用熔断
//         */
//        private boolean circuitBreakerEnabled = true;
//
//        /**
//         * 熔断错误阈值百分比
//         */
//        private float failureRateThreshold = 50;
//
//        /**
//         * 熔断后等待恢复时间（毫秒）
//         */
//        private long waitDurationInOpenState = 60000;
//
//        /**
//         * 熔断器半开状态时的环形缓冲区大小
//         */
//        private int ringBufferSizeInHalfOpenState = 10;
//
//        /**
//         * 熔断器关闭状态时的环形缓冲区大小
//         */
//        private int ringBufferSizeInClosedState = 100;
//    }
//}