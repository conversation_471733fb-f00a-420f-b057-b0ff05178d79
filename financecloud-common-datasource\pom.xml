<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>financecloud-common</artifactId>
        <groupId>com.lg</groupId>
        <version>3.10.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <!-- 移除重复的groupId -->
    <artifactId>financecloud-common-datasource</artifactId>
    <packaging>jar</packaging>
    <description>financecloud 动态切换数据源</description>

    <dependencies>
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic-ds.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>
</project>