package com.lg.financecloud.common.data.datafilter;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户运行时代理<br/>
 * 这是一个工具类，用于切换租户运行时，保护租户ID上下文<br/>
 * 下面这段代码演示问题所在 <pre>
 *     void methodA(){
 *         // 因为某些特殊原因，需要手动指定租户
 *         TenantContextHolder.setTenantId(1);
 *         // do something ...
 *     }
 *     void methodB(){
 *         // 因为某些特殊原因，需要手动指定租户
 *         TenantContextHolder.setTenantId(2);
 *         methodA();
 *         // 此时租户ID已经变成 1
 *         // do something ...
 *     }
 * </pre> 嵌套设置租户ID会导致租户上下文难以维护,并且很难察觉，容易导致数据错乱。 推荐的写法： <pre>
 *     void methodA(){
 *         TenantBroker.RunAs(1,() -> {
 *             // do something ...
 *         });
 *     }
 *     void methodB(){
 *         TenantBroker.RunAs(2,() -> {
 *              methodA();
 *             // do something ...
 *         });
 *     }
 * </pre>
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/6/12
 * @since 3.9
 */
@Slf4j
@UtilityClass
public class SoulTableExcludeBroker {

	@FunctionalInterface
	public interface RunAs {

		/**
		 * 执行业务逻辑
		 * @param excludeState
		 * @throws Exception
		 */
		void run() throws Exception;

	}

	@FunctionalInterface
	public interface ApplyAs< R> {
		/**
		 * 执行业务逻辑,返回一个值
		 * @param tenantId
		 * @return
		 * @throws Exception
		 */
		R apply() throws Exception;

	}

	public void runAs(RunAs func) {
		try {

			SoulTableInterceptor.setSoulTableInterceptorExclude();
			func.run();
		}
		catch (Exception e) {
			log.error(e.getLocalizedMessage());

		}
		finally {
			SoulTableInterceptor.cleanSoulTableInterceptorExclude();
		}
	}

	public <T> T applyAs(ApplyAs<T> func) {
		try {
			SoulTableInterceptor.setSoulTableInterceptorExclude();
			return func.apply();
		}
		catch (Exception e) {
		}
		finally {
			SoulTableInterceptor.cleanSoulTableInterceptorExclude();
		}
		return null;
	}




}
