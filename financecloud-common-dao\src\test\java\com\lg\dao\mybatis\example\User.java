package com.lg.dao.mybatis.example;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
public class User {

    @Id
    private Long id;
    
    private String username;
    
    private String email;
    
    private String status;
    
    @Column(name = "user_name")
    private String userName;
    
    private Integer age;
    
    @Column(name = "tenant_id")
    private String tenantId;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 