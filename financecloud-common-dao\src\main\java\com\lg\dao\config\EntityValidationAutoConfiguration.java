package com.lg.dao.config;

import com.lg.dao.config.properties.TenantProperties;
import com.lg.dao.core.EntityInfoRegistry;
import com.lg.dao.core.cache.UnifiedCacheManager;
import com.lg.dao.core.validation.EntitySchemaValidator;
import com.lg.dao.core.validation.ValidationResult;
import com.lg.dao.core.tenant.TenantSqlInterceptor;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.sql.DataSource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 实体架构验证自动配置
 * 自动验证实体架构与数据库一致性
 * 仅在开发环境下启用，避免影响生产环境性能
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(TenantProperties.class)
@ConditionalOnProperty(prefix = "light.orm.validation", name = "enabled", havingValue = "true", matchIfMissing = false)
public class EntityValidationAutoConfiguration {

    @Value("${light.orm.validation.packages:}")
    private String[] entityPackages;
    
    @Value("${light.orm.validation.generate-sql:true}")
    private boolean generateSql;
    
    @Value("${light.orm.validation.log-warnings:true}")
    private boolean logWarnings;
    
    @Value("${light.orm.validation.async:true}")
    private boolean asyncValidation;
    
    @Value("${light.orm.validation.dev-only:true}")
    private boolean devOnly;
    
    @Autowired
    @Qualifier("dataSource") // 确保只使用主数据源
    private DataSource dataSource;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private TenantProperties tenantProperties;
    
    @Autowired(required = false)
    private TenantSqlInterceptor tenantSqlInterceptor;
    
    @Autowired
    private Environment environment;

    @Autowired(required = false)
    protected UnifiedCacheManager unifiedCacheManager;
    
    // 实体信息注册表
    private EntityInfoRegistry entityInfoRegistry;
    
    // 验证线程池
    private final ExecutorService validationExecutor = Executors.newSingleThreadExecutor(r -> {
        Thread thread = new Thread(r, "entity-validation-thread");
        thread.setDaemon(true);
        return thread;
    });

    // 路径匹配器，用于支持通配符
    private final AntPathMatcher pathMatcher = new AntPathMatcher(".");

    @PostConstruct
    public void init() {
        // 检查是否是开发环境
        if (devOnly && !isDevEnvironment()) {
            log.info("非开发环境，跳过实体架构验证");
            return;
        }
        
        if (entityPackages == null || entityPackages.length == 0) {
            log.info("未配置验证实体包，跳过实体架构验证");
            return;
        }
        
        log.info("开始准备实体架构验证: {}", Arrays.toString(entityPackages));
        
        // 初始化实体信息注册表
        initEntityInfoRegistry();
        
        // 配置租户SQL拦截器
        if (tenantSqlInterceptor != null) {
            tenantSqlInterceptor.withDataSource(dataSource);
        }
        
        // 异步执行验证，不阻塞应用启动
        if (asyncValidation) {
            log.info("使用异步模式执行实体架构验证");
            validationExecutor.submit(this::executeValidation);
        } else {
            log.info("使用同步模式执行实体架构验证");
            executeValidation();
        }
    }
    
    /**
     * 检查当前是否为开发环境
     */
    private boolean isDevEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if (profile.contains("dev") || profile.contains("local") || profile.contains("test")) {
                return true;
            }
        }
        
        // 检查spring.profiles.active属性
        String profiles = environment.getProperty("spring.profiles.active");
        if (profiles != null) {
            for (String profile : profiles.split(",")) {
                if (profile.trim().contains("dev") || profile.trim().contains("local") || profile.trim().contains("test")) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 执行验证过程
     */
    private void executeValidation() {
        try {
            // 设置默认租户ID用于验证（如果使用多租户）
            if (tenantProperties != null && tenantProperties.isEnable()) {
                TenantContextHolder.setTenantId(1); // 使用ID为1的租户进行验证
            }
            
            try {
                List<Class<?>> entityClasses = scanEntityClasses();
                validateEntities(entityClasses);
            } catch (Exception e) {
                log.error("实体架构验证过程中出错", e);
            } finally {
                // 清除租户上下文
                if (tenantProperties != null && tenantProperties.isEnable()) {
                    TenantContextHolder.clear();
                }
            }
        } finally {
            // 关闭线程池（如果是异步模式）
            if (asyncValidation) {
                validationExecutor.shutdown();
            }
        }
    }
    
    /**
     * 初始化实体信息注册表
     */
    private void initEntityInfoRegistry() {
        entityInfoRegistry = EntityInfoRegistry.getInstance();
        
        String schema = null;
        if (tenantProperties != null && tenantProperties.isEnable() && 
                tenantProperties.getMode() == TenantProperties.TenantMode.SCHEMA) {
            schema = tenantProperties.getSchemaPrefix() + "1"; // 使用ID为1的租户schema
        }
        
        entityInfoRegistry.initialize(dataSource, schema);
    }
    
    /**
     * 创建EntitySchemaValidator bean
     */
    @Bean
    public EntitySchemaValidator entitySchemaValidator() {
        return entityInfoRegistry.getValidator();
    }
    
    /**
     * 扫描配置包中的实体类
     * 支持通配符配置，如 com.example.*.entity, com.example.module.**.entity
     */
    private List<Class<?>> scanEntityClasses() throws Exception {
        List<Class<?>> entityClasses = new ArrayList<>();
        PathMatchingResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
        CachingMetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory(resourceResolver);
        
        // 获取所有已加载的类
        Set<String> loadedClassNames = getAllLoadedClassNames();
        
        // 处理通配符包名
        Set<String> expandedPackages = expandWildcardPackages(entityPackages, loadedClassNames);
        log.info("展开通配符后的包名: {}", expandedPackages);
        
        for (String basePackage : expandedPackages) {
            String packageSearchPath = "classpath*:" + 
                    basePackage.replace('.', '/') + "/**/*.class";
            
            Resource[] resources = resourceResolver.getResources(packageSearchPath);
            for (Resource resource : resources) {
                if (resource.isReadable()) {
                    MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(resource);
                    String className = metadataReader.getClassMetadata().getClassName();
                    
                    try {
                        Class<?> clazz = Class.forName(className);
                        if (clazz.isAnnotationPresent(Entity.class) || 
                            clazz.isAnnotationPresent(Table.class) ||
                            clazz.isAnnotationPresent(com.baomidou.mybatisplus.annotation.TableName.class)) {
                            entityClasses.add(clazz);
                        }
                    } catch (Throwable e) {
                        log.warn("加载类失败: {}", className, e);
                    }
                }
            }
        }
        
        log.info("找到 {} 个实体类待验证", entityClasses.size());
        return entityClasses;
    }
    
    /**
     * 获取所有已加载的类名
     */
    private Set<String> getAllLoadedClassNames() {
        Set<String> classNames = new HashSet<>();
        
        try {
            // 获取Spring上下文中所有的bean
            String[] beanNames = applicationContext.getBeanDefinitionNames();
            for (String beanName : beanNames) {
                Object bean = applicationContext.getBean(beanName);
                if (bean != null) {
                    classNames.add(bean.getClass().getName());
                    // 添加包名
                    String packageName = ClassUtils.getPackageName(bean.getClass());
                    classNames.add(packageName);
                }
            }
            
            // 添加一些常见的包名
            classNames.add("com.lg");
            classNames.add("com.lg.dao");
            classNames.add("com.lg.financecloud");
            
        } catch (Exception e) {
            log.warn("获取已加载类名失败", e);
        }
        
        return classNames;
    }
    
    /**
     * 展开通配符包名
     * 支持 * 和 ** 通配符
     * * 表示单层匹配，如 com.example.*.entity 匹配 com.example.module.entity
     * ** 表示多层匹配，如 com.example.**.entity 匹配 com.example.module.submodule.entity
     */
    private Set<String> expandWildcardPackages(String[] wildcardPackages, Set<String> loadedClassNames) {
        Set<String> expandedPackages = new HashSet<>();
        
        for (String wildcardPackage : wildcardPackages) {
            // 如果没有通配符，直接添加
            if (!wildcardPackage.contains("*")) {
                expandedPackages.add(wildcardPackage);
                continue;
            }
            
            // 处理通配符
            boolean found = false;
            for (String className : loadedClassNames) {
                if (pathMatcher.match(wildcardPackage, className)) {
                    // 对于类名，获取其包名
                    String packageName = ClassUtils.getPackageName(className);
                    expandedPackages.add(packageName);
                    found = true;
                } else if (className.contains(".") && pathMatcher.match(wildcardPackage, className)) {
                    // 直接匹配包名
                    expandedPackages.add(className);
                    found = true;
                }
            }
            
            // 如果没有找到匹配的包，尝试去掉通配符后的基础包名
            if (!found) {
                String basePackage = wildcardPackage.replaceAll("\\*+", "");
                basePackage = basePackage.replaceAll("\\.+$", ""); // 移除末尾的点
                basePackage = basePackage.replaceAll("^\\.+", ""); // 移除开头的点
                
                if (StringUtils.hasText(basePackage)) {
                    expandedPackages.add(basePackage);
                }
            }
        }
        
        return expandedPackages;
    }
    
    /**
     * 验证所有发现的实体类
     */
    private void validateEntities(List<Class<?>> entityClasses) {
        EntitySchemaValidator validator = entitySchemaValidator();
        AtomicInteger validationErrors = new AtomicInteger(0);
        
        entityClasses.forEach(entityClass -> {
            try {
                ValidationResult result = validator.validate(entityClass);
                
                if (result.hasDifferences()) {
                    validationErrors.incrementAndGet();
                }
            } catch (Exception e) {
                log.error("验证实体出错: {}", entityClass.getName(), e);
                validationErrors.incrementAndGet();
            }
        });
        
        log.info("实体验证完成. 发现 {} 个实体存在验证错误", 
                validationErrors.get());
    }
} 