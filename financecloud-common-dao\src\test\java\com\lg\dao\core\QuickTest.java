package com.lg.dao.core;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 快速测试类 - 用于验证基本功能
 */
@Slf4j
public class QuickTest {

    @Data
    @Table(name = "simple_user")
    public static class SimpleUser {
        @Id
        @Column(name = "user_id")
        private Long userId;
        
        @Column(name = "user_name")
        private String userName;
    }

    public static void main(String[] args) {
        try {
            log.info("=== 开始快速测试 ===");
            
            // 测试EntityInfo创建
            EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SimpleUser.class);
            log.info("EntityInfo创建成功，字段数量: {}", entityInfo.getFields().size());
            
            // 打印所有字段
            for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
                log.info("字段: {} -> 列: {}", field.getPropertyName(), field.getColumn());
            }
            
            // 测试字段查找
            EntityInfo.FieldInfo userIdField = entityInfo.findFieldInfoByColumn("user_id");
            log.info("user_id字段查找结果: {}", userIdField != null ? userIdField.getPropertyName() : "null");
            
            EntityInfo.FieldInfo userNameField = entityInfo.findFieldInfoByColumn("user_name");
            log.info("user_name字段查找结果: {}", userNameField != null ? userNameField.getPropertyName() : "null");
            
            // 测试属性设置
            if (userNameField != null) {
                SimpleUser user = new SimpleUser();
                userNameField.setPropertyValue(user, "Test User");
                log.info("属性设置测试: {}", user.getUserName());
            }
            
            log.info("=== 快速测试完成 ===");
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
}
