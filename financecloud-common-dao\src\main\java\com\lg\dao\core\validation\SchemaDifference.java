package com.lg.dao.core.validation;

import lombok.Builder;
import lombok.Data;

/**
 * 实体与数据库架构差异对象
 */
@Data
@Builder
public class SchemaDifference {
    /**
     * 差异类型
     */
    private DifferenceType type;
    
    /**
     * 对象名称（表名、列名等）
     */
    private String objectName;
    
    /**
     * 实体字段名（如适用）
     */
    private String entityField;
    
    /**
     * 差异详细信息
     */
    private String details;
} 