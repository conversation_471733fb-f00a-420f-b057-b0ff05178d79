package com.lg.dao.mybatis.example;

import com.lg.dao.mybatis.annotation.Delete;
import com.lg.dao.mybatis.annotation.Insert;
import com.lg.dao.mybatis.annotation.Select;
import com.lg.dao.mybatis.annotation.Update;

import java.util.List;

/**
 * 用户Mapper接口示例
 */
public interface UserMapper {
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM t_user WHERE id = #{id}")
    User getUserById(Long id);
    
    /**
     * 查询所有用户
     */
    @Select("SELECT * FROM t_user")
    List<User> getAllUsers();
    
    /**
     * 根据ID列表查询用户 - IN查询测试
     */
    @Select("SELECT * FROM t_user WHERE id IN #{ids}")
    List<User> getUsersByIds(List<Long> ids);
    
    /**
     * 根据状态列表查询用户 - IN查询测试
     */
    @Select("SELECT * FROM t_user WHERE status IN #{statusList}")
    List<User> getUsersByStatusList(List<String> statusList);
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO t_user(username, email, status) VALUES(#{username}, #{email}, #{status})")
    int insertUser(User user);
    
    /**
     * 更新用户
     */
    @Update("UPDATE t_user SET username = #{username}, email = #{email}, status = #{status} WHERE id = #{id}")
    int updateUser(User user);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM t_user WHERE id = #{id}")
    int deleteUser(Long id);
    
    /**
     * 根据状态查询用户
     * 这个方法没有注解，将从XML文件中获取SQL
     */
    List<User> getUsersByStatus(String status);
} 