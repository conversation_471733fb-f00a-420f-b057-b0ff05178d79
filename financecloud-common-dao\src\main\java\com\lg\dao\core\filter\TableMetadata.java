package com.lg.dao.core.filter;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 表元数据
 * 对应sys_table_metadata表
 *
 * <AUTHOR>
 */
@Data
@Table(name = "sys_table_metadata")
public class TableMetadata {
    /**
     * ID
     */
    @Id
    private Long id;
    
    /**
     * 表编码
     */
    private String tableCode;
    
    /**
     * 表名称
     */
    private String name;
    
    /**
     * 排序信息
     */
    private JsonNode sortInfo;
    
    /**
     * 字段信息
     */
    @Column(name = "field_info")
    private JsonNode fieldInfo;
    
    /**
     * 列宽调整信息
     */
    @Column(name = "resizable_info")
    private JsonNode resizableInfo;
    
    /**
     * 列隐藏显示信息
     */
    @Column(name = "visible_info")
    private JsonNode visibleInfo;
    
    /**
     * 模块名称
     */
    @Column(name = "module_name")
    private String moduleName;
    
    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private String tenantId;
    
    /**
     * 创建人ID
     */
    @Column(name = "create_by")
    private String createBy;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改人ID
     */
    private String updateBy;
    
    /**
     * 修改时间
     */
    private Date updateTime;
    
    /**
     * 是否已删除
     */
    private Boolean delFlag;
    
    /**
     * 删除人ID
     */
    private String deleteBy;
    
    /**
     * 删除时间
     */
    private Date deleteTime;
    
    /**
     * 数据版本号
     */
    private Integer version;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 获取字段映射
     * 将字段信息转换为字段映射表
     *
     * @return 字段映射表，key为字段名，value为字段配置
     */
    public Map<String, FieldConfig> getFieldMapping() {
        Map<String, FieldConfig> fieldMapping = new HashMap<>();
        
        if (fieldInfo != null && fieldInfo.has("fields") && fieldInfo.get("fields").isArray()) {
            for (JsonNode field : fieldInfo.get("fields")) {
                if (field.has("field")) {
                    String fieldName = field.get("field").asText();
                    FieldConfig fieldConfig = new FieldConfig();
                    
                    if (field.has("fieldName")) {
                        fieldConfig.setFieldName(field.get("fieldName").asText());
                    }
                    
                    if (field.has("enterMethod")) {
                        fieldConfig.setEnterMethod(field.get("enterMethod").asText());
                    }
                    
                    if (field.has("selectObj") && field.get("selectObj").isArray()) {
                        for (JsonNode option : field.get("selectObj")) {
                            if (option.has("value") && option.has("label")) {
                                fieldConfig.addOption(option.get("value").asText(), option.get("label").asText());
                            }
                        }
                    }
                    
                    if (field.has("dataSource")) {
                        JsonNode dataSource = field.get("dataSource");
                        FieldDataSource fieldDataSource = new FieldDataSource();
                        
                        if (dataSource.has("sourceType")) {
                            fieldDataSource.setSourceType(dataSource.get("sourceType").asText());
                        }
                        
                        if (dataSource.has("dictKey")) {
                            fieldDataSource.setDictKey(dataSource.get("dictKey").asText());
                        }
                        
                        if (dataSource.has("apiUrl")) {
                            fieldDataSource.setApiUrl(dataSource.get("apiUrl").asText());
                        }
                        
                        if (dataSource.has("fieldKey")) {
                            fieldDataSource.setFieldKey(dataSource.get("fieldKey").asText());
                        }
                        
                        if (dataSource.has("fieldValue")) {
                            fieldDataSource.setFieldValue(dataSource.get("fieldValue").asText());
                        }
                        
                        fieldConfig.setDataSource(fieldDataSource);
                    }
                    
                    if (field.has("fieldMapping")) {
                        fieldConfig.setFieldMapping(field.get("fieldMapping").asText());
                    }
                    
                    fieldMapping.put(fieldName, fieldConfig);
                }
            }
        }
        
        return fieldMapping;
    }
    
    /**
     * 字段配置
     */
    @Data
    public static class FieldConfig {
        /**
         * 字段显示名称
         */
        private String fieldName;
        
        /**
         * 输入方式
         */
        private String enterMethod;
        
        /**
         * 选项列表
         */
        private Map<String, String> options = new HashMap<>();
        
        /**
         * 数据源
         */
        private FieldDataSource dataSource;
        
        /**
         * 字段映射
         */
        private String fieldMapping;
        
        /**
         * 添加选项
         *
         * @param value 值
         * @param label 标签
         */
        public void addOption(String value, String label) {
            options.put(value, label);
        }
    }
    
    /**
     * 字段数据源
     */
    @Data
    public static class FieldDataSource {
        /**
         * 数据源类型
         * 1: 静态数据
         * 2: 数据字典
         * 3: API接口
         */
        private String sourceType;
        
        /**
         * 数据字典键
         */
        private String dictKey;
        
        /**
         * API接口地址
         */
        private String apiUrl;
        
        /**
         * 字段键
         */
        private String fieldKey;
        
        /**
         * 字段值
         */
        private String fieldValue;
    }
}