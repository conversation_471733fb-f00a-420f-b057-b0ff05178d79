/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 定时任务表
 *
 * <AUTHOR> cloud
 * @date 2024-06-24 10:02:27
 */
@Data
@TableName("sys_task")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "定时任务表")
public class SysTask extends Model<SysTask> {
    public static final String TASK_RUN_MODE_STANDALONE = "Standalone";
    private static final long serialVersionUID = 1L;

  public static final Integer STATUS_NORMAL = 1;
  public static final Integer STATUS_DISABLE = 0;
  public static final Integer STATUS_DELETE = 2;

    /**
     * 
     */
    @TableId(type= IdType.INPUT)
    @ApiModelProperty(value="")
    private Long id;
    /**
     * 名称
     */
    @ApiModelProperty(value="名称")
    private String taskName;
    /**
     * 定时任务的key，可有可无，但是不能重复
     */
    @ApiModelProperty(value="定时任务的key，可有可无，但是不能重复")
    private String taskCode;
    /**
     * 状态 0禁用，1启用，2删除
     */
    @ApiModelProperty(value="状态 0禁用，1启用，2删除")
    private Integer status;

    /**
     * cron表达式
     */
    @ApiModelProperty(value="cron表达式")
    private String cron;
    /**
     * 延迟时间（仅正对延迟任）
     */
    @ApiModelProperty(value="延迟时间（仅正对延迟任）")
    private Integer delay;
    /**
     * 运行模式 Standalone 单机模式 集群模式Distributed
     */
    @ApiModelProperty(value="运行模式 Standalone 单机模式 集群模式Distributed")
    private String runMode;
    /**
     * 任务并发数 
     */
    @ApiModelProperty(value="任务并发数 ")
    private Integer consumeQps;
    /**
     * 任务执行线程池大小
     */
    @ApiModelProperty(value="任务执行线程池大小")
    private Integer taskPoolSize;
    /**
     * 任务描述
     */
    @ApiModelProperty(value="任务描述")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;
    /**
     * 定时任务类型  1.普通任务 2.延时任务  2.定时任务
     */
    @ApiModelProperty(value="定时任务类型  1.普通任务 2.延时任务  2.定时任务")
    private Integer taskType;
    /**
     * 所属模块
     */
    @ApiModelProperty(value="所属模块")
    private String module;
    /**
     * 执行类的地址
     */
    @ApiModelProperty(value="执行类的地址")
    private String classPath;
    /**
     * job 参数
     */
    @ApiModelProperty(value="job 参数")
    private String contextParam;



  // 队列名称
  @TableField(exist = false)
  private   String queueName;
  @TableField(exist = false)
  private String hostname;
  /****
   * 获取队列名称
   * @return
   */
  public String getFullQueueCode(){
    if(this.queueName==null){
      this.queueName =   getRunMode().equals("Standalone")? signleNodeQuename():getTaskCode();
    }
    return queueName;
  }

  private String signleNodeQuename(){
    return getTaskCode().concat("_").concat(StrUtil.emptyToDefault(getHostname(),hostname()));
  }

  public Boolean isScheduleTask(){
    return StrUtil.isNotEmpty(getCron());
  }


  public String getHostname() {
    return hostname;
  }

  public void setHostname(String hostname) {
    this.hostname = hostname;
  }

  public String hostname(){

    return  NetUtil.getLocalhostStr();
  }

    }
