package com.lg.dao.mybatis;

import com.lg.dao.core.cache.UnifiedCacheManager;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于 MyBatis 原生解析器的 XML 解析器
 * 利用 MyBatis 的 Configuration 和 XMLMapperBuilder 来解析 XML
 */
public class MybatisNativeXmlParser implements InitializingBean {
    
    private static final Logger logger = LoggerFactory.getLogger(MybatisNativeXmlParser.class);
    
    private final Configuration configuration;
    private final Map<String, MappedStatement> mappedStatements = new ConcurrentHashMap<>();
    private String mapperLocations = "classpath*:mapper/**/*.xml";
    private UnifiedCacheManager unifiedCacheManager;
    private volatile boolean initialized = false;
    
    public MybatisNativeXmlParser() {
        this(null);
    }

    public MybatisNativeXmlParser(UnifiedCacheManager unifiedCacheManager) {
        // 创建 MyBatis Configuration
        this.configuration = new Configuration();
        // 设置基本配置
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.SIMPLE);
        this.unifiedCacheManager = unifiedCacheManager;
    }
    
    public void setMapperLocations(String mapperLocations) {
        this.mapperLocations = mapperLocations;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        // 如果没有统一缓存管理器，立即加载
        if (unifiedCacheManager == null) {
            loadMapperXml();
        } else {
            logger.info("使用统一缓存管理器，MyBatis XML 将延迟加载");
        }
    }

    /**
     * 确保已初始化
     */
    private void ensureInitialized() {
        if (!initialized && unifiedCacheManager != null) {
            synchronized (this) {
                if (!initialized) {
                    try {
                        loadMapperXml();
                        initialized = true;
                    } catch (Exception e) {
                        logger.error("延迟初始化 MyBatis XML 解析器失败", e);
                        throw new RuntimeException("延迟初始化 MyBatis XML 解析器失败", e);
                    }
                }
            }
        }
    }

    /**
     * 加载 Mapper XML 文件
     */
    private void loadMapperXml() {
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(mapperLocations);
            
            logger.info("找到 {} 个 MyBatis XML 文件", resources.length);
            
            for (Resource resource : resources) {
                parseXmlWithMybatis(resource);
            }
            
            logger.info("成功解析 {} 个 MappedStatement", mappedStatements.size());
        } catch (Exception e) {
            logger.error("加载 Mapper XML 文件失败", e);
        }
    }
    
    /**
     * 使用 MyBatis 原生解析器解析 XML
     */
    private void parseXmlWithMybatis(Resource resource) {
        try (InputStream inputStream = resource.getInputStream()) {
            // 使用 MyBatis 的 XMLMapperBuilder 解析 XML
            XMLMapperBuilder builder = new XMLMapperBuilder(
                inputStream, 
                configuration, 
                resource.getURL().toString(), 
                configuration.getSqlFragments()
            );
            
            // 解析 XML 文件
            builder.parse();
            
            // 获取解析后的 MappedStatement
            for (String id : configuration.getMappedStatementNames()) {
                MappedStatement ms = configuration.getMappedStatement(id);
                mappedStatements.put(id, ms);
            }
            
            logger.debug("成功解析 XML 文件: {}", resource.getFilename());
        } catch (Exception e) {
            logger.error("解析 XML 文件失败: {}", resource.getFilename(), e);
        }
    }
    
    /**
     * 获取解析后的 SQL 和参数信息
     * 使用统一缓存管理器优化性能，避免重复解析
     */
    public ParsedSqlInfo getParsedSql(String statementId, Object parameterObject) {
        ensureInitialized();

        // 如果启用了统一缓存且参数不包含集合类型，使用缓存
        if (unifiedCacheManager != null && !containsCollectionParam(parameterObject)) {
            String cacheKey = buildCacheKey(statementId, parameterObject);
            logger.debug("使用缓存解析 SQL: {}", cacheKey);
            return unifiedCacheManager.get(
                UnifiedCacheManager.CacheType.MYBATIS_SQL_PARSE_CACHE,
                cacheKey,
                () -> {
                    logger.debug("缓存未命中，执行 SQL 解析: {}", statementId);
                    return doGetParsedSql(statementId, parameterObject);
                }
            );
        }

        // 直接解析（不缓存）
        logger.debug("跳过缓存，直接解析 SQL: {} (原因: {})",
                    statementId,
                    unifiedCacheManager == null ? "未启用缓存" : "参数包含集合类型");
        return doGetParsedSql(statementId, parameterObject);
    }

    /**
     * 实际执行SQL解析的方法
     */
    private ParsedSqlInfo doGetParsedSql(String statementId, Object parameterObject) {
        MappedStatement ms = mappedStatements.get(statementId);
        if (ms == null) {
            return null;
        }
        
        try {
            // 使用 MyBatis 的 BoundSql 获取解析后的 SQL
            BoundSql boundSql = ms.getBoundSql(parameterObject);
            
            // 提取参数信息
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            Object[] parameters = extractParameters(boundSql, parameterMappings, parameterObject);
            
            return new ParsedSqlInfo(
                boundSql.getSql(),
                parameters,
                parameterMappings,
                ms.getSqlCommandType()
            );
        } catch (Exception e) {
            logger.error("解析 SQL 失败: {}", statementId, e);
            return null;
        }
    }
    
    /**
     * 提取参数值
     */
    private Object[] extractParameters(BoundSql boundSql, List<ParameterMapping> parameterMappings, Object parameterObject) {
        if (parameterMappings.isEmpty()) {
            return new Object[0];
        }
        
        Object[] parameters = new Object[parameterMappings.size()];
        
        for (int i = 0; i < parameterMappings.size(); i++) {
            ParameterMapping parameterMapping = parameterMappings.get(i);
            String property = parameterMapping.getProperty();
            
            Object value;
            if (boundSql.hasAdditionalParameter(property)) {
                value = boundSql.getAdditionalParameter(property);
            } else if (parameterObject == null) {
                value = null;
            } else if (configuration.getTypeHandlerRegistry().hasTypeHandler(parameterObject.getClass())) {
                value = parameterObject;
            } else {
                value = getPropertyValue(parameterObject, property);
            }
            
            parameters[i] = value;
        }
        
        return parameters;
    }
    
    /**
     * 获取对象属性值
     */
    private Object getPropertyValue(Object object, String property) {
        if (object == null) {
            return null;
        }
        
        if (object instanceof Map) {
            return ((Map<?, ?>) object).get(property);
        }
        
        try {
            // 使用反射获取属性值
            java.lang.reflect.Field field = findField(object.getClass(), property);
            if (field != null) {
                field.setAccessible(true);
                return field.get(object);
            }
            
            // 尝试 getter 方法
            String getterName = "get" + Character.toUpperCase(property.charAt(0)) + property.substring(1);
            java.lang.reflect.Method getter = object.getClass().getMethod(getterName);
            return getter.invoke(object);
        } catch (Exception e) {
            logger.warn("无法获取属性值: {}.{}", object.getClass().getSimpleName(), property);
            return null;
        }
    }
    
    /**
     * 查找字段（包括父类）
     */
    private java.lang.reflect.Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
    
    /**
     * 获取 MappedStatement
     */
    public MappedStatement getMappedStatement(String statementId) {
        return mappedStatements.get(statementId);
    }
    
    /**
     * 获取 MyBatis Configuration
     */
    public Configuration getConfiguration() {
        return configuration;
    }
    
    /**
     * 检查是否存在指定的语句
     */
    public boolean hasStatement(String statementId) {
        ensureInitialized();
        return mappedStatements.containsKey(statementId);
    }
    
    /**
     * 获取所有语句 ID
     */
    public java.util.Set<String> getStatementIds() {
        return mappedStatements.keySet();
    }

    /**
     * 检测参数中是否包含集合类型
     * 避免缓存陷阱：集合参数变化频繁，缓存意义不大且可能导致内存溢出
     */
    private boolean containsCollectionParam(Object parameterObject) {
        if (parameterObject == null) {
            return false;
        }

        // 直接是集合类型
        if (parameterObject instanceof Collection || parameterObject instanceof Object[]) {
            return true;
        }

        // Map 类型参数，检查值是否包含集合
        if (parameterObject instanceof Map) {
            Map<?, ?> paramMap = (Map<?, ?>) parameterObject;
            for (Object value : paramMap.values()) {
                if (value instanceof Collection || value instanceof Object[]) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 构建缓存键
     * 格式：statementId:parameterSignature
     * 使用参数的类型和关键值构建签名，避免 hashCode 冲突
     */
    private String buildCacheKey(String statementId, Object parameterObject) {
        if (parameterObject == null) {
            return statementId + ":null";
        }

        StringBuilder keyBuilder = new StringBuilder(statementId).append(":");

        // 根据参数类型构建更稳定的缓存键
        if (parameterObject instanceof Map) {
            Map<?, ?> paramMap = (Map<?, ?>) parameterObject;
            keyBuilder.append("map:").append(paramMap.size());
            // 对于 Map 参数，只使用键的集合作为签名（避免值变化导致缓存失效）
            if (!paramMap.isEmpty()) {
                keyBuilder.append(":").append(paramMap.keySet().hashCode());
            }
        } else if (parameterObject instanceof String ||
                   parameterObject instanceof Number ||
                   parameterObject instanceof Boolean) {
            // 基本类型直接使用值
            keyBuilder.append("primitive:").append(parameterObject);
        } else {
            // 复杂对象使用类名和 hashCode
            keyBuilder.append("object:")
                     .append(parameterObject.getClass().getSimpleName())
                     .append(":")
                     .append(parameterObject.hashCode());
        }

        return keyBuilder.toString();
    }
}
