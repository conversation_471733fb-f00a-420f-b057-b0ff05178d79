# 系统架构说明

## 🏗️ 整体架构

### 系统组成

```
┌─────────────────────────────────────────────────────────────┐
│                      API Gateway                           │
├─────────────────────────────────────────────────────────────┤
│  用户中心  │  财务管理  │  OA系统  │  低代码平台  │  其他服务  │
│   服务    │   系统    │        │   服务      │          │
├─────────────────────────────────────────────────────────────┤
│                    前端系统                                  │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   cloudx-ui     │  │      Workflow-ui               │   │
│  │ (业务系统前端)    │  │   (低代码平台前端)              │   │
│  │ - 财务管理UI     │  │ - 页面设计器                    │   │
│  │ - OA系统UI      │  │ - 数据模型管理                   │   │
│  │ - 系统管理      │  │ - 组件管理                      │   │
│  │ - 菜单管理      │  │ - 模板市场                      │   │
│  │ - 用户管理      │  │ - 运行时引擎                     │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 📋 系统职责

### cloudx-ui (主业务系统前端)
**技术栈**: Vue 2.0 + Element UI

**主要功能**:
- 🏦 **财务管理系统UI**: 账户管理、交易记录、财务报表、预算管理
- 🏢 **OA系统UI**: 工作流程、审批中心、文档管理、会议管理
- ⚙️ **系统管理**: 用户管理、角色管理、权限管理、部门管理
- 📋 **菜单管理**: 统一的菜单配置、权限分配、动态菜单
- 🔐 **基础功能**: 登录认证、首页仪表板、个人中心、消息通知

**核心特点**:
- 承载所有业务系统的UI界面
- 统一的用户认证和权限管理
- 集中的菜单配置和管理
- 统一的UI风格和交互规范

### Workflow-ui (低代码平台前端)
**技术栈**: Vue 2.0 + Element UI

**主要功能**:
- 🎨 **页面设计器**: 拖拽式页面构建、组件配置、样式设计
- 🗃️ **数据模型管理**: 动态表结构设计、字段配置、索引管理
- 🧩 **组件管理**: 组件库管理、自定义组件开发、组件市场
- 📄 **模板市场**: 页面模板管理、模板分享、模板应用
- ⚡ **运行时引擎**: 低代码页面渲染、数据绑定、事件处理

**核心特点**:
- 专注于低代码平台功能
- 独立的设计和开发环境
- 可视化的页面构建工具
- 灵活的组件和模板系统

## 🔄 集成方案

### 集成策略: 菜单配置集成

#### 1. 菜单配置方式
在 **cloudx-ui** 的菜单管理中配置低代码平台相关菜单:

```javascript
// 在 cloudx-ui 菜单管理中配置
const lowcodeMenus = [
  {
    id: 'lowcode',
    name: '低代码平台',
    icon: 'el-icon-magic-stick',
    type: 'group',
    children: [
      {
        id: 'lowcode-designer',
        name: '页面设计器',
        icon: 'el-icon-edit',
        url: 'http://workflow-ui-domain/designer',
        openType: 'new', // 新窗口打开
        permission: 'lowcode:design'
      },
      {
        id: 'lowcode-pages',
        name: '页面管理',
        icon: 'el-icon-document',
        url: 'http://workflow-ui-domain/pages',
        openType: 'iframe', // 内嵌iframe
        permission: 'lowcode:page:view'
      }
    ]
  }
]
```

#### 2. 动态页面集成
低代码平台生成的页面通过API注册到菜单系统:

```javascript
// 低代码页面发布后自动注册菜单
const registerPageToMenu = async (pageInfo) => {
  await axios.post('/api/user-center/menus/register', {
    name: pageInfo.pageName,
    url: `http://workflow-ui-domain/runtime/${pageInfo.id}`,
    icon: pageInfo.icon,
    permission: pageInfo.permission,
    parentId: 'business-pages' // 挂载到业务页面分组下
  })
}
```

#### 3. 权限统一管理
所有权限在用户中心统一管理:

```javascript
// 低代码平台权限定义
const lowcodePermissions = [
  'lowcode:access',      // 低代码平台访问权限
  'lowcode:design',      // 页面设计权限
  'lowcode:publish',     // 页面发布权限
  'lowcode:admin'        // 平台管理权限
]
```

## 🚀 技术实现

### 1. 跨域配置
```javascript
// cloudx-ui 中配置代理
module.exports = {
  devServer: {
    proxy: {
      '/api/lc': {
        target: 'http://lowcode-service:8080',
        changeOrigin: true
      }
    }
  }
}
```

### 2. 单点登录
```javascript
// 共享认证token
const token = localStorage.getItem('token')
const lowcodeUrl = `http://workflow-ui-domain/designer?token=${token}`
```

### 3. 消息通信
```javascript
// 页面间消息通信
window.addEventListener('message', (event) => {
  if (event.data.type === 'lowcode:page:published') {
    // 刷新菜单
    this.refreshMenus()
  }
})
```

## 📁 目录结构

### cloudx-ui 项目结构
```
cloudx-ui/
├── src/
│   ├── views/
│   │   ├── finance/          # 财务管理页面
│   │   ├── oa/              # OA系统页面
│   │   ├── system/          # 系统管理页面
│   │   │   ├── user/        # 用户管理
│   │   │   ├── role/        # 角色管理
│   │   │   ├── menu/        # 菜单管理 ⭐
│   │   │   └── permission/  # 权限管理
│   │   └── dashboard/       # 首页仪表板
│   ├── components/
│   │   ├── finance/         # 财务组件
│   │   ├── oa/             # OA组件
│   │   └── common/         # 通用组件
│   └── router/
│       ├── finance.js      # 财务路由
│       ├── oa.js          # OA路由
│       └── system.js      # 系统管理路由
```

### Workflow-ui 项目结构
```
Workflow-ui/
├── src/
│   ├── views/
│   │   ├── designer/        # 页面设计器
│   │   ├── pages/          # 页面管理
│   │   ├── models/         # 数据模型
│   │   ├── components/     # 组件管理
│   │   ├── templates/      # 模板市场
│   │   └── runtime/        # 运行时引擎
│   ├── components/
│   │   ├── designer/       # 设计器组件
│   │   ├── runtime/        # 运行时组件
│   │   └── common/         # 通用组件
│   └── engine/
│       ├── designer/       # 设计器引擎
│       ├── runtime/        # 运行时引擎
│       └── renderer/       # 页面渲染器
```

## 🎯 集成优势

### 1. 系统独立性
- **cloudx-ui** 和 **Workflow-ui** 可以独立开发和部署
- 各自维护自己的技术栈和依赖
- 降低系统间的耦合度

### 2. 用户体验统一
- 通过菜单配置实现统一入口
- 保持一致的权限控制
- 统一的UI风格和交互

### 3. 开发效率
- 低代码平台专注于平台功能开发
- 业务系统专注于业务功能开发
- 通过菜单配置快速集成新功能

### 4. 扩展性强
- 新的业务系统可以快速接入
- 低代码生成的页面可以灵活配置
- 支持多种集成方式(新窗口、iframe、路由跳转)

## 📞 下一步计划

1. **完善菜单管理功能**: 在cloudx-ui中增强菜单配置功能
2. **开发集成接口**: 低代码平台提供菜单注册API
3. **统一权限体系**: 完善跨系统的权限管理
4. **优化用户体验**: 统一登录状态和消息通知
5. **监控和运维**: 建立跨系统的监控体系
