/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.data.mybatis;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.lg.financecloud.common.data.datafilter.SoulTableInterceptor;
import com.lg.financecloud.common.data.datascope.DataScopeSqlInjector;
import com.lg.financecloud.common.data.intercept.PaginationInnerInterceptor;
import com.lg.financecloud.common.data.intercept.TenantInterceptor;
import com.lg.financecloud.common.data.intercept.TenantSqlParser;
import com.lg.financecloud.common.data.tenant.CloudxTenantConfigProperties;
import com.lg.financecloud.common.data.tenant.CloudxTenantHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2020-02-08
 */
@Configuration
@ConditionalOnBean(DataSource.class)
@AutoConfigureAfter(DataSourceAutoConfiguration.class)
//@MapperScan("com.lg.financecloud.**.mapper")
@MapperScan(basePackages = {
		"com.lg.**.mapper"

})
public class MybatisPlusConfiguration {



	@Bean
	public MybatisPlusInterceptor mybatisPlusInterceptor(TenantInterceptor tenantInterceptor) {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
		interceptor.addInnerInterceptor(tenantInterceptor);

		// 乐观锁支持
		interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
		
		// 注入多租户支持
		//interceptor.addInnerInterceptor(tenantLineInnerInterceptor);
		// 数据权限
		//interceptor.addInnerInterceptor(dataScopeInterceptor);
		//interceptor.addInnerInterceptor(new SwithDbIntercept());
//		interceptor.addInnerInterceptor(new SoulTableInnerInterceptor());
		// 分页支持
		PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
		paginationInnerInterceptor.setMaxLimit(10000L);
		interceptor.addInnerInterceptor(paginationInnerInterceptor);
		return interceptor;
	}
	@Bean
	public SoulTableInterceptor soulTableInterceptor() {
		SoulTableInterceptor interceptor = new SoulTableInterceptor();
		return interceptor;
	}


	/**
	 * 创建租户维护处理器对象
	 * @return 处理后的租户维护处理器
	 */
	@Bean
	@ConditionalOnMissingBean
	public TenantInterceptor tenantLineInnerInterceptor(CloudxTenantConfigProperties tenantConfigProperties) {
		TenantInterceptor tenantLineInnerInterceptor = new TenantInterceptor();
		tenantLineInnerInterceptor.setTenantSqlParser(new TenantSqlParser(new CloudxTenantHandler(tenantConfigProperties)));
		return tenantLineInnerInterceptor;
	}







	/**
	 * 数据权限拦截器
	 * @return DataScopeInterceptor
	 */
//	@Bean
//	@ConditionalOnMissingBean
//	@ConditionalOnClass(SessionUser.class)
//	public DataScopeInterceptor dataScopeInterceptor(RemoteDataScopeService dataScopeService) {
//		DataScopeInnerInterceptor dataScopeInnerInterceptor = new DataScopeInnerInterceptor();
//		dataScopeInnerInterceptor.setDataScopeHandle(new CloudxDefaultDatascopeHandle(dataScopeService));
//		return dataScopeInnerInterceptor;
//	}


	@Bean
//	@ConditionalOnBean(DataScopeHandle.class)
	public DataScopeSqlInjector dataScopeSqlInjector() {
		return new DataScopeSqlInjector();
	}

}
