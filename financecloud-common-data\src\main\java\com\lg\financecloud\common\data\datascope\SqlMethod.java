package com.lg.financecloud.common.data.datascope;

/*
 * Copyright (c) 2011-2024, bao<PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * MybatisPlus 支持 SQL 方法
 *
 * <AUTHOR>
 * @since 2016-01-23
 */
public enum SqlMethod {

    /**
     * 删除
     */
    deleteByIdPhysical("deleteByIdPhysical", "根据ID 删除一条数据", "DELETE FROM %s WHERE %s=#{%s}"),

    deletePhysical("deletePhysical", "根据 entity 条件删除记录", "<script>\nDELETE FROM %s %s %s\n</script>");


    private final String method;
    private final String desc;
    private final String sql;

    SqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }

    public String getMethod() {
        return method;
    }

    public String getDesc() {
        return desc;
    }

    public String getSql() {
        return sql;
    }
}
