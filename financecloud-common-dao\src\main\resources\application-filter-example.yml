# 数据筛选和权限功能配置示例
# 将此配置添加到您的 application.yml 或 application.properties 中

light:
  orm:
    filter:
      # 启用数据筛选和权限功能，默认为 false（关闭）
      enable: true
      # 启用数据权限拦截器，默认为 true
      enable-data-permission: true
      # 启用数据过滤切面，默认为 true
      enable-data-filter: true
      # 启用元数据缓存，默认为 true
      enable-metadata-cache: true
      # 元数据缓存过期时间（秒），默认30分钟
      metadata-cache-expire-seconds: 1800
      # 数据权限规则缓存过期时间（秒），默认10分钟
      rules-cache-expire-seconds: 600

# 或者在 application.properties 中使用：
# light.orm.filter.enable=true
# light.orm.filter.enable-data-permission=true
# light.orm.filter.enable-data-filter=true