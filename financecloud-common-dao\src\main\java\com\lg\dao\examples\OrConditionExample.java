package com.lg.dao.examples;

import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.helper.DaoHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 展示如何使用OR条件连接符
 */
//@Service
public class OrConditionExample {

    /**
     * 用户实体类
     */
    public static class User {
        private Long id;
        private String userName;
        private String status;
        private String role;
        private String department;
        private String tenantId;
        private Integer age;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public String getDepartment() { return department; }
        public void setDepartment(String department) { this.department = department; }
        
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }

    // 使用DaoHelper创建DAO
    private final GenericDao<User, Long> userDao = DaoHelper.dao(User.class);

    /**
     * 1. 使用or()方法添加OR条件连接符
     */
    public List<User> findUsersWithOrCondition() {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .or()  // 添加OR连接符
            .eq(User::getStatus, "PENDING")
            .list();
    }

    /**
     * 2. 使用and()方法添加AND条件连接符
     */
    public List<User> findUsersWithAndCondition() {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .and()  // 添加AND连接符
            .eq(User::getRole, "ADMIN")
            .list();
    }

    /**
     * 3. 组合使用or()和and()方法
     */
    public List<User> findUsersWithMixedConditions() {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .or()  // 添加OR连接符
            .eq(User::getStatus, "PENDING")
            .and()  // 添加AND连接符
            .eq(User::getRole, "ADMIN")
            .list();
    }

    /**
     * 4. 使用or()方法与嵌套条件组合
     */
    public List<User> findUsersWithOrAndNestedConditions() {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .or()  // 添加OR连接符
            .and(wrapper -> wrapper  // 嵌套AND条件组
                .eq(User::getStatus, "PENDING")
                .eq(User::getRole, "ADMIN")
            )
            .list();
    }

    /**
     * 5. 复杂条件组合示例
     */
    public List<User> findUsersWithComplexConditions() {
        return userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            // 第一个条件组：status = 'ACTIVE' OR status = 'PENDING'
            .and(wrapper -> wrapper
                .eq(User::getStatus, "ACTIVE")
                .or()
                .eq(User::getStatus, "PENDING")
            )
            // OR连接符
            .or()
            // 第二个条件组：role = 'ADMIN' AND department = '技术部'
            .and(wrapper -> wrapper
                .eq(User::getRole, "ADMIN")
                .eq(User::getDepartment, "技术部")
            )
            .list();
    }

    /**
     * 6. 使用便捷API与OR条件结合
     */
    public List<User> findUsersWithOrConditionUsingFlexibleApi() {
        return userDao.query(query -> {
            query.eq(User::getTenantId, "1001");
            query.eq(User::getStatus, "ACTIVE");
            query.or();  // 添加OR连接符
            query.eq(User::getStatus, "PENDING");
        });
    }

    /**
     * 7. 动态条件与OR条件结合
     */
    public List<User> findUsersWithDynamicOrConditions(String status1, String status2) {
        return userDao.query(query -> {
            query.eq(User::getTenantId, "1001");
            
            // 只有当status1不为null时才添加此条件
            if (status1 != null) {
                query.eq(User::getStatus, status1);
            }
            
            // 只有当status2不为null时才添加OR条件
            if (status2 != null) {
                query.or();
                query.eq(User::getStatus, status2);
            }
        });
    }
} 