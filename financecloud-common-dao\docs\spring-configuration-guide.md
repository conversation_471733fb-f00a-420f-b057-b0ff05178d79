# Spring配置指南

## 概述

financecloud-common-dao框架采用Spring Boot自动配置机制，所有组件都通过Spring容器管理，确保正确的依赖注入和生命周期管理。

## 配置结构

### 1. 主配置类
- `LightORMAutoConfiguration`: 主要的自动配置类
- `CacheConfiguration`: 缓存相关组件配置
- `InterceptorConfiguration`: 拦截器相关组件配置

### 2. 组件管理
所有核心组件都通过Spring容器管理，不使用`@Component`注解，而是通过配置类的`@Bean`方法创建：

```java
@Configuration
@Import({CacheConfiguration.class, InterceptorConfiguration.class})
public class LightORMAutoConfiguration {
    // 基础组件配置
}

@Configuration
@ConditionalOnProperty(prefix = "light.orm.cache", name = "enable", havingValue = "true")
public class CacheConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public UnifiedCacheManager unifiedCacheManager() {
        return new UnifiedCacheManager();
    }
    
    @Bean
    @ConditionalOnMissingBean
    public CacheMonitor cacheMonitor(UnifiedCacheManager unifiedCacheManager) {
        // 手动设置依赖注入
        return new CacheMonitor();
    }
}
```

## 配置启用

### 1. 基础配置
```yaml
light:
  orm:
    enable: true              # 启用Light ORM框架
    cache:
      enable: true            # 启用缓存功能
    filter:
      enable: true            # 启用数据过滤功能
      enableDataPermission: true  # 启用数据权限
    debug:
      enable: false           # 生产环境禁用调试功能
```

### 2. 详细配置
```yaml
light:
  orm:
    cache:
      enable: true
      type: CAFFEINE
      caffeine:
        initialCapacity: 100
        maximumSize: 10000
        expireAfterWrite: 3600
        expireAfterAccess: 1800
    filter:
      enable: true
      enableDataPermission: true
    debug:
      enable: true            # 仅开发环境启用
```

## 依赖注入使用

### 1. 正确的使用方式
```java
@Service
public class UserService {
    
    @Autowired
    private UnifiedCacheManager cacheManager;  // ✅ 通过Spring注入
    
    @Autowired
    private CacheMonitor cacheMonitor;         // ✅ 通过Spring注入
    
    public void someMethod() {
        // 使用缓存管理器
        String value = cacheManager.get(
            UnifiedCacheManager.CacheType.DAO_CACHE, 
            "key", 
            () -> "default_value"
        );
        
        // 检查缓存健康状态
        Health health = cacheMonitor.health();
    }
}
```

### 2. 错误的使用方式
```java
@Service
public class UserService {
    
    public void someMethod() {
        // ❌ 不要直接实例化
        UnifiedCacheManager cacheManager = new UnifiedCacheManager();
        
        // ❌ 这样创建的实例没有经过Spring初始化
        CacheMonitor monitor = new CacheMonitor();
    }
}
```

## 测试配置

### 1. 单元测试
```java
@ExtendWith(MockitoExtension.class)
public class ServiceTest {
    
    @Mock
    private UnifiedCacheManager cacheManager;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void testMethod() {
        // 模拟缓存行为
        when(cacheManager.get(any(), any(), any())).thenReturn("mocked_value");
        
        // 执行测试
        String result = userService.someMethod();
        assertEquals("expected", result);
    }
}
```

### 2. 集成测试
```java
@SpringBootTest(classes = {
    UnifiedCacheManager.class,
    CacheMonitor.class,
    InterceptorCleanupFilter.class
})
@TestPropertySource(properties = {
    "light.orm.cache.enable=true",
    "light.orm.filter.enable=true"
})
public class IntegrationTest {
    
    @Autowired
    private UnifiedCacheManager cacheManager;
    
    @Test
    void testCacheIntegration() {
        // 测试真实的缓存功能
        cacheManager.put(UnifiedCacheManager.CacheType.DAO_CACHE, "test", "value");
        String result = cacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, "test", () -> "default");
        assertEquals("value", result);
    }
}
```

## 条件配置

### 1. 缓存功能
只有在`light.orm.cache.enable=true`时才会创建缓存相关的Bean：
- `UnifiedCacheManager`
- `CacheMonitor`
- 定时清理任务

### 2. 调试功能
只有在`light.orm.debug.enable=true`时才会创建调试相关的Bean：
- `InterceptorManagementController`

### 3. 过滤器
`InterceptorCleanupFilter`总是会创建，确保线程本地变量的清理。

## 自定义配置

### 1. 覆盖默认Bean
```java
@Configuration
public class CustomConfiguration {
    
    @Bean
    @Primary
    public UnifiedCacheManager customCacheManager() {
        // 自定义缓存管理器实现
        return new CustomUnifiedCacheManager();
    }
}
```

### 2. 扩展配置
```java
@Configuration
@ConditionalOnProperty(prefix = "app.custom", name = "enable", havingValue = "true")
public class ExtendedConfiguration {
    
    @Bean
    public CustomCacheService customCacheService(UnifiedCacheManager cacheManager) {
        return new CustomCacheService(cacheManager);
    }
}
```

## 最佳实践

### 1. 依赖注入
- 总是通过`@Autowired`注入组件，不要直接实例化
- 使用`@ConditionalOnMissingBean`允许用户覆盖默认配置
- 在测试中使用`@MockBean`或`@Mock`进行模拟

### 2. 配置管理
- 使用配置属性类管理配置参数
- 通过条件注解控制组件的创建
- 提供合理的默认值

### 3. 生命周期管理
- 实现`InitializingBean`进行初始化
- 实现`DisposableBean`进行清理
- 使用`@PreDestroy`进行资源释放

### 4. 错误处理
- 提供降级机制，确保组件不可用时系统仍能工作
- 使用`required = false`处理可选依赖
- 添加适当的日志记录

## 故障排查

### 1. Bean未创建
检查配置属性是否正确设置：
```yaml
light:
  orm:
    cache:
      enable: true  # 确保启用
```

### 2. 依赖注入失败
确保组件在Spring扫描路径中：
```java
@SpringBootApplication
@ComponentScan(basePackages = {"com.lg.dao", "your.package"})
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 循环依赖
使用`@Lazy`注解或重构依赖关系：
```java
@Autowired
@Lazy
private SomeService someService;
```

通过正确的Spring配置，确保所有组件都能正确创建、注入和管理，避免直接实例化导致的问题。
