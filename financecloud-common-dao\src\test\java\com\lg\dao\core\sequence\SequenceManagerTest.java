package com.lg.dao.core.sequence;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class SequenceManagerTest {


    @Autowired
    private SequenceManager sequenceManager;

    @Test
    @Transactional
    public void testBasicSequence() {
        // 测试基本序列生成
        String value1 = sequenceManager.nextValue("tenant1", "test_seq", 4);
        String value2 = sequenceManager.nextValue("tenant1", "test_seq", 4);
        
        assertEquals(4, value1.length());
        assertEquals(4, value2.length());
        assertEquals("0001", value1);
        assertEquals("0002", value2);
    }

    @Test
    @Transactional
    public void testMultiTenant() {
        // 测试多租户隔离
        String tenant1Value1 = sequenceManager.nextValue("tenant1", "shared_seq", 4);
        String tenant1Value2 = sequenceManager.nextValue("tenant1", "shared_seq", 4);
        String tenant2Value1 = sequenceManager.nextValue("tenant2", "shared_seq", 4);
        
        assertEquals("0001", tenant1Value1);
        assertEquals("0002", tenant1Value2);
        assertEquals("0001", tenant2Value1); // 新租户应该从1开始
    }

    @Test
    @Transactional
    public void testDailyStrategy() {
        // 测试按天重置的序列
        String value = sequenceManager.nextValue("tenant1", "daily_seq", SequenceStrategy.DAILY, 4);
        
        // 验证格式：yyyyMMdd + 序号
        assertTrue(Pattern.matches("\\d{8}\\d{4}", value));
        assertEquals(12, value.length());
    }

    @Test
    @Transactional
    public void testMonthlyStrategy() {
        // 测试按月重置的序列
        String value = sequenceManager.nextValue("tenant1", "monthly_seq", SequenceStrategy.MONTHLY, 4);
        
        // 验证格式：yyyyMM + 序号
        assertTrue(Pattern.matches("\\d{6}\\d{4}", value));
        assertEquals(10, value.length());
    }

    @Test
    @Transactional
    public void testCustomPrefix() {
        // 测试自定义前缀
        String value = sequenceManager.nextValue("tenant1", "prefix_seq", 
            SequenceStrategy.NEVER, "TEST", 4);
        
        assertTrue(value.startsWith("TEST"));
        assertEquals(8, value.length()); // "TEST" + "0001"
        assertEquals("TEST0001", value);
    }

    @Test
    @Transactional
    public void testConcurrentAccess() throws InterruptedException {
        int threadCount = 10;
        int iterationsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<String> generatedValues = new HashSet<>();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        String value = sequenceManager.nextValue("tenant1", "concurrent_seq", 6);
                        synchronized (generatedValues) {
                            assertTrue(generatedValues.add(value), 
                                "Duplicate value generated: " + value);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        assertEquals(threadCount * iterationsPerThread, generatedValues.size());
    }

    @Test
    @Transactional
    public void testSuffixLength() {
        // 测试不同长度的序号
        String value3 = sequenceManager.nextValue("tenant1", "length_seq", 3);
        String value6 = sequenceManager.nextValue("tenant1", "length_seq2", 6);
        
        assertEquals(3, value3.length());
        assertEquals(6, value6.length());
        assertEquals("001", value3);
        assertEquals("000001", value6);
    }

    @Test
    @Transactional
    public void testMultipleStrategies() {
        // 测试不同策略的序列
        String neverValue = sequenceManager.nextValue("tenant1", "never_seq", SequenceStrategy.NEVER, 4);
        String dailyValue = sequenceManager.nextValue("tenant1", "daily_seq", SequenceStrategy.DAILY, 4);
        String monthlyValue = sequenceManager.nextValue("tenant1", "monthly_seq", SequenceStrategy.MONTHLY, 4);
        String yearlyValue = sequenceManager.nextValue("tenant1", "yearly_seq", SequenceStrategy.YEARLY, 4);
        
        assertEquals(4, neverValue.length());
        assertTrue(Pattern.matches("\\d{8}\\d{4}", dailyValue));
        assertTrue(Pattern.matches("\\d{6}\\d{4}", monthlyValue));
        assertTrue(Pattern.matches("\\d{4}\\d{4}", yearlyValue));
    }

    @Test
    @Transactional
    public void testAutoCreation() {
        // 测试序列的自动创建
        String value1 = sequenceManager.nextValue("tenant1", "auto_seq", 4);
        String value2 = sequenceManager.nextValue("tenant1", "auto_seq", 4);
        String value3 = sequenceManager.nextValue("tenant2", "auto_seq", 4);
        
        assertEquals("0001", value1);
        assertEquals("0002", value2);
        assertEquals("0001", value3);  // 新租户的序列应该从1开始
    }
}