package com.lg.dao.factory;

import com.lg.dao.core.BaseDao;
import com.lg.dao.core.GenericDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * GenericDao 工厂类 - 零配置使用
 */
@Component
public class DaoFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    // DAO实例缓存
    private static final ConcurrentHashMap<String, GenericDao<?, ?>> DAO_CACHE = new ConcurrentHashMap<>(128);
    private static BaseDao BASE_DAO_INSTANCE;

    /**
     * 获取指定实体的GenericDao实例
     * @param entityClass 实体类
     * @param keyClass 主键类
     * @return GenericDao实例
     */
    @SuppressWarnings("unchecked")
    public <T, K> GenericDao<T, K> getDao(Class<T> entityClass, Class<K> keyClass) {
        // 使用类的hashCode作为缓存键，减少字符串拼接开销
        String cacheKey = entityClass.getName().hashCode() + "_" + keyClass.getName().hashCode();
        
        return (GenericDao<T, K>) DAO_CACHE.computeIfAbsent(cacheKey, key -> {
            // 创建动态GenericDao实例
            GenericDao<T, K> dao = new GenericDao<T, K>(entityClass) {};
            // 手动注入Spring依赖
            applicationContext.getAutowireCapableBeanFactory().autowireBean(dao);
            return dao;
        });
    }
    
    /**
     * 获取指定实体的GenericDao实例（主键默认为Long）
     * @param entityClass 实体类
     * @return GenericDao实例
     */
    public <T> GenericDao<T, Long> getDao(Class<T> entityClass) {
        return getDao(entityClass, Long.class);
    }
    
    /**
     * 获取BaseDao实例，用于无实体查询
     * @return BaseDao实例
     */
    public BaseDao getBaseDao() {
        if (BASE_DAO_INSTANCE == null) {
            synchronized (DaoFactory.class) {
                if (BASE_DAO_INSTANCE == null) {
                    BaseDao dao = new BaseDao();
                    // 手动注入Spring依赖
                    applicationContext.getAutowireCapableBeanFactory().autowireBean(dao);
                    BASE_DAO_INSTANCE = dao;
                }
            }
        }
        return BASE_DAO_INSTANCE;
    }
    
    /**
     * 清除所有缓存
     * 在需要重新加载DAO实例时调用
     */
    public void clearCache() {
        DAO_CACHE.clear();
        BASE_DAO_INSTANCE = null;
    }
}