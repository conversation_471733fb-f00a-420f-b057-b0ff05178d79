/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.dto;

import com.lg.financecloud.admin.api.entity.SysUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/5
 */
@Data
@ApiModel(value = "系统用户传输对象")
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends SysUser {

	/**
	 * 角色ID
	 */
	@ApiModelProperty(value = "角色id集合")
	private List<Integer> role;

	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "部门id")
	private Long deptId;

	/**
	 * 新密码
	 */
	@ApiModelProperty(value = "新密码")
	private String newpassword1;

	public Integer province;

	public Integer city;

	public Integer county;

	public Long orgId;

	public String staffId;//账户管联的员工id

	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像地址")
	public String avatar;

	/**
	 * name 昵称
	 */
	@ApiModelProperty(value = "头像地址")
	public String name;

	/**
	 * brithDay
	 */
	@ApiModelProperty(value = "生日")
	public LocalDateTime brithDay;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	public String sex;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "老手机号码")
	public String oldPhone;

	/**
	 * 真实姓名
	 */
	@ApiModelProperty(value = "真实姓名")
	public String realName;


//	/**
//	 * 真实姓名
//	 */
//	@ApiModelProperty(value = "订单信息")
//	public PayGoodsOrder payGoodsOrder;

	/**
	 * 定制爱服务绑定小孩的名称
	 */
	@ApiModelProperty(value="定制爱服务绑定小孩的名称")
	public String stundentName;

	private String jobId;

}
