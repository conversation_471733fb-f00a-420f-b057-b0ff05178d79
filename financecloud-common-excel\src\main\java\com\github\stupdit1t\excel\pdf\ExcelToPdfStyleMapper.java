package com.github.stupdit1t.excel.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFColor;

import java.awt.Color;
import java.util.HashMap;
import java.util.Map;

/**
 * Excel样式到PDF样式的映射工具类
 * 负责将Excel的CellStyle转换为对应的PDF样式
 */
public class ExcelToPdfStyleMapper {

    private final PdfTemplateConfig config;
    private final Map<String, com.itextpdf.text.Font> fontCache = new HashMap<>();
    private final Map<String, BaseColor> colorCache = new HashMap<>();
    private BaseFont chineseBaseFont;
    private BaseFont defaultBaseFont;

    public ExcelToPdfStyleMapper(PdfTemplateConfig config) {
        this.config = config;
        initializeFonts();
    }

    /**
     * 初始化字体
     */
    private void initializeFonts() {
        try {
            if (config.isEnableChineseFont()) {
                if (config.getCustomFontPath() != null) {
                    chineseBaseFont = BaseFont.createFont(config.getCustomFontPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                } else {
                    chineseBaseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                }
            }
            defaultBaseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            try {
                defaultBaseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
                chineseBaseFont = defaultBaseFont;
            } catch (Exception ex) {
                throw new RuntimeException("无法初始化字体", ex);
            }
        }
    }

    /**
     * 将Excel单元格样式转换为PDF字体
     */
    public com.itextpdf.text.Font convertToFont(CellStyle cellStyle, Workbook workbook) {
        if (cellStyle == null) {
            return getDefaultFont();
        }

        String cacheKey = generateFontCacheKey(cellStyle, workbook);
        if (fontCache.containsKey(cacheKey)) {
            return fontCache.get(cacheKey);
        }

        org.apache.poi.ss.usermodel.Font excelFont = workbook.getFontAt(cellStyle.getFontIndex());
        
        // 确定字体大小
        float fontSize = excelFont.getFontHeightInPoints();
        if (fontSize <= 0) {
            fontSize = config.getFontSize();
        }

        // 确定字体样式
        int fontStyle = com.itextpdf.text.Font.NORMAL;
        if (excelFont.getBold()) {
            fontStyle |= com.itextpdf.text.Font.BOLD;
        }
        if (excelFont.getItalic()) {
            fontStyle |= com.itextpdf.text.Font.ITALIC;
        }
        if (excelFont.getUnderline() != 0) {
            fontStyle |= com.itextpdf.text.Font.UNDERLINE;
        }

        // 确定字体颜色
        BaseColor fontColor = convertFontColor(excelFont);

        // 创建字体
        BaseFont baseFont = needsChineseFont(excelFont.getFontName()) ? chineseBaseFont : defaultBaseFont;
        com.itextpdf.text.Font pdfFont = new com.itextpdf.text.Font(baseFont, fontSize, fontStyle, fontColor);

        fontCache.put(cacheKey, pdfFont);
        return pdfFont;
    }

    /**
     * 应用单元格样式到PDF单元格
     */
    public void applyCellStyle(PdfPCell pdfCell, CellStyle cellStyle, Workbook workbook) {
        if (cellStyle == null) {
            return;
        }

        // 设置背景色
        BaseColor backgroundColor = convertBackgroundColor(cellStyle);
        if (backgroundColor != null) {
            pdfCell.setBackgroundColor(backgroundColor);
        }

        // 设置边框
        applyBorders(pdfCell, cellStyle);

        // 设置对齐方式
        applyAlignment(pdfCell, cellStyle);

        // 设置内边距
        pdfCell.setPadding(4f);
        pdfCell.setPaddingLeft(6f);
        pdfCell.setPaddingRight(6f);
        pdfCell.setPaddingTop(4f);
        pdfCell.setPaddingBottom(4f);
    }

    /**
     * 转换背景色
     */
    private BaseColor convertBackgroundColor(CellStyle cellStyle) {
        if (cellStyle.getFillPattern() == FillPatternType.SOLID_FOREGROUND) {
            return convertColor(cellStyle.getFillForegroundColorColor());
        }
        return null;
    }

    /**
     * 转换字体颜色
     */
    private BaseColor convertFontColor(org.apache.poi.ss.usermodel.Font font) {
        // 获取字体颜色
        if (font instanceof org.apache.poi.xssf.usermodel.XSSFFont) {
            org.apache.poi.xssf.usermodel.XSSFFont xssfFont = (org.apache.poi.xssf.usermodel.XSSFFont) font;
            XSSFColor xssfColor = xssfFont.getXSSFColor();
            if (xssfColor != null) {
                return convertXSSFColor(xssfColor);
            }
        }
        
        // 默认黑色
        return BaseColor.BLACK;
    }

    /**
     * 转换颜色
     */
    private BaseColor convertColor(org.apache.poi.ss.usermodel.Color color) {
        if (color == null) {
            return null;
        }

        String cacheKey = color.toString();
        if (colorCache.containsKey(cacheKey)) {
            return colorCache.get(cacheKey);
        }

        BaseColor baseColor = null;
        
        if (color instanceof XSSFColor) {
            baseColor = convertXSSFColor((XSSFColor) color);
        } else {
            // 对于其他类型的颜色，尝试获取RGB值
            try {
                // 使用反射获取RGB值
                java.lang.reflect.Method getRGBMethod = color.getClass().getMethod("getRGB");
                byte[] rgb = (byte[]) getRGBMethod.invoke(color);
                if (rgb != null && rgb.length >= 3) {
                    baseColor = new BaseColor(rgb[0] & 0xFF, rgb[1] & 0xFF, rgb[2] & 0xFF);
                }
            } catch (Exception e) {
                // 如果无法获取RGB值，返回null
            }
        }

        if (baseColor != null) {
            colorCache.put(cacheKey, baseColor);
        }
        
        return baseColor;
    }

    /**
     * 转换XSSF颜色
     */
    private BaseColor convertXSSFColor(XSSFColor xssfColor) {
        byte[] rgb = xssfColor.getRGB();
        if (rgb != null && rgb.length >= 3) {
            return new BaseColor(
                rgb[0] & 0xFF,
                rgb[1] & 0xFF,
                rgb[2] & 0xFF
            );
        }
        return null;
    }



    /**
     * 应用边框样式
     */
    private void applyBorders(PdfPCell pdfCell, CellStyle cellStyle) {
        float borderWidth = 0.5f;
        BaseColor borderColor = BaseColor.BLACK;

        // 设置边框
        if (cellStyle.getBorderTop() != BorderStyle.NONE) {
            pdfCell.setBorderWidthTop(borderWidth);
            pdfCell.setBorderColorTop(borderColor);
        }
        if (cellStyle.getBorderBottom() != BorderStyle.NONE) {
            pdfCell.setBorderWidthBottom(borderWidth);
            pdfCell.setBorderColorBottom(borderColor);
        }
        if (cellStyle.getBorderLeft() != BorderStyle.NONE) {
            pdfCell.setBorderWidthLeft(borderWidth);
            pdfCell.setBorderColorLeft(borderColor);
        }
        if (cellStyle.getBorderRight() != BorderStyle.NONE) {
            pdfCell.setBorderWidthRight(borderWidth);
            pdfCell.setBorderColorRight(borderColor);
        }

        // 如果没有边框，根据配置决定是否显示网格线
        if (cellStyle.getBorderTop() == BorderStyle.NONE && 
            cellStyle.getBorderBottom() == BorderStyle.NONE &&
            cellStyle.getBorderLeft() == BorderStyle.NONE && 
            cellStyle.getBorderRight() == BorderStyle.NONE) {
            
            if (config.isShowGridLines()) {
                pdfCell.setBorder(Rectangle.BOX);
                pdfCell.setBorderWidth(0.25f);
                pdfCell.setBorderColor(BaseColor.LIGHT_GRAY);
            } else {
                pdfCell.setBorder(Rectangle.NO_BORDER);
            }
        }
    }

    /**
     * 应用对齐方式
     */
    private void applyAlignment(PdfPCell pdfCell, CellStyle cellStyle) {
        // 水平对齐
        switch (cellStyle.getAlignment()) {
            case LEFT:
                pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                break;
            case CENTER:
                pdfCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                break;
            case RIGHT:
                pdfCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                break;
            case JUSTIFY:
                pdfCell.setHorizontalAlignment(Element.ALIGN_JUSTIFIED);
                break;
            default:
                pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                break;
        }

        // 垂直对齐
        switch (cellStyle.getVerticalAlignment()) {
            case TOP:
                pdfCell.setVerticalAlignment(Element.ALIGN_TOP);
                break;
            case CENTER:
                pdfCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                break;
            case BOTTOM:
                pdfCell.setVerticalAlignment(Element.ALIGN_BOTTOM);
                break;
            default:
                pdfCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                break;
        }
    }

    /**
     * 判断是否需要中文字体
     */
    private boolean needsChineseFont(String fontName) {
        if (!config.isEnableChineseFont()) {
            return false;
        }
        
        // 如果字体名包含中文字符或者是常见的中文字体名
        return fontName != null && (
            fontName.matches(".*[\u4e00-\u9fa5].*") ||
            fontName.contains("宋体") ||
            fontName.contains("黑体") ||
            fontName.contains("楷体") ||
            fontName.contains("SimSun") ||
            fontName.contains("SimHei") ||
            fontName.contains("KaiTi")
        );
    }

    /**
     * 生成字体缓存键
     */
    private String generateFontCacheKey(CellStyle cellStyle, Workbook workbook) {
        org.apache.poi.ss.usermodel.Font font = workbook.getFontAt(cellStyle.getFontIndex());
        return String.format("%s_%d_%b_%b_%d", 
            font.getFontName(), 
            font.getFontHeightInPoints(),
            font.getBold(),
            font.getItalic(),
            font.getUnderline()
        );
    }

    /**
     * 获取默认字体
     */
    private com.itextpdf.text.Font getDefaultFont() {
        BaseFont baseFont = config.isEnableChineseFont() ? chineseBaseFont : defaultBaseFont;
        return new com.itextpdf.text.Font(baseFont, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        fontCache.clear();
        colorCache.clear();
    }
}
