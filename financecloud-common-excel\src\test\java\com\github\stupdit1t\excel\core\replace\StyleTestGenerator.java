package com.github.stupdit1t.excel.core.replace;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 样式测试生成器
 * 用于验证循环功能是否正确保持单元格样式
 */
public class StyleTestGenerator {
    
    public static void main(String[] args) throws IOException {
        generateStyleTestTemplate();
    }
    
    /**
     * 生成样式测试模板
     */
    public static void generateStyleTestTemplate() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("样式测试");
        
        // 创建蓝色字体样式
        CellStyle blueStyle = workbook.createCellStyle();
        Font blueFont = workbook.createFont();
        blueFont.setColor(IndexedColors.BLUE.getIndex());
        blueFont.setBold(true);
        blueStyle.setFont(blueFont);
        
        // 创建红色字体样式
        CellStyle redStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redStyle.setFont(redFont);
        
        // 创建绿色背景样式
        CellStyle greenBgStyle = workbook.createCellStyle();
        greenBgStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        greenBgStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        int rowIndex = 0;
        
        // 第0行：公司名称（蓝色字体）
        Row companyRow = sheet.createRow(rowIndex++);
        Cell companyCell = companyRow.createCell(0);
        companyCell.setCellValue("${companyName}");
        companyCell.setCellStyle(blueStyle);
        
        // 第1行：报表日期（红色字体）
        Row dateRow = sheet.createRow(rowIndex++);
        Cell dateCell = dateRow.createCell(0);
        dateCell.setCellValue("报表日期: ${reportDate}");
        dateCell.setCellStyle(redStyle);
        
        // 第2行：空行
        sheet.createRow(rowIndex++);
        
        // 第3行：表头（绿色背景）
        Row headerRow = sheet.createRow(rowIndex++);
        Cell[] headerCells = {
            headerRow.createCell(0),
            headerRow.createCell(1),
            headerRow.createCell(2),
            headerRow.createCell(3)
        };
        String[] headers = {"员工编号", "姓名", "部门", "薪资"};
        for (int i = 0; i < headerCells.length; i++) {
            headerCells[i].setCellValue(headers[i]);
            headerCells[i].setCellStyle(greenBgStyle);
        }
        
        // 第4行：循环开始标记
        Row startRow = sheet.createRow(rowIndex++);
        Cell startCell = startRow.createCell(0);
        startCell.setCellValue("${#foreach employees}");
        
        // 第5行：循环体（蓝色字体）
        Row bodyRow = sheet.createRow(rowIndex++);
        Cell empNoCell = bodyRow.createCell(0);
        empNoCell.setCellValue("${item.empNo}");
        empNoCell.setCellStyle(blueStyle);
        
        Cell nameCell = bodyRow.createCell(1);
        nameCell.setCellValue("${item.name}");
        nameCell.setCellStyle(blueStyle);
        
        Cell deptCell = bodyRow.createCell(2);
        deptCell.setCellValue("${item.department}");
        deptCell.setCellStyle(blueStyle);
        
        Cell salaryCell = bodyRow.createCell(3);
        salaryCell.setCellValue("${item.salary}");
        salaryCell.setCellStyle(blueStyle);
        
        // 第6行：循环结束标记
        Row endRow = sheet.createRow(rowIndex++);
        Cell endCell = endRow.createCell(0);
        endCell.setCellValue("${/foreach}");
        
        // 第7行：空行
        sheet.createRow(rowIndex++);
        
        // 第8行：总计（红色字体）
        Row totalRow = sheet.createRow(rowIndex++);
        Cell totalCell = totalRow.createCell(0);
        totalCell.setCellValue("总计: ${totalCount} 人");
        totalCell.setCellStyle(redStyle);
        
        // 设置列宽
        for (int i = 0; i < 4; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        
        // 保存文件
        try (FileOutputStream fileOut = new FileOutputStream("style_test_template.xlsx")) {
            workbook.write(fileOut);
        }
        
        workbook.close();
        
        System.out.println("样式测试模板已生成: style_test_template.xlsx");
    }
}