/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: SysOrderService
 * @Description: 系统单据接口
 * @author: skyeye云系列--卫志强
 * @date: 2022/3/26 18:34
 * @Copyright: 2021 . All rights reserved.
 *
 */
@FeignClient(value = "skyeye-pro-dev", contextId = "Finance2ErpFlowAbleService")
public interface Finance2ErpFlowAbleService {

    //erp单据生成财务凭证更新erp单据信息
    @PostMapping("/updateOrderInfomations")
    String updateOrderInfomations(@RequestParam(value = "body",required = true) String body);
    //查询需要结转成本的销售出库单数据信息
    @PostMapping("/getNeedCarryoverCostOrderList")
    String getNeedCarryoverCostOrderList(@RequestParam(value = "body",required = true) String body);

}
