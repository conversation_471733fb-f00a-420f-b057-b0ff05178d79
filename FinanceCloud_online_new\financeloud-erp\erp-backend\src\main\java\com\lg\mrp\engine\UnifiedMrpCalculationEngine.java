package com.lg.mrp.engine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lg.erp.entity.ErpMaterial;
import com.lg.erp.mapper.MaterialDao;
import com.lg.erp.v1.entity.MaterialBom;
import com.lg.erp.v1.entity.MaterialInventory;
import com.lg.erp.v1.mapper.MaterialBomMapper;
import com.lg.erp.v1.mapper.MaterialInventoryMapper;
import com.lg.mrp.entity.MrpPlan;
import com.lg.mrp.entity.MrpPlanItem;
import com.lg.mrp.entity.MrpRequirement;
import com.lg.mrp.enums.MrpSourceTypeEnum;
import com.lg.mrp.mapper.MrpPlanItemMapper;
import com.lg.mrp.mapper.MrpPlanMapper;
import com.lg.mrp.mapper.MrpRequirementMapper;
import com.lg.mrp.vo.MrpCalculationResultVO;
import com.lg.mrp.vo.MrpPlanVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一MRP计算引擎
 * 合并了需求处理、BOM展开和净需求计算功能
 * 优化数据查询，避免重复查询以提升性能
 */
@Component
@Slf4j
@AllArgsConstructor
public class UnifiedMrpCalculationEngine {

    private final MrpPlanMapper mrpPlanMapper;
    private final MrpPlanItemMapper mrpPlanItemMapper;
    private final MrpRequirementMapper mrpRequirementMapper;
    private final MaterialDao materialDao;
    private final MaterialBomMapper materialBomMapper;
    private final MaterialInventoryMapper materialInventoryMapper;

    /**
     * 执行完整的MRP计算流程
     */
    @Transactional(rollbackFor = Exception.class)
    public MrpCalculationResultVO executeFullMrpCalculation(String planId) {
        log.info("开始执行统一MRP计算，计划ID: {}", planId);
        
        try {
            if (!validateCalculationParameters(planId)) {
                return new MrpCalculationResultVO(false, "参数验证失败", 0, 0, 0);
            }
            
            MrpPlan plan = mrpPlanMapper.selectById(planId);
            
            // 清理旧的计算结果
            clearOldCalculationResults(planId);
            
            MrpCalculationContext context = preloadCalculationData(plan);
            
            // 1. 处理需求
            int demandCount = processDemands(plan, context);
            log.info("需求处理完成，处理数量: {}", demandCount);
            
            // 2. BOM展开
            int bomCount = expandBom(plan, context);
            log.info("BOM展开完成，展开数量: {}", bomCount);
            
            // 3. 净需求计算
            int netCount = calculateNetRequirements(plan, context);
            log.info("净需求计算完成，计算数量: {}", netCount);
            
            log.info("统一MRP计算完成，计划ID: {}", planId);
            return new MrpCalculationResultVO(true, "计算成功", demandCount, bomCount, netCount);
            
        } catch (Exception e) {
            log.error("统一MRP计算失败，计划ID: {}", planId, e);
            rollbackMrpCalculation(planId);
            return new MrpCalculationResultVO(false, "计算失败: " + e.getMessage(), 0, 0, 0);
        }
    }

    /**
     * 预加载计算所需的数据
     */
    private MrpCalculationContext preloadCalculationData(MrpPlan plan) {
        log.info("预加载计算数据，计划ID: {}", plan.getId());
        
        MrpCalculationContext context = new MrpCalculationContext();
        context.setPlan(plan);
        
        // 加载计划项
        LambdaQueryWrapper<MrpPlanItem> planItemWrapper = new LambdaQueryWrapper<>();
        planItemWrapper.eq(MrpPlanItem::getPlanId, plan.getId());
        context.setPlanItems(mrpPlanItemMapper.selectList(planItemWrapper));
        
        // 加载现有需求
        LambdaQueryWrapper<MrpRequirement> reqWrapper = new LambdaQueryWrapper<>();
        reqWrapper.eq(MrpRequirement::getPlanId, plan.getId());
        context.setExistingRequirements(mrpRequirementMapper.selectList(reqWrapper));
        
        // 预加载所有相关物料的BOM信息
        Set<String> allMaterialIds = new HashSet<>();
        context.getPlanItems().forEach(item -> allMaterialIds.add(item.getMaterialId()));

        for (String materialId : allMaterialIds) {
            // 加载BOM信息
            LambdaQueryWrapper<MaterialBom> bomWrapper = new LambdaQueryWrapper<>();
            bomWrapper.eq(MaterialBom::getParentMaterialId, materialId);
            List<MaterialBom> bomList = materialBomMapper.selectList(bomWrapper);
            if (CollUtil.isNotEmpty(bomList)) {
                context.getBomMap().put(materialId, bomList);
                // 添加子物料ID到集合中
                bomList.forEach(bom -> allMaterialIds.add(bom.getChildMaterialId()));
            }
            
            // 加载库存信息
            LambdaQueryWrapper<MaterialInventory> invWrapper = new LambdaQueryWrapper<>();
            invWrapper.eq(MaterialInventory::getMaterialId, materialId);
            List<MaterialInventory> inventories = materialInventoryMapper.selectList(invWrapper);
            if (CollUtil.isNotEmpty(inventories)) {
                InventoryInfo inventoryInfo = new InventoryInfo();
                for (MaterialInventory inv : inventories) {
                    inventoryInfo.setCurrentInventory(inventoryInfo.getCurrentInventory().add(inv.getCurrentStock() != null ? inv.getCurrentStock() : BigDecimal.ZERO));
                    inventoryInfo.setAvailableInventory(inventoryInfo.getAvailableInventory().add(inv.getAvailableStock() != null ? inv.getAvailableStock() : BigDecimal.ZERO));
                    inventoryInfo.setInTransitInventory(inventoryInfo.getInTransitInventory().add(inv.getInTransitStock() != null ? inv.getInTransitStock() : BigDecimal.ZERO));
                    inventoryInfo.setAllocatedInventory(inventoryInfo.getAllocatedInventory().add(inv.getAllocatedStock() != null ? inv.getAllocatedStock() : BigDecimal.ZERO));
                    inventoryInfo.setSafetyStock(inventoryInfo.getSafetyStock().add(inv.getSafetyStock() != null ? inv.getSafetyStock() : BigDecimal.ZERO));
                }
                context.getInventoryMap().put(materialId, inventoryInfo);
            }
        }
        
        // 批量加载物料类型信息，避免循环查询
        if (CollUtil.isNotEmpty(allMaterialIds)) {
            try {
                LambdaQueryWrapper<ErpMaterial> materialWrapper = new LambdaQueryWrapper<>();
                materialWrapper.in(ErpMaterial::getId, allMaterialIds)
                              .select(ErpMaterial::getId, ErpMaterial::getType);
                List<ErpMaterial> materials = materialDao.selectList(materialWrapper);
                
                for (ErpMaterial material : materials) {
                    String materialType = material.getType() != null ? material.getType().toString() : "2";
                    context.getMaterialTypeMap().put(material.getId(), materialType);
                }
                
                // 为没有查询到的物料设置默认类型
                for (String materialId : allMaterialIds) {
                    if (!context.getMaterialTypeMap().containsKey(materialId)) {
                        context.getMaterialTypeMap().put(materialId, "2");
                        log.warn("物料ID: {} 未找到类型信息，使用默认值2（外购）", materialId);
                    }
                }
            } catch (Exception e) {
                log.warn("批量获取物料类型失败，使用默认值", e);
                // 如果批量查询失败，为所有物料设置默认类型
                for (String materialId : allMaterialIds) {
                    context.getMaterialTypeMap().put(materialId, "2");
                }
            }
        }
        
        log.info("数据预加载完成，物料数量: {}, BOM数量: {}, 库存数量: {}", 
                allMaterialIds.size(), context.getBomMap().size(), context.getInventoryMap().size());
        
        return context;
    }

    /**
     * 处理需求（原MrpDemandProcessingEngine功能）
     */
    private int processDemands(MrpPlan plan, MrpCalculationContext context) {
        log.info("开始处理需求，计划ID: {}", plan.getId());
        
        List<MrpRequirement> requirements = new ArrayList<>();
        
        for (MrpPlanItem item : context.getPlanItems()) {
            try {
                MrpRequirement requirement = createRequirementFromPlanItem(item, plan, context);
                requirements.add(requirement);
                
                // 处理安全库存需求
                if (plan.getConsiderSafetyStock() != null && plan.getConsiderSafetyStock()) {
                    InventoryInfo inventoryInfo = context.getInventoryMap().get(item.getMaterialId());
                    if (inventoryInfo != null && inventoryInfo.getSafetyStock().compareTo(BigDecimal.ZERO) > 0) {
                        MrpRequirement safetyRequirement = createSafetyStockRequirement(item, plan, context, inventoryInfo);
                        requirements.add(safetyRequirement);
                    }
                }
                
            } catch (Exception e) {
                log.error("处理计划项失败，项目ID: {}", item.getId(), e);
            }
        }
        
        // 批量插入需求
        if (CollUtil.isNotEmpty(requirements)) {
            requirements.forEach(req -> mrpRequirementMapper.insert(req));
        }
        
        return requirements.size();
    }

    /**
     * 从计划项创建需求记录
     */
    private MrpRequirement createRequirementFromPlanItem(MrpPlanItem item, MrpPlan plan, MrpCalculationContext context) {
        MrpRequirement requirement = new MrpRequirement();
        requirement.setId(UUID.randomUUID().toString());
        requirement.setTenantId(item.getTenantId());
        requirement.setPlanId(plan.getId());
        requirement.setMaterialId(item.getMaterialId());
        requirement.setMaterialCode(item.getMaterialCode());
        requirement.setMaterialName(item.getMaterialName());
        requirement.setMaterialSpec(item.getMaterialSpec());
        requirement.setUnit(item.getUnit());
        requirement.setUnitId(item.getUnitId());
        requirement.setNormsId(item.getNormsId());
        requirement.setWorkCenterId(item.getWorkCenterId());
        requirement.setWorkCenterName(item.getWorkCenterName());
        requirement.setEngineeringSolutionId(item.getEngineeringSolutionId());
        requirement.setEngineeringSolutionName(item.getEngineeringSolutionName());
        requirement.setGrossRequirement(item.getQuantity());
        requirement.setNetRequirement(item.getQuantity());
        requirement.setRequirementDate(item.getDemandDate());
        requirement.setSourceType(item.getSourceType());
        requirement.setSourceId(item.getId());
        requirement.setSourceNo(item.getSourceNo());
        requirement.setBomLevel(0);
        requirement.setParentMaterialId(null);
        
        // 设置物料类型
        String materialType = context.getMaterialTypeMap().get(item.getMaterialId());
        requirement.setMaterialType(materialType != null ? materialType : "2");
        
        requirement.setCreateTime(LocalDateTime.now());
        requirement.setUpdateTime(LocalDateTime.now());
        
        return requirement;
    }
    
    /**
     * 创建安全库存需求
     */
    private MrpRequirement createSafetyStockRequirement(MrpPlanItem item, MrpPlan plan, MrpCalculationContext context, InventoryInfo inventoryInfo) {
        MrpRequirement requirement = new MrpRequirement();
        requirement.setId(UUID.randomUUID().toString());
        requirement.setTenantId(item.getTenantId());
         requirement.setPlanId(plan.getId());
         requirement.setMaterialId(item.getMaterialId());
        requirement.setMaterialCode(item.getMaterialCode());
        requirement.setMaterialName(item.getMaterialName());
        requirement.setMaterialSpec(item.getMaterialSpec());
        requirement.setUnit(item.getUnit());
        requirement.setUnitId(item.getUnitId());
        requirement.setNormsId(item.getNormsId());
        requirement.setGrossRequirement(inventoryInfo.getSafetyStock());
        requirement.setNetRequirement(inventoryInfo.getSafetyStock());
        requirement.setRequirementDate(item.getDemandDate());
        requirement.setSourceType(MrpSourceTypeEnum.SAFETY_STOCK.getCode());
        requirement.setSourceId(item.getId());
        requirement.setSourceNo(item.getSourceNo());
        requirement.setBomLevel(0);
        requirement.setParentMaterialId(null);
        
        // 设置物料类型
        String materialType = context.getMaterialTypeMap().get(item.getMaterialId());
        requirement.setMaterialType(materialType != null ? materialType : "2");
        
        requirement.setCreateTime(LocalDateTime.now());
        requirement.setUpdateTime(LocalDateTime.now());
        
        return requirement;
    }

    /**
     * BOM展开（原MrpBomExplosionEngine功能）
     */
    private int expandBom(MrpPlan plan, MrpCalculationContext context) {
        log.info("开始BOM展开，计划ID: {}", plan.getId());
        
        // 获取所有需要展开的需求
        LambdaQueryWrapper<MrpRequirement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MrpRequirement::getPlanId, plan.getId())
               .eq(MrpRequirement::getBomLevel, 0);
        List<MrpRequirement> topLevelRequirements = mrpRequirementMapper.selectList(wrapper);
        
        int totalExpanded = 0;
        Set<String> processedMaterials = new HashSet<>();
        
        for (MrpRequirement requirement : topLevelRequirements) {
            try {
                int expanded = expandBomForRequirement(requirement, plan, context, processedMaterials, 1);
                totalExpanded += expanded;
            } catch (Exception e) {
                log.error("BOM展开失败，需求ID: {}", requirement.getId(), e);
            }
        }
        
        return totalExpanded;
    }
    
    /**
     * 为特定需求展开BOM
     */
    private int expandBomForRequirement(MrpRequirement parentRequirement, MrpPlan plan, 
                                       MrpCalculationContext context, Set<String> processedMaterials, int level) {
        String materialId = parentRequirement.getMaterialId();
        
        // 防止循环BOM
        String processKey = materialId + "_" + level;
        if (processedMaterials.contains(processKey)) {
            log.warn("检测到循环BOM，跳过处理: {}", processKey);
            return 0;
        }
        processedMaterials.add(processKey);
        
        List<MaterialBom> bomList = context.getBomMap().get(materialId);
        if (CollUtil.isEmpty(bomList)) {
            return 0;
        }
        
        List<MrpRequirement> childRequirements = new ArrayList<>();
        
        for (MaterialBom bom : bomList) {
            try {
                MrpRequirement childRequirement = new MrpRequirement();
                childRequirement.setId(UUID.randomUUID().toString());
                childRequirement.setTenantId(parentRequirement.getTenantId());
                childRequirement.setPlanId(parentRequirement.getPlanId());
                childRequirement.setMaterialId(bom.getChildMaterialId());
                childRequirement.setMaterialCode(bom.getChildMaterialCode());
                childRequirement.setMaterialName(bom.getChildMaterialName());
                // 设置子物料规格信息
                childRequirement.setMaterialSpec(bom.getChildMaterialSpec());
                childRequirement.setNormsId(bom.getChildNormsId());
                childRequirement.setUnit(bom.getUnit());

                // 计算子物料需求量
                BigDecimal childQuantity = parentRequirement.getGrossRequirement()
                    .multiply(bom.getQuantity() != null ? bom.getQuantity() : BigDecimal.ONE);
                childRequirement.setGrossRequirement(childQuantity);
                childRequirement.setNetRequirement(childQuantity);
                
                // 计算子物料需求日期
                Date childDate = calculateChildRequirementDate(parentRequirement.getRequirementDate(), bom);
                childRequirement.setRequirementDate(childDate);
                
                childRequirement.setSourceType(MrpSourceTypeEnum.BOM.code());
                childRequirement.setSourceId(bom.getId());
                childRequirement.setBomLevel(level);
                childRequirement.setParentMaterialId(materialId);
                
                // 从当前 MaterialBom 对象获取工作中心和工程方案信息
                childRequirement.setEngineeringSolutionId(bom.getEngineeringSolutionId());
                childRequirement.setEngineeringSolutionName(bom.getSolutionName());
                
                // 设置物料类型
                String materialType = context.getMaterialTypeMap().get(bom.getChildMaterialId());
                childRequirement.setMaterialType(materialType != null ? materialType : "2");
                
                childRequirement.setCreateTime(LocalDateTime.now());
                childRequirement.setUpdateTime(LocalDateTime.now());
                
                childRequirements.add(childRequirement);
                
            } catch (Exception e) {
                log.error("创建子需求失败，BOM ID: {}", bom.getId(), e);
            }
        }
        
        // 批量插入子需求
        if (CollUtil.isNotEmpty(childRequirements)) {
            childRequirements.forEach(req -> mrpRequirementMapper.insert(req));
            
            // 递归展开子物料的BOM
            for (MrpRequirement childReq : childRequirements) {
                expandBomForRequirement(childReq, plan, context, processedMaterials, level + 1);
            }
        }
        
        return childRequirements.size();
    }
    
    /**
     * 计算子需求日期
     */
    private Date calculateChildRequirementDate(Date parentDate, MaterialBom bom) {
        if (parentDate == null) {
            return new Date();
        }
        
        // 考虑提前期
        long leadTimeDays = bom.getLeadTime() != null ? bom.getLeadTime() : 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentDate);
        calendar.add(Calendar.DAY_OF_MONTH, (int) -leadTimeDays);
        
        return calendar.getTime();
    }

    /**
     * 计算净需求（原MrpNetRequirementEngine功能）
     */
    private int calculateNetRequirements(MrpPlan plan, MrpCalculationContext context) {
        log.info("开始计算净需求，计划ID: {}", plan.getId());
        
        // 获取所有需求并按物料分组
        LambdaQueryWrapper<MrpRequirement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MrpRequirement::getPlanId, plan.getId());
        List<MrpRequirement> allRequirements = mrpRequirementMapper.selectList(wrapper);
        
        Map<String, List<MrpRequirement>> requirementsByMaterial = allRequirements.stream()
            .collect(Collectors.groupingBy(MrpRequirement::getMaterialId));
        
        int calculatedCount = 0;
        
        for (Map.Entry<String, List<MrpRequirement>> entry : requirementsByMaterial.entrySet()) {
            String materialId = entry.getKey();
            List<MrpRequirement> requirements = entry.getValue();
            
            try {
                int count = calculateNetRequirementForMaterial(plan, materialId, requirements, context);
                calculatedCount += count;
            } catch (Exception e) {
                log.error("计算物料{}的净需求失败", materialId, e);
            }
        }
        
        return calculatedCount;
    }
    
    /**
     * 计算特定物料的净需求
     */
    private int calculateNetRequirementForMaterial(MrpPlan plan, String materialId, 
                                                  List<MrpRequirement> requirements, MrpCalculationContext context) {
        InventoryInfo inventoryInfo = context.getInventoryMap().get(materialId);
        if (inventoryInfo == null) {
            inventoryInfo = new InventoryInfo();
        }
        
        // 计算可用库存
        BigDecimal availableInventory = calculateAvailableInventory(inventoryInfo, plan);
        
        // 按日期排序需求
        requirements.sort(Comparator.comparing(MrpRequirement::getRequirementDate));
        
        BigDecimal runningInventory = availableInventory;
        
        for (MrpRequirement requirement : requirements) {
            try {
                BigDecimal grossRequirement = requirement.getGrossRequirement();
                BigDecimal netRequirement;
                
                if (runningInventory.compareTo(grossRequirement) >= 0) {
                    // 库存足够
                    netRequirement = BigDecimal.ZERO;
                    runningInventory = runningInventory.subtract(grossRequirement);
                } else {
                    // 库存不足
                    netRequirement = grossRequirement.subtract(runningInventory);
                    runningInventory = BigDecimal.ZERO;
                }
                
                requirement.setNetRequirement(netRequirement);
                requirement.setUpdateTime(LocalDateTime.now());
                mrpRequirementMapper.updateById(requirement);
                
            } catch (Exception e) {
                log.error("计算需求{}的净需求失败", requirement.getId(), e);
                requirement.setNetRequirement(requirement.getGrossRequirement());
            }
        }
        
        return requirements.size();
    }
    
    /**
     * 计算可用库存
     */
    private BigDecimal calculateAvailableInventory(InventoryInfo inventoryInfo, MrpPlan plan) {
        BigDecimal available = inventoryInfo.getCurrentInventory();
        
        // 考虑在途库存
        if (plan.getConsiderInTransit() != null && plan.getConsiderInTransit()) {
            available = available.add(inventoryInfo.getInTransitInventory());
        }
        
        // 减去已分配库存
        if (plan.getConsiderAllocated() != null && plan.getConsiderAllocated()) {
            available = available.subtract(inventoryInfo.getAllocatedInventory());
        }
        
        // 确保不为负数
        return available.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : available;
    }

    /**
     * 验证计算参数
     */
    private boolean validateCalculationParameters(String planId) {
        if (StrUtil.isEmpty(planId)) {
            log.error("计划ID不能为空");
            return false;
        }
        
        MrpPlan plan = mrpPlanMapper.selectById(planId);
        if (plan == null) {
            log.error("计划不存在: {}", planId);
            return false;
        }
        
        return true;
    }

    /**
     * 清理旧的计算结果
     */
    private void clearOldCalculationResults(String planId) {
        log.info("清理旧的计算结果，计划ID: {}", planId);
        
        try {
            // 删除该计划的所有需求记录
            LambdaQueryWrapper<MrpRequirement> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MrpRequirement::getPlanId, planId);
            int deletedCount = mrpRequirementMapper.delete(wrapper);
            
            log.info("清理旧计算结果完成，计划ID: {}，删除需求记录数: {}", planId, deletedCount);
        } catch (Exception e) {
            log.error("清理旧计算结果失败，计划ID: {}", planId, e);
            throw new RuntimeException("清理旧计算结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 回滚MRP计算
     */
    private void rollbackMrpCalculation(String planId) {
        log.info("回滚MRP计算，计划ID: {}", planId);
        
        try {
            // 删除本次计算生成的需求
            LambdaQueryWrapper<MrpRequirement> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MrpRequirement::getPlanId, planId);
            mrpRequirementMapper.delete(wrapper);
            
            log.info("MRP计算回滚完成，计划ID: {}", planId);
        } catch (Exception e) {
            log.error("MRP计算回滚失败，计划ID: {}", planId, e);
        }
    }
    
    // 公共方法，供MrpCalculationEngineServiceImpl调用
    public int processPlanDemands(String planId) {
        MrpPlan plan = mrpPlanMapper.selectById(planId);
        MrpCalculationContext context = preloadCalculationData(plan);
        return processDemands(plan, context);
    }
    
    public int explodeBom(String planId) {
        MrpPlan plan = mrpPlanMapper.selectById(planId);
        MrpCalculationContext context = preloadCalculationData(plan);
        return expandBom(plan, context);
    }
    
    public int calculateNetRequirements(String planId) {
        MrpPlan plan = mrpPlanMapper.selectById(planId);
        MrpCalculationContext context = preloadCalculationData(plan);
        return calculateNetRequirements(plan, context);
    }
    
    // 新增：支持传入已有context的方法，避免重复数据加载
    public int processPlanDemandsWithContext(MrpPlan plan, MrpCalculationContext context) {
        return processDemands(plan, context);
    }
    
    public int explodeBomWithContext(MrpPlan plan, MrpCalculationContext context) {
        return expandBom(plan, context);
    }
    
    public int calculateNetRequirementsWithContext(MrpPlan plan, MrpCalculationContext context) {
        return calculateNetRequirements(plan, context);
    }
    
    // 新增：获取预加载的计算上下文
    public MrpCalculationContext getCalculationContext(String planId) {
        MrpPlan plan = mrpPlanMapper.selectById(planId);
        return preloadCalculationData(plan);
    }
    
    public void rollbackCalculation(String planId) {
        rollbackMrpCalculation(planId);
    }

    /**
     * 计算上下文，用于缓存预加载的数据
     */
    @Data
    public static class MrpCalculationContext {
        private MrpPlan plan;
        private List<MrpPlanItem> planItems;
        private List<MrpRequirement> existingRequirements;
        private Map<String, List<MaterialBom>> bomMap = new HashMap<>();
        private Map<String, InventoryInfo> inventoryMap = new HashMap<>();
        private Map<String, String> materialTypeMap = new HashMap<>();
    }
    
    /**
     * 库存信息
     */
    @Data
    public static class InventoryInfo {
        private BigDecimal currentInventory = BigDecimal.ZERO;
        private BigDecimal availableInventory = BigDecimal.ZERO;
        private BigDecimal inTransitInventory = BigDecimal.ZERO;
        private BigDecimal allocatedInventory = BigDecimal.ZERO;
        private BigDecimal safetyStock = BigDecimal.ZERO;
    }
    

}