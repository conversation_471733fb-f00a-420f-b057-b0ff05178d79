### v4.0.0

1. 添加 `jakarta.servlet-api` 支持，最低java11

### v3.3.0

1. 导入导出链式调用的调整
2. 导入解析处理调整
3. 部分方法名调整
4. 支持自动映射属性，自动映射字段

### v3.2.4

1. 公式数字类型读取

### v3.2.3

1. 导出新增addImage方法，自定义添加图片到指定区域
2. 合并单元格操作只保留左上角单元格值

### v3.2.2

1. fix多sheet导出，填充下拉框导致sheet页不正确问题

### v3.2.1

1. 导入解析数据，新增列上map方法，用于转换和处理数据，如字典值转换
2. 读模板替换变量，支持替换图片
3. 若干功能优化, 部分方法更名，移除intStr()

### v3.2.0 (相对3.1.x，有部分不兼容改动)

1. 重构解析表格异常收集，a.提供行级别异常输出，b.单元格级别异常输出，c.自定义异常输入
2. 新增解析数字格式的单元格，用String接收带小数点.0的问题，提供intStr()参数，转换为整形

### v3.1.5

1. xls格式导出下拉框不能支持太多数据，更换为引用支持更多的数据
2. 列数太多，引用单元格BUG处理

### v3.1.4

1. 导出导入回调注释添加
2. 导入field方法支持只传入列和字段，不需要title
3. 导出SXSSFWorkbook格式删除临时文件

### v3.1.3

1. 解析Excel遇到未知异常捕获至PoiResult
2. 解析Excel链式方法调整，新增defaultValue
3. 增加大数据事件流分批导入功能，如一次处理百万数据，避免直接读取到内存OOM问题

### v3.1.2

1. 解析回调处理步骤POI Exception
2. 添加支持非1904日期的识别
3. 解析列添加trim方法

### v3.1.1

1. 导出支持读取Excel追加sheet页

### v3.1.0

1. 支持单元格级别的批注功能, 参考simpleExport2

### v3.0.9

1. 表头相同名字重复设置报错, fixbug

### v3.0.8

1. 导出添加设置列换行显示属性 参考简单导出simpleExport2
2. 添加sheet设置全局的单元格宽度属性 参考简单导出simpleExport2

### v3.0.7

1. POI版本升级 5.1.0 ----- 5.2.2

### v3.0.6

1. 增加导出自动感知行数据合并行功能, 方法为mergerRepeat, 参考 简单导出 + 自定义属性完整示例

### v3.0.5

1. 导出参数为空检验
2. 部分方法名调整

### v3.0.4

1. 日期格式化方法名修改 UPDATE
2. 保留导出workbook, 提供灵活性 NEW
3. 支持xlsx添加密码 NEW

### v3.0.2 ( 不兼容[1.x.x](README-1.x.md) 和 [2.x.x](README-2.x.md) 版本)

1. 提供ExcelHepler链式构建类, 帮助快捷构建. 本身还是调用ExcelUtil类
2. 优化代码结构和层次
3. 提供更精确的单元格样式控制