#findById
SELECT 
    id,
    user_name,
    real_name,
    mobile,
    email,
    status,
    tenant_id,
    create_time,
    update_time
FROM sys_user 
WHERE id = #{id}
    AND tenant_id = #{tenantId}
    AND deleted = 0

#findByUserNames
SELECT 
    id, user_name, real_name, age, tenant_id, create_time, update_time
FROM t_user
WHERE user_name in
    <foreach collection="userNames" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
#findPage
SELECT 
    u.id,
    u.user_name,
    u.real_name,
    u.mobile,
    u.email,
    u.status,
    u.tenant_id,
    u.create_time,
    u.update_time,
    d.dept_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.id
WHERE u.deleted = 0 
    AND u.tenant_id = #{tenantId}
    <if test="userName != null and userName != ''">
    AND u.user_name LIKE CONCAT('%', #{userName}, '%')
    </if>
    <if test="realName != null and realName != ''">
    AND u.real_name LIKE CONCAT('%', #{realName}, '%')
    </if>
    <if test="mobile != null and mobile != ''">
    AND u.mobile = #{mobile}
    </if>
    <if test="status != null">
    AND u.status = #{status}
    </if>
    <if test="deptId != null">
    AND u.dept_id = #{deptId}
    </if>
    <if test="startTime != null">
    AND u.create_time >= #{startTime}
    </if>
    <if test="endTime != null">
    AND u.create_time <= #{endTime}
    </if>
ORDER BY u.create_time DESC

#insertUser
INSERT INTO sys_user (
    user_name,
    real_name,
    password,
    salt,
    mobile,
    email,
    status,
    dept_id,
    tenant_id,
    create_time,
    create_by,
    deleted
) VALUES (
    #{userName},
    #{realName},
    #{password},
    #{salt},
    #{mobile},
    #{email},
    #{status},
    #{deptId},
    #{tenantId},
    NOW(),
    #{createBy},
    0
)

#updateUser
UPDATE sys_user 
SET 
    <if test="realName != null">
    real_name = #{realName},
    </if>
    <if test="mobile != null">
    mobile = #{mobile},
    </if>
    <if test="email != null">
    email = #{email},
    </if>
    <if test="status != null">
    status = #{status},
    </if>
    <if test="deptId != null">
    dept_id = #{deptId},
    </if>
    update_time = NOW(),
    update_by = #{updateBy}
WHERE id = #{id}
    AND tenant_id = #{tenantId}
    AND deleted = 0

#updatePassword
UPDATE sys_user 
SET 
    password = #{newPassword},
    salt = #{newSalt},
    update_time = NOW(),
    update_by = #{updateBy}
WHERE id = #{id}
    AND tenant_id = #{tenantId}
    AND deleted = 0

#deleteUser
UPDATE sys_user 
SET 
    deleted = 1,
    update_time = NOW(),
    update_by = #{updateBy}
WHERE id = #{id}
    AND tenant_id = #{tenantId}
    AND deleted = 0

#countByUserName
SELECT COUNT(1)
FROM sys_user
WHERE user_name = #{userName}
    AND tenant_id = #{tenantId}
    AND deleted = 0
    <if test="excludeId != null">
    AND id != #{excludeId}
    </if>

#findByIds
SELECT 
    id,
    user_name,
    real_name,
    mobile,
    email,
    status,
    tenant_id,
    create_time,
    update_time
FROM sys_user 
WHERE id IN 
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND tenant_id = #{tenantId}
    AND deleted = 0

#findByUserNameAndStatus
                       SELECT * FROM sys_user
                                WHERE user_name = #{userName} AND status = #{status}

#findByUserNameAndStatusAndTenantId
   SELECT * FROM sys_user
    WHERE user_name = #{userName} AND status = #{status} AND tenant_id = #{tenantId}


#testIfQuery
    <!-- 查询我的待办任务 aaabbb -->
    <![CDATA[

SELECT
    wft.id AS id,
    wft.tenant_id AS tenantId,
    wft.instance_id AS instanceId,
    wft.business_key AS businessKey,
    wft.node_id AS nodeId,
    wft.node_name AS nodeName,
    wft.assignee_id AS assigneeId,
    wft.assignee_name AS assigneeName,
    wft.`STATUS` AS `status`,
    wft.action AS action,
    wft.`COMMENT` AS `comment`,
    wft.form_data AS formData,
    wft.`VARIABLES` AS `variables`,
    wft.priority AS priority,
    wft.due_date AS dueDate,
    wft.claim_time AS claimTime,
    wft.complete_time AS completeTime,
    wft.create_time AS createTime,
    wft.update_time AS updateTime,
    wfi.title,
    wfd.name processName,
    wfd.category,
    wfc.category_code as categoryCode,
    wfc.category_name as categoryName,
    wfd.pc_form_url pcFormUrl,
    wfd.mobile_form_url mobileFormUrl,
    wfi.status AS instanceStatus
FROM
    workflow_task wft
    INNER JOIN workflow_instance wfi ON (wfi.id = wft.instance_id)
    INNER JOIN workflow_definition wfd ON (wfd.id = wfi.definition_id)
    LEFT JOIN workflow_category wfc ON (wfd.category = wfc.id)
WHERE
    wft.assignee_id = #{userId}
  AND wft.node_type = #{nodeType}
    <if test="instanceStatus != null">
  AND wfi.status = #{instanceStatus}
    </if>
    <if test="title != null">
  AND wfi.title LIKE #{title}
    </if>
    <if test="category != null">
  AND wfd.category = #{category}
    </if>
    <if test="startTime != null">
  AND wft.create_time >= #{startTime}
    </if>
             <if test="endTime != null">
               AND wft.create_time  <= #{endTime}
          </if>
ORDER BY
    wft.create_time DESC

    ]]>

