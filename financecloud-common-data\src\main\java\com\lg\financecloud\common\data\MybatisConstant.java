package com.lg.financecloud.common.data;

import java.util.HashMap;
import java.util.Map;

public interface MybatisConstant {
    String MASTER_DB_NAME = "master";
    String SWITH_DB_INTERCEPT_NAME = "swithDb@true";

    String DP_RULE_CACHE_PREFIX = "gl:dp_rule";
    String DP_RULE_VAR_PREFIX = "{current";

    // 内部类 RuleVariable
    class RuleVariable {
        private String variable;
        private String description;
        private String uiComment;

        public RuleVariable(String variable, String description) {
            this.variable = variable;
            this.description = description;
        }
        public RuleVariable(String variable, String description, String uiComment) {
            this.variable = variable;
            this.uiComment = uiComment;
            this.description = description;
        }

        public String getVariable() {
            return variable;
        }

        public String getDescription() {
            return description;
        }
        public String getUiComment() {
            return uiComment;
        }

        @Override
        public String toString() {
            return "RuleVariable{" +
                    "variable='" + variable + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    // RuleVariable 对象
    RuleVariable DP_RULE_VAR_CURRENT_USER_ID = new RuleVariable("{currentUserId}", "当前用户ID","selectUser");
    RuleVariable DP_RULE_VAR_CURRENT_JOB_ID = new RuleVariable("{currentJobId}", "当前职位ID","selectJob");
    RuleVariable DP_RULE_VAR_CURRENT_STAFF_ID = new RuleVariable("{currentStaffId}", "当前员工ID","selectStaff");
    RuleVariable DP_RULE_VAR_CURRENT_ROLE_ID = new RuleVariable("{currentRoleId}", "当前角色ID","selectRole");
    RuleVariable DP_RULE_VAR_CURRENT_DEPT_ID = new RuleVariable("{currentDeptId}", "当前部门ID","selectDept");
    RuleVariable DP_RULE_VAR_CURRENT_COMPANY_ID = new RuleVariable("{currentCompanyId}", "当前公司ID","selectCompany");
    RuleVariable DP_RULE_VAR_CURRENT_DEPT_AND_SUB = new RuleVariable("{currentDeptAndSub}", "本部门以及子部门");
    RuleVariable DP_RULE_VAR_CURRENT_COMPANY_AND_SUB = new RuleVariable("{currentCompanyAndSub}", "本公司以及子公司");




    String DP_RULE_CACHE_LOAD_TOPIC = "dpRuleCacheLoad";
    String TABLE_METADATA_LOAD_TOPIC = "tableMetaDataLoad";


    // 方法：获取所有支持的变量
      static Map<String, RuleVariable> getAllSupportedVariables() {
        Map<String, RuleVariable> variables = new HashMap<>();
        variables.put(DP_RULE_VAR_CURRENT_USER_ID.getVariable(), DP_RULE_VAR_CURRENT_USER_ID);
        variables.put(DP_RULE_VAR_CURRENT_STAFF_ID.getVariable(), DP_RULE_VAR_CURRENT_STAFF_ID);
        variables.put(DP_RULE_VAR_CURRENT_JOB_ID.getVariable(), DP_RULE_VAR_CURRENT_JOB_ID);
        variables.put(DP_RULE_VAR_CURRENT_ROLE_ID.getVariable(), DP_RULE_VAR_CURRENT_ROLE_ID);
        variables.put(DP_RULE_VAR_CURRENT_DEPT_ID.getVariable(), DP_RULE_VAR_CURRENT_DEPT_ID);
        variables.put(DP_RULE_VAR_CURRENT_COMPANY_ID.getVariable(), DP_RULE_VAR_CURRENT_COMPANY_ID);
//        variables.put(DP_RULE_VAR_CURRENT_DEPT_AND_SUB.getVariable(), DP_RULE_VAR_CURRENT_DEPT_AND_SUB);
//        variables.put(DP_RULE_VAR_CURRENT_COMPANY_AND_SUB.getVariable(), DP_RULE_VAR_CURRENT_COMPANY_AND_SUB);
        return variables;
    }
}
