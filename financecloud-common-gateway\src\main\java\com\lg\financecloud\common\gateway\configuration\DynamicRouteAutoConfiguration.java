/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.gateway.configuration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.config.PropertiesRouteDefinitionLocator;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2018/11/5
 * <p>
 * 动态路由配置类
 */
@Slf4j
@Configuration
@ComponentScan("com.lg.financecloud.common.gateway")
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@AllArgsConstructor
public class DynamicRouteAutoConfiguration {
	private final ApplicationEventPublisher applicationEventPublisher;
	/**
	 * 配置文件设置为空 redis 加载为准
	 * @return
	 */
	@Bean
	public PropertiesRouteDefinitionLocator propertiesRouteDefinitionLocator(GatewayProperties properties) {
		return new PropertiesRouteDefinitionLocator(properties);
	}

//	/**
//	 * redis 监听配置
//	 * @param redisConnectionFactory redis 配置
//	 * @return
//	 */
//	@Bean
//	public RedisMessageListenerContainer redisContainer(RedisConnectionFactory redisConnectionFactory) {
//		RedisMessageListenerContainer container = new RedisMessageListenerContainer();
//		container.setConnectionFactory(redisConnectionFactory);
//		container.addMessageListener((message, bytes) -> {
//			log.warn("接收到重新JVM 重新加载路由事件");
//			RouteCacheHolder.removeRouteList();
//			RedisRouteDefinitionWriter redisRouteDefinitionWriter = SpringContextHolder.getBean(RedisRouteDefinitionWriter.class);
////			redisRouteDefinitionWriter.getRouteDefinitions();
//			applicationEventPublisher.publishEvent(new RefreshRoutesEvent(this));
//		}, new ChannelTopic(CacheConstants.ROUTE_JVM_RELOAD_TOPIC));
//		return container;
//	}

//	@Bean
//	@ConditionalOnProperty(value = "spring.redis.cluster.enable", havingValue = "true")
//	public LettuceConnectionFactory redisConnectionFactory(RedisProperties redisProperties) {
//		RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(
//				redisProperties.getCluster().getNodes());
//
//		// https://github.com/lettuce-io/lettuce-core/wiki/Redis-Cluster#user-content-refreshing-the-cluster-topology-view
//		ClusterTopologyRefreshOptions clusterTopologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
//				.enablePeriodicRefresh().enableAllAdaptiveRefreshTriggers().refreshPeriod(Duration.ofSeconds(5))
//				.build();
//
//		ClusterClientOptions clusterClientOptions = ClusterClientOptions.builder()
//				.topologyRefreshOptions(clusterTopologyRefreshOptions).build();
//
//		// https://github.com/lettuce-io/lettuce-core/wiki/ReadFrom-Settings
//		LettuceClientConfiguration lettuceClientConfiguration = LettuceClientConfiguration.builder()
//				.readFrom(ReadFrom.REPLICA_PREFERRED).clientOptions(clusterClientOptions).build();
//
//		return new LettuceConnectionFactory(redisClusterConfiguration, lettuceClientConfiguration);
//	}

}
