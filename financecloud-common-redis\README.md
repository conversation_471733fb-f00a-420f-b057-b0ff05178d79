# Finance Cloud Redis缓存模块

## 概述

本模块为Finance Cloud Framework提供了灵活的缓存策略：

1. **纯Redis模式**：使用Redis作为唯一的缓存存储
2. **纯内存模式**：使用本地JVM内存(Caffeine)作为唯一的缓存存储
3. **二级缓存模式**：同时使用内存(L1)和Redis(L2)进行缓存

## 配置方法

在应用配置文件中配置缓存模式和相关设置：

```yaml
ligth:
  cache:
    # 缓存模式: REDIS, MEMORY, 或 TWO_LEVEL (默认: REDIS)
    cache-mode: REDIS
    
    # 内存缓存的最大条目数 (适用于 MEMORY 和 TWO_LEVEL 模式)
    max-size: 1000
    
    # 内存缓存条目的存活时间(秒) (默认: 3600 = 1小时)
    time-to-live-seconds: 3600
    
    # 内存缓存条目的空闲失效时间(秒) (默认: 1800 = 30分钟)
    time-to-idle-seconds: 1800
```

## 缓存模式说明

### REDIS模式 (默认)

- 所有缓存操作都使用Redis
- 适合于分布式系统，需要在多个节点之间保持数据一致性
- 适用于已配置合适Redis的生产环境

### MEMORY模式

- 所有缓存操作都使用本地内存(Caffeine)
- 高性能，无需网络调用
- 适合于开发、测试环境，或Redis不可用的场景
- 注意：在集群环境中，每个节点都有独立的缓存数据

### TWO_LEVEL模式

- 先检查本地内存缓存(L1)，再检查Redis(L2)
- 结合了内存缓存的性能与Redis分布式缓存的一致性
- 从Redis读取的数据自动填充到本地内存缓存
- 写操作同时更新内存和Redis缓存
- 适合于在分布式环境中有大量读操作的场景

## 缓存TTL配置

可以使用以下格式为缓存指定自定义的生存时间(TTL)值：

```java
@Cacheable("cacheName#60") // 缓存有60秒的TTL
```

## 全局缓存与租户特定缓存

默认情况下，所有缓存都是特定于租户的（以租户ID为前缀）。

要使用全局缓存（非租户特定），请使用`gl:`前缀：

```java
@Cacheable(CacheConstants.GLOBALLY + "cacheName")
```

## 缓存统计

启用了缓存统计功能，可以查看每个缓存的命中率和其他关键指标：

- 对于内存缓存：可以查看命中次数、未命中次数等 
- 对于二级缓存：可以查看L1命中、L2命中以及未命中次数等

## 性能提示

- 纯内存模式的性能最好，但不支持分布式场景
- 二级缓存模式在读取操作上接近纯内存模式，同时保证了分布式一致性
- 纯Redis模式适合于缓存内容较少但要求一致性的场景 