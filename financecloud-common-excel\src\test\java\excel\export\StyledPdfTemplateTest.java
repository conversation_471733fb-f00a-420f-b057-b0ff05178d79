package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.util.*;

/**
 * 带样式的PDF模板测试
 * 验证PDF输出是否正确继承了Excel模板的样式
 */
public class StyledPdfTemplateTest {

    public static void main(String[] args) {
        System.out.println("=== 带样式的PDF模板导出测试 ===");
        
        try {
            // 检查PDF依赖
            try {
                Class.forName("com.itextpdf.text.Document");
                System.out.println("✓ PDF依赖检查通过");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠ 警告: 未找到iText依赖，跳过PDF导出测试");
                return;
            }
            
            StyledPdfTemplateTest test = new StyledPdfTemplateTest();
            
            // 创建带样式的Excel模板
            String templatePath = "financecloud-common-excel/src/test/java/excel/export/excel/styled_template.xlsx";
            test.createStyledTemplate(templatePath);
            System.out.println("✓ 带样式的Excel模板创建成功");
            
            // 测试PDF模板导出
            test.testStyledPdfExport(templatePath);
            System.out.println("✓ 带样式的PDF模板导出测试完成");
            
        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建带样式的Excel模板
     */
    private void createStyledTemplate(String templatePath) throws Exception {
        // 确保目录存在
        java.io.File file = new java.io.File(templatePath);
        file.getParentFile().mkdirs();
        
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("样式模板");
        
        // 创建各种样式
        Map<String, CellStyle> styles = createStyles(workbook);
        
        // 第一行：公司标题（大字体，居中，背景色）
        Row row0 = sheet.createRow(0);
        Cell titleCell = row0.createCell(0);
        titleCell.setCellValue("${companyName}");
        titleCell.setCellStyle(styles.get("title"));
        
        // 合并标题单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        
        // 第二行：报表标题
        Row row1 = sheet.createRow(1);
        Cell reportTitleCell = row1.createCell(0);
        reportTitleCell.setCellValue("${reportTitle}");
        reportTitleCell.setCellStyle(styles.get("subtitle"));
        
        Cell dateCell = row1.createCell(3);
        dateCell.setCellValue("报表日期: ${reportDate}");
        dateCell.setCellStyle(styles.get("info"));
        
        // 第三行：空行
        Row row2 = sheet.createRow(2);
        
        // 第四行：表头
        Row headerRow = sheet.createRow(3);
        String[] headers = {"员工编号", "姓名", "部门", "职位", "薪资"};
        for (int i = 0; i < headers.length; i++) {
            Cell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(headers[i]);
            headerCell.setCellStyle(styles.get("header"));
        }
        
        // 第五行：循环开始标记
        Row loopStartRow = sheet.createRow(4);
        Cell loopStartCell = loopStartRow.createCell(0);
        loopStartCell.setCellValue("${#foreach employees}");
        loopStartCell.setCellStyle(styles.get("hidden"));
        
        // 第六行：数据行模板
        Row dataRow = sheet.createRow(5);
        String[] dataFields = {"${employeeId}", "${name}", "${department}", "${position}", "${salary}"};
        for (int i = 0; i < dataFields.length; i++) {
            Cell dataCell = dataRow.createCell(i);
            dataCell.setCellValue(dataFields[i]);
            if (i == 4) { // 薪资列使用数字样式
                dataCell.setCellStyle(styles.get("currency"));
            } else {
                dataCell.setCellStyle(styles.get("data"));
            }
        }
        
        // 第七行：循环结束标记
        Row loopEndRow = sheet.createRow(6);
        Cell loopEndCell = loopEndRow.createCell(0);
        loopEndCell.setCellValue("${/foreach}");
        loopEndCell.setCellStyle(styles.get("hidden"));
        
        // 第八行：空行
        Row row7 = sheet.createRow(7);
        
        // 第九行：汇总信息
        Row summaryRow = sheet.createRow(8);
        Cell summaryCell = summaryRow.createCell(0);
        summaryCell.setCellValue("总人数: ${totalEmployees}");
        summaryCell.setCellStyle(styles.get("summary"));
        
        Cell avgSalaryCell = summaryRow.createCell(3);
        avgSalaryCell.setCellValue("平均薪资: ${avgSalary}");
        avgSalaryCell.setCellStyle(styles.get("summary"));
        
        // 设置列宽
        sheet.setColumnWidth(0, 3000);  // 员工编号
        sheet.setColumnWidth(1, 4000);  // 姓名
        sheet.setColumnWidth(2, 4000);  // 部门
        sheet.setColumnWidth(3, 4000);  // 职位
        sheet.setColumnWidth(4, 4000);  // 薪资
        
        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(templatePath)) {
            workbook.write(fos);
        }
        
        workbook.close();
    }

    /**
     * 创建各种样式
     */
    private Map<String, CellStyle> createStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();
        
        // 标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 18);
        titleFont.setBold(true);
        titleFont.setColor(IndexedColors.DARK_BLUE.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setBorderTop(BorderStyle.THICK);
        titleStyle.setBorderBottom(BorderStyle.THICK);
        titleStyle.setBorderLeft(BorderStyle.THICK);
        titleStyle.setBorderRight(BorderStyle.THICK);
        styles.put("title", titleStyle);
        
        // 副标题样式
        CellStyle subtitleStyle = workbook.createCellStyle();
        Font subtitleFont = workbook.createFont();
        subtitleFont.setFontHeightInPoints((short) 14);
        subtitleFont.setBold(true);
        subtitleFont.setColor(IndexedColors.DARK_GREEN.getIndex());
        subtitleStyle.setFont(subtitleFont);
        subtitleStyle.setAlignment(HorizontalAlignment.LEFT);
        subtitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.put("subtitle", subtitleStyle);
        
        // 信息样式
        CellStyle infoStyle = workbook.createCellStyle();
        Font infoFont = workbook.createFont();
        infoFont.setFontHeightInPoints((short) 10);
        infoFont.setItalic(true);
        infoFont.setColor(IndexedColors.GREY_50_PERCENT.getIndex());
        infoStyle.setFont(infoFont);
        infoStyle.setAlignment(HorizontalAlignment.RIGHT);
        styles.put("info", infoStyle);
        
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        styles.put("header", headerStyle);
        
        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        styles.put("data", dataStyle);
        
        // 货币样式
        CellStyle currencyStyle = workbook.createCellStyle();
        Font currencyFont = workbook.createFont();
        currencyFont.setFontHeightInPoints((short) 10);
        currencyFont.setBold(true);
        currencyFont.setColor(IndexedColors.DARK_RED.getIndex());
        currencyStyle.setFont(currencyFont);
        currencyStyle.setAlignment(HorizontalAlignment.RIGHT);
        currencyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        currencyStyle.setBorderTop(BorderStyle.THIN);
        currencyStyle.setBorderBottom(BorderStyle.THIN);
        currencyStyle.setBorderLeft(BorderStyle.THIN);
        currencyStyle.setBorderRight(BorderStyle.THIN);
        currencyStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        currencyStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("currency", currencyStyle);
        
        // 汇总样式
        CellStyle summaryStyle = workbook.createCellStyle();
        Font summaryFont = workbook.createFont();
        summaryFont.setFontHeightInPoints((short) 11);
        summaryFont.setBold(true);
        summaryFont.setColor(IndexedColors.DARK_BLUE.getIndex());
        summaryStyle.setFont(summaryFont);
        summaryStyle.setAlignment(HorizontalAlignment.LEFT);
        summaryStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        summaryStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        summaryStyle.setBorderTop(BorderStyle.MEDIUM);
        summaryStyle.setBorderBottom(BorderStyle.MEDIUM);
        summaryStyle.setBorderLeft(BorderStyle.MEDIUM);
        summaryStyle.setBorderRight(BorderStyle.MEDIUM);
        styles.put("summary", summaryStyle);
        
        // 隐藏样式（用于循环标记）
        CellStyle hiddenStyle = workbook.createCellStyle();
        Font hiddenFont = workbook.createFont();
        hiddenFont.setFontHeightInPoints((short) 8);
        hiddenFont.setColor(IndexedColors.WHITE.getIndex());
        hiddenStyle.setFont(hiddenFont);
        hiddenStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        hiddenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("hidden", hiddenStyle);
        
        return styles;
    }

    /**
     * 测试带样式的PDF导出
     */
    private void testStyledPdfExport(String templatePath) throws Exception {
        // 准备数据
        Map<String, Object> variables = new HashMap<>();
        variables.put("companyName", "金融云科技有限公司");
        variables.put("reportTitle", "员工薪资报表");
        variables.put("reportDate", "2025年08月21日");
        variables.put("totalEmployees", "5");
        variables.put("avgSalary", "¥12,000");
        
        // 员工数据
        List<Map<String, Object>> employees = new ArrayList<>();
        
        String[] names = {"张三", "李四", "王五", "赵六", "钱七"};
        String[] departments = {"技术部", "销售部", "财务部", "人事部", "市场部"};
        String[] positions = {"高级工程师", "销售经理", "财务专员", "HR专员", "市场专员"};
        String[] salaries = {"¥15,000", "¥12,000", "¥10,000", "¥9,000", "¥14,000"};
        
        for (int i = 0; i < names.length; i++) {
            Map<String, Object> employee = new HashMap<>();
            employee.put("employeeId", "EMP" + String.format("%03d", i + 1));
            employee.put("name", names[i]);
            employee.put("department", departments[i]);
            employee.put("position", positions[i]);
            employee.put("salary", salaries[i]);
            employees.add(employee);
        }
        
        // 执行PDF模板导出
        String outputPath = "financecloud-common-excel/src/test/java/excel/export/excel/styled_result.pdf";
        
        ExcelHelper.opsPdfTemplate(templatePath)
                .vars(variables)
                .loop("employees", employees)
                .fontSize(10f)
                .enableChineseFont(true)
                .orientation(com.github.stupdit1t.excel.pdf.PdfTemplateConfig.PageOrientation.PORTRAIT)
                .pageSize(com.github.stupdit1t.excel.pdf.PdfTemplateConfig.PageSize.A4)
                .margins(20f)
                .showGridLines(false) // 使用Excel样式的边框，不显示默认网格线
                .exportTo(outputPath);
        
        // 验证输出文件
        java.io.File outputFile = new java.io.File(outputPath);
        if (outputFile.exists() && outputFile.length() > 0) {
            System.out.println("✓ 带样式的PDF文件生成成功");
            System.out.println("  模板文件: " + templatePath);
            System.out.println("  输出文件: " + outputPath);
            System.out.println("  文件大小: " + outputFile.length() + " 字节");
        } else {
            System.out.println("⚠ PDF文件未生成或为空");
        }
    }
}
