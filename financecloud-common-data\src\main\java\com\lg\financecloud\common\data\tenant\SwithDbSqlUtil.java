package com.lg.financecloud.common.data.tenant;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLName;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlRenameTableStatement;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
import com.alibaba.druid.util.JdbcConstants;
import com.lg.financecloud.common.core.constant.CommonConstants;
import com.lg.financecloud.common.core.util.SpringContextHolder;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@ConditionalOnBean(DataSource.class)
public class SwithDbSqlUtil {


    public static List<String> filterTableNames = new ArrayList<>() ;


    @Autowired
    private CloudxTenantConfigProperties cloudxTenantConfigProperties;


    @PostConstruct
    private void init(){
        List<String> tables = cloudxTenantConfigProperties.getGlobTables();
        filterTableNames = tables;
    }

    public static  String MASTER_DB_NAME = null;

//
//    private static void appendSchema(Table tableName,String schemaName) {
//        List<String> globTables = SwithDbSqlUtil.getGlobTables();
//        Boolean globTableFlag=false;
//        for(String globTable : globTables){
//            if(tableName.getName().toUpperCase().equals(globTable.toUpperCase())){
//                globTableFlag = true;
//                break;
//            }
//        }
//        if (!globTableFlag) {
//            if(StrUtil.isNotEmpty(schemaName))
//                tableName.setSchemaName(schemaName);
//        }
//    }
//
//    /***
//     * jsql parse 实现
//     * @param sql
//     * @return
//     * @throws JSQLParserException
//     */
//    private static String replaceTableNameWithDbName4jsqlparse(String sql) throws JSQLParserException {
//         try {
//             Statement stmt = CCJSqlParserUtil.parse(sql);
//             String switchDBName = getDbName();
//             String schemeName = "";
//             if(StrUtil.isNotEmpty(switchDBName)){
//                   schemeName = String.format("`%s`", switchDBName);
//             }
//             List<Table> tables = new ArrayList<>();
//             TablesNamesFinder tablesNamesFinder = new TablesNamesFinder() {
//                 @Override
//                 public void visit(Table tableName) {
//                     tables.add(tableName);
//                 }
//             };
//             // 优化 仅查找表名， 不解析列
//             tablesNamesFinder.getTableList(stmt);
//
//             for (Table table : tables) {
//                 appendSchema(table,schemeName);
//             }
//              return  stmt.toString();
//
//         }catch(Exception e){
//             log.error("jsql parse error orign sql={}",sql);
//             return getSwithDbSql(sql);
//         }
//
//    }


//    /***
//     * 两种sql parse 引擎实现sql  解析
//     *
//     * update  和delete  交给 jsqlparse
//     * 查询 和ddl  交给durid  处理
//     *
//     * 两者都处理不了 交给 正则表达式 替换，性能低
//     * @param sql
//     * @return
//     */
//    public static String getSwithDbSql4SqlParse(String sql)  {
//
//        try {
//            // 批量 sql  交给durid
//            if (sql.contains(";")) {
//                StringBuffer sb = new StringBuffer();
//                for (String sqlQuery : sql.split(";")) {
//                    sb.append(replaceTableNameWithDbName4durid(sqlQuery)).append(";");
//                }
//                return sb.toString();
//            } else if(StrUtil.containsAnyIgnoreCase(sql,"update ","delete ")){
//                return replaceTableNameWithDbName4jsqlparse(sql);
//            } else if(StrUtil.containsAnyIgnoreCase(sql,"create ")){
//                return replaceTableNameWithDbName4durid(sql);
//            } else if(StrUtil.containsAll(sql.toLowerCase(),"alter","to")){
//                return getSwithDbSql(sql);
//            }
//
//            return replaceTableNameWithDbName4durid(sql);
//
//
//        }catch(Exception e){
//            return getSwithDbSql(sql);
//        }
//    }

//
//    private static String replaceTableNameWithDbName4durid(String originalSql) {
//        SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(originalSql, JdbcConstants.MYSQL);
//
//
//        SQLStatement statement = parser.parseStatement();
//        String dbName = getDbName();
//
//        // 遍历 SQL 语句中的表达式，将表名替换为带有库名的形式
//        statement.accept(new SQLASTVisitorAdapter() {
//            public boolean visit(SQLExprTableSource x) {
//                List<String> globTables = SwithDbSqlUtil.getGlobTables();
//                Boolean globTableFlag=false;
//                for(String globTable : globTables){
//                    if(x.getName().getSimpleName().toUpperCase().equals(globTable.toUpperCase())){
//                        globTableFlag = true;
//                        break;
//                    }
//                }
//
//                if (!globTableFlag) {
//                    if(StrUtil.isNotEmpty(dbName))
//                        if(StrUtil.isEmpty(x.getSchema()))
//                        x.setSchema(dbName);
//                }
//
//
//                return true;
//            }
//        });
//
//        // 重新生成替换后的 SQL 语句
//        return SQLUtils.toSQLString(statement, JdbcConstants.MYSQL);
//    }
//
//
//    // 根据原始SQL  生成分库sql
//    private static String getSwithDbSql(String originalSql){
//        if (log.isDebugEnabled()){
//            log.debug("originalSql=={}",originalSql);
//        }
//
//        List<String> 	tableNameList = null;
//        String sql = originalSql;
//        try {
//            //获取sql中的全部表名
//            tableNameList = getTableNameBySql(originalSql);
//        } catch (Exception e) {
//        }
//        if (tableNameList != null) {
//            //更新sql语句，表名加上数据库前缀
//            String switchDBName = getDbName();
//
//            List<String> globTables = getGlobTables();
//            for (String tableName : tableNameList) {
//                //忽略的动态切换数据库的表名
//
//                Boolean globTableFlag=false;
//                for(String globTable : globTables){
//                    if(tableName.toUpperCase().equals(globTable.toUpperCase())){
//                        globTableFlag = true;
//                    }
//                }
//                if (!globTableFlag) {
//                   // String regex = "(?<=(\\s+|\\r\\n|\\r|\\n|,))" + tableName + "(?=(\\s*|\\r\\n|\\r|\\n|,))";
//                    String regex = "(?<=(\\s+))" + tableName + "(?=(\\s*))";
//                    if(StrUtil.isNotEmpty(switchDBName)){
//                        if (!tableName.toUpperCase().contains(switchDBName.toUpperCase()+".")){//默认带了库名的暂时以不做修改处理，（可能出现的情况是，带的库名不一定正确）
//                            sql = sql.replaceAll(regex, switchDBName  +"."+ tableName);
//                        }
//                    }
//
//
//                }
//            }
//
//        }
//
//        return sql;
//    }
//    /**
//     * 使用druid获取sql中的表名
//     *
//     * @param sql
//     * @return java.util.List<java.lang.String>
//     * <AUTHOR>
//     * @date 2022-12-2
//     **/
//    private static List<String> getTableNameBySql(String sql) {
//          ;
//        List<String> tableNameList = new ArrayList<>();
//        try {
//            List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, JdbcConstants.MYSQL);
//            if (CollectionUtil.isNotEmpty(stmtList)) {
//                for (SQLStatement sqlStatement : stmtList) {
//                    if (sqlStatement instanceof MySqlRenameTableStatement) {
//                        MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) sqlStatement;
//                        tableNameList.add( renameTableStatement.getItems().get(0).getName().getSimpleName());
//                        tableNameList.add( renameTableStatement.getItems().get(0).getTo().getSimpleName());
//                        continue;
//                    }
//                    MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
//                    sqlStatement.accept(visitor);
//                    Map<TableStat.Name, TableStat> visitorTables = visitor.getTables();
//                    Set<TableStat.Name> tableNameSet = visitorTables.keySet();
//                    for (TableStat.Name name : tableNameSet) {
//
//                        String tableName = name.getName();
//                        if (StrUtil.isNotBlank(tableName)) {
//                            tableNameList.add(tableName);
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("getTableNameBySql error",e);
//        }
//        return tableNameList;
//    }

    public static List<String> getGlobTables(){
        return filterTableNames;
    }

    public static String getDbName(){
        Integer tenantId = TenantContextHolder.getTenantId();
        if(tenantId ==null){
            tenantId = 1;
        }
        if( tenantId ==1){
            return "";
        }
        return getMasterDbName()+ StrUtil.UNDERLINE+tenantId;
    }

    /***
     * 根据jdbc  url 获取 主数据源库名
     * @return
     */
    public static String getMasterDbName() {
        if(StrUtil.isNotEmpty((MASTER_DB_NAME))){
            return MASTER_DB_NAME;
        }
        String jdbcUrl = SpringUtil.getProperty("spring.datasource.url");
//        HikariDataSource dataSource = (HikariDataSource) SpringContextHolder.getBean(HikariDataSource.class);
        Pattern p = Pattern.compile("jdbc:(?<db>\\w+):.*((//)|@)(?<host>.+):(?<port>\\d+)(/|(;DatabaseName=)|:)(?<dbName>\\w+)\\??.*");
        Matcher m = p.matcher(jdbcUrl);
        if(m.find()) {
            String dbName = m.group("dbName");
            MASTER_DB_NAME = dbName;
            return dbName;
        }

        return null;
    }


    public static String getSwithDbSql4SqlParse(String sql,List<SQLStatement> parsedList) {
        {
            if(!CommonConstants.ERP_ENABLE_DEFAULT_VAL.equals(SpringUtil.getProperty(CommonConstants.ERP_ENABLE))) {
                return sql;
            }
            String dbName = getDbName();
            if(StrUtil.isEmpty(dbName)){
                return sql;
            }
            StringBuffer stringBuffer = new StringBuffer();
            List<String> tableNameList = new ArrayList<String>();
            DbType mysql = JdbcConstants.MYSQL;
            List<SQLStatement> stmtList =null;
            if(parsedList != null){
                stmtList = parsedList;
            }else {
                 stmtList = SQLUtils.parseStatements(sql, mysql);
            }

            List<String> globTable = getGlobTables();
            // 全局表转为小写
            List<String> lowerGlobTable = new ArrayList<>();
            for(String glob:globTable){
                lowerGlobTable.add(glob.toLowerCase());
            }
            for (SQLStatement sqlStatement : stmtList) {
                List<SQLName> originalTables = new ArrayList<>();
                // rename sql 单独处理
                if (sqlStatement instanceof MySqlRenameTableStatement) {
                    MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) sqlStatement;
                    MySqlRenameTableStatement.Item item = renameTableStatement.getItems().get(0);
                    originalTables.add( item.getName());
                    originalTables.add( item.getTo());
                }else{
                    MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
                    sqlStatement.accept(visitor);
                    originalTables = visitor.getOriginalTables().stream().filter(x->{

                        String tableName = x.getSimpleName();
                        if(x.getSimpleName().contains("`")){
                            tableName = x.getSimpleName().replaceAll("`","");
                        }

                        return  !CollectionUtil.contains(lowerGlobTable, tableName.toLowerCase());
                    }).collect(Collectors.toList());
                }

                for(SQLName sqlname: originalTables){
                    if(StrUtil.isNotEmpty(dbName)){
                        if(!sqlname.toString().contains(dbName) && !StrUtil.contains(sqlname.toString(),StrUtil.DOT )){
                            String newDbName = String.format("`%s`.%s",dbName,sqlname.getSimpleName());
                            ReflectUtil.setFieldValue(sqlname, "name", newDbName);
                        }
                    }
                }
                String newSql = SQLUtils.toSQLString(sqlStatement, JdbcConstants.MYSQL);
                stringBuffer.append(newSql);
            }
            //System.err.println(stringBuffer);
            return stringBuffer.toString();
        }
    }


    public static String getSwithDbSql4SqlParse(String sql) {
        if(!CommonConstants.ERP_ENABLE_DEFAULT_VAL.equals(SpringUtil.getProperty(CommonConstants.ERP_ENABLE))) {
            return sql;
        }
        String dbName = getDbName();
        if(StrUtil.isEmpty(dbName)){
            return sql;
        }
        StringBuffer stringBuffer = new StringBuffer();
        List<String> tableNameList = new ArrayList<String>();
        DbType mysql = JdbcConstants.MYSQL;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, mysql);

        List<String> globTable = getGlobTables();
        // 全局表转为小写
        List<String> lowerGlobTable = new ArrayList<>();
        for(String glob:globTable){
            lowerGlobTable.add(glob.toLowerCase());
        }
        for (SQLStatement sqlStatement : stmtList) {
            List<SQLName> originalTables = new ArrayList<>();
            // rename sql 单独处理
            if (sqlStatement instanceof MySqlRenameTableStatement) {
                MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) sqlStatement;
                MySqlRenameTableStatement.Item item = renameTableStatement.getItems().get(0);
                originalTables.add( item.getName());
                originalTables.add( item.getTo());
            }else{
                MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
                sqlStatement.accept(visitor);
                originalTables = visitor.getOriginalTables().stream().filter(x->{
                    String tableName = x.getSimpleName();
                    if(x.getSimpleName().contains("`")){
                        tableName = x.getSimpleName().replaceAll("`","");
                    }

                    return  !CollectionUtil.contains(lowerGlobTable, tableName.toLowerCase());
                }).collect(Collectors.toList());
            }

            for(SQLName sqlname: originalTables){
                if(StrUtil.isNotEmpty(dbName)){
                    if(!sqlname.toString().contains(dbName) && !StrUtil.contains(sqlname.toString(),StrUtil.DOT )){
                        String newDbName = String.format("`%s`.%s",dbName,sqlname.getSimpleName());
                        ReflectUtil.setFieldValue(sqlname, "name", newDbName);
                    }
                }
            }
            String newSql = SQLUtils.toSQLString(sqlStatement, JdbcConstants.MYSQL);
            stringBuffer.append(newSql);
        }
        //System.err.println(stringBuffer);
        return stringBuffer.toString();
    }

}
