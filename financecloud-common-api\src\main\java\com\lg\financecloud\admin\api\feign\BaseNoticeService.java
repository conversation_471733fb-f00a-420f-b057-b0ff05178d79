/*
 *
 *      Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: cloudx
 *
 */

package com.lg.financecloud.admin.api.feign;

import com.lg.financecloud.admin.api.dto.BaseNoticeDto;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "baseNoticeService", value = ServiceNameConstants.UPMS_SERVICE)
public interface BaseNoticeService {

	/**
	 * 添加通知
	 *
	 * @param baseNoticeDto
	 * @return
	 */
	@PostMapping("/baseNotice/saveInner")
	R saveInner(@RequestBody BaseNoticeDto baseNoticeDto);

	@PostMapping("/baseNotice/sendInnerMsg")
	R sendInnerMsg(@RequestBody BaseNoticeDto baseNoticeDto);

	/**
	 * 删除通知
	 *
	 * @param baseNoticeDto
	 * @return
	 */
	@PostMapping("/baseNotice/removeByIdInner")
	R removeByIdInner(@RequestBody BaseNoticeDto baseNoticeDto);
	/**
	 * 远程调用sse接口
	 *
	 * @param baseNoticeDto
	 * @return
	 */
	@PostMapping("/baseNotice/sendMsg4SSE")
	R sendMsg4SSE(@RequestBody BaseNoticeDto baseNoticeDto);
}
