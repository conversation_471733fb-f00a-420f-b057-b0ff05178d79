package com.lg.financecloud.common.data.tenant;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLName;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLAlterTableStatement;
import com.alibaba.druid.sql.ast.statement.SQLCreateTableStatement;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlRenameTableStatement;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
import com.alibaba.druid.sql.parser.SQLParserUtils;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.alibaba.druid.sql.visitor.SQLASTVisitorAdapter;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;
import com.lg.financecloud.common.core.util.SpringContextHolder;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.util.TablesNamesFinder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@ConditionalOnBean(DataSource.class)
public class SwithDbSqlUtilTest {


    public static List<String> filterTableNames = new ArrayList<>() ;


   // @Autowired
   // private SwithDbSqlConfig swithDbSqlConfig;


    @PostConstruct
    private void init(){
      //  List<String> tables = swithDbSqlConfig.getTables();
      //  filterTableNames = tables;
    }

    public static  String MASTER_DB_NAME = null;

    private static void appendSchema(Table tableName,String schemaName) {
        List<String> globTables = SwithDbSqlUtilTest.getGlobTables();
        Boolean globTableFlag=false;
        for(String globTable : globTables){
            if(tableName.getName().toUpperCase().equals(globTable.toUpperCase())){
                globTableFlag = true;
                break;
            }
        }
        if (!globTableFlag) {
            if(StrUtil.isNotEmpty(schemaName))
                tableName.setSchemaName(schemaName);
        }
    }

    /***
     * jsql parse 实现
     * @param sql
     * @return
     * @throws JSQLParserException
     */
    public static String replaceTableNameWithDbName4jsqlparse(String sql) throws JSQLParserException {
         try {
             Statement stmt = CCJSqlParserUtil.parse(sql);
             String switchDBName = getDbName();
             String schemeName = "";
             if(StrUtil.isNotEmpty(switchDBName)){
                   schemeName = String.format("`%s`", switchDBName);
             }
             List<Table> tables = new ArrayList<>();
             TablesNamesFinder tablesNamesFinder = new TablesNamesFinder() {
                 @Override
                 public void visit(Table tableName) {
                     tables.add(tableName);
                 }
             };
             // 优化 仅查找表名， 不解析列
             tablesNamesFinder.getTableList(stmt);

             for (Table table : tables) {
                 appendSchema(table,schemeName);
             }
              return  stmt.toString();

         }catch(Exception e){
             log.error("jsql parse error orign sql={}",sql);
             return getSwithDbSql(sql);
         }

    }


    /***
     * 两种sql parse 引擎实现sql  解析
     *
     * update  和delete  交给 jsqlparse
     * 查询 和ddl  交给durid  处理
     *
     * 两者都处理不了 交给 正则表达式 替换，性能低
     * @param sql
     * @return
     */
    public static String getSwithDbSql4SqlParse(String sql)  {

        try {
            // 批量 sql  交给durid
            if (sql.contains(";")) {
                StringBuffer sb = new StringBuffer();
                for (String sqlQuery : sql.split(";")) {
                    sb.append(replaceTableNameWithDbName4durid(sqlQuery)).append(";");
                }
                return sb.toString();
            } else if(StrUtil.containsAnyIgnoreCase(sql,"update","delete ")){

                return replaceTableNameWithDbName4jsqlparse(sql);
            } else if(StrUtil.containsAnyIgnoreCase(sql,"create")){
                return replaceTableNameWithDbName4durid(sql);
            } else if(StrUtil.containsAll(sql.toLowerCase(),"alter","to")){
                return getSwithDbSql(sql);
            }

            return replaceTableNameWithDbName4durid(sql);


        }catch(Exception e){
           return getSwithDbSql(sql);
        }
    }


    public static String replaceTableNameWithDbName4durid(String originalSql) {
        SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(originalSql, JdbcConstants.MYSQL);


        SQLStatement statement = parser.parseStatement();
        String dbName = getDbName();

        // 遍历 SQL 语句中的表达式，将表名替换为带有库名的形式
        statement.accept(new SQLASTVisitorAdapter() {

//            @Override
//            public boolean visit(SQLIdentifierExpr x) {
//
//                x.setName(dbName + "." + x.getName());
//                return super.visit(x);
//            }

            public boolean visit(SQLExprTableSource x) {
                List<String> globTables = SwithDbSqlUtilTest.getGlobTables();
                Boolean globTableFlag=false;
                for(String globTable : globTables){
                    if(x.getName().getSimpleName().toUpperCase().equals(globTable.toUpperCase())){
                        globTableFlag = true;
                        break;
                    }
                }

                if (!globTableFlag) {
                    if(StrUtil.isNotEmpty(dbName))
                        if(StrUtil.isEmpty(x.getSchema()))
                        x.setSchema(dbName);
                }


                return true;
            }
        });

        // 重新生成替换后的 SQL 语句
        return SQLUtils.toSQLString(statement, JdbcConstants.MYSQL);
    }


    // 根据原始SQL  生成分库sql
    public static String getSwithDbSql(String originalSql){
        if (log.isDebugEnabled()){
            log.debug("originalSql=={}",originalSql);
        }

        List<String> 	tableNameList = null;
        String sql = originalSql;
        try {
            //获取sql中的全部表名
            tableNameList = getTableNameBySql(originalSql);
        } catch (Exception e) {
        }
        if (tableNameList != null) {
            //更新sql语句，表名加上数据库前缀
            String switchDBName = getDbName();

            List<String> globTables = getGlobTables();
            for (String tableName : tableNameList) {
                //忽略的动态切换数据库的表名

                Boolean globTableFlag=false;
                for(String globTable : globTables){
                    if(tableName.toUpperCase().equals(globTable.toUpperCase())){
                        globTableFlag = true;
                    }
                }
                if (!globTableFlag) {
                    //String regex = "(?<=(\\s*|\\r\\n|\\r|\\n|,))" + tableName + "(?=(\\s*|\\r\\n|\\r|\\n|,))";
                    String regex = "(?<=(\\s+))" + tableName + "(?=(\\s*))";
                    if(StrUtil.isNotEmpty(switchDBName)){
                        if (!tableName.toUpperCase().contains(switchDBName.toUpperCase()+".")){//默认带了库名的暂时以不做修改处理，（可能出现的情况是，带的库名不一定正确）
                            sql = sql.replaceAll(regex, switchDBName  +"."+ tableName);
                        }
                    }


                }
            }

        }

        return sql;
    }
    /**
     * 使用druid获取sql中的表名
     *
     * @param sql
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022-12-2
     **/
    private static List<String> getTableNameBySql(String sql) {
          ;
        List<String> tableNameList = new ArrayList<>();
        try {
            List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, JdbcConstants.MYSQL);
            if (CollectionUtil.isNotEmpty(stmtList)) {
                for (SQLStatement sqlStatement : stmtList) {
                    if (sqlStatement instanceof MySqlRenameTableStatement) {
                        MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) sqlStatement;
                        tableNameList.add( renameTableStatement.getItems().get(0).getName().getSimpleName());
                        tableNameList.add( renameTableStatement.getItems().get(0).getTo().getSimpleName());
                        continue;
                    }
                    MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
                    sqlStatement.accept(visitor);
                    Map<TableStat.Name, TableStat> visitorTables = visitor.getTables();
                    Set<TableStat.Name> tableNameSet = visitorTables.keySet();
                    for (TableStat.Name name : tableNameSet) {

                        String tableName = name.getName();
                        if (StrUtil.isNotBlank(tableName)) {
                            tableNameList.add(tableName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getTableNameBySql error",e);
        }
        return tableNameList;
    }

    public static List<String> getGlobTables(){
        return filterTableNames;
    }

    public static String getDbName(){
       return "lg_erp_100";
    }

    /***
     * 根据jdbc  url 获取 主数据源库名
     * @return
     */
    public static String getMasterDbName() {
        if(StrUtil.isNotEmpty((MASTER_DB_NAME))){
            return MASTER_DB_NAME;
        }
        HikariDataSource dataSource = (HikariDataSource) SpringContextHolder.getBean(HikariDataSource.class);
        Pattern p = Pattern.compile("jdbc:(?<db>\\w+):.*((//)|@)(?<host>.+):(?<port>\\d+)(/|(;DatabaseName=)|:)(?<dbName>\\w+)\\??.*");
        Matcher m = p.matcher(dataSource.getJdbcUrl());
        if(m.find()) {
            String dbName = m.group("dbName");
            MASTER_DB_NAME = dbName;
            return dbName;
        }

        return null;
    }

//    public static void main(String[] args) {
//        TenantContextHolder.setTenantId(688);
//        String sql1 = "ALTER table Wf_Track rename to nd_track";
//
//       String str11= getSwithDbSql4SqlParse(sql1);
//       System.err.println(str11);
//    }

    public static List<String> getTableName(String sql) {
        List<String> tables = new ArrayList<>();
        SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(sql, JdbcConstants.MYSQL);
        SQLStatement statement = parser.parseStatement();

        if (statement instanceof SQLCreateTableStatement) {
            SQLCreateTableStatement createTableStatement = (SQLCreateTableStatement) statement;
            tables.add( createTableStatement.getName().getSimpleName());
        } else if (statement instanceof SQLAlterTableStatement) {
            SQLAlterTableStatement alterTableStatement = (SQLAlterTableStatement) statement;
            tables.add( alterTableStatement.getName().getSimpleName());
        } else if (statement instanceof MySqlRenameTableStatement) {
            MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) statement;
            tables.add( renameTableStatement.getItems().get(0).getName().getSimpleName());
            tables.add( renameTableStatement.getItems().get(0).getTo().getSimpleName());



        }

        return tables;
    }

    public static void main(String[] args) {
        /***
        // 示例SQL语句
        String sql1 = "CREATE TABLE my_table (id INT, name VARCHAR(100));";
        String sql2 = "ALTER TABLE my_table ADD COLUMN age INT;";
        String sql3 = "ALTER TABLE my_table RENAME TO new_table";
        String sql4 = "SELECT * FROM my_table WHERE id = 1;";

        List<String> tableName1 = getTableName(sql1);
        System.out.println("Table name: " + tableName1);

        List<String> tableName2 = getTableName(sql2);
        System.out.println("Table name: " + tableName2);

        List<String> tableName3 = getTableName(sql3);
        System.out.println("Table name: " + tableName3);

        getTableNameBySql(sql1);
     System.err.println(   getSwithDbSql4SqlParse(sql1));;
     System.err.println(   getSwithDbSql4SqlParse(sql2));;
     System.err.println(   getSwithDbSql4SqlParse(sql3));;
     System.err.println(   getSwithDbSql4SqlParse(sql4));;

        List<String> tableName4 = getTableName(sql4);
        System.out.println("Table name: " + tableName4);


        TenantContextHolder.setTenantId(100);
        String  query1="DELETE\n a, b FROM erp_bom_child a LEFT JOIN erp_bom_child_procedure b ON a.id = b.bom_child_id WHERE a.bom_id = ? ";
        String swithDbSql = getSwithDbSql4SqlParse(query1);
        System.err.println(swithDbSql);


        MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();



        String sql = "DELETE a, b FROM erp_bom_child a LEFT JOIN erp_bom_child_procedure b ON a.id = b.bom_child_id WHERE a.bom_id = ?";

        try {
            // 解析SQL语句
            SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(sql, "mysql");
            SQLStatement statement = parser.parseStatement();

            // 获取解析结果
            String result = SQLUtils.toMySqlString(statement);

            System.out.println("解析结果：" + result);
        } catch (Exception e) {
            System.out.println("SQL解析错误：" + e.getMessage());
        }








        // 用druid解析出表名
        batchSqlParse("DELETE a, b FROM erp_bom_child a LEFT JOIN erp_bom_child_procedure b ON a.id = b.bom_child_id WHERE a.bom_id = ?;select * from yyds a,yym b where a.id=b.id ");
        batchSqlParse("insert into \n\t YYDS (a,b,c) values(1,2,3),(1,2,3),(1,2,3),(1,2,3),(1,2,3),(1,2,3); insert into \n\t dds (a,b,c) values(1,2,3),(1,2,3),(1,2,3),(1,2,3),(1,2,3),(1,2,3);");
        batchSqlParse("insert into yyds (a,b,c) select * from source_tbs");
        batchSqlParse("CREATE TABLE my_table (id INT, name VARCHAR(100));");
        batchSqlParse("ALTER TABLE my_table RENAME TO new_table");
        batchSqlParse("ALTER TABLE my_table ADD COLUMN age INT;");
        batchSqlParse("UPDATE employees, departments SET employees.department = departments.id WHERE employees.department_name = departments.name;");
        batchSqlParse("WITH RECURSIVE org_tree AS (\n" +
                "  SELECT id, name, parent_id, 0 AS level\n" +
                "  FROM organization\n" +
                "  WHERE id = 1 -- 根组织ID\n" +
                "\n" +
                "  UNION ALL\n" +
                "\n" +
                "  SELECT o.id, o.name, o.parent_id, ot.level + 1\n" +
                "  FROM organization o\n" +
                "  JOIN org_tree ot ON o.parent_id = ot.id\n" +
                ")\n" +
                "SELECT id, name, parent_id, level\n" +
                "FROM org_tree; insert into yym(a,b,c,d) values(1,2,3,4);select a,b,c,d from tb1 t1 inner join tb2 t2 on t1.id=t2.id ");



        batchSqlParse("select a,b,c,d from tb1 t1 inner join tb2 t2 on t1.id=t2.id inner join yyds yd on yd.id =t1.id ");

        getSwithDbSql4SqlParse11("select * from sys_user a,usert t where a.id=t.id; insert into yyds (a,b,c) select * from sys_tenant;select * from sys_user s,sys_tenant t,sys_yy y where t.id=s.id and t.id=y.id and  t.id not in( select id from yyds)");
        getSwithDbSql4SqlParse11("SELECT\n" +
                "\tmenu_id\n" +
                "FROM\n" +
                "\tsys_role sr,\n" +
                "\tsys_role_menu sur\n" +
                "WHERE\n" +
                "\tsr.role_code = ?\n" +
                "\tAND sr.role_id = sur.role_id");
        getSwithDbSql4SqlParse11("select * from yyds a join yym a1 on a.id=a1.id join yyddd yy on a.id=yy.id join sys_user u on u.id=a.id and exists(select count(1) from sys_tenant1 where tenant_id =a.id)");
        getSwithDbSql4SqlParse11("alter table xx add column a1 varchar(100)");
        getSwithDbSql4SqlParse11("SELECT count(0)\n" +
                "FROM (\n" +
                "\tSELECT emc.name AS categoryName, m.id, m.name, m.model, m.material_code\n" +
                "\t\t, m.unit\n" +
                "\t\t, CASE m.`type`\n" +
                "\t\t\tWHEN '1' THEN '自产'\n" +
                "\t\t\tWHEN '2' THEN '外购'\n" +
                "\t\t\tELSE '参数错误'\n" +
                "\t\tEND AS typeName, CONVERT(m.create_time, char) AS createTime, emn.safety_tock AS safetyTock\n" +
                "\t\t, SUM(s.initial_tock) + IFNULL(SUM(d.stock_num), 0) AS allTock\n" +
                "\tFROM `lg_erp_723`.erp_material m\n" +
                "\t\tINNER JOIN `lg_erp_723`.erp_material_norms emn ON m.id = emn.meterial_id\n" +
                "\t\tINNER JOIN `lg_erp_723`.erp_material_category emc ON M.category_id = emc.id\n" +
                "\t\tINNER JOIN `lg_erp_723`.erp_material_norms_tock s ON m.ID = s.material_id\n" +
                "\t\tLEFT JOIN `lg_erp_723`.erp_material_depot_stock d ON s.material_id = d.material_id\n" +
                "\tWHERE m.delete_flag = '0'\n" +
                "\t\tAND m.enabled = '1'\n" +
                "\tGROUP BY s.material_id\n" +
                ") bb\n" +
                "WHERE safetyTock > allTock");

        getSwithDbSql4SqlParse11("SELECT table_name AS tableName, table_comment AS tableComment, TABLE_ROWS AS tablesRows, DATA_LENGTH AS tableSize, INDEX_LENGTH AS indexSize FROM information_schema.TABLES WHERE table_schema = ?");
        getSwithDbSql4SqlParse11("alter table xx rename to a1");
         **/

//        getSwithDbSql4SqlParse11("SELECT\n" +
//                "  jt.inoutitemId AS id,\n" +
//                "  jt.otherPrice\n" +
//                " \n" +
//                "FROM JSON_TABLE (\n" +
//                "'[{\"inoutitemId\":\"11\",\"otherPrice\":\"22\"},{\"inoutitemId\":\"222\",\"otherPrice\":\"23332\"}]',\n" +
//                "'$[*]' COLUMNS (inoutitemId varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci PATH '$.inoutitemId', otherPrice varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci PATH '$.otherPrice')) AS jt");

        String  sqlxxxx= "update `fc_stock_rpt_m` set update_time = now()\n" +
                "\n" +
                "             \n" +
                "             \n" +
                "                ,curr_credit_accumulated = curr_credit_accumulated - ?\n" +
                "             \n" +
                "             \n" +
                "             \n" +
                "                ,year_credit_accumulated = year_credit_accumulated - ?\n" +
                "             \n" +
                "\n" +
                "             \n" +
                "             \n" +
                "                ,curr_credit_quantity = curr_credit_quantity - ?\n" +
                "             \n" +
                "             \n" +
                "             \n" +
                "                ,year_credit_quantity = year_credit_quantity - ?\n" +
                "             \n" +
                "             \n" +
                "                ,`end_debit_balance` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_debit_balance` ,0) +\n" +
                "                IFNULL(`curr_debit_accumulated` ,0)-\n" +
                "                IFNULL(`curr_credit_accumulated` ,0)-\n" +
                "                IFNULL(`begin_credit_balance`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_debit_balance` ,0) +\n" +
                "                IFNULL(`curr_debit_accumulated` ,0)-\n" +
                "                IFNULL(`curr_credit_accumulated` ,0)-\n" +
                "                IFNULL(`begin_credit_balance`,0)) END,\n" +
                "                `end_credit_balance` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_credit_balance` ,0) +\n" +
                "                IFNULL(`curr_credit_accumulated`,0)-\n" +
                "                IFNULL(`curr_debit_accumulated`,0)-\n" +
                "                IFNULL(`begin_debit_balance`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_credit_balance` ,0) +\n" +
                "                IFNULL(`curr_credit_accumulated`,0)-\n" +
                "                IFNULL(`curr_debit_accumulated`,0)-\n" +
                "                IFNULL(`begin_debit_balance`,0)) END\n" +
                "             \n" +
                "\n" +
                "             \n" +
                "                ,`end_debit_quantity` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_debit_quantity` ,0) +\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`begin_credit_quantity`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_debit_quantity` ,0) +\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`begin_credit_quantity`,0)) END\n" +
                "\n" +
                "                ,`end_credit_quantity` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_credit_quantity` ,0) +\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`begin_debit_quantity`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_credit_quantity` ,0) +\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`begin_debit_quantity`,0)) END\n" +
                "             \n" +
                "            ,`end_stock_price` = case when end_debit_quantity >0 then  ROUND(end_debit_balance / end_debit_quantity, ?) else end_stock_price end\n" +
                "            ,`end_stock_price` = case when end_credit_quantity >0 then  ROUND(end_credit_balance / end_credit_quantity, ?) else end_stock_price end\n" +
                "\n" +
                "\n" +
                "            where tenant_id =? and account_id = ? and period = ? and\n" +
                "            stock_id = ?\n" +
                "         ; \n" +
                "            update `fc_stock_rpt_m` set update_time = now()\n" +
                "\n" +
                "             \n" +
                "             \n" +
                "                ,curr_credit_accumulated = curr_credit_accumulated - ?\n" +
                "             \n" +
                "             \n" +
                "             \n" +
                "                ,year_credit_accumulated = year_credit_accumulated - ?\n" +
                "             \n" +
                "\n" +
                "             \n" +
                "             \n" +
                "                ,curr_credit_quantity = curr_credit_quantity - ?\n" +
                "             \n" +
                "             \n" +
                "             \n" +
                "                ,year_credit_quantity = year_credit_quantity - ?\n" +
                "             \n" +
                "             \n" +
                "                ,`end_debit_balance` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_debit_balance` ,0) +\n" +
                "                IFNULL(`curr_debit_accumulated` ,0)-\n" +
                "                IFNULL(`curr_credit_accumulated` ,0)-\n" +
                "                IFNULL(`begin_credit_balance`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_debit_balance` ,0) +\n" +
                "                IFNULL(`curr_debit_accumulated` ,0)-\n" +
                "                IFNULL(`curr_credit_accumulated` ,0)-\n" +
                "                IFNULL(`begin_credit_balance`,0)) END,\n" +
                "                `end_credit_balance` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_credit_balance` ,0) +\n" +
                "                IFNULL(`curr_credit_accumulated`,0)-\n" +
                "                IFNULL(`curr_debit_accumulated`,0)-\n" +
                "                IFNULL(`begin_debit_balance`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_credit_balance` ,0) +\n" +
                "                IFNULL(`curr_credit_accumulated`,0)-\n" +
                "                IFNULL(`curr_debit_accumulated`,0)-\n" +
                "                IFNULL(`begin_debit_balance`,0)) END\n" +
                "             \n" +
                "\n" +
                "             \n" +
                "                ,`end_debit_quantity` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_debit_quantity` ,0) +\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`begin_credit_quantity`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_debit_quantity` ,0) +\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`begin_credit_quantity`,0)) END\n" +
                "\n" +
                "                ,`end_credit_quantity` =\n" +
                "                CASE WHEN\n" +
                "                (IFNULL(`begin_credit_quantity` ,0) +\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`begin_debit_quantity`,0)) <0 THEN 0\n" +
                "                ELSE\n" +
                "                (IFNULL(`begin_credit_quantity` ,0) +\n" +
                "                IFNULL(`curr_credit_quantity` ,0)-\n" +
                "                IFNULL(`curr_debit_quantity` ,0)-\n" +
                "                IFNULL(`begin_debit_quantity`,0)) END\n" +
                "             \n" +
                "            ,`end_stock_price` = case when end_debit_quantity >0 then  ROUND(end_debit_balance / end_debit_quantity, ?) else end_stock_price end\n" +
                "            ,`end_stock_price` = case when end_credit_quantity >0 then  ROUND(end_credit_balance / end_credit_quantity, ?) else end_stock_price end\n" +
                "\n" +
                "\n" +
                "            where tenant_id =? and account_id = ? and period = ? and\n" +
                "            stock_id = ?";
        getSwithDbSql4SqlParse11(sqlxxxx);


    }

    private static String batchSqlParse(String sql) {

//        if(StrUtil.containsAll(sql.toLowerCase(),"alter","to")){
//            return getSwithDbSql(sql);
//        }

        StringBuffer stringBuffer = new StringBuffer();
        List<String> tableNameList = new ArrayList<String>();
        DbType mysql = JdbcConstants.MYSQL;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, mysql);
        for (SQLStatement sqlStatement : stmtList) {
            MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
            sqlStatement.accept(visitor);
            List<SQLName> originalTables = visitor.getOriginalTables();

            for(SQLName sqlname: originalTables){
                if(!sqlname.getSimpleName().contains("lg_erp."))
                ReflectUtil.setFieldValue(sqlname, "name", "lg_erp."+sqlname.getSimpleName());
                tableNameList.add(sqlname.getSimpleName());
            }
            String newSql = SQLUtils.toSQLString(sqlStatement, JdbcConstants.MYSQL);
            stringBuffer.append(newSql);
        }
       // stringBuffer.deleteCharAt(stringBuffer.length()-1);

        System.err.println(stringBuffer);
        return stringBuffer.toString();

    }





    private static String getSwithDbSql4SqlParse11(String sql) {
        StringBuffer stringBuffer = new StringBuffer();
        List<String> tableNameList = new ArrayList<String>();
        DbType mysql = JdbcConstants.MYSQL;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, mysql);
        String dbName = "lg_erp_723";
        List<String> globTable = CollectionUtil.newArrayList("sys_user","sys_tenant","information_schema.TABLES");
        List<String> lowerGlobTable = new ArrayList<>();
        for(String glob:globTable){
            lowerGlobTable.add(glob.toLowerCase());
        }
        for (SQLStatement sqlStatement : stmtList) {

            List<SQLName> originalTables = new ArrayList<>();
            if (sqlStatement instanceof MySqlRenameTableStatement) {
                MySqlRenameTableStatement renameTableStatement = (MySqlRenameTableStatement) sqlStatement;
                MySqlRenameTableStatement.Item item = renameTableStatement.getItems().get(0);
                originalTables.add( item.getName());
                originalTables.add( item.getTo());
            }else{
                MySqlSchemaStatVisitor visitor = new MySqlSchemaStatVisitor();
                sqlStatement.accept(visitor);
                originalTables = visitor.getOriginalTables().stream().filter(x->{
                    return  !CollectionUtil.contains(lowerGlobTable, x.getSimpleName().toLowerCase());
                }).collect(Collectors.toList());
            }



            for(SQLName sqlname: originalTables){

                    if(StrUtil.isNotEmpty(dbName)){
                        if(!sqlname.toString().contains(dbName) && !StrUtil.contains(sqlname.toString(),StrUtil.DOT )){
                            String newDbName = String.format("`%s`.%s",dbName,sqlname.getSimpleName());
                            ReflectUtil.setFieldValue(sqlname, "name", newDbName);
                        }
                    }
            }
            String newSql = SQLUtils.toSQLString(sqlStatement, JdbcConstants.MYSQL);
            stringBuffer.append(newSql);
        }

        System.err.println(stringBuffer);
        return stringBuffer.toString();

    }




}
