import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.lg.dao.DaoMode;
import com.lg.dao.core.GenericDao;
import com.lg.dao.helper.DaoHelper;
import com.lg.financecloud.common.data.dbcache.DbCache;
import com.lg.financecloud.common.data.dbcache.DbCacheUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * 无数据源环境测试
 * 验证在没有配置数据源的情况下自动降级到内存数据库
 */
//@SpringBootTest(classes = TestApplication.class)
@SpringBootTest(classes = {
        com.lg.dao.config.LightORMAutoConfiguration.class,
        com.lg.dao.config.MemoryDataSourceConfiguration.class
})
@TestPropertySource(properties = {
    "spring.datasource.url=",
    "spring.datasource.driver-class-name=",
    "spring.datasource.username=",
    "spring.datasource.password="
})
public class NoDataSourceTest {

    @Test
    @DisplayName("测试无数据源自动降级")
    void testCustomDao() {
        GenericDao<DbCache, Long> dbCacheLongGenericDao = DaoHelper.memoryDao(DbCache.class);
        // 创建2条数据
        DbCache dbCache = new DbCache();
        dbCache.setId(IdUtil.getSnowflakeNextIdStr());
        dbCache.setCatalog("businesssettings");
        dbCache.setArea("test");
        dbCache.setOwnerType("1");
        dbCache.setOwnerId("1");
        dbCache.setValue("test");
        dbCache.setCreateTime(new Date());
        dbCache.setUpdateTime(new Date());
        dbCacheLongGenericDao.batchInsert(ListUtil.toList(dbCache));



        List<JSONObject> jsonObjects = DaoHelper.getBaseDao().selectJsonList("select * from sys_db_cache");
        jsonObjects.forEach(jsonObject -> {
             System.err.println(jsonObject);
        });



    }

}