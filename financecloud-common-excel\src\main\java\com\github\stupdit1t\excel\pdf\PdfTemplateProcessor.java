package com.github.stupdit1t.excel.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * PDF模板处理器
 * 处理基于模板的PDF生成，支持变量替换和循环数据处理
 */
public class PdfTemplateProcessor {

    private PdfTemplateConfig config;
    private com.itextpdf.text.Font chineseFont;
    private com.itextpdf.text.Font headerFont;

    // 变量替换正则表达式
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    // 循环开始标记
    private static final Pattern LOOP_START_PATTERN = Pattern.compile("\\$\\{#foreach\\s+([^}]+)\\}");
    // 循环结束标记
    private static final Pattern LOOP_END_PATTERN = Pattern.compile("\\$\\{/foreach\\}");

    public PdfTemplateProcessor(PdfTemplateConfig config) {
        this.config = config;
        initializeFonts();
    }

    /**
     * 初始化字体
     */
    private void initializeFonts() {
        try {
            if (config.isEnableChineseFont()) {
                BaseFont baseFont;
                if (config.getCustomFontPath() != null) {
                    baseFont = BaseFont.createFont(config.getCustomFontPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                } else {
                    // 使用iText内置的中文字体
                    baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                }
                chineseFont = new com.itextpdf.text.Font(baseFont, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
                headerFont = new com.itextpdf.text.Font(baseFont, config.getFontSize(), com.itextpdf.text.Font.BOLD);
            } else {
                chineseFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
                headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.BOLD);
            }
        } catch (Exception e) {
            // 如果中文字体加载失败，使用默认字体
            chineseFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.NORMAL);
            headerFont = new com.itextpdf.text.Font(com.itextpdf.text.Font.FontFamily.HELVETICA, config.getFontSize(), com.itextpdf.text.Font.BOLD);
        }
    }

    /**
     * 处理模板并生成PDF
     * @param outputStream 输出流
     * @throws Exception 处理异常
     */
    public void processTemplate(OutputStream outputStream) throws Exception {
        switch (config.getTemplateType()) {
            case EXCEL_TO_PDF:
                processExcelTemplate(outputStream);
                break;
            case HTML_TO_PDF:
                processHtmlTemplate(outputStream);
                break;
            case DIRECT_PDF:
                processDirectPdfTemplate(outputStream);
                break;
            default:
                throw new UnsupportedOperationException("不支持的模板类型: " + config.getTemplateType());
        }
    }

    /**
     * 处理模板并生成PDF到文件
     * @param filePath 文件路径
     * @throws Exception 处理异常
     */
    public void processTemplate(String filePath) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            processTemplate(fos);
        }
    }

    /**
     * 处理模板并生成PDF到HTTP响应
     * @param response HTTP响应
     * @param fileName 文件名
     * @throws Exception 处理异常
     */
    public void processTemplate(HttpServletResponse response, String fileName) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        processTemplate(response.getOutputStream());
    }

    /**
     * 处理Excel模板
     */
    private void processExcelTemplate(OutputStream outputStream) throws Exception {
        Workbook templateWorkbook = loadTemplateWorkbook();
        
        // 处理模板中的变量和循环
        processWorkbookTemplate(templateWorkbook);
        
        // 将处理后的Excel转换为PDF
        PdfExporter pdfExporter = new PdfExporter(config);
        pdfExporter.exportFromWorkbook(templateWorkbook, outputStream);
        
        templateWorkbook.close();
    }

    /**
     * 加载模板工作簿
     */
    private Workbook loadTemplateWorkbook() throws Exception {
        if (config.getTemplateInputStream() != null) {
            return WorkbookFactory.create(config.getTemplateInputStream());
        } else if (config.getTemplatePath() != null) {
            return WorkbookFactory.create(new FileInputStream(config.getTemplatePath()));
        } else {
            throw new IllegalArgumentException("模板路径或输入流不能为空");
        }
    }

    /**
     * 处理工作簿模板
     */
    private void processWorkbookTemplate(Workbook workbook) {
        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            processSheetTemplate(sheet);
        }
    }

    /**
     * 处理Sheet模板
     */
    private void processSheetTemplate(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();
        
        for (int rowIndex = 0; rowIndex <= lastRowNum; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            
            // 检查是否包含循环标记
            if (containsLoopStart(row)) {
                rowIndex = processLoopRows(sheet, rowIndex);
            } else {
                // 处理普通变量替换
                processRowVariables(row);
            }
        }
    }

    /**
     * 检查行是否包含循环开始标记
     */
    private boolean containsLoopStart(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue();
                if (LOOP_START_PATTERN.matcher(cellValue).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 处理循环行
     */
    private int processLoopRows(Sheet sheet, int startRowIndex) {
        Row startRow = sheet.getRow(startRowIndex);
        String loopVariable = extractLoopVariable(startRow);
        
        if (loopVariable == null || !config.getLoops().containsKey(loopVariable)) {
            return startRowIndex;
        }
        
        List<?> loopData = config.getLoops().get(loopVariable);
        
        // 找到循环结束行
        int endRowIndex = findLoopEndRow(sheet, startRowIndex);
        if (endRowIndex == -1) {
            return startRowIndex;
        }
        
        // 复制循环模板行
        for (int dataIndex = 0; dataIndex < loopData.size(); dataIndex++) {
            Object dataItem = loopData.get(dataIndex);
            
            for (int templateRowIndex = startRowIndex + 1; templateRowIndex < endRowIndex; templateRowIndex++) {
                Row templateRow = sheet.getRow(templateRowIndex);
                if (templateRow == null) continue;
                
                // 创建新行
                int newRowIndex = endRowIndex + dataIndex * (endRowIndex - startRowIndex - 1) + (templateRowIndex - startRowIndex - 1);
                Row newRow = sheet.createRow(newRowIndex);
                
                // 复制行数据并替换变量
                copyRowWithDataReplacement(templateRow, newRow, dataItem, dataIndex);
            }
        }
        
        // 删除原始模板行
        for (int i = startRowIndex; i <= endRowIndex; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
        
        return endRowIndex + loopData.size() * (endRowIndex - startRowIndex - 1);
    }

    /**
     * 提取循环变量名
     */
    private String extractLoopVariable(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue();
                Matcher matcher = LOOP_START_PATTERN.matcher(cellValue);
                if (matcher.find()) {
                    return matcher.group(1).trim();
                }
            }
        }
        return null;
    }

    /**
     * 找到循环结束行
     */
    private int findLoopEndRow(Sheet sheet, int startRowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        
        for (int rowIndex = startRowIndex + 1; rowIndex <= lastRowNum; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            
            for (Cell cell : row) {
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    String cellValue = cell.getStringCellValue();
                    if (LOOP_END_PATTERN.matcher(cellValue).find()) {
                        return rowIndex;
                    }
                }
            }
        }
        return -1;
    }

    /**
     * 复制行并替换数据
     */
    private void copyRowWithDataReplacement(Row sourceRow, Row targetRow, Object dataItem, int dataIndex) {
        targetRow.setHeight(sourceRow.getHeight());
        
        for (int cellIndex = 0; cellIndex < sourceRow.getLastCellNum(); cellIndex++) {
            Cell sourceCell = sourceRow.getCell(cellIndex);
            if (sourceCell == null) continue;
            
            Cell targetCell = targetRow.createCell(cellIndex);
            targetCell.setCellStyle(sourceCell.getCellStyle());
            
            if (sourceCell.getCellType() == CellType.STRING) {
                String cellValue = sourceCell.getStringCellValue();
                String replacedValue = replaceVariablesInString(cellValue, dataItem, dataIndex);
                targetCell.setCellValue(replacedValue);
            } else {
                // 复制其他类型的单元格
                copyCellValue(sourceCell, targetCell);
            }
        }
    }

    /**
     * 处理行变量替换
     */
    private void processRowVariables(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue();
                String replacedValue = replaceVariablesInString(cellValue, null, -1);
                cell.setCellValue(replacedValue);
            }
        }
    }

    /**
     * 替换字符串中的变量
     */
    private String replaceVariablesInString(String text, Object dataItem, int dataIndex) {
        if (text == null) return "";
        
        Matcher matcher = VARIABLE_PATTERN.matcher(text);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            String replacement = getVariableValue(variableName, dataItem, dataIndex);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * 获取变量值
     */
    private String getVariableValue(String variableName, Object dataItem, int dataIndex) {
        // 首先检查是否是循环数据项的属性
        if (dataItem != null) {
            try {
                if (dataItem instanceof Map) {
                    Map<?, ?> dataMap = (Map<?, ?>) dataItem;
                    Object value = dataMap.get(variableName);
                    if (value != null) {
                        return value.toString();
                    }
                } else {
                    // 使用反射获取对象属性
                    java.lang.reflect.Field field = dataItem.getClass().getDeclaredField(variableName);
                    field.setAccessible(true);
                    Object value = field.get(dataItem);
                    if (value != null) {
                        return value.toString();
                    }
                }
            } catch (Exception e) {
                // 忽略反射异常，继续查找全局变量
            }
        }
        
        // 检查全局变量
        Object value = config.getVariables().get(variableName);
        if (value != null) {
            return value.toString();
        }
        
        // 特殊变量处理
        if ("index".equals(variableName) && dataIndex >= 0) {
            return String.valueOf(dataIndex + 1);
        }
        
        return "${" + variableName + "}"; // 保持原样
    }

    /**
     * 复制单元格值
     */
    private void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
        }
    }

    /**
     * 处理HTML模板（预留接口）
     */
    private void processHtmlTemplate(OutputStream outputStream) throws Exception {
        throw new UnsupportedOperationException("HTML模板功能暂未实现");
    }

    /**
     * 处理直接PDF模板（预留接口）
     */
    private void processDirectPdfTemplate(OutputStream outputStream) throws Exception {
        throw new UnsupportedOperationException("直接PDF模板功能暂未实现");
    }
}
