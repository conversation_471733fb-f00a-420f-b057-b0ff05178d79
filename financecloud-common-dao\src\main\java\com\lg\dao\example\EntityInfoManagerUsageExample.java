package com.lg.dao.example;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.EntityInfoManager;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * EntityInfoManager 使用示例
 * 展示统一实体信息管理的简化 API
 */
@Slf4j
public class EntityInfoManagerUsageExample {

    /**
     * 示例实体类
     */
    @Table(name = "example_user")
    public static class ExampleUser {
        @Id
        @Column(name = "user_id")
        private Long id;
        
        @Column(name = "user_name")
        private String name;
        
        @Column(name = "user_email")
        private String email;
        
        private Integer age;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }

    /**
     * 基本使用示例
     */
    public static void basicUsageExample() {
        System.out.println("=== EntityInfoManager 基本使用示例 ===");
        
        // 获取 EntityInfoManager 实例（无需传入缓存管理器）
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 获取实体信息
        EntityInfo entityInfo = manager.getEntityInfo(ExampleUser.class);
        
        System.out.println("表名: " + entityInfo.getTableName());
        System.out.println("字段数量: " + entityInfo.getFields().size());
        
        System.out.println("\n字段列表:");
        entityInfo.getFields().forEach(field -> {
            System.out.println("- Java字段: " + field.getField().getName() + 
                             " -> 数据库列: " + field.getColumn());
        });
        
        System.out.println("✅ 基本使用示例完成\n");
    }

    /**
     * 缓存管理示例
     */
    public static void cacheManagementExample() {
        System.out.println("=== EntityInfoManager 缓存管理示例 ===");
        
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 预加载实体信息
        System.out.println("预加载实体信息...");
        manager.preloadEntityInfo(ExampleUser.class);
        
        // 获取缓存统计
        System.out.println("缓存统计信息:");
        String stats = manager.getCacheStats();
        System.out.println(stats);
        
        // 清除指定实体缓存
        System.out.println("清除指定实体缓存...");
        manager.evictEntityInfo(ExampleUser.class);
        
        // 再次获取实体信息（会重新创建）
        EntityInfo entityInfo = manager.getEntityInfo(ExampleUser.class);
        System.out.println("重新获取实体信息: " + entityInfo.getTableName());
        
        System.out.println("✅ 缓存管理示例完成\n");
    }

    /**
     * 性能对比示例
     */
    public static void performanceComparisonExample() {
        System.out.println("=== EntityInfoManager 性能对比示例 ===");
        
        int iterations = 1000;
        
        // 测试直接创建的性能
        long startTime1 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            EntityInfo.of(ExampleUser.class);
        }
        long endTime1 = System.nanoTime();
        long directTime = endTime1 - startTime1;
        
        // 测试使用 EntityInfoManager 的性能
        EntityInfoManager manager = EntityInfoManager.getInstance();
        long startTime2 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            manager.getEntityInfo(ExampleUser.class);
        }
        long endTime2 = System.nanoTime();
        long managerTime = endTime2 - startTime2;
        
        System.out.println("性能对比结果 (" + iterations + " 次调用):");
        System.out.println("直接创建总时间: " + directTime / 1_000_000 + " ms");
        System.out.println("使用管理器总时间: " + managerTime / 1_000_000 + " ms");
        
        if (directTime > managerTime) {
            double improvement = (double) directTime / managerTime;
            System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
        } else {
            System.out.println("性能差异不明显");
        }
        
        System.out.println("✅ 性能对比示例完成\n");
    }

    /**
     * 多线程安全示例
     */
    public static void threadSafetyExample() {
        System.out.println("=== EntityInfoManager 多线程安全示例 ===");
        
        int threadCount = 10;
        int iterationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                EntityInfoManager manager = EntityInfoManager.getInstance();
                
                for (int j = 0; j < iterationsPerThread; j++) {
                    EntityInfo entityInfo = manager.getEntityInfo(ExampleUser.class);
                    
                    // 验证结果一致性
                    if (!"example_user".equals(entityInfo.getTableName())) {
                        System.err.println("线程 " + threadId + " 获取到错误的表名: " + entityInfo.getTableName());
                    }
                }
                
                System.out.println("线程 " + threadId + " 完成 " + iterationsPerThread + " 次调用");
            });
        }
        
        // 启动所有线程
        long startTime = System.currentTimeMillis();
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        try {
            for (Thread thread : threads) {
                thread.join();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("线程被中断: " + e.getMessage());
        }
        
        long endTime = System.currentTimeMillis();
        System.out.println("多线程测试完成，总耗时: " + (endTime - startTime) + " ms");
        System.out.println("✅ 多线程安全示例完成\n");
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        System.out.println("=== EntityInfoManager 错误处理示例 ===");
        
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 测试 null 参数处理
        try {
            manager.getEntityInfo(null);
            System.err.println("应该抛出异常");
        } catch (IllegalArgumentException e) {
            System.out.println("正确处理 null 参数: " + e.getMessage());
        }
        
        // 测试清除 null 实体缓存（应该不抛异常）
        try {
            manager.evictEntityInfo(null);
            System.out.println("正确处理清除 null 实体缓存");
        } catch (Exception e) {
            System.err.println("清除 null 实体缓存不应抛异常: " + e.getMessage());
        }
        
        System.out.println("✅ 错误处理示例完成\n");
    }

    /**
     * 主方法 - 运行所有示例
     */
    public static void main(String[] args) {
        System.out.println("EntityInfoManager 使用示例开始\n");
        
        try {
            basicUsageExample();
            cacheManagementExample();
            performanceComparisonExample();
            threadSafetyExample();
            errorHandlingExample();
            
            System.out.println("🎉 所有示例运行完成！");
            
        } catch (Exception e) {
            System.err.println("示例运行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
