package com.lg.financecloud.common.redis;

import com.lg.financecloud.common.redis.cache.RedisTemplateConfig;
import com.lg.financecloud.common.redis.idempotent.aspect.IdempotentAspect;
import com.lg.financecloud.common.redis.idempotent.expression.ExpressionResolver;
import com.lg.financecloud.common.redis.idempotent.expression.KeyResolver;
import com.lg.financecloud.common.redis.mq.MqDbDao;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/25
 * <p>
 * 幂等插件初始化
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(RedisTemplateConfig.class)
public class RedisAutoConfig {

	/**
	 * 切面 拦截处理所有 @Idempotent
	 * @return Aspect
	 */
	@Bean
	public IdempotentAspect idempotentAspect() {
		return new IdempotentAspect();
	}

	/**
	 * key 解析器
	 * @return KeyResolver
	 */
	@Bean
	@ConditionalOnMissingBean(KeyResolver.class)
	public KeyResolver keyResolver() {
		return new ExpressionResolver();
	}



//	@Bean
//	@ConditionalOnBean(DataSource.class)
//	public RedisAmq redisQueue(RedissonClient redissonClient, DAO dao) {
//		return new RedisAmq(redissonClient,dao);
//	}




}
