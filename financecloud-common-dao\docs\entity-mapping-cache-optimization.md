# 实体映射组件缓存优化（重构版）

## 📋 优化概述

本次优化针对 DAO 框架中的实体映射组件 `EntityMapper` 和 `EntityRowMapper`，实现了高效简洁的缓存策略，使用独立的缓存类型，去除冗余代码，确保高效性能。

## 🎯 优化目标

1. **独立缓存类型**：不同类型的缓存使用独立的缓存类型，避免混乱
2. **去除冗余代码**：简化逻辑，避免臃肿，保证高效
3. **优化反射操作**：缓存字段访问，避免重复的反射操作
4. **提升映射性能**：缓存实体信息和列名映射关系

## 🔧 核心改进

### 1. EntityMapper 优化

#### 使用 METADATA_CACHE 缓存类型
```java
public class EntityMapper<T> implements RowMapper<T> {
    private final UnifiedCacheManager unifiedCacheManager;
    private final Map<String, Field> fieldCache = new ConcurrentHashMap<>();

    public EntityMapper(Class<T> entityClass, UnifiedCacheManager unifiedCacheManager) {
        this.entityClass = entityClass;
        this.unifiedCacheManager = unifiedCacheManager;
        this.columnMap = getColumnMap(entityClass);
        preloadFieldCache();
    }
}
```

#### 简洁的列映射获取
```java
private Map<String, ColumnInfo> getColumnMap(Class<?> clazz) {
    if (unifiedCacheManager != null) {
        String cacheKey = "entity_mapping:" + clazz.getName();
        return unifiedCacheManager.get(
            UnifiedCacheManager.CacheType.METADATA_CACHE,
            cacheKey,
            () -> AnnotationParser.parseEntityFields(clazz)
        );
    }
    return COLUMN_CACHE.computeIfAbsent(clazz, AnnotationParser::parseEntityFields);
}
```

#### 高效的字段访问缓存
```java
private void preloadFieldCache() {
    try {
        for (ColumnInfo columnInfo : columnMap.values()) {
            String fieldName = columnInfo.getFieldName();
            Field field = entityClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            fieldCache.put(fieldName, field);
        }
    } catch (Exception e) {
        log.warn("预加载字段缓存失败: {}", entityClass.getName(), e);
    }
}

private Field getFieldFromCache(String fieldName) {
    return fieldCache.computeIfAbsent(fieldName, name -> {
        try {
            Field field = entityClass.getDeclaredField(name);
            field.setAccessible(true);
            return field;
        } catch (Exception e) {
            log.warn("获取字段失败: {}.{}", entityClass.getName(), name, e);
            return null;
        }
    });
}
```

### 2. EntityRowMapper 优化

#### 使用独立缓存类型
```java
public class EntityRowMapper<T> implements RowMapper<T> {
    private final UnifiedCacheManager unifiedCacheManager;

    public EntityRowMapper(Class<T> entityClass, UnifiedCacheManager unifiedCacheManager) {
        this.entityClass = entityClass;
        this.unifiedCacheManager = unifiedCacheManager;
    }
}
```

#### 实体信息缓存（ENTITY_CACHE）
```java
private EntityInfo getEntityInfo(Class<?> clazz) {
    if (unifiedCacheManager != null) {
        String cacheKey = "entity_info:" + clazz.getName();
        return unifiedCacheManager.get(
            UnifiedCacheManager.CacheType.ENTITY_CACHE,
            cacheKey,
            () -> EntityInfo.of(clazz)
        );
    }
    return ENTITY_INFO_CACHE.computeIfAbsent(clazz, EntityInfo::of);
}
```

#### 列名映射缓存（METADATA_CACHE）
```java
private String findPropertyName(String column, EntityInfo entityInfo) {
    if (unifiedCacheManager != null) {
        String cacheKey = "column_mapping:" + entityClass.getName() + ":" + column;
        return unifiedCacheManager.get(
            UnifiedCacheManager.CacheType.METADATA_CACHE,
            cacheKey,
            () -> doFindPropertyName(column, entityInfo)
        );
    }
    return doFindPropertyName(column, entityInfo);
}
```

#### 简化的属性名查找逻辑
```java
private String doFindPropertyName(String column, EntityInfo entityInfo) {
    // 1. 直接匹配
    for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
        if (column.equalsIgnoreCase(fieldInfo.getColumn())) {
            return fieldInfo.getField().getName();
        }
    }

    // 2. 驼峰转换匹配
    String camelProperty = StrUtil.toCamelCase(column.toLowerCase());
    for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
        if (camelProperty.equals(fieldInfo.getField().getName())) {
            return camelProperty;
        }
    }

    // 3. 去前缀匹配（user_id -> id）
    if (column.contains("_")) {
        String[] parts = column.split("_", 2);
        if (parts.length > 1) {
            String unprefixed = parts[1];
            for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
                if (unprefixed.equalsIgnoreCase(fieldInfo.getColumn()) ||
                    StrUtil.toCamelCase(unprefixed.toLowerCase()).equals(fieldInfo.getField().getName())) {
                    return fieldInfo.getField().getName();
                }
            }
        }
    }

    return camelProperty; // 返回驼峰命名，让BeanUtil尝试
}
```

## 🚀 性能提升

### 优化前的问题
1. **重复反射操作**：每次 `mapRow` 都要调用 `getDeclaredField` 和 `setAccessible`
2. **重复字段映射计算**：复杂的列名到属性名转换逻辑重复执行
3. **本地缓存限制**：无法跨实例共享，缓存策略不统一

### 优化后的改进
1. **字段访问缓存**：预加载字段信息，避免运行时反射操作
2. **统一缓存管理**：使用 `ENTITY_CACHE` 缓存类型，统一管理
3. **多层缓存策略**：实例缓存 + 统一缓存，最大化性能
4. **智能缓存键**：稳定的缓存键生成，避免冲突

## 📊 独立缓存类型策略

### 缓存类型分离
- **ENTITY_CACHE**：存储实体信息
  - 实体信息：`entity_info:{className}`

- **METADATA_CACHE**：存储元数据映射信息
  - 实体列映射信息：`entity_mapping:{className}`
  - 列名映射：`column_mapping:{className}:{columnName}`

### 缓存层次结构
```
实体映射缓存架构：
├── EntityMapper
│   ├── 实例级缓存（fieldCache）
│   └── METADATA_CACHE（entity_mapping）
└── EntityRowMapper
    ├── ENTITY_CACHE（entity_info）
    └── METADATA_CACHE（column_mapping）
```

### 优化亮点
1. **缓存类型独立**：不同类型的缓存使用不同的缓存类型，避免混乱
2. **代码简洁**：去除冗余的多层缓存逻辑，保持高效
3. **职责清晰**：每种缓存类型有明确的职责范围

## 🔧 使用方式

### 1. 带缓存的实体映射器
```java
// 注入统一缓存管理器
@Autowired
private UnifiedCacheManager unifiedCacheManager;

// 创建带缓存的 EntityMapper
EntityMapper<User> mapper = new EntityMapper<>(User.class, unifiedCacheManager);

// 创建带缓存的 EntityRowMapper
EntityRowMapper<User> rowMapper = new EntityRowMapper<>(User.class, unifiedCacheManager);
```

### 2. 向后兼容
```java
// 不带缓存的使用方式（向后兼容）
EntityMapper<User> mapper = new EntityMapper<>(User.class);
EntityRowMapper<User> rowMapper = new EntityRowMapper<>(User.class);
```

## 🔍 监控和调试

### 启用调试日志
```yaml
logging:
  level:
    com.lg.dao.core.EntityMapper: DEBUG
    com.lg.dao.core.EntityRowMapper: DEBUG
```

### 日志输出示例
```
DEBUG - 使用统一缓存获取实体映射信息: entity_mapping:com.example.User
DEBUG - 缓存未命中，解析实体字段: com.example.User
DEBUG - 预加载字段缓存完成: 5 个字段
DEBUG - 使用统一缓存获取实体信息: entity_info:com.example.User
DEBUG - 缓存未命中，创建实体信息: com.example.User
```

## 📝 测试覆盖

### 功能测试
- ✅ EntityMapper 缓存功能测试
- ✅ EntityRowMapper 缓存功能测试
- ✅ 缓存一致性测试
- ✅ 缓存禁用状态测试

### 性能测试
- ✅ 实体映射性能对比测试
- ✅ 字段访问性能测试
- ✅ 列名映射性能测试

## 🎯 优化亮点

1. **统一缓存集成**：与其他组件使用相同的缓存管理策略
2. **多层缓存优化**：实例缓存 + 统一缓存，最大化性能
3. **反射操作优化**：预加载字段缓存，避免运行时反射
4. **向后兼容**：保持原有API不变，平滑升级
5. **智能缓存键**：稳定的缓存键生成，避免冲突

## 🔄 后续优化建议

1. **字段类型缓存**：缓存字段类型信息，优化类型转换
2. **构造器缓存**：缓存实体构造器，优化实例创建
3. **注解信息缓存**：缓存注解解析结果，避免重复解析
4. **批量映射优化**：针对批量数据映射的专门优化

## 📋 总结

通过本次优化，实体映射组件的性能得到了显著提升：

1. **反射操作优化**：预加载字段缓存，避免重复反射
2. **映射计算优化**：缓存列名到属性名的映射关系
3. **统一缓存管理**：集成到 UnifiedCacheManager 体系
4. **多层缓存策略**：实例缓存 + 统一缓存，最大化性能
5. **向后兼容**：保持原有API，平滑升级

这为 DAO 框架的整体性能提升奠定了坚实的基础，特别是在高并发和大量实体映射的场景下会有显著的性能改善。
