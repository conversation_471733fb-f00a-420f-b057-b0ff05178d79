/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.redis.cache;

import cn.hutool.core.util.StrUtil;
import com.lg.financecloud.common.core.constant.CacheConstants;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.convert.DurationStyle;
import org.springframework.cache.Cache;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * redis cache 扩展cache name自动化配置
 *
 * <AUTHOR>
 * <AUTHOR>
 * <p>
 * cachename = xx#ttl
 */
@Slf4j
public class RedisAutoCacheManager extends RedisCacheManager {

	private static final String SPLIT_FLAG = "#";

	private static final int CACHE_LENGTH = 2;
	
	private final RedisCacheWriter cacheWriter;
	private final RedisCacheConfiguration defaultCacheConfig;

	RedisAutoCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultCacheConfiguration,
			Map<String, RedisCacheConfiguration> initialCacheConfigurations, boolean allowInFlightCacheCreation) {
		super(cacheWriter, defaultCacheConfiguration, initialCacheConfigurations, allowInFlightCacheCreation);
		this.cacheWriter = cacheWriter;
		this.defaultCacheConfig = defaultCacheConfiguration;
		log.debug("初始化RedisAutoCacheManager");
	}

	@Override
	protected RedisCache createRedisCache(String name, @Nullable RedisCacheConfiguration cacheConfig) {
		String cacheName = name;
		
		if (StrUtil.isBlank(name)) {
			return super.createRedisCache(name, cacheConfig);
		}
		
		if (name.contains(SPLIT_FLAG)) {
			String[] cacheArray = name.split(SPLIT_FLAG);
			if (cacheArray.length >= CACHE_LENGTH) {
				cacheName = cacheArray[0];
				
				try {
					Duration duration = DurationStyle.detectAndParse(cacheArray[1], ChronoUnit.SECONDS);
					if (cacheConfig != null) {
						cacheConfig = cacheConfig.entryTtl(duration);
						log.debug("缓存[{}]设置TTL: {}秒", cacheName, duration.getSeconds());
					}
				} catch (IllegalArgumentException e) {
					log.error("缓存名称格式无效。预期格式为 'cacheName#timeToLive'，其中timeToLive是秒数.", e);
					// 回退到默认配置
				}
			}
		}
		
		return super.createRedisCache(cacheName, cacheConfig);
	}

	/**
	 * 从上下文中获取租户ID，重写@Cacheable value 值
	 * @param name 缓存名称
	 * @return 缓存实例
	 */
	@Override
	public Cache getCache(String name) {
		// see https://gitee.wang/pig/cloudx/issues/613
		if (name.startsWith(CacheConstants.GLOBALLY)) {
			log.trace("获取全局缓存: {}", name);
			return super.getCache(name);
		}
		
		String tenantCacheName = TenantContextHolder.getTenantId() + StrUtil.COLON + name;
		log.trace("获取租户缓存: {}", tenantCacheName);
		return super.getCache(tenantCacheName);
	}
}
