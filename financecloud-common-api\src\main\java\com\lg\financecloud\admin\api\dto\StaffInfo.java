package com.lg.financecloud.admin.api.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "员工信息")
public class StaffInfo implements Serializable {
    //员工id
    private String id;
    //员工工号
    private String jobNumber;
    //员工名字
    private String userName;
    //员工电话
    private String phone;
    //员工身份证号码
    private String identity;
    //员工所在公司id
    private Integer companyId;
    //员工所在职位
    private String jobId;
    //员工所在职位的职级
    private String jobScoreId;
    //员工主岗位
    private String majorPositionId;
    //员工兼岗
    private String partTimeJob;

    //员工用户id
    private Long userId;

    public Long getStaffId() {
        return Long.valueOf(id);
    }
}
