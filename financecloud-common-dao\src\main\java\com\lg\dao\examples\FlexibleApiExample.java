package com.lg.dao.examples;

import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.core.builder.DeleteBuilder;
import com.lg.dao.core.builder.UpdateBuilder;
import com.lg.dao.core.query.LambdaQuery;
import com.lg.dao.helper.DaoHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 展示如何使用GenericDao的便捷API
 */
//@Service
public class FlexibleApiExample {

    /**
     * 用户实体类
     */
    public static class User {
        private Long id;
        private String userName;
        private String status;
        private String role;
        private String department;
        private String tenantId;
        private Integer age;

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public String getDepartment() { return department; }
        public void setDepartment(String department) { this.department = department; }
        
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }

    // 使用DaoHelper创建DAO
    private final GenericDao<User, Long> userDao = DaoHelper.dao(User.class);

    /**
     * 1. 使用预构建的LambdaQuery执行查询
     */
    public List<User> findUsersWithPrebuiltQuery() {
        // 预先构建查询条件
        LambdaQuery<User> query = userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .orderByDesc(User::getId);
        
        // 使用预构建的查询条件执行查询
        return userDao.query(query);
    }

    /**
     * 2. 使用Lambda表达式构建查询条件并执行查询
     */
    public List<User> findUsersWithLambdaConsumer() {
        return userDao.query(query -> query
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .orderByDesc(User::getId)
        );
    }

    /**
     * 3. 使用预构建的LambdaQuery执行分页查询
     */
    public Page<User> findUsersByPageWithPrebuiltQuery() {
        // 预先构建查询条件
        LambdaQuery<User> query = userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .orderByDesc(User::getId);
        
        // 使用预构建的查询条件执行分页查询
        return userDao.queryPage(query, 1, 10);
    }

    /**
     * 4. 使用Lambda表达式构建查询条件并执行分页查询
     */
    public Page<User> findUsersByPageWithLambdaConsumer() {
        return userDao.queryPage(query -> query
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
            .orderByDesc(User::getId),
            1, 10
        );
    }

    /**
     * 5. 使用预构建的LambdaQuery执行单条记录查询
     */
    public User findOneUserWithPrebuiltQuery() {
        // 预先构建查询条件
        LambdaQuery<User> query = userDao.lambdaQuery()
            .eq(User::getId, 1L);
        
        // 使用预构建的查询条件执行单条记录查询
        return userDao.queryOne(query);
    }

    /**
     * 6. 使用Lambda表达式构建查询条件并执行单条记录查询
     */
    public User findOneUserWithLambdaConsumer() {
        return userDao.queryOne(query -> query
            .eq(User::getId, 1L)
        );
    }

    /**
     * 7. 使用预构建的LambdaQuery执行统计查询
     */
    public long countUsersWithPrebuiltQuery() {
        // 预先构建查询条件
        LambdaQuery<User> query = userDao.lambdaQuery()
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE");
        
        // 使用预构建的查询条件执行统计查询
        return userDao.count(query);
    }

    /**
     * 8. 使用Lambda表达式构建查询条件并执行统计查询
     */
    public long countUsersWithLambdaConsumer() {
        return userDao.count(query -> query
            .eq(User::getTenantId, "1001")
            .eq(User::getStatus, "ACTIVE")
        );
    }

    /**
     * 9. 使用Lambda表达式构建更新条件并执行更新
     */
    public int updateUsersWithLambdaConsumer() {
        return userDao.update(update -> update
            .set(User::getStatus, "INACTIVE")
            .eq(User::getDepartment, "销售部")
        );
    }

    /**
     * 10. 使用Lambda表达式构建删除条件并执行删除
     */
    public int deleteUsersWithLambdaConsumer() {
        return userDao.delete(delete -> delete
            .eq(User::getStatus, "DELETED")
        );
    }

    /**
     * 11. 复杂查询示例 - 嵌套条件与便捷API结合
     */
    public List<User> findUsersWithComplexConditions(String tenantId, List<String> roles, Integer minAge) {
        return userDao.query(query -> query
            .eq(User::getTenantId, tenantId)
            .and(wrapper -> wrapper
                .eq(User::getStatus, "ACTIVE")
                .and(subWrapper -> subWrapper
                    .in(User::getRole, roles.toArray())
                    .or()
                    .ge(User::getAge, minAge)
                )
            )
            .or(wrapper -> wrapper
                .eq(User::getRole, "SUPER_ADMIN")
            )
            .orderByDesc(User::getId)
        );
    }

    /**
     * 12. 动态查询示例 - 条件判断与便捷API结合
     */
    public List<User> findUsersWithDynamicConditions(String tenantId, String status, String role, String department) {
        return userDao.query(query -> {
            query.eq(User::getTenantId, tenantId);
            
            // 动态添加条件
            if (status != null) {
                query.eq(User::getStatus, status);
            }
            
            // 动态嵌套条件
            if (role != null && department != null) {
                query.and(wrapper -> wrapper
                    .eq(User::getRole, role)
                    .or()
                    .eq(User::getDepartment, department)
                );
            }
            
            query.orderByDesc(User::getId);
        });
    }
} 