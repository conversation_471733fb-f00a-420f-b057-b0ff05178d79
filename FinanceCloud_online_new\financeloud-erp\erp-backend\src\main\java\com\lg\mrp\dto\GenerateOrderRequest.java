package com.lg.mrp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 生成订单请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "生成订单请求")
public class GenerateOrderRequest {

    @ApiModelProperty(value = "MRP计划ID", required = true)
    @NotNull(message = "MRP计划ID不能为空")
    private String planId;

    @ApiModelProperty(value = "需求ID列表", required = true)
    @NotEmpty(message = "需求ID列表不能为空")
    private List<String> requirementIds;

    @ApiModelProperty(value = "物料类型：1-自产，2-外购", required = true)
    @NotNull(message = "物料类型不能为空")
    private String materialType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;
}