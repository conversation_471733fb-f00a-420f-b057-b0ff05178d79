package com.lg.financecloud.common.redis.mq;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import com.lg.dao.helper.DaoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理 REST API
 * 提供任务查询、状态管理等常用接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/mq/task")
public class TaskController {
    
    @Autowired(required = false)
    private TaskRecoveryService taskRecoveryService;
    
    /**
     * 查询任务详情
     */
    @GetMapping("/{taskId}")
    public Map<String, Object> getTaskDetail(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            MqDbDao mqDbDao = new MqDbDao();
            JobMateMationDto task = mqDbDao.getQueueLogById(taskId);
            
            if (task != null) {
                result.put("success", true);
                result.put("data", sanitizeTaskForFrontend(task));
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            log.error("查询任务详情失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 查询任务状态
     */
    @GetMapping("/{taskId}/status")
    public Map<String, Object> getTaskStatus(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String status = RedisJob.getTaskStatus(taskId);
            if (status != null) {
                result.put("success", true);
                result.put("taskId", taskId);
                result.put("status", status);
                result.put("statusText", getStatusText(status));
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            log.error("查询任务状态失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 分页查询任务列表
     */
    @GetMapping("/list")
    public Map<String, Object> getTaskList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String queueCode,
            @RequestParam(required = false) String createBy) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            // 分页查询
            Page<JobMateMationDto> pageResult = dao.lambdaQuery().in(JobMateMationDto::getStatus, status)
                    .eqIfNotEmpty(JobMateMationDto::getJobType, queueCode)
                    .eqIfNotEmpty(JobMateMationDto::getCreateId, createBy)

                    .orderByDesc(JobMateMationDto::getCreateTime)
                    .page((page - 1) * size, size);
            result.put("success", true);
            result.put("data", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("page", page);
            result.put("size", size);
            
        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 取消任务
     */
    @PostMapping("/{taskId}/cancel")
    public Map<String, Object> cancelTask(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = RedisJob.cancelTask(taskId);
            if (success) {
                result.put("success", true);
                result.put("message", "任务取消成功");
            } else {
                result.put("success", false);
                result.put("message", "任务取消失败，可能任务不存在或已完成");
            }
            
        } catch (Exception e) {
            log.error("取消任务失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "取消失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 重试任务（需要管理员权限）
     */
    @PostMapping("/{taskId}/retry")
    public Map<String, Object> retryTask(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加权限检查
            // if (!hasAdminPermission()) {
            //     result.put("success", false);
            //     result.put("message", "权限不足");
            //     return result;
            // }
            
            if (taskRecoveryService != null) {
                // 手动触发单个任务的恢复
                taskRecoveryService.manualRecovery();
                result.put("success", true);
                result.put("message", "重试请求已提交，请稍后查看任务状态");
            } else {
                result.put("success", false);
                result.put("message", "任务恢复服务未启用");
            }
            
        } catch (Exception e) {
            log.error("重试任务失败: {}", taskId, e);
            result.put("success", false);
            result.put("message", "重试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取任务统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getTaskStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            GenericDao<JobMateMationDto, String> dao = DaoHelper.daoString(JobMateMationDto.class);
            
            // 统计各状态的任务数量
            long waitingCount = dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_WAIT).count();
            long processingCount = dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_PROCESSING).count();
            long successCount = dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_SUCCESS).count();
            long failedCount = dao.lambdaQuery().eq(JobMateMationDto::getStatus, BaseTask.TASK_STATE_FAIL).count();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("waiting", waitingCount);
            stats.put("processing", processingCount);
            stats.put("success", successCount);
            stats.put("failed", failedCount);
            stats.put("total", waitingCount + processingCount + successCount + failedCount);
            
            result.put("success", true);
            result.put("data", stats);
            
        } catch (Exception e) {
            log.error("获取任务统计失败", e);
            result.put("success", false);
            result.put("message", "获取统计失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 清理敏感信息，返回给前端的任务数据
     */
    private Map<String, Object> sanitizeTaskForFrontend(JobMateMationDto task) {
        Map<String, Object> sanitized = new HashMap<>();
        
        // 基本信息
        sanitized.put("taskId", task.getJobId());
        sanitized.put("title", task.getTitle());
        sanitized.put("status", task.getStatus());
        sanitized.put("statusText", getStatusText(task.getStatus()));
        sanitized.put("queueCode", task.getJobType());
        sanitized.put("createTime", task.getCreateTime());
        sanitized.put("completeTime", task.getComplateTime());
        sanitized.put("retryCount", task.getRetrys());
        
        // 清理 requestBody 中的敏感信息
        if (StrUtil.isNotBlank(task.getRequestBody())) {
            try {
                JSONObject requestBody = JSONUtil.parseObj(task.getRequestBody());
                
                // 移除内部字段
                JSONObject taskData = requestBody.getJSONObject("taskData");
                if (taskData != null) {
                    taskData.remove("_internal_listenerClass");
                    taskData.remove("_internal_listenerSimpleName");
                    taskData.remove("_listenerClass"); // 兼容旧字段
                }
                
                // 移除监听器类字段
                requestBody.remove("listenerClass");
                
                sanitized.put("taskData", taskData);
                
            } catch (Exception e) {
                log.debug("解析任务数据失败: {}", task.getJobId(), e);
                sanitized.put("taskData", null);
            }
        }
        
        return sanitized;
    }
    
    /**
     * 获取状态文本描述
     */
    private String getStatusText(String status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case BaseTask.TASK_STATE_WAIT:
                return "等待处理";
            case BaseTask.TASK_STATE_PROCESSING:
                return "处理中";
            case BaseTask.TASK_STATE_SUCCESS:
                return "执行成功";
            case BaseTask.TASK_STATE_FAIL:
                return "执行失败";
            case BaseTask.TASK_STATE_PARTIAL_SUCCESS:
                return "部分成功";
            default:
                return "未知状态";
        }
    }
}
