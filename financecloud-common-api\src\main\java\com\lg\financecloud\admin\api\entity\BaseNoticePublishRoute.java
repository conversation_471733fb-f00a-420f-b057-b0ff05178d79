/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 通知发送渠道
 *
 * <AUTHOR>
 * @date 2023-03-23 14:09:39
 */
@Data
@ApiModel(value = "通知发送渠道")
public class BaseNoticePublishRoute   {
private static final long serialVersionUID = 1L;


    /**
     * 0 默认站内 1 邮件 2短信  3移动端通知栏 
     */
    @ApiModelProperty(value="0 默认站内 1 邮件 2短信  3移动端通知栏 ")
    private String routeType;
    /**
     * 模板id
     */
    @ApiModelProperty(value="模板id")
    private Long messageTplId;
    /**
     * 模板参数
     */
    @ApiModelProperty(value="模板参数")
    private String messageParam;
    @TableField(exist = false)
    private Map<String,String> msgParam;

    public BaseNoticePublishRoute(String routeType, Long messageTplId, String messageParam, Map<String, String> msgParam) {
        this.routeType = routeType;
        this.messageTplId = messageTplId;
        this.messageParam = messageParam;
        this.msgParam = msgParam;
    }
    public BaseNoticePublishRoute(String routeType) {
        this.routeType = routeType;

    }
    public BaseNoticePublishRoute() {
    }
}
