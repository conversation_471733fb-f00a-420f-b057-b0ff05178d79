/*
 *    Copyright (c) 2018-2025, cloudx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: cloudx
 */

package com.lg.financecloud.common.data.tenant;

import cn.hutool.core.util.NumberUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2018/9/13
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TenantContextHolderFilter extends GenericFilterBean {

	/**
	 * header 中租户ID
	 */
	public static final String TENANT_ID = "TENANT-ID";

	/****
	 * 账套ID
	 */
	public static final String ACCOUNT_ID = "ACCOUNT-ID";
	/**
	 * 租户ID
	 */
	public static final 	Integer TENANT_ID_1 = 1;

	@Override
	@SneakyThrows
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) {
		HttpServletRequest request = (HttpServletRequest) servletRequest;
		HttpServletResponse response = (HttpServletResponse) servletResponse;

		String tenantId = request.getHeader(TENANT_ID);
		String accountId = request.getHeader(ACCOUNT_ID);

		if(TenantContextHolder.getTenantId()==null){
			if (NumberUtil.isInteger(tenantId)) {
			  TenantContextHolder.setTenantId(Integer.parseInt(tenantId));
			}else {
				TenantContextHolder.setTenantId(TENANT_ID_1);
			}
		}

		// 账套ID 省去每次都需要传递账套ID
		if (NumberUtil.isLong(accountId)) {
			TenantContextHolder.setAccountId(Long.valueOf(accountId));
		}


		filterChain.doFilter(request, response);
		TenantContextHolder.clearAll();
	}

}
