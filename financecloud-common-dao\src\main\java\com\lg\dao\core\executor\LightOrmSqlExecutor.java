package com.lg.dao.core.executor;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.util.List;
import java.util.Map;

/**
 * SQL执行器接口
 */
public interface LightOrmSqlExecutor {


    /**
     * 执行查询
     *
     * @param sql SQL语句
     * @param params 参数
     * @param resultType 结果类型
     * @return 查询结果列表
     */
    <T> List<T> executeQuery(String sql, List<Object> params, Class<T> resultType);

    /**
     * 执行查询
     *
     * @param sql SQL语句
     * @param params 参数
     * @param rowMapper 行映射器
     * @return 查询结果列表
     */
    <T> List<T> executeQuery(String sql, List<Object> params, RowMapper<T> rowMapper);

    /**
     * 执行更新
     *
     * @param sql SQL语句
     * @param params 参数
     * @return 影响行数
     */
    int executeUpdate(String sql, List<Object> params);

    /**
     * 执行批量更新
     *
     * @param sql SQL语句
     * @param batchParams 批量参数列表，每个元素是一组参数
     * @return 每个批次影响的行数数组
     */
    int[] executeBatch(String sql, List<List<Object>> batchParams);

    /**
     * 执行查询返回Map结果（无实体映射）
     *
     * @param sql SQL语句
     * @param params 参数
     * @return 查询结果列表，每行数据为Map<String, Object>
     */
    List<Map<String, Object>> executeQueryForMap(String sql, List<Object> params);

    /**
     * 执行查询返回JSONArray结果（支持驼峰命名转换）
     *
     * @param sql SQL语句
     * @param params 参数
     * @param toCamelCase 是否转换为驼峰命名，默认true
     * @return 查询结果JSONArray
     */
    JSONArray executeQueryForJson(String sql, List<Object> params, boolean toCamelCase);

    /**
     * 执行查询返回JSONArray结果（默认驼峰命名转换）
     *
     * @param sql SQL语句
     * @param params 参数
     * @return 查询结果JSONArray
     */
    default JSONArray executeQueryForJson(String sql, List<Object> params) {
        return executeQueryForJson(sql, params, true);
    }

    /**
     * 获取JdbcTemplate
     * @return JdbcTemplate实例
     */
    JdbcTemplate getJdbcTemplate();
}