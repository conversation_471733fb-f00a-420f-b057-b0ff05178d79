package com.lg.dao.core.query;

import com.lg.dao.config.LightORMAutoConfiguration;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.Page;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LambdaJoinQuery Spring集成测试
 * 使用H2内存数据库和Spring测试框架
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Sql(scripts = {
    "classpath:schema-join-test.sql",
    "classpath:data-join-test.sql"
})
public class LambdaJoinQuerySpringTest {

    @SpringBootApplication
    @Import(LightORMAutoConfiguration.class)
    static class TestConfig {
        @Bean
        public OrderDao orderDao() {
            return new OrderDao();
        }

        @Bean
        public UserDao userDao() {
            return new UserDao();
        }

        @Bean
        public OrderItemDao orderItemDao() {
            return new OrderItemDao();
        }
    }

    @Autowired
    private OrderDao orderDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private OrderItemDao orderItemDao;

    @BeforeEach
    public void setUp() {
        // 可以在这里添加测试前的准备工作
    }

    /**
     * 测试基本的两表JOIN查询
     */
    @Test
    public void testBasicJoinQuery() {
        // 执行JOIN查询
        List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
            // 先设置表别名
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            // 然后才使用selectFields方法
            .selectFields(Order.class, Order::getId, Order::getAmount, Order::getStatus)
            .select("u.username", "u.email")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .eq(User.class, User::getStatus, "active")
            .listMap();

        // 验证结果
        assertNotNull(results);
        
        // 打印结果
        System.out.println("=== 基本JOIN查询结果 ===");
        for (Map<String, Object> row : results) {
            System.out.println(row);
        }
    }

    /**
     * 测试返回DTO对象
     */
    @Test
    public void testReturnDTO() {
        // 执行JOIN查询，返回DTO对象
        List<OrderUserDTO> results = orderDao.createLambdaJoinQuery(Order.class)
            .select("o.id as orderId", "o.amount", "o.status as orderStatus")
            .select("u.id as userId", "u.username", "u.status as userStatus")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .eq(Order.class, Order::getStatus, "PAID")
            .list(OrderUserDTO.class);

        // 验证结果
        assertNotNull(results);
        assertTrue(results.size() > 0);
        
        // 打印结果
        System.out.println("=== 返回DTO对象结果 ===");
        for (OrderUserDTO dto : results) {
            System.out.println("订单ID: " + dto.getOrderId() + 
                    ", 金额: " + dto.getAmount() + 
                    ", 状态: " + dto.getOrderStatus() + 
                    ", 用户: " + dto.getUsername());
        }
    }

    /**
     * 测试返回带嵌套对象的实体
     */
    @Test
    public void testReturnNestedEntity() {
        // 执行JOIN查询，返回Map结果
        List<Map<String, Object>> resultMaps = orderDao.createLambdaJoinQuery(Order.class)
            .select("o.*")
            .select("u.id as u_id", "u.username as u_username", "u.email as u_email", "u.status as u_status")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .eq(Order.class, Order::getStatus, "PAID")
            .listMap();

        // 手动转换为带嵌套对象的实体
        List<Order> orders = orderDao.convertToNestedEntities(resultMaps);

        // 验证结果
        assertNotNull(orders);
        assertTrue(orders.size() > 0);
        
        // 打印结果
        System.out.println("=== 返回嵌套实体结果 ===");
        for (Order order : orders) {
            System.out.println("订单ID: " + order.getId() + 
                    ", 金额: " + order.getAmount() + 
                    ", 用户: " + (order.getUser() != null ? order.getUser().getUsername() : "null"));
        }
    }

    /**
     * 测试分页查询
     */
    @Test
    public void testPageQuery() {
        // 执行分页查询
        Page<OrderUserDTO> page = orderDao.createLambdaJoinQuery(Order.class)
            .select("o.id as orderId", "o.amount", "o.status as orderStatus")
            .select("u.id as userId", "u.username", "u.status as userStatus")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .orderBy(Order.class, Order::getCreateTime)
            .desc()
            .page(OrderUserDTO.class, 1, 2);

        // 验证分页结果
        assertNotNull(page);
        assertTrue(page.getTotal() > 0);
        assertEquals(1, page.getPageNum());
        assertEquals(2, page.getPageSize());
        
        // 打印结果
        System.out.println("=== 分页查询结果 ===");
        System.out.println("总记录数: " + page.getTotal() + ", 当前页记录数: " + page.getRecords().size());
        for (OrderUserDTO dto : page.getRecords()) {
            System.out.println("订单ID: " + dto.getOrderId() + 
                    ", 金额: " + dto.getAmount() + 
                    ", 状态: " + dto.getOrderStatus() + 
                    ", 用户: " + dto.getUsername());
        }
    }

    /**
     * 测试动态条件
     */
    @Test
    public void testDynamicConditions() {
        String username = "john";
        String status = null; // 空值不应该被添加到查询条件中

        // 执行带动态条件的查询
        List<OrderUserDTO> results = orderDao.createLambdaJoinQuery(Order.class)
            .select("o.id as orderId", "o.amount", "o.status as orderStatus")
            .select("u.id as userId", "u.username", "u.status as userStatus")
            .from(Order.class, "o")
            .leftJoin(User.class, "u")
            .on(Order.class, Order::getUserId, User.class, User::getId)
            .where()
            .likeIfNotEmpty(User.class, User::getUsername, username)
            .and()
            .eqIfNotNull(Order.class, Order::getStatus, status)
            .list(OrderUserDTO.class);

        // 验证结果
        assertNotNull(results);
        
        // 打印结果
        System.out.println("=== 动态条件查询结果 ===");
        System.out.println("查询条件: username like '%john%', status = " + status);
        System.out.println("结果数量: " + results.size());
        for (OrderUserDTO dto : results) {
            System.out.println("订单ID: " + dto.getOrderId() + 
                    ", 用户: " + dto.getUsername() + 
                    ", 状态: " + dto.getOrderStatus());
        }
    }

    /**
     * 测试三表关联查询
     */
    @Test
    public void testThreeTableJoin() {
        // 执行三表关联查询
        try {
            System.out.println("===== 开始执行三表关联查询测试 =====");
            
            // 先打印要查询的表名及结构
            System.out.println("Order表: " + Order.class.getAnnotation(Table.class).name());
            System.out.println("User表: " + User.class.getAnnotation(Table.class).name());
            System.out.println("OrderItem表: " + OrderItem.class.getAnnotation(Table.class).name());
            
            // 使用基本的SQL语法进行测试
            List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
                // 简化查询，只选择少量字段
                .select("o.id", "o.amount")
                .select("u.username")
                .select("i.product_name")
                .from(Order.class, "o")
                .leftJoin(User.class, "u")
                .on(Order.class, Order::getUserId, User.class, User::getId)
                .leftJoin(OrderItem.class, "i")
                .on("i.order_id = o.id")
                .where()
                .eq(Order.class, Order::getId, 1L)
                .listMap();

            // 验证结果
            assertNotNull(results);
            
            // 打印结果
            System.out.println("=== 三表关联查询结果 ===");
            for (Map<String, Object> row : results) {
                System.out.println(row);
            }
            
            System.out.println("===== 三表关联查询测试完成 =====");
        } catch (Exception e) {
            System.err.println("测试出现异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试Lambda表达式简洁写法
     * 注意：当前由于静态内部类的限制，无法使用Lambda表达式进行跨实体查询
     * 可以考虑将实体类移到外部类，或者使用字符串方式查询
     */
    @Test
    public void testLambdaSelectSyntax() {
        // 执行查询，使用字段字符串而不是Lambda表达式
        try {
            System.out.println("===== 开始测试简洁查询写法 =====");
            
            List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
                // 使用字符串指定查询字段
                .select("o.id", "o.amount", "u.username", "i.product_name")
                .from(Order.class, "o")
                .leftJoin(User.class, "u")
                .on("o.user_id = u.id")  // 使用字符串条件而不是Lambda
                .leftJoin(OrderItem.class, "i")
                .on("i.order_id = o.id")
                .where()
                .eq("o.id", 1L)  // 使用字符串而不是Lambda
                .listMap();

            // 验证结果
            assertNotNull(results);
            
            // 打印结果
            System.out.println("=== 简洁查询写法结果 ===");
            for (Map<String, Object> row : results) {
                System.out.println(row);
            }
            
            System.out.println("===== 简洁查询写法测试完成 =====");
        } catch (Exception e) {
            System.err.println("测试出现异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试简单的Lambda表达式处理
     */
    @Test
    public void testSimplifiedQueryWithLambda() {
        try {
            System.out.println("===== 开始测试简化查询和Lambda表达式结合 =====");
            
            // 使用增强的selectFields方法
            List<Map<String, Object>> results = orderDao.createLambdaJoinQuery(Order.class)
                // 先设置表别名
                .from(Order.class, "o")
                .leftJoin(User.class, "u")
                    .on(Order.class, Order::getUserId, User.class, User::getId)
                .leftJoin(OrderItem.class, "i")
                    .on("i.order_id = o.id")
                // 然后才使用selectFields方法
                .selectFields(Order.class, Order::getId, Order::getAmount)
                .selectFields(User.class, User::getUsername)
                .selectFields(OrderItem.class, OrderItem::getProductName)


                .where()
                .eq(Order.class, Order::getId, 1L)
                .listMap();

            // 验证结果
            assertNotNull(results);
            
            // 打印结果
            System.out.println("=== 简化查询和Lambda表达式结合结果 ===");
            for (Map<String, Object> row : results) {
                System.out.println(row);
            }
            
            System.out.println("===== 简化查询和Lambda表达式结合测试完成 =====");
        } catch (Exception e) {
            System.err.println("测试出现异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    // 测试实体类
    @Table(name = "t_order")
    @Data
    public static class Order {
        @Id
        private Long id;
        
        @Column(name = "user_id")
        private Long userId;
        
        private BigDecimal amount;
        
        @Column(name = "create_time")
        private Timestamp createTime;
        
        private String status;
        
        @Transient
        private User user;  // 关联的用户对象
    }

    @Table(name = "t_user")
    @Data
    public static class User {
        @Id
        private Long id;
        
        private String username;
        
        private String email;
        
        private String status;
    }

    @Table(name = "t_order_item")
    @Data
    public static class OrderItem {
        @Id
        private Long id;
        
        @Column(name = "order_id")
        private Long orderId;
        
        @Column(name = "product_name")
        private String productName;
        
        private Integer quantity;
        
        private BigDecimal price;
    }

    // DTO类
    @Data
    public static class OrderUserDTO {
        private Long orderId;
        private BigDecimal amount;
        private String orderStatus;
        private Long userId;
        private String username;
        private String userStatus;
    }

    // DAO类
    public static class OrderDao extends GenericDao<Order, Long> {
        /**
         * 创建Lambda风格JOIN查询的方法
         */
        public <E> LambdaJoinQuery<E> createLambdaJoinQuery(Class<E> entityClass) {
            return new LambdaJoinQuery<>((GenericDao<E, ?>) this, entityClass);
        }
        
        /**
         * 将Map转换为带嵌套对象的实体
         */
        public List<Order> convertToNestedEntities(List<Map<String, Object>> maps) {
            List<Order> result = new java.util.ArrayList<>();
            
            for (Map<String, Object> map : maps) {
                // 创建Order对象
                Order order = new Order();
                order.setId(getLongValue(map, "id"));
                order.setUserId(getLongValue(map, "user_id"));
                order.setAmount((BigDecimal) map.get("amount"));
                order.setCreateTime((Timestamp) map.get("create_time"));
                order.setStatus((String) map.get("status"));
                
                // 创建User对象
                User user = new User();
                user.setId(getLongValue(map, "u_id"));
                user.setUsername((String) map.get("u_username"));
                user.setEmail((String) map.get("u_email"));
                user.setStatus((String) map.get("u_status"));
                
                // 设置关联
                order.setUser(user);
                
                result.add(order);
            }
            
            return result;
        }
        
        private Long getLongValue(Map<String, Object> map, String key) {
            Object value = map.get(key);
            if (value == null) {
                return null;
            }
            if (value instanceof Long) {
                return (Long) value;
            }
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            return null;
        }
    }

    public static class UserDao extends GenericDao<User, Long> {
    }

    public static class OrderItemDao extends GenericDao<OrderItem, Long> {
    }
} 