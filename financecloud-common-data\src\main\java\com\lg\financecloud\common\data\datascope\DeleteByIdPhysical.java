package com.lg.financecloud.common.data.datascope;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

@Slf4j
public class DeleteByIdPhysical extends AbstractMethod {

	protected DeleteByIdPhysical() {
		super("deleteByIdPhysical");
	}

	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
		// 强制使用物理删除，忽略逻辑删除
		final String sql = "<script>DELETE FROM " + tableInfo.getTableName() + 
			" WHERE " + tableInfo.getKeyColumn() + " = #{id}</script>";
		SqlSource sqlSource = super.createSqlSource(configuration, sql, modelClass);
		return this.addDeleteMappedStatement(mapperClass, methodName, sqlSource);
	}
}
