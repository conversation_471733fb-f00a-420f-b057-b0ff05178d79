package com.lg.dao.core.builder;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.GenericDao;
import com.lg.dao.core.condition.ConditionStrategy;
import com.lg.dao.core.condition.ConditionStrategyUtils;
import com.lg.dao.core.func.LFunction;
import com.lg.dao.core.sql.SqlBuilder;
import com.lg.dao.core.util.LambdaUtils;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * 删除构建器 - 支持Lambda表达式
 */
@RequiredArgsConstructor
public class DeleteBuilder<T> {
    
    private final Class<T> entityClass;
    private final EntityInfo entityInfo;
    private final List<WhereClause> whereClauses = new ArrayList<>();

    private final GenericDao dao;
    
    /**
     * 条件 - Lambda方式
     */
    public <R> DeleteBuilder<T> eq(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "=", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> ne(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "!=", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> gt(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, ">", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> ge(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, ">=", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> lt(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "<", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> le(LFunction<T, R> getter, R value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "<=", value));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> like(LFunction<T, R> getter, String value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "LIKE", "%" + value + "%"));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> in(LFunction<T, R> getter, List<R> values) {
        if (values != null && !values.isEmpty()) {
            String fieldName = LambdaUtils.getFieldName(getter);
            String columnName = entityInfo.getColumnName(fieldName);
            if (columnName != null) {
                whereClauses.add(new WhereClause(columnName, "IN", values));
            }
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> isNull(LFunction<T, R> getter) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "IS NULL", null));
        }
        return this;
    }
    
    public <R> DeleteBuilder<T> isNotNull(LFunction<T, R> getter) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "IS NOT NULL", null));
        }
        return this;
    }

    /**
     * 右模糊查询
     */
    public <R> DeleteBuilder<T> likeRight(LFunction<T, R> getter, String value) {
        String fieldName = LambdaUtils.getFieldName(getter);
        String columnName = entityInfo.getColumnName(fieldName);
        if (columnName != null) {
            whereClauses.add(new WhereClause(columnName, "LIKE",  value + "%"));
        }
        return this;
    }

    // ==================== 支持条件策略的方法 ====================

    /**
     * 根据条件策略动态添加等于条件
     */
    public <R> DeleteBuilder<T> eq(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return eq(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加不等于条件
     */
    public <R> DeleteBuilder<T> ne(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return ne(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加大于条件
     */
    public <R> DeleteBuilder<T> gt(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return gt(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加大于等于条件
     */
    public <R> DeleteBuilder<T> ge(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return ge(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加小于条件
     */
    public <R> DeleteBuilder<T> lt(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return lt(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加小于等于条件
     */
    public <R> DeleteBuilder<T> le(LFunction<T, R> getter, R value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return le(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加模糊查询条件
     */
    public <R> DeleteBuilder<T> like(LFunction<T, R> getter, String value, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
            return like(getter, value);
        }
        return this;
    }

    /**
     * 根据条件策略动态添加IN条件
     */
    public <R> DeleteBuilder<T> in(LFunction<T, R> getter, List<R> values, ConditionStrategy strategy) {
        if (ConditionStrategyUtils.shouldAddCondition(values, strategy)) {
            return in(getter, values);
        }
        return this;
    }

    // ==================== 便捷方法：使用默认策略 ====================

    /**
     * 使用IS_NOT_EMPTY策略的等于条件（常用）
     */
    public <R> DeleteBuilder<T> eqIfNotEmpty(LFunction<T, R> getter, R value) {
        return eq(getter, value, ConditionStrategy.IS_NOT_EMPTY);
    }

    /**
     * 使用IS_NOT_EMPTY策略的模糊查询条件（常用）
     */
    public <R> DeleteBuilder<T> likeIfNotEmpty(LFunction<T, R> getter, String value) {
        return like(getter, value, ConditionStrategy.IS_NOT_EMPTY);
    }

    /**
     * 使用IS_NOT_NULL策略的等于条件
     */
    public <R> DeleteBuilder<T> eqIfNotNull(LFunction<T, R> getter, R value) {
        return eq(getter, value, ConditionStrategy.IS_NOT_NULL);
    }

    // ==================== allEq 方法 ====================

    /**
     * 根据实体对象的所有属性值自动生成等值条件
     * @param entity 实体对象
     * @param strategy 条件策略
     * @return DeleteBuilder对象
     */
    public DeleteBuilder<T> allEq(T entity, ConditionStrategy strategy) {
        if (entity == null) {
            return this;
        }

        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            try {
                Object value = fieldInfo.getField().get(entity);
                if (ConditionStrategyUtils.shouldAddCondition(value, strategy)) {
                    whereClauses.add(new WhereClause(fieldInfo.getColumn(), "=", value));
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access field: " + fieldInfo.getField().getName(), e);
            }
        }
        return this;
    }

    /**
     * 根据实体对象的所有属性值自动生成等值条件（使用IS_NOT_EMPTY策略）
     * @param entity 实体对象
     * @return DeleteBuilder对象
     */
    public DeleteBuilder<T> allEq(T entity) {
        return allEq(entity, ConditionStrategy.IS_NOT_EMPTY);
    }

    public int execute() {
        SqlBuilder builder = build();
       return  dao.getSqlExecutor().executeUpdate(builder.getSql(), builder.getParams());
    }
    /**
     * 构建SQL
     */
    public SqlBuilder build() {
        SqlBuilder builder = new SqlBuilder();
        builder.append("DELETE FROM " + entityInfo.getTableName());
        
        // 添加WHERE子句
        if (!whereClauses.isEmpty()) {
            builder.append(" WHERE ");
            for (int i = 0; i < whereClauses.size(); i++) {
                if (i > 0) {
                    builder.append(" AND ");
                }
                WhereClause whereClause = whereClauses.get(i);
                if ("IN".equals(whereClause.operator)) {
                    List<?> values = (List<?>) whereClause.value;
                    builder.append(whereClause.column + " IN (");
                    for (int j = 0; j < values.size(); j++) {
                        if (j > 0) builder.append(", ");
                        builder.append("?");
                        builder.addParam(values.get(j));
                    }
                    builder.append(")");
                } else if ("IS NULL".equals(whereClause.operator) || "IS NOT NULL".equals(whereClause.operator)) {
                    builder.append(whereClause.column + " " + whereClause.operator);
                } else {
                    builder.append(whereClause.column + " " + whereClause.operator + " ?");
                    builder.addParam(whereClause.value);
                }
            }
        }
        
        return builder;
    }
    
    private static class WhereClause {
        final String column;
        final String operator;
        final Object value;
        
        WhereClause(String column, String operator, Object value) {
            this.column = column;
            this.operator = operator;
            this.value = value;
        }
    }
}