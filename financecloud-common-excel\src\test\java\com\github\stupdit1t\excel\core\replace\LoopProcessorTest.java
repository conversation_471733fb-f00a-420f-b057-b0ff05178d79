package com.github.stupdit1t.excel.core.replace;

import com.github.stupdit1t.excel.core.ExcelHelper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;

import java.io.*;
import java.util.*;

import static org.junit.Assert.*;


/**
 * Excel循环处理器测试类
 */
public class LoopProcessorTest {
    
    private Workbook workbook;
    private Sheet sheet;
    
    @Before
    public void setUp() {
        workbook = new XSSFWorkbook();
        sheet = workbook.createSheet("测试表");
    }
    
    @Test
    public void testSimpleLoop() {
        // 创建模板
        createSimpleLoopTemplate();
        
        // 准备数据
        List<Map<String, Object>> users = Arrays.asList(
            createUser("张三", 25, "开发工程师"),
            createUser("李四", 30, "产品经理"),
            createUser("王五", 28, "测试工程师")
        );
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("users", users);
        variables.put("title", "员工信息表");
        
        // 处理循环
        LoopProcessor.processLoops(sheet, variables);
        
        // 验证结果
        assertEquals("员工信息表", getCellValue(sheet, 0, 0));
        assertEquals("张三", getCellValue(sheet, 1, 0));
        assertEquals("25", getCellValue(sheet, 1, 1));
        assertEquals("开发工程师", getCellValue(sheet, 1, 2));
        assertEquals("李四", getCellValue(sheet, 2, 0));
        assertEquals("30", getCellValue(sheet, 2, 1));
        assertEquals("产品经理", getCellValue(sheet, 2, 2));
        assertEquals("王五", getCellValue(sheet, 3, 0));
        assertEquals("28", getCellValue(sheet, 3, 1));
        assertEquals("测试工程师", getCellValue(sheet, 3, 2));
    }
    
    @Test
    public void testEmptyListLoop() {
        // 创建模板
        createSimpleLoopTemplate();
        
        // 准备空数据
        List<Map<String, Object>> users = new ArrayList<>();
        Map<String, Object> variables = new HashMap<>();
        variables.put("users", users);
        variables.put("title", "员工信息表");
        
        // 处理循环
        LoopProcessor.processLoops(sheet, variables);
        
        // 验证结果 - 循环区域应该被删除
        assertEquals("员工信息表", getCellValue(sheet, 0, 0));
        // 第1行应该不存在或为空（循环区域被删除）
        assertTrue(sheet.getRow(1) == null || isRowEmpty(sheet.getRow(1)));
    }
    
    @Test
    public void testNestedObjectLoop() {
        // 创建嵌套对象模板
        createNestedObjectTemplate();
        
        // 准备嵌套数据
        List<Map<String, Object>> orders = Arrays.asList(
            createOrder("ORD001", "张三", "北京市朝阳区"),
            createOrder("ORD002", "李四", "上海市浦东新区")
        );
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("orders", orders);
        
        // 处理循环
        LoopProcessor.processLoops(sheet, variables);
        
        // 验证结果
        assertEquals("ORD001", getCellValue(sheet, 1, 0));
        assertEquals("张三", getCellValue(sheet, 1, 1));
        assertEquals("北京市朝阳区", getCellValue(sheet, 1, 2));
        assertEquals("ORD002", getCellValue(sheet, 2, 0));
        assertEquals("李四", getCellValue(sheet, 2, 1));
        assertEquals("上海市浦东新区", getCellValue(sheet, 2, 2));
    }
    
    @Test
    public void testMultiRowLoop() {
        // 创建多行循环模板
        createMultiRowLoopTemplate();
        
        // 准备数据
        List<Map<String, Object>> products = Arrays.asList(
            createProduct("P001", "笔记本电脑", 5999.00),
            createProduct("P002", "智能手机", 2999.00)
        );
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("products", products);
        
        // 处理循环
        LoopProcessor.processLoops(sheet, variables);
        
        // 验证结果 - 每个产品应该占用2行
        assertEquals("产品编号: P001", getCellValue(sheet, 1, 0));
        assertEquals("产品名称: 笔记本电脑 价格: 5999.0", getCellValue(sheet, 2, 0));
        assertEquals("产品编号: P002", getCellValue(sheet, 3, 0));
        assertEquals("产品名称: 智能手机 价格: 2999.0", getCellValue(sheet, 4, 0));
    }
    
    @Test
    public void testCompleteExcelReplace() throws IOException {
        // 生成可视化的Excel模板对比文件
        generateVisualExcelTemplate();
        
        // 创建完整模板
        createCompleteTemplate();
        
        // 准备数据
        List<Map<String, Object>> employees = Arrays.asList(
            createEmployee("E001", "张三", "开发部", 8000.00),
            createEmployee("E002", "李四", "产品部", 7000.00),
            createEmployee("E003", "王五", "测试部", 6500.00)
        );
        
        // 先将workbook写入到ByteArrayOutputStream，然后作为InputStream使用
        ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
        workbook.write(tempStream);
        
        // 先保存原始模板到文件，方便查看
        writeTemplateToTextFile(workbook, "template_original.txt", "原始Excel模板内容");
        
        ByteArrayInputStream inputStream = new ByteArrayInputStream(tempStream.toByteArray());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // 使用ExcelHelper进行完整测试
        ExcelHelper.opsReplace()
            .from(inputStream)
            .var("companyName", "科技有限公司")
            .var("reportDate", "2024-01-15")
            .loop("employees", employees)
            .var("totalCount", employees.size())
            .replaceTo(outputStream);
        
        // 将结果写入文件
        String fileName = "testCompleteTemplate_" + System.currentTimeMillis() + ".xlsx";
        try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
            outputStream.writeTo(fileOut);
        }
        
        // 保存替换后的结果到文本文件
        ByteArrayInputStream resultStream = new ByteArrayInputStream(outputStream.toByteArray());
        Workbook resultWorkbook = new XSSFWorkbook(resultStream);
        writeTemplateToTextFile(resultWorkbook, "template_replaced.txt", "替换后的Excel内容");
        resultWorkbook.close();

        System.out.println("Excel文件已生成: " + fileName);
            System.out.println("模板文件已生成: template_original.txt");
            System.out.println("替换结果已生成: template_replaced.txt");

        // 验证结果
        assertTrue(outputStream.size() > 0);
    }
    
    /**
      * 将模板内容写入文本文件，方便查看
      */
     private void writeTemplateToTextFile(Workbook workbook, String fileName, String description) {
         try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(fileName), "UTF-8");
              PrintWriter printWriter = new PrintWriter(writer)) {
             printWriter.println(description);
             printWriter.println("===========================================\n");
             
             for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                 Sheet sheet = workbook.getSheetAt(sheetIndex);
                 printWriter.println("工作表: " + sheet.getSheetName());
                 printWriter.println("-------------------------------------------");
                 
                 for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                     Row row = sheet.getRow(rowIndex);
                     if (row != null) {
                         printWriter.print("第" + rowIndex + "行: ");
                         for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
                             Cell cell = row.getCell(cellIndex);
                             if (cell != null) {
                                 printWriter.print("[" + cellIndex + "]" + cell.toString() + " ");
                             }
                         }
                         printWriter.println();
                     }
                 }
                 printWriter.println();
             }
             
             // 添加模板说明
             printWriter.println("\n模板说明:");
             printWriter.println("- ${companyName}: 公司名称变量");
             printWriter.println("- ${reportDate}: 报表日期变量");
             printWriter.println("- ${#foreach employees}: 循环开始标记");
             printWriter.println("- ${item.empNo}: 员工编号");
             printWriter.println("- ${item.name}: 员工姓名");
             printWriter.println("- ${item.department}: 部门");
             printWriter.println("- ${item.salary}: 薪资");
             printWriter.println("- ${/foreach}: 循环结束标记");
             printWriter.println("- ${totalCount}: 总人数变量");
         } catch (IOException e) {
            System.err.println("写入文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成可视化的Excel模板对比文件
     */
    private void generateVisualExcelTemplate() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        
        // 创建原始模板工作表
        Sheet originalSheet = workbook.createSheet("原始模板");
        createVisualOriginalTemplate(originalSheet);
        
        // 创建替换结果工作表
        Sheet resultSheet = workbook.createSheet("替换结果");
        createVisualReplacedResult(resultSheet);
        
        // 创建说明工作表
        Sheet instructionSheet = workbook.createSheet("使用说明");
        createInstructionSheet(instructionSheet);
        
        // 保存文件
         String fileName = "excel_template_comparison_" + System.currentTimeMillis() + ".xlsx";
         try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
             workbook.write(fileOut);
         }
         
         System.out.println("可视化Excel模板已生成: " + fileName);
        
        workbook.close();
    }
    
    /**
     * 创建可视化的原始模板
     */
    private void createVisualOriginalTemplate(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        
        // 创建变量样式
        CellStyle variableStyle = sheet.getWorkbook().createCellStyle();
        Font variableFont = sheet.getWorkbook().createFont();
        variableFont.setColor(IndexedColors.BLUE.getIndex());
        variableStyle.setFont(variableFont);
        
        // 创建循环标记样式
        CellStyle loopStyle = sheet.getWorkbook().createCellStyle();
        Font loopFont = sheet.getWorkbook().createFont();
        loopFont.setColor(IndexedColors.RED.getIndex());
        loopFont.setBold(true);
        loopStyle.setFont(loopFont);
        
        int rowIndex = 0;
        
        // 第0行：公司名称
        Row companyRow = sheet.createRow(rowIndex++);
        Cell companyCell = companyRow.createCell(0);
        companyCell.setCellValue("${companyName}");
        companyCell.setCellStyle(variableStyle);
        
        // 第1行：报表日期
        Row dateRow = sheet.createRow(rowIndex++);
        Cell dateCell = dateRow.createCell(0);
        dateCell.setCellValue("报表日期: ${reportDate}");
        dateCell.setCellStyle(variableStyle);
        
        // 第2行：空行
        sheet.createRow(rowIndex++);
        
        // 第3行：表头
        Row headerRow = sheet.createRow(rowIndex++);
        headerRow.createCell(0).setCellValue("员工编号");
        headerRow.createCell(1).setCellValue("姓名");
        headerRow.createCell(2).setCellValue("部门");
        headerRow.createCell(3).setCellValue("薪资");
        
        // 第4行：循环开始
        Row startRow = sheet.createRow(rowIndex++);
        Cell startCell = startRow.createCell(0);
        startCell.setCellValue("${#foreach employees}");
        startCell.setCellStyle(loopStyle);
        
        // 第5行：循环体
        Row bodyRow = sheet.createRow(rowIndex++);
        Cell empNoCell = bodyRow.createCell(0);
        empNoCell.setCellValue("${item.empNo}");
        empNoCell.setCellStyle(variableStyle);
        
        Cell nameCell = bodyRow.createCell(1);
        nameCell.setCellValue("${item.name}");
        nameCell.setCellStyle(variableStyle);
        
        Cell deptCell = bodyRow.createCell(2);
        deptCell.setCellValue("${item.department}");
        deptCell.setCellStyle(variableStyle);
        
        Cell salaryCell = bodyRow.createCell(3);
        salaryCell.setCellValue("${item.salary}");
        salaryCell.setCellStyle(variableStyle);
        
        // 第6行：循环结束
        Row endRow = sheet.createRow(rowIndex++);
        Cell endCell = endRow.createCell(0);
        endCell.setCellValue("${/foreach}");
        endCell.setCellStyle(loopStyle);
        
        // 第7行：空行
        sheet.createRow(rowIndex++);
        
        // 第8行：总计
        Row totalRow = sheet.createRow(rowIndex++);
        Cell totalCell = totalRow.createCell(0);
        totalCell.setCellValue("总计: ${totalCount} 人");
        totalCell.setCellStyle(variableStyle);
    }
    
    /**
     * 创建可视化的替换结果示例
     */
    private void createVisualReplacedResult(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        
        int rowIndex = 0;
        
        // 第0行：公司名称（已替换）
        Row companyRow = sheet.createRow(rowIndex++);
        companyRow.createCell(0).setCellValue("科技有限公司");
        
        // 第1行：报表日期（已替换）
        Row dateRow = sheet.createRow(rowIndex++);
        dateRow.createCell(0).setCellValue("报表日期: 2024-01-15");
        
        // 第2行：空行
        sheet.createRow(rowIndex++);
        
        // 第3行：表头
        Row headerRow = sheet.createRow(rowIndex++);
        headerRow.createCell(0).setCellValue("员工编号");
        headerRow.createCell(1).setCellValue("姓名");
        headerRow.createCell(2).setCellValue("部门");
        headerRow.createCell(3).setCellValue("薪资");
        
        // 第4-6行：员工数据（循环展开后的结果）
        String[][] employees = {
            {"E001", "张三", "开发部", "8000.0"},
            {"E002", "李四", "产品部", "7000.0"},
            {"E003", "王五", "测试部", "6500.0"}
        };
        
        for (String[] employee : employees) {
            Row empRow = sheet.createRow(rowIndex++);
            empRow.createCell(0).setCellValue(employee[0]);
            empRow.createCell(1).setCellValue(employee[1]);
            empRow.createCell(2).setCellValue(employee[2]);
            empRow.createCell(3).setCellValue(employee[3]);
        }
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 总计（已替换）
        Row totalRow = sheet.createRow(rowIndex++);
        totalRow.createCell(0).setCellValue("总计: 3 人");
    }
    
    /**
     * 创建使用说明工作表
     */
    private void createInstructionSheet(Sheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 8000);
        sheet.setColumnWidth(1, 12000);
        
        // 创建标题样式
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        
        // 创建子标题样式
        CellStyle subTitleStyle = sheet.getWorkbook().createCellStyle();
        Font subTitleFont = sheet.getWorkbook().createFont();
        subTitleFont.setBold(true);
        subTitleFont.setFontHeightInPoints((short) 12);
        subTitleStyle.setFont(subTitleFont);
        
        int rowIndex = 0;
        
        // 标题
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("Excel模板循环功能使用说明");
        titleCell.setCellStyle(titleStyle);
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 功能介绍
        Row introRow = sheet.createRow(rowIndex++);
        Cell introCell = introRow.createCell(0);
        introCell.setCellValue("功能介绍:");
        introCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("本功能支持在Excel模板中使用循环语法，可以根据数据列表动态生成多行内容。");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 语法说明
        Row syntaxRow = sheet.createRow(rowIndex++);
        Cell syntaxCell = syntaxRow.createCell(0);
        syntaxCell.setCellValue("语法说明:");
        syntaxCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("1. 普通变量: ${variableName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("2. 循环开始: ${#foreach listName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("3. 循环变量: ${item.propertyName}");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("4. 循环结束: ${/foreach}");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 使用示例
        Row exampleRow = sheet.createRow(rowIndex++);
        Cell exampleCell = exampleRow.createCell(0);
        exampleCell.setCellValue("Java代码示例:");
        exampleCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("ExcelHelper.opsReplace()");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .from(inputStream)");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"companyName\", \"科技有限公司\")");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"reportDate\", \"2024-01-15\")");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .loop(\"employees\", employeeList)");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .var(\"totalCount\", employeeList.size())");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("    .replaceTo(outputStream);");
        
        // 空行
        sheet.createRow(rowIndex++);
        
        // 注意事项
        Row noteRow = sheet.createRow(rowIndex++);
        Cell noteCell = noteRow.createCell(0);
        noteCell.setCellValue("注意事项:");
        noteCell.setCellStyle(subTitleStyle);
        
        sheet.createRow(rowIndex++).createCell(0).setCellValue("1. 循环标记必须独占一行");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("2. 循环体中的变量使用item.属性名格式");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("3. 支持嵌套对象属性访问");
        sheet.createRow(rowIndex++).createCell(0).setCellValue("4. 循环会自动复制模板行并替换变量");
    }
    
    /**
     * 创建简单循环模板
     */
    private void createSimpleLoopTemplate() {
        // 第0行：标题
        Row titleRow = sheet.createRow(0);
        titleRow.createCell(0).setCellValue("${title}");
        
        // 第1行：循环开始
        Row startRow = sheet.createRow(1);
        startRow.createCell(0).setCellValue("${#foreach users}");
        
        // 第2行：循环体
        Row bodyRow = sheet.createRow(2);
        bodyRow.createCell(0).setCellValue("${item.name}");
        bodyRow.createCell(1).setCellValue("${item.age}");
        bodyRow.createCell(2).setCellValue("${item.position}");
        
        // 第3行：循环结束
        Row endRow = sheet.createRow(3);
        endRow.createCell(0).setCellValue("${/foreach}");
    }
    
    /**
     * 创建嵌套对象模板
     */
    private void createNestedObjectTemplate() {
        // 第0行：循环开始
        Row startRow = sheet.createRow(0);
        startRow.createCell(0).setCellValue("${#foreach orders}");
        
        // 第1行：循环体
        Row bodyRow = sheet.createRow(1);
        bodyRow.createCell(0).setCellValue("${item.orderNo}");
        bodyRow.createCell(1).setCellValue("${item.customer.name}");
        bodyRow.createCell(2).setCellValue("${item.customer.address}");
        
        // 第2行：循环结束
        Row endRow = sheet.createRow(2);
        endRow.createCell(0).setCellValue("${/foreach}");
    }
    
    /**
     * 创建多行循环模板
     */
    private void createMultiRowLoopTemplate() {
        // 第0行：循环开始
        Row startRow = sheet.createRow(0);
        startRow.createCell(0).setCellValue("${#foreach products}");
        
        // 第1行：循环体第一行
        Row bodyRow1 = sheet.createRow(1);
        bodyRow1.createCell(0).setCellValue("产品编号: ${item.code}");
        
        // 第2行：循环体第二行
        Row bodyRow2 = sheet.createRow(2);
        bodyRow2.createCell(0).setCellValue("产品名称: ${item.name} 价格: ${item.price}");
        
        // 第3行：循环结束
        Row endRow = sheet.createRow(3);
        endRow.createCell(0).setCellValue("${/foreach}");
    }
    
    /**
     * 创建完整模板
     */
    private void createCompleteTemplate() {
        // 第0行：公司名称
        Row companyRow = sheet.createRow(0);
        companyRow.createCell(0).setCellValue("${companyName}");
        
        // 第1行：报表日期
        Row dateRow = sheet.createRow(1);
        dateRow.createCell(0).setCellValue("报表日期111: ${reportDate}");
        
        // 第2行：循环开始
        Row startRow = sheet.createRow(2);
        startRow.createCell(0).setCellValue("${#foreach employees}");
        
        // 第3行：循环体
        Row bodyRow = sheet.createRow(3);
        bodyRow.createCell(0).setCellValue("${item.empNo}");
        bodyRow.createCell(1).setCellValue("${item.name}");
        bodyRow.createCell(2).setCellValue("${item.department}");
        bodyRow.createCell(3).setCellValue("${item.salary}");
        
        // 第4行：循环结束
        Row endRow = sheet.createRow(4);
        endRow.createCell(0).setCellValue("${/foreach}");
        
        // 第5行：总计
        Row totalRow = sheet.createRow(5);
        totalRow.createCell(0).setCellValue("总计: ${totalCount} 人");
    }
    
    /**
     * 创建用户数据
     */
    private Map<String, Object> createUser(String name, int age, String position) {
        Map<String, Object> user = new HashMap<>();
        user.put("name", name);
        user.put("age", String.valueOf(age));
        user.put("position", position);
        return user;
    }
    
    /**
     * 创建订单数据
     */
    private Map<String, Object> createOrder(String orderNo, String customerName, String address) {
        Map<String, Object> order = new HashMap<>();
        order.put("orderNo", orderNo);
        
        Map<String, Object> customer = new HashMap<>();
        customer.put("name", customerName);
        customer.put("address", address);
        order.put("customer", customer);
        
        return order;
    }
    
    /**
     * 创建产品数据
     */
    private Map<String, Object> createProduct(String code, String name, double price) {
        Map<String, Object> product = new HashMap<>();
        product.put("code", code);
        product.put("name", name);
        product.put("price", price);
        return product;
    }
    
    /**
     * 创建员工数据
     */
    private Map<String, Object> createEmployee(String empNo, String name, String department, double salary) {
        Map<String, Object> employee = new HashMap<>();
        employee.put("empNo", empNo);
        employee.put("name", name);
        employee.put("department", department);
        employee.put("salary", salary);
        return employee;
    }
    
    /**
     * 获取单元格值
     */
    private String getCellValue(Sheet sheet, int rowIndex, int cellIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) return null;
        
        Cell cell = row.getCell(cellIndex);
        if (cell == null) return null;
        
        return cell.getStringCellValue();
    }
    
    /**
     * 检查行是否为空
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) return true;
        
        short lastCellNum = row.getLastCellNum();
        for (short i = 0; i < lastCellNum; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = cell.toString().trim();
                if (!value.isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }
}