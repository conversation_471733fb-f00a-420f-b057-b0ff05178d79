//package com.lg.financecloud.common.data.tenant;
//
//import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import javax.sql.DataSource;
//import java.util.List;
//
//@ConfigurationProperties(prefix = "spring.datasource.glob-tables")
//@ConditionalOnBean(DataSource.class)
//public class SwithDbSqlConfig {
//    public   List<String> tables ;
//
//    public List<String> getTables() {
//        return tables;
//    }
//
//    public void setTables(List<String> tables) {
//        this.tables = tables;
//    }
//}
