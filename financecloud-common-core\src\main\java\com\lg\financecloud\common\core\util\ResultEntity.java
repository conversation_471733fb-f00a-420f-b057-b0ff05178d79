/*******************************************************************************
 ******************************************************************************/

package com.lg.financecloud.common.core.util;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: ResultEntity
 * @Description: 调用外部系统接口返回的结果集
 * @date: 2022/2/2 16:02
 * @Copyright: 2021 . All rights reserved.
 *
 */
@Data
public class ResultEntity {

    private Integer returnCode;

    private String returnMessage;

    private Integer total;

    private Map<String, Object> bean;

    private List<Map<String, Object>> rows;

}
