package com.lg.dao.core;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.JdbcUtils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * Map类型的结果行映射器
 * 专门用于处理返回Map类型结果的查询
 */
public class MapRowMapper implements RowMapper<Map<String, Object>> {
    
    @Override
    public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
        Map<String, Object> map = new HashMap<>();
        
        // 获取结果集元数据
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnCount = rsmd.getColumnCount();
        
        // 遍历所有列
        for (int i = 1; i <= columnCount; i++) {
            String columnName = JdbcUtils.lookupColumnName(rsmd, i);
            Object value = JdbcUtils.getResultSetValue(rs, i, Object.class);
            
            // 将下划线命名转换为驼峰命名
            String propertyName = columnToProperty(columnName.toLowerCase());
            map.put(propertyName, value);
        }
        
        return map;
    }
    
    /**
     * 将下划线命名的列名转换为驼峰命名的属性名
     */
    private String columnToProperty(String column) {
        StringBuilder result = new StringBuilder();
        boolean upperCase = false;
        
        for (int i = 0; i < column.length(); i++) {
            char c = column.charAt(i);
            if (c == '_') {
                upperCase = true;
            } else {
                result.append(upperCase ? Character.toUpperCase(c) : c);
                upperCase = false;
            }
        }
        
        return result.toString();
    }
} 