package com.lg.financecloud.common.ai.util;

import java.util.List;

/**
 * 向量计算工具类
 * 提供向量相关的通用计算功能
 */
public class VectorUtils {

    /**
     * 计算两个向量的余弦相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度值，范围[-1,1]
     */
    public static double cosineSimilarity(List<Double> vector1, List<Double> vector2) {
        if (vector1 == null || vector2 == null || vector1.size() != vector2.size()) {
            throw new IllegalArgumentException("向量不能为空且维度必须相同");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.size(); i++) {
            dotProduct += vector1.get(i) * vector2.get(i);
            norm1 += vector1.get(i) * vector1.get(i);
            norm2 += vector2.get(i) * vector2.get(i);
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / Math.sqrt(norm1 * norm2);
    }

    /**
     * 向量归一化
     *
     * @param vector 需要归一化的向量
     * @return 归一化后的向量
     */
    public static List<Double> normalize(List<Double> vector) {
        if (vector == null || vector.isEmpty()) {
            throw new IllegalArgumentException("向量不能为空");
        }

        double norm = 0.0;
        for (double value : vector) {
            norm += value * value;
        }
        norm = Math.sqrt(norm);

        if (norm == 0.0) {
            return vector;
        }

        for (int i = 0; i < vector.size(); i++) {
            vector.set(i, vector.get(i) / norm);
        }

        return vector;
    }

    /**
     * 计算两个向量的欧氏距离
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 欧氏距离值
     */
    public static double euclideanDistance(List<Double> vector1, List<Double> vector2) {
        if (vector1 == null || vector2 == null || vector1.size() != vector2.size()) {
            throw new IllegalArgumentException("向量不能为空且维度必须相同");
        }

        double sum = 0.0;
        for (int i = 0; i < vector1.size(); i++) {
            double diff = vector1.get(i) - vector2.get(i);
            sum += diff * diff;
        }

        return Math.sqrt(sum);
    }
}