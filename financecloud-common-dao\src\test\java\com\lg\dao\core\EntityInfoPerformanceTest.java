package com.lg.dao.core;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.cache.UnifiedCacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * EntityInfo 性能专项测试
 * 专门测试缓存性能优化效果
 */
public class EntityInfoPerformanceTest {

    private CacheProperties cacheProperties;
    private UnifiedCacheManager cacheManager;

    @BeforeEach
    public void setUp() throws Exception {
        cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);
        
        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();
        
        // 初始化 EntityInfoManager 的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
    }

    /**
     * 测试实体类
     */
    @Table(name = "perf_test_user")
    public static class PerfTestUser {
        @Id
        @Column(name = "user_id")
        private Long id;
        
        @Column(name = "user_name")
        private String name;
        
        @Column(name = "user_email")
        private String email;
        
        private Integer age;
        private String address;
        private String phone;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }

    /**
     * 详细的性能对比测试
     */
    @Test
    public void testDetailedPerformanceComparison() {
        System.out.println("=== EntityInfo 详细性能对比测试 ===");
        
        int warmupIterations = 1000;
        int testIterations = 50000; // 大量测试以显示缓存效果
        
        // 1. JVM 预热
        System.out.println("1. JVM 预热中...");
        for (int i = 0; i < warmupIterations; i++) {
            EntityInfo.of(PerfTestUser.class);
        }
        
        // 2. 测试直接创建性能
        System.out.println("2. 测试直接创建性能...");
        long directStartTime = System.nanoTime();
        for (int i = 0; i < testIterations; i++) {
            EntityInfo.of(PerfTestUser.class);
        }
        long directEndTime = System.nanoTime();
        long directTime = directEndTime - directStartTime;
        
        // 3. 预热缓存
        System.out.println("3. 预热缓存...");
        EntityInfoManager manager = EntityInfoManager.getInstance();
        for (int i = 0; i < warmupIterations; i++) {
            manager.getEntityInfo(PerfTestUser.class);
        }
        
        // 4. 测试缓存性能
        System.out.println("4. 测试缓存性能...");
        long cacheStartTime = System.nanoTime();
        for (int i = 0; i < testIterations; i++) {
            manager.getEntityInfo(PerfTestUser.class);
        }
        long cacheEndTime = System.nanoTime();
        long cacheTime = cacheEndTime - cacheStartTime;
        
        // 5. 输出结果
        System.out.println("\n=== 性能测试结果 ===");
        System.out.println("测试次数: " + testIterations);
        System.out.println("直接创建总时间: " + directTime / 1_000_000 + " ms");
        System.out.println("缓存调用总时间: " + cacheTime / 1_000_000 + " ms");
        
        double avgDirectTime = (double) directTime / testIterations;
        double avgCacheTime = (double) cacheTime / testIterations;
        
        System.out.println("直接创建平均时间: " + String.format("%.2f", avgDirectTime) + " ns");
        System.out.println("缓存调用平均时间: " + String.format("%.2f", avgCacheTime) + " ns");
        
        if (directTime > cacheTime) {
            double improvement = (double) directTime / cacheTime;
            System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
            System.out.println("✅ 缓存性能优于直接创建");
        } else {
            double overhead = (double) cacheTime / directTime;
            System.out.println("性能开销: " + String.format("%.2f", overhead) + "x");
            System.out.println("⚠️ 缓存性能不如直接创建");
        }
        
        // 6. 分析结果
        System.out.println("\n=== 性能分析 ===");
        if (cacheTime < directTime) {
            System.out.println("缓存策略有效，建议在生产环境中使用");
        } else {
            System.out.println("缓存开销较大，可能的原因：");
            System.out.println("- 缓存管理器开销");
            System.out.println("- 同步机制开销");
            System.out.println("- 缓存键生成开销");
            System.out.println("建议：对于简单对象，可以考虑直接创建");
        }
    }

    /**
     * 测试不同场景下的性能
     */
    @Test
    public void testDifferentScenarios() {
        System.out.println("\n=== 不同场景性能测试 ===");
        
        EntityInfoManager manager = EntityInfoManager.getInstance();
        
        // 场景1：单次调用
        testSingleCall(manager);
        
        // 场景2：少量重复调用
        testFewCalls(manager);
        
        // 场景3：大量重复调用
        testManyCalls(manager);
        
        // 场景4：多线程并发调用
        testConcurrentCalls(manager);
    }
    
    private void testSingleCall(EntityInfoManager manager) {
        System.out.println("\n--- 场景1：单次调用 ---");
        
        // 直接创建
        long start1 = System.nanoTime();
        EntityInfo.of(PerfTestUser.class);
        long end1 = System.nanoTime();
        
        // 缓存调用
        long start2 = System.nanoTime();
        manager.getEntityInfo(PerfTestUser.class);
        long end2 = System.nanoTime();
        
        System.out.println("直接创建: " + (end1 - start1) + " ns");
        System.out.println("缓存调用: " + (end2 - start2) + " ns");
    }
    
    private void testFewCalls(EntityInfoManager manager) {
        System.out.println("\n--- 场景2：少量重复调用 (100次) ---");
        
        int iterations = 100;
        
        // 直接创建
        long start1 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            EntityInfo.of(PerfTestUser.class);
        }
        long end1 = System.nanoTime();
        
        // 缓存调用
        long start2 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            manager.getEntityInfo(PerfTestUser.class);
        }
        long end2 = System.nanoTime();
        
        System.out.println("直接创建: " + (end1 - start1) / 1_000_000 + " ms");
        System.out.println("缓存调用: " + (end2 - start2) / 1_000_000 + " ms");
    }
    
    private void testManyCalls(EntityInfoManager manager) {
        System.out.println("\n--- 场景3：大量重复调用 (10000次) ---");
        
        int iterations = 10000;
        
        // 直接创建
        long start1 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            EntityInfo.of(PerfTestUser.class);
        }
        long end1 = System.nanoTime();
        
        // 缓存调用
        long start2 = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            manager.getEntityInfo(PerfTestUser.class);
        }
        long end2 = System.nanoTime();
        
        System.out.println("直接创建: " + (end1 - start1) / 1_000_000 + " ms");
        System.out.println("缓存调用: " + (end2 - start2) / 1_000_000 + " ms");
        
        if ((end2 - start2) < (end1 - start1)) {
            double improvement = (double) (end1 - start1) / (end2 - start2);
            System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
        }
    }
    
    private void testConcurrentCalls(EntityInfoManager manager) {
        System.out.println("\n--- 场景4：多线程并发调用 ---");
        
        int threadCount = 10;
        int iterationsPerThread = 1000;
        
        Thread[] threads = new Thread[threadCount];
        long[] results = new long[threadCount];
        
        // 预热
        manager.getEntityInfo(PerfTestUser.class);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                long start = System.nanoTime();
                for (int j = 0; j < iterationsPerThread; j++) {
                    manager.getEntityInfo(PerfTestUser.class);
                }
                long end = System.nanoTime();
                results[threadIndex] = end - start;
            });
        }
        
        long overallStart = System.currentTimeMillis();
        for (Thread thread : threads) {
            thread.start();
        }
        
        try {
            for (Thread thread : threads) {
                thread.join();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        long overallEnd = System.currentTimeMillis();
        
        long totalTime = 0;
        for (long result : results) {
            totalTime += result;
        }
        
        System.out.println("总体耗时: " + (overallEnd - overallStart) + " ms");
        System.out.println("平均每线程: " + (totalTime / threadCount) / 1_000_000 + " ms");
        System.out.println("总调用次数: " + (threadCount * iterationsPerThread));
    }
}
