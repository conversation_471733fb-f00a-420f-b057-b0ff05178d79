# 模板SQL缓存优化

## 📋 优化概述

本次优化针对 `SqlTemplateManager` 和 `MybatisTemplateEngine` 的模板SQL解析功能，使用统一缓存管理器 `UnifiedCacheManager` 来缓存解析结果，避免重复解析，提升性能。

## 🎯 优化目标

1. **提升性能**：避免重复解析相同的模板SQL
2. **避免缓存陷阱**：检测集合参数，避免无效缓存和内存溢出
3. **统一管理**：使用 `UnifiedCacheManager` 统一管理缓存
4. **保持一致性**：与 `MybatisNativeXmlParser` 优化方案保持一致

## 🔧 核心改进

### 1. MybatisTemplateEngine 缓存优化

#### 多层缓存策略
```java
public class MybatisTemplateEngine {
    // Configuration 缓存
    private final Map<String, Configuration> configurationCache = new ConcurrentHashMap<>();
    // MappedStatement 缓存
    private final Map<String, MappedStatement> mappedStatementCache = new ConcurrentHashMap<>();
    
    // 渲染结果缓存（通过 UnifiedCacheManager）
    public RenderResult renderWithParams(String template, Map<String, Object> params) {
        if (unifiedCacheManager != null && !containsCollectionParam(params)) {
            String cacheKey = buildCacheKey(template, params);
            return unifiedCacheManager.get(
                UnifiedCacheManager.CacheType.SQL_TEMPLATE_CACHE,
                cacheKey,
                () -> parseSqlWithParams(template, params)
            );
        }
        return parseSqlWithParams(template, params);
    }
}
```

#### 避免重复XML解析
```java
private MappedStatement getOrCreateMappedStatement(String xmlSql) {
    String templateKey = String.valueOf(xmlSql.hashCode());
    
    // 先从缓存中查找
    MappedStatement cachedStatement = mappedStatementCache.get(templateKey);
    if (cachedStatement != null) {
        return cachedStatement;
    }
    
    // 缓存未命中，创建新的 MappedStatement
    Configuration configuration = getOrCreateConfiguration(templateKey);
    // ... XML解析逻辑
    mappedStatementCache.put(templateKey, mappedStatement);
    return mappedStatement;
}
```

### 2. SqlTemplateManager 缓存改进

#### 智能缓存键生成
```java
private String buildCacheKey(String templateName, Map<String, Object> params) {
    StringBuilder keyBuilder = new StringBuilder(templateName).append(":");
    
    if (params.size() == 1) {
        Object singleValue = params.values().iterator().next();
        if (singleValue instanceof String || 
            singleValue instanceof Number || 
            singleValue instanceof Boolean) {
            keyBuilder.append("primitive:").append(singleValue);
        } else {
            keyBuilder.append("object:")
                     .append(singleValue.getClass().getSimpleName())
                     .append(":")
                     .append(singleValue.hashCode());
        }
    } else {
        // 多参数使用键集合的哈希和参数数量
        keyBuilder.append("map:")
                 .append(params.size())
                 .append(":")
                 .append(params.keySet().hashCode());
    }
    
    return keyBuilder.toString();
}
```

#### 集合参数检测
```java
private boolean containsCollectionParam(Map<String, Object> params) {
    if (params == null || params.isEmpty()) {
        return false;
    }
    
    for (Object value : params.values()) {
        if (value instanceof Collection || 
            value instanceof Object[] || 
            value instanceof Map) {
            return true;
        }
    }
    return false;
}
```

## 🚀 性能提升

### 缓存场景
- ✅ **简单参数模板**：如 `findById` 模板
- ✅ **固定 Map 参数**：如条件查询模板
- ✅ **基本类型参数**：如状态查询模板

### 不缓存场景
- ❌ **集合参数**：如 `findByIds` 模板
- ❌ **数组参数**：如批量操作模板
- ❌ **Map 中包含集合**：如复杂查询条件

## 📊 优化效果

### 三层缓存架构
1. **Configuration 缓存**：避免重复创建 MyBatis Configuration
2. **MappedStatement 缓存**：避免重复解析 XML 模板
3. **渲染结果缓存**：避免重复渲染相同参数的模板

### 性能提升预期
- **简单模板**：5-10倍性能提升
- **复杂动态模板**：10-20倍性能提升
- **高并发场景**：更显著的性能提升

## 🔧 配置说明

### 缓存类型配置
```yaml
light:
  orm:
    cache:
      enable: true
      types:
        sql_template:
          max-size: 1000
          expire-seconds: 3600
```

### 使用方式
```java
// 自动注入带缓存的模板管理器
@Autowired
private SqlTemplateManager sqlTemplateManager;

// 使用模板渲染SQL
Map<String, Object> params = new HashMap<>();
params.put("status", "ACTIVE");
MybatisTemplateEngine.RenderResult result = 
    sqlTemplateManager.getSqlWithParams("findByStatus", params);
```

## 🔍 监控和调试

### 启用调试日志
```yaml
logging:
  level:
    com.lg.dao.core.SqlTemplateManager: DEBUG
    com.lg.dao.core.template.MybatisTemplateEngine: DEBUG
```

### 日志输出示例
```
DEBUG - 使用缓存渲染SQL模板: findByStatus:primitive:ACTIVE
DEBUG - 缓存未命中，执行SQL模板渲染: findByStatus
DEBUG - 使用缓存的 MappedStatement: 12345678
DEBUG - 跳过缓存，直接渲染SQL模板: findByIds (原因: 参数包含集合类型)
```

## 📝 测试验证

### 缓存功能测试
```java
@Test
public void testTemplateCacheFunction() {
    Map<String, Object> params = new HashMap<>();
    params.put("status", "ACTIVE");
    
    // 第一次调用
    RenderResult result1 = sqlTemplateManager.getSqlWithParams("findByStatus", params);
    // 第二次调用（应该从缓存获取）
    RenderResult result2 = sqlTemplateManager.getSqlWithParams("findByStatus", params);
    
    assertEquals(result1.getSql(), result2.getSql());
}
```

### 性能对比测试
```java
@Test
public void testTemplatePerformanceComparison() {
    // 500次渲染测试
    // 预期结果：有缓存比无缓存快5-10倍
}
```

## 🎯 优化亮点

1. **三层缓存架构**：Configuration → MappedStatement → RenderResult
2. **智能缓存策略**：根据参数类型决定是否缓存
3. **内存安全**：避免集合参数导致的缓存膨胀
4. **统一管理**：与其他缓存组件保持一致
5. **向后兼容**：不影响现有API使用

## 🔄 后续优化建议

1. **缓存预热**：应用启动时预热常用模板
2. **缓存统计**：添加缓存命中率监控
3. **动态配置**：支持运行时调整缓存策略
4. **模板版本管理**：支持模板热更新时的缓存失效

## 📋 注意事项

1. **内存使用**：合理配置缓存大小，避免内存溢出
2. **缓存一致性**：模板文件变更时需要重启应用
3. **参数变化**：对象参数的内部状态变化不会影响缓存键
4. **调试模式**：开发环境建议启用 DEBUG 日志
