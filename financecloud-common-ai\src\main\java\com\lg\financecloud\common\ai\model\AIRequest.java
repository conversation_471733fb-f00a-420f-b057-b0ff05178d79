package com.lg.financecloud.common.ai.model;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强版AI请求模型
 * 支持多模态消息构建和流式输出配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AIRequest {
    private static final String DEFAULT_TEXT_MODEL = "qwen-turbo";
    private static final String DEFAULT_MULTIMODAL_MODEL = "qwen-vl-plus";
    private static final String ROLE_USER = "user";
    private static final String ROLE_ASSISTANT = "assistant";
    private static final String ROLE_SYSTEM = "system";

    private String requestId;
    private String requestUrl;
    private String apiKey;
    private String modelName;

    private Integer timeout;
    private Map<String, Object> businessData;

    private List<ChatMessage> messages = new ArrayList<>();

    private Integer maxTokens;

    private Boolean streamOutput = false;

    private String streamCallbackUrl;

    public static MultimodalBuilder multimodalBuilder(String apiKey) {
        return new MultimodalBuilder(apiKey);
    }

    public static TextBuilder textBuilder(String apiKey) {
        return new TextBuilder(apiKey);
    }

    public static class TextBuilder {
        private final String apiKey;
        private String modelName = DEFAULT_TEXT_MODEL;
        private final List<ChatMessage> messages = new ArrayList<>();

        public TextBuilder(String apiKey) {
            this.apiKey = apiKey;
        }

        public TextBuilder model(String modelName) {
            this.modelName = modelName;
            return this;
        }

        public TextBuilder addMessage(String role, String content) {
            this.messages.add(new ChatMessage(role, content));
            return this;
        }

        public TextBuilder addMessage(ChatMessage message) {
            this.messages.add(message);
            return this;
        }

        public TextBuilder addUserMessage(String content) {
            return addMessage(ROLE_USER, content);
        }

        public TextBuilder addAssistantMessage(String content) {
            return addMessage(ROLE_ASSISTANT, content);
        }

        public TextBuilder addSystemMessage(String content) {
            return addMessage(ROLE_SYSTEM, content);
        }

        public AIRequest build() {
            AIRequest request = new AIRequest();
            request.setApiKey(apiKey);
            request.setModelName(modelName);
            request.setMessages(messages);
            return request;
        }
    }

    public static class MultimodalBuilder {
        private final String apiKey;
        private String modelName = DEFAULT_MULTIMODAL_MODEL;
        private final List<ChatMessage> messages = new ArrayList<>();

        public MultimodalBuilder(String apiKey) {
            this.apiKey = apiKey;
        }

        public MultimodalBuilder model(String modelName) {
            this.modelName = modelName;
            return this;
        }

        public MultimodalBuilder addImage(String role, File imageFile) {
            try {
                byte[] fileContent = Files.readAllBytes(imageFile.toPath());
                String base64 = Base64.encode(fileContent);
                base64 = "data:image/jpeg;base64,"+base64;
                JSONArray jsonArray = createImageJsonArray(base64);
                messages.add(ChatMessage.of(role, jsonArray));
            } catch (IOException e) {
                System.err.println("Failed to read image file: " + e.getMessage());
            }
            return this;
        }

        public MultimodalBuilder addUserImage(File imageFile) {
            return addImage(ROLE_USER, imageFile);
        }

        public MultimodalBuilder addMessage(String role, String content) {
            JSONArray jsonArray = createTextJsonArray(content);
            messages.add(ChatMessage.of(role, jsonArray));
            return this;
        }

        public MultimodalBuilder addUserMessage(String content) {
            return addMessage(ROLE_USER, content);
        }

        public MultimodalBuilder addImage(String role, String imageUrlOrBase64Image) {
            JSONArray jsonArray = createImageJsonArray(imageUrlOrBase64Image);
            messages.add(ChatMessage.of(role, jsonArray));
            return this;
        }

        public MultimodalBuilder addUserImage(String imageUrlOrBase64Image) {
            return addImage(ROLE_USER, imageUrlOrBase64Image);
        }

        public AIRequest build() {
            AIRequest request = new AIRequest();
            request.setApiKey(apiKey);
            request.setModelName(modelName);
            request.setMessages(messages);
            return request;
        }

        private JSONArray createImageJsonArray(String imageUrlOrBase64Image) {
            // 判断是base64 图片
            JSONArray jsonArray = JSONUtil.createArray();
            jsonArray.add(
                        JSONUtil.createObj().set("type", "image_url").set("image_url", JSONUtil.createObj().set("url", imageUrlOrBase64Image))
            );
            return jsonArray;
        }

        private JSONArray createTextJsonArray(String content) {
            JSONArray jsonArray = JSONUtil.createArray();
            jsonArray.add(JSONUtil.createObj().set("type", "text").set("text", content));
            return jsonArray;
        }
    }


    // 4. 在AIRequest中扩展构建器
    public static class BatchBuilder {
        private final BatchRequest request = new BatchRequest();
        private final String apiKey;

        public BatchBuilder(String apiKey) {
            this.apiKey = apiKey;
        }

        public BatchBuilder inputFileId(String fileId) {
            request.setInputFileId(fileId);
            return this;
        }

        public BatchBuilder endpoint(String endpoint) {
            request.setEndpoint(endpoint);
            return this;
        }

        public BatchBuilder completionWindow(String window) {
            request.setCompletionWindow(window);
            return this;
        }

        public BatchBuilder metadata(String name, String description) {
            Map<String, String> metadata = new HashMap<>();
            metadata.put("ds_name", name);
            metadata.put("ds_description", description);
            request.setMetadata(metadata);
            return this;
        }

        public   BatchRequest build() {
            return request;
        }
    }
}
