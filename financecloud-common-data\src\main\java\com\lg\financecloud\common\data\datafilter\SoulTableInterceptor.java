package com.lg.financecloud.common.data.datafilter;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.NamingCase;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.baomidou.mybatisplus.annotation.TableField;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.lg.dao.core.BaseDao;
import com.lg.dao.helper.Dao<PERSON>elper;
import com.lg.financecloud.admin.api.dto.SessionUser;
import com.lg.financecloud.common.core.util.WebUtils;
import com.lg.financecloud.common.data.MybatisConstant;
import com.lg.financecloud.common.data.RedisUtils;
import com.lg.financecloud.common.data.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lg.financecloud.common.data.MybatisConstant.*;

@Intercepts(
        {
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        }
)
@Slf4j
@ConditionalOnBean(DataSource.class)
public class SoulTableInterceptor implements Interceptor ,  ApplicationListener<ApplicationStartedEvent> {

    static Cache<String, JSONObject> DP_RULE_CACHE = Caffeine.newBuilder().maximumSize(500).expireAfterAccess(600, TimeUnit.SECONDS).build();
    static Cache<String, JSONObject> TABLEMETADATA_CACHE = Caffeine.newBuilder().build();




    public static ThreadLocal<Boolean> getSoulTableInterceptorExclude() {
        return THREAD_LOCAL_EXCLUDE;
    }

    public static void setThreadLocalExclude(ThreadLocal<Boolean> threadLocalExclude) {
        THREAD_LOCAL_EXCLUDE = threadLocalExclude;
    }

    private  static    ThreadLocal<Boolean> THREAD_LOCAL_EXCLUDE = new TransmittableThreadLocal<>();




    public static void setSoulTableInterceptorExclude(){
        THREAD_LOCAL_EXCLUDE.set(true);
    }
    public static void cleanSoulTableInterceptorExclude(){
        THREAD_LOCAL_EXCLUDE.remove();
    }

    private String dbType;



    private static void cacheDpRule(JSONObject record) {
        String cacheKey  = buildDpRuleCacheKey(record.getStr("tenantId"),record.getStr("ownId"),record.getStr("tableCode"));
        DP_RULE_CACHE.put(cacheKey,record);
        RedisUtils.setCacheMapValue(DP_RULE_CACHE_PREFIX,cacheKey,record);
    }

    private void cacheTableMetadata(JSONObject record) {
        JSONObject fieldInfo = record.getJSONObject("fieldInfo");
        String cacheKey = buildTableMetadataCacheKey(record.getStr("tenantId"), record.getStr("tableCode"));
        TABLEMETADATA_CACHE.put(cacheKey,fieldInfo);
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {

        RedisUtils.subscribe(TABLE_METADATA_LOAD_TOPIC, String.class, x-> {
            JSONObject jsonObject = JSONUtil.parseObj(x);
            log.error("收到表元数据更新消息："+jsonObject);

            cacheTableMetadata(jsonObject);

        });

        RedisUtils.subscribe(DP_RULE_CACHE_LOAD_TOPIC, String.class,x-> {
            JSONObject jsonObject = JSONUtil.parseObj(x);
            log.error("收到数据权限规则更新消息："+jsonObject);
            cacheDpRule(jsonObject);
        });
        BaseDao dao = DaoHelper.getBaseDao();
        List<cn.hutool.json.JSONObject> dpRuleQueryList  = dao.selectJsonList("select * from sys_table_metadata");
        Map<String, cn.hutool.json.JSONObject> dpRuleMap = dpRuleQueryList.stream().collect(Collectors.toMap(rcd -> rcd.getStr("tenantId")+rcd.getStr("ownId")+rcd.getStr("tableCode"), rcd -> rcd));
        dpRuleQueryList.stream().forEach(rcd -> {
            cacheDpRule(rcd);
        });
        RedisUtils.setCacheMap(DP_RULE_CACHE_PREFIX,dpRuleMap);
        dpRuleQueryList.stream().forEach(rcd -> {
            cacheTableMetadata(rcd);
        });


    }

    private enum DB_DIALECT {ORACLE, MYSQL};

    public String getDbType() {
        return dbType;
    }
    //这里的dbType设置请参考在351行
    public void setDbType(String dbType) {
        this.dbType = dbType;
    }


    private static final String CURRENT_USER_KEY = "sessionUser";
    private static final String SO_FILTER_PARAM = "dynamicFilter";
    private static final int NEW_ADDING_SIZE = 2;




    private Map processParameterObject( MappedStatement ms, Object parameterObject) {

        Map paramMap = null;
        if (parameterObject == null) {
            paramMap = new HashMap(NEW_ADDING_SIZE);
        } else if (parameterObject instanceof Map) {
            paramMap = new HashMap(((Map) parameterObject).size() + NEW_ADDING_SIZE);
            paramMap.putAll((Map) parameterObject);
        } else {
            paramMap = new HashMap(5);
            boolean hasTypeHandler = ms.getConfiguration().getTypeHandlerRegistry().hasTypeHandler(parameterObject.getClass());
            MetaObject metaObject = MetaObjectUtil.forObject(parameterObject);
            if (!hasTypeHandler) {
                String[] getterNames = metaObject.getGetterNames();
                int getterNamesLength = getterNames.length;

                for (int i = 0; i < getterNamesLength; ++i) {
                    String name = getterNames[i];
                    paramMap.put(name, metaObject.getValue(name));
                }
            } else {
                //注意、只有一个参数情况下只能使用'_parameter'注入了。
                paramMap.put("_parameter", parameterObject);
            }

            return this.processParameter(paramMap);
        }

        return this.processParameter(paramMap);
    }

    private Map<String, Object> processParameter(Map<String, Object> paramMap) {

        SessionUser sessionUser = TenantContextHolder.getCurrentSessionUser();

        if (sessionUser!=null) {
            paramMap.put("sessionUser", sessionUser);
        }
        paramMap.put(SO_FILTER_PARAM   , null);
        return paramMap;
    }



    private void putParamsIfNecessary(BoundSql boundSql,String condition) {
        boundSql.setAdditionalParameter(SO_FILTER_PARAM, condition);
    }

    private DynamicDataFilter getDynamicDataFilterAnnotation(MappedStatement mappedStatement) {
        DynamicDataFilter annotation = null;
        try {
            String id = mappedStatement.getId();
            String className = id.substring(0, id.lastIndexOf("."));
            String methodName = id.substring(id.lastIndexOf(".") + 1);
            final Method[] method = Class.forName(className).getMethods();
            for (Method me : method) {
                if (me.getName().equals(methodName) && me.isAnnotationPresent(DynamicDataFilter.class)) {
                    return me.getAnnotation(DynamicDataFilter.class);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return annotation;
    }
    private ExcludeDataFilter getExcludeDataFilterAnnotation(MappedStatement mappedStatement) {
        ExcludeDataFilter annotation = null;
        try {
            String id = mappedStatement.getId();
            String className = id.substring(0, id.lastIndexOf("."));
            String methodName = id.substring(id.lastIndexOf(".") + 1);
            final Method[] method = Class.forName(className).getMethods();
            for (Method me : method) {
                if (me.getName().equals(methodName) && me.isAnnotationPresent(ExcludeDataFilter.class)) {
                    return me.getAnnotation(ExcludeDataFilter.class);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return annotation;
    }


    @Override
    public Object intercept(Invocation invocation) throws Throwable {




        // 排除拦截， 优先级最高
        if(THREAD_LOCAL_EXCLUDE.get()!=null){
            return invocation.proceed();
        }

        return doIntercept(invocation);

    }

    private Object doIntercept(Invocation invocation) throws InvocationTargetException, IllegalAccessException {

        HttpServletRequest request = WebUtils.getRequest();
        if(request==null){
            return invocation.proceed();
        }

        Map<String, String>  parameterMap  = ServletUtil.getParamMap(request);

        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];


        ExcludeDataFilter excludeDataFilterAnnotation = getExcludeDataFilterAnnotation(ms);
        // 排除筛选
        if(excludeDataFilterAnnotation!=null){
            return invocation.proceed();
        }

        DynamicDataFilter dynamicDataFilter = getDynamicDataFilterAnnotation(ms);


        Object parameter = args[1];
        // 注入参数
        parameter = processParameterObject(ms, parameter);
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler statementHandler = (ResultHandler) args[3];
        Executor executor = (Executor) invocation.getTarget();
        CacheKey cacheKey;
        BoundSql boundSql;
        //由于逻辑关系，只会进入一次
        if (args.length == 4) {
            //4 个参数时
            boundSql = ms.getBoundSql(parameter);
            cacheKey = executor.createCacheKey(ms, parameter, rowBounds, boundSql);
        } else {
            //6 个参数时
            cacheKey = (CacheKey) args[4];
            boundSql = (BoundSql) args[5];
        }
        // 原始的SQL语句
        String sql = boundSql.getSql();


        boolean hasSos = parameterMap.containsKey("filterSos");
        boolean hasField = parameterMap.containsKey("field");
        boolean hasOrder = parameterMap.containsKey("order");
        boolean hasTableCode = parameterMap.containsKey("tableCode");
        Boolean soTable = false;
        if(hasSos ==true|| ( hasField==true &&hasOrder==true) || hasTableCode){
            soTable=true;
        }

        // 带注解 但是没有入参 也直接放过
        if(dynamicDataFilter!=null && soTable==false){
            ((Map) parameter).put(SO_FILTER_PARAM, null);
            boundSql = ms.getBoundSql(parameter);
            MappedStatement newMappedStatement = MybatisPluginHelper.newMappedStatement(  ms,  MybatisPluginHelper.newBoundSql(ms, boundSql, boundSql.getSql()));
            args[0] = newMappedStatement;
            return invocation.proceed();
        }

        if(!soTable){
            return invocation.proceed();
        }


        // 没有 soulPage 不需要拦截
        if (soTable) {

            SoulPage soulPage = new SoulPage();
            // 获取数据权限规则
            soulPage.setTableCode(parameterMap.get("tableCode"));
            String filterSos1 = URLUtil.decode( parameterMap.get("filterSos"));
            soulPage.setFilterSos(filterSos1);
            soulPage.setTableFilterType(parameterMap.get("tableFilterType"));
            if(StrUtil.isEmpty(soulPage.getOrder()))
                soulPage.setOrder(parameterMap.get("order"));
            if(StrUtil.isEmpty(soulPage.getOrder())){
                soulPage.setOrder("asc");
            }
            if(StrUtil.isEmpty(soulPage.getField()))
                soulPage.setField(parameterMap.get("field"));
            // 兼容 页面传入，防止旧版本漏改
            String filedMappingStr =   URLUtil.decode(parameterMap.get("fieldMapping"));
            String toUnderLineCase =null;
            JSONObject tableMetadata = TABLEMETADATA_CACHE.getIfPresent(buildTableMetadataCacheKey(TenantContextHolder.getTenantId()+"",soulPage.getTableCode()));
            if(tableMetadata!=null) {

                JSONArray tableMetadataFields = tableMetadata.getJSONArray("fields");
                for(int i=0;i<tableMetadataFields.size();i++){
                    JSONObject jsonObject = tableMetadataFields.getJSONObject(i);
                    String fieldMapping = jsonObject.getStr("fieldMapping");
                    if(StrUtil.isNotEmpty(fieldMapping)){
                        soulPage.getFieldMapping().put(jsonObject.getStr("field"), fieldMapping);
                    }
                }
                toUnderLineCase = tableMetadata.getStr("toUnderLineCase");
            }
            if (StrUtil.isEmpty(toUnderLineCase)) {
                toUnderLineCase = parameterMap.get("toUnderLineCase");
            }




            Boolean toUnderlineCaseFlag =Boolean.valueOf( StrUtil.emptyToDefault(toUnderLineCase,"true"));


            if(StrUtil.isNotEmpty(filedMappingStr)){
                soulPage.getFieldMapping().putAll(JSONUtil.parseObj(filedMappingStr));
            }


            List<FilterSo> filterSos = soulPage.getFilterSos();
            Integer tenantId = TenantContextHolder.getTenantId();
            // 合并dpRule
            mergeDpRule(soulPage, parameterMap, tenantId, filterSos);


            if (soulPage.isColumn()) {
                // 排序
                return invocation.proceed();
            } else {
                Map<String, String> fieldMap = null;


                Boolean isDynamicDataFilter = false;
                if(dynamicDataFilter!=null){
                    isDynamicDataFilter = true;
                    FilterField[] filterFields = dynamicDataFilter.value();
                    if(filterFields!=null && filterFields.length>0){
                        fieldMap = new HashMap<>();
                        for(FilterField filterField:filterFields){
                            fieldMap.put(filterField.key(), filterField.value());
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(soulPage.getFieldMapping())){
                    fieldMap = soulPage.getFieldMapping();
                }
                if(isDynamicDataFilter){
                    StringBuffer filterSql = new StringBuffer("");
                    StringBuffer dynamicFilterSql = buildQuery(sql, filterSql, soulPage, fieldMap, filterSos);
                    Map paramMap = (Map) parameter;
                    paramMap.put(SO_FILTER_PARAM, dynamicFilterSql.toString());
                    if(log.isInfoEnabled()){
                        log.info("filter data sql: {}",filterSql);
                    }
                    boundSql = ms.getBoundSql(parameter);
                    MappedStatement newMappedStatement = MybatisPluginHelper.newMappedStatement(  ms,  MybatisPluginHelper.newBoundSql(ms, boundSql, boundSql.getSql()));
                    args[0] = newMappedStatement;
                    return invocation.proceed();
                }


                if(fieldMap==null){
                    fieldMap = MapUtil.newHashMap();
                }
                // 返回类型是Map 标志
                Boolean mapResultClass = false;
                if (ms.getResultMaps().get(0).getResultMappings().size()>0) {
                    for (ResultMapping resultMapping : ms.getResultMaps().get(0).getResultMappings()) {
                        fieldMap.put(resultMapping.getProperty(),resultMapping.getColumn());
                    }
                } else if (ms.getResultMaps().get(0).getType() != null) {

                    Class<?> type = ms.getResultMaps().get(0).getType();

                    if(StrUtil.equalsIgnoreCase(type.getSimpleName(),"Map")){
                        mapResultClass = true;
                    }else{
                        // 兼容 tableField 的写法
                        Field[] fields = ReflectUtil.getFields(ms.getResultMaps().get(0).getType());
                        //TODO  判断基类是@ENTITY 需特需处理
                        Class<?> superclass = ms.getResultMaps().get(0).getType().getSuperclass();
                        //如果当前实体超类是Entity，则不转下划线
                        if (superclass!=null && superclass.getName().equals("com.github.foxnic.dao.entity.Entity")){
                            toUnderlineCaseFlag = false;
                        }
                        if(ArrayUtil.isNotEmpty(fields))
                            for (Field field : fields) {
                                TableField tableField = field.getAnnotation(TableField.class);
                                if(toUnderlineCaseFlag){
                                    if (tableField == null) {
                                        String underlineCase = NamingCase.toUnderlineCase(field.getName());
                                        fieldMap.put(field.getName(), underlineCase);
                                    } else {
                                        String underlineCase = NamingCase.toUnderlineCase(field.getName());
                                        if(tableField.exist()){
                                            fieldMap.put(field.getName(),underlineCase);
                                        }else{
                                            fieldMap.put(field.getName(),field.getName());
                                        }

                                    }
                                }else{
                                    if (tableField == null) {
                                        fieldMap.put(field.getName(), field.getName());
                                    } else {
                                        fieldMap.put(field.getName(),field.getName());
                                    }
                                }

                            }
                    }

                }

                // 特殊字段 映射， specialFiledMapping 覆盖已有字段
                String  specialFiledMapping = parameterMap.get("specialFiledMapping");

                if(StrUtil.isNotEmpty(specialFiledMapping)){
                    fieldMap.putAll(JSONUtil.toBean(specialFiledMapping,Map.class));
                }
                Boolean existsFilterField = false;
                if(CollectionUtil.isNotEmpty(filterSos)) {
                    for (FilterSo filterSo : filterSos) {
                        existsFilterField = getIgnoreCaseMapValue(fieldMap,  filterSo.getField())!=null;
                        if (existsFilterField) {
                            break;
                        }
                    }
                }

                if(  getIgnoreCaseMapValue(fieldMap,  soulPage.getField())!=null ){
                    existsFilterField = true;
                }

                // 有排序 或者 存在筛选字段 则继续
                if(existsFilterField|| hasOrder || soTable){

                }else{
                    return invocation.proceed();
                }

                StringBuffer withinSql = new StringBuffer();
                List<FilterSo> withinSos =filterSos==null? new ArrayList<>() : filterSos.stream().filter(e -> "within".equals(e.getQueryMethod())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(withinSos)){
                    doDuildQuery(sql, withinSql, soulPage, fieldMap, withinSos);
                    String withinsql=withinSql.toString();
                    int index1=withinsql.indexOf(" ");
                    int index2=withinsql.substring(index1+1).indexOf(" ");
                    sql = addWhereClause(sql, withinsql.substring(index2+1));
                }
                /**
                 * 分页
                 */
                filterSos= filterSos==null? new ArrayList<>() :filterSos.stream().filter(e->!"within".equals(e.getQueryMethod())).collect(Collectors.toList());
                StringBuffer filterSql = new StringBuffer("select * from (" + sql + ") soul WHERE");
                buildQuery(sql, filterSql,soulPage, fieldMap, filterSos);

                if(log.isInfoEnabled()){
                    log.info("filter data sql: {}",filterSql);
                }
                MappedStatement newMappedStatement = MybatisPluginHelper.newMappedStatement(  ms,  MybatisPluginHelper.newBoundSql(ms, boundSql, filterSql.toString()));
                args[0] = newMappedStatement;
                return invocation.proceed();
            }
        }

        return invocation.proceed();
    }

    /****
     * 合并数据权限规则
     * @param soulPage
     * @param parameterMap
     * @param tenantId
     * @param filterSos
     */
    private static void mergeDpRule(SoulPage soulPage, Map<String, String> parameterMap, Integer tenantId, List<FilterSo> filterSos) {

        if(StrUtil.isNotEmpty(soulPage.getTableCode())){
            // 未配置规则
            // 超级管理员不需要过滤
            SessionUser sessionUser = TenantContextHolder.getCurrentSessionUser();
            if(sessionUser!=null ){
                if( sessionUser.isSuperAdmin()|| sessionUser.isAdmin()){
                    return;
                }
            }
            String userDpRuleCacheKey = buildDpRuleCacheKey(tenantId+"", sessionUser.getId()+"", soulPage.getTableCode());

            JSONObject userDpRuleObject = getDpRuleFromCache(userDpRuleCacheKey);
            if(userDpRuleObject==null) {
                Integer[] roleIds = sessionUser.getRoleIds();
                if (roleIds == null || roleIds.length == 0) {
                    return;
                }
                // 多角色场景 合并多角色数据权限规则
                for (Integer roleId : roleIds) {
                    String roleDpRuleCacheKey = buildDpRuleCacheKey(tenantId+"", roleId+"", soulPage.getTableCode());
                    JSONObject dpRuleObject = getDpRuleFromCache(roleDpRuleCacheKey);
                    doMergeDpRule(filterSos, dpRuleObject) ;
                }
            }else{
                doMergeDpRule(filterSos, userDpRuleObject);
            }

        }
    }

    public static  String buildDpRuleCacheKey(String tenantId,String ownId,String tableCode){
        return tenantId+StrUtil.UNDERLINE+ownId+StrUtil.UNDERLINE+tableCode;
    }
    public static  String buildTableMetadataCacheKey(String tenantId,String tableCode){
        return tenantId+StrUtil.UNDERLINE+tableCode;
    }

    /***
     * 从二级缓存获取数据权限规则
     * @param key
     * @return
     */
    public  static JSONObject getDpRuleFromCache(String key){
        JSONObject ifPresent = DP_RULE_CACHE.getIfPresent(key);
        if(ifPresent ==null){
            ifPresent =  RedisUtils.getCacheMapValue(MybatisConstant.DP_RULE_CACHE_PREFIX,key);
            if(ifPresent!=null)
                DP_RULE_CACHE.put(key,ifPresent);
        }
        return ifPresent;
    }

    /***
     * 合并数据权限规则
     * @param filterSos
     * @param dpRuleObject
     */
    private static void doMergeDpRule(List<FilterSo> filterSos, JSONObject dpRuleObject) {
        if (dpRuleObject == null) {
            return ;
        }
        //没有启用规则
        if (dpRuleObject.getStr("enabledFlag").equals("0")) {
            return ;
        }
        JSONArray dpRuleJsonObject = dpRuleObject.getJSONArray("dpRule");
        if (dpRuleJsonObject == null || dpRuleJsonObject.isEmpty()) {
            return ;
        }
        List<FilterSo> dpRuleList = dpRuleJsonObject.toList(FilterSo.class);
        // 合并数据权限规则
        if (CollectionUtil.isNotEmpty(dpRuleList)) {
            filterSos.addAll(dpRuleList);
        }
    }

    private StringBuffer doDuildQuery(String orignSql,StringBuffer filterSql, SoulPage soulPage, Map<String, String> fieldMap, List<FilterSo> filterSos) {

        // 获取前端指定类型
        Map<String, Map<String, String>> typeMap = soulPage.getTypeMap();
        if (CollectionUtil.isNotEmpty(filterSos)) {

            filterSos.forEach(filterSo->{
                handleFilterSo(filterSo, typeMap, fieldMap, filterSql);
            });
        }else{
            if(StrUtil.endWith(filterSql,"WHERE" )){
                filterSql.append(" 1=1");
            }
            //
        }
        return filterSql;
    }

    private StringBuffer buildQuery(String orignSql,StringBuffer filterSql, SoulPage soulPage, Map<String, String> fieldMap, List<FilterSo> filterSos) {

        // 获取前端指定类型
        Map<String, Map<String, String>> typeMap = soulPage.getTypeMap();
        if (CollectionUtil.isNotEmpty(filterSos)) {

            filterSos.forEach(filterSo->{
                handleFilterSo(filterSo, typeMap, fieldMap, filterSql);
            });
        }else{
            if(StrUtil.endWith(filterSql,"WHERE" )){
                filterSql.append(" 1=1");
            }
            //
        }
        // 排序
        if (StrUtil.isNotBlank(soulPage.getField())) {
            String sortField = getIgnoreCaseMapValue(fieldMap, soulPage.getField());
            filterSql.append(" order by ").append(sortField).append(" ").append(soulPage.getOrder());
        }
        return filterSql;
    }

    /**
     * 处理表头筛选数据
     *
     * @param filterSo
     * @param typeMap
     * @param fieldMap
     * @param filterSql
     * @return void
     */
    private void handleFilterSo(FilterSo filterSo, Map<String, Map<String, String>> typeMap, Map<String, String> fieldMap, StringBuffer filterSql) {
        if (!StrUtil.endWith(filterSql, "(") && !StrUtil.endWith(filterSql, "WHERE")) {
            filterSql.append(StrUtil.isBlank(filterSo.getPrefix())?" and":" "+filterSo.getPrefix());
        }

        String field = getIgnoreCaseMapValue(fieldMap,filterSo.getField() );
        String value = filterSo.getValue();


        switch (filterSo.getMode()) {
            // 关键字搜索
            case "search":
                switch (filterSo.getType()) {
                    case "all":
                        String searchFields = filterSo.getField();
                        List<String> searchFieldArray = StrUtil.split(searchFields, StrUtil.COMMA);
                        if(CollectionUtil.isNotEmpty(searchFieldArray)){
                            filterSql.append(" ");
                            filterSql.append("( ");
                            for(int i=0;i<searchFieldArray.size();i++){
                                String ignoreCaseMapValue = getIgnoreCaseMapValue(fieldMap, searchFieldArray.get(i));
                                String searchF = ignoreCaseMapValue==null? searchFieldArray.get(i): ignoreCaseMapValue ;
                                filterSql.append(searchF);
                                filterSql.append(" like '%").append(value).append("%'");
                                if(i!=searchFieldArray.size()-1){
                                    filterSql.append(" or ");
                                }
                            }
                            filterSql.append(" )");
                        }
                        break;
                }

                break;
            case "in":
                if (filterSo.getValues()==null || filterSo.getValues().size()==0) {
                    filterSql.append(" 1=1");
                    break;
                }
                switch (typeMap.get(field)==null?"":typeMap.get(field).get("type")) {
                    case "date":
                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                            filterSql.append(" to_char(");
                        } else {
                            filterSql.append(" DATE_FORMAT(");
                        }

                        filterSql.append(field)
                                .append(", '");
                        if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                            filterSql.append(typeMap.get(field).get("value").replaceAll("HH", "HH24").replaceAll("mm", "mi"));
                        } else {
                            filterSql.append(typeMap.get(field).get("value")
                                    .replaceAll("yyyy", "%Y")
                                    .replaceAll("MM", "%m")
                                    .replaceAll("dd", "%d")
                                    .replaceAll("HH", "%H")
                                    .replaceAll("mm", "%i")
                                    .replaceAll("ss", "%s"));
                        }

                        filterSql.append("') in ('")
                                .append(StrUtil.join( "','",filterSo.getValues()))
                                .append("')");
                        break;
                    default:
                        if (StrUtil.isBlank(filterSo.getSplit())) {
                            filterSql.append(" ")
                                    .append(field)
                                    .append(" in ('")
                                    .append(StrUtil.join("','",filterSo.getValues()))
                                    .append("')");
                        } else {
                            //todo 兼容value值内包含正则特殊字符
                            if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                                filterSql.append(" regexp_like(")
                                        .append(field)
                                        .append(", '");
                                for (String filterSoValue : filterSo.getValues()) {
                                    filterSql.append("("+filterSo.getSplit()+"|^){1}"+filterSoValue+"("+filterSo.getSplit()+"|$){1}|");
                                }
                                filterSql.deleteCharAt(filterSql.length()-1);
                                filterSql.append("')");
                            } else {
                                filterSql.append(" ")
                                        .append(field)
                                        .append(" regexp '(");
                                for (String filterSoValue : filterSo.getValues()) {
                                    filterSql.append("("+filterSo.getSplit()+"|^){1}"+filterSoValue+"("+filterSo.getSplit()+"|$){1}|");
                                }
                                filterSql.deleteCharAt(filterSql.length()-1);
                                filterSql.append(")+'");
                            }
                        }

                        break;
                }
                break;
            case "condition":
                if (StrUtil.isBlank(filterSo.getType()) || ((!"null".equals(filterSo.getType()) && !"notNull".equals(filterSo.getType())) && StrUtil.isBlank(filterSo.getValue()))) {
                    filterSql.append(" 1=1");
                    break;
                }
                if(filterSo.getField().startsWith(DP_RULE_VAR_PREFIX) || filterSo.getValue().startsWith(DP_RULE_VAR_PREFIX) ){
                    this.handleFilterSo4DpRuleVariables(filterSo, typeMap, fieldMap, filterSql);
                    return;
                }
                filterSql.append(" ");
                filterSql.append(field);
                appendCondition(filterSo, filterSql, value);
                break;
            case "date":
                filterSql.append(" ");
                filterSql.append(field);
                appendDateCondition(filterSo, filterSql);
                break;
            case "group":
                filterSql.append(" (");
                if (filterSo.getChildren().size()>0) {
                    filterSo.getChildren().forEach(f->{
                        handleFilterSo(f, typeMap, fieldMap ,filterSql);
                    });
                } else {
                    filterSql.append(" 1=1");
                }
                filterSql.append(" )");
            default:break;
        }

    }

    private void appendDateCondition(FilterSo filterSo, StringBuffer filterSql) {
        switch (filterSo.getType()) {
            case "yesterday":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between trunc(sysdate - 1) and trunc(sysdate)-1/(24*60*60) ");
                } else {
                    filterSql.append(" between date_add(curdate(), interval -1 day) and date_add(curdate(),  interval -1 second) ");
                }
                break;
            case "thisWeek":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between trunc(sysdate - to_char(sysdate-2,'D')) and trunc(sysdate - to_char(sysdate-2,'D') + 7)-1/(24*60*60) ");
                } else {
                    filterSql.append(" between date_add(curdate(), interval - weekday(curdate()) day) and date_add(date_add(curdate(), interval - weekday(curdate())+7 day), interval -1 second) ");
                }
                break;
            case "lastWeek":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between trunc(sysdate - to_char(sysdate-2,'D') - 7) and trunc(sysdate - to_char(sysdate-2,'D'))-1/(24*60*60) ");
                } else {
                    filterSql.append(" between date_add(curdate(), interval - weekday(curdate())-7 day) and date_add(date_add(curdate(), interval - weekday(curdate()) day), interval -1 second) ");
                }
                break;
            case "thisMonth":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between trunc(sysdate, 'mm') and trunc(last_day(sysdate)+1)-1/(24*60*60) ");
                } else {
                    filterSql.append(" between date_add(curdate(), interval - day(curdate()) + 1 day) and DATE_ADD(last_day(curdate()), interval 24*60*60-1 second) ");
                }
                break;
            case "thisYear":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between trunc(sysdate, 'yyyy') and to_date(to_char(sysdate,'yyyy')||'-12-31 23:59:59', 'yyyy-mm-dd hh24:mi:ss') ");
                } else {
                    filterSql.append(" between date_sub(curdate(),interval dayofyear(now())-1 day) and str_to_date(concat(year(now()),'-12-31 23:59:59'), '%Y-%m-%d %H:%i:%s') ");
                }

                break;
            case "specific":
                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between to_date('").append(filterSo.getValue()).append("', 'yyyy-mm-dd') and to_date('").append(filterSo.getValue()).append("', 'yyyy-mm-dd')+1-1/(24*60*60) ");
                } else {
                    filterSql.append(" between str_to_date('").append(filterSo.getValue()).append("', '%Y-%m-%d') and str_to_date(concat('").append(filterSo.getValue()).append("',' 23:59:59'), '%Y-%m-%d %H:%i:%s') ");
                }
                break;
            case "dateRange":
                String filterSoValue = filterSo.getValue();
                String startTime = StrUtil.split(filterSoValue, StrUtil.COMMA).get(0);
                String endTime = StrUtil.split(filterSoValue, StrUtil.COMMA).get(1);

                if (DB_DIALECT.ORACLE.name().equalsIgnoreCase(dbType)) {
                    filterSql.append(" between to_date('").append(startTime).append("', 'yyyy-mm-dd') and to_date('").append(endTime).append("', 'yyyy-mm-dd')+1-1/(24*60*60) ");
                } else {
                    filterSql.append(" between str_to_date('").append(startTime).append("', '%Y-%m-%d %H:%i:%s') and str_to_date('").append(endTime).append("', '%Y-%m-%d %H:%i:%s') ");
                }
                break;

            case "all":
            default:
                filterSql.delete(filterSql.lastIndexOf(" "), filterSql.length());
                filterSql.append(" 1=1");
                break;
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        this.dbType = properties.getProperty("dbType");
        if (StrUtil.isEmpty(dbType)) {
            dbType = DB_DIALECT.MYSQL.name();
        }
    }

    //忽略大小写 map
    public  String getIgnoreCaseMapValue(Map<String,String> map,String key){
        for(String stringKey:map.keySet()){
            if(StrUtil.equalsIgnoreCase(key, stringKey)){
                return map.get(stringKey);
            }
        }
        return key;
    }
    public static String addWhereClause(String originalSql, String newCondition) {
        try {
            Select selectStatement = (Select) CCJSqlParserUtil.parse(originalSql.replace("#","--"));
            Select selectBody = selectStatement.getSelectBody();
            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;
                Expression where = plainSelect.getWhere();
                plainSelect.getGroupBy();
                if (where == null) {
                    plainSelect.setWhere(CCJSqlParserUtil.parseCondExpression(newCondition));
                } else {
                    // 如果已有WHERE子句，添加新条件
                    CCJSqlParserUtil.parseCondExpression(newCondition); // 确保新条件是合法的
                    plainSelect.setWhere(new AndExpression(where, CCJSqlParserUtil.parseCondExpression(newCondition)));
                }
                return selectStatement.toString();
            } else {
                throw new UnsupportedOperationException("Only PlainSelect statements are supported for adding WHERE clause.");
            }
        } catch (JSQLParserException e) {
            throw new RuntimeException("Error while parsing SQL", e);
        }
    }

    /****
     * 处理数据权限规则变量 匹配数据权限规则变量
     * @param filterSo
     * @param typeMap
     * @param fieldMap
     * @param filterSql
     */
    private void handleFilterSo4DpRuleVariables(FilterSo filterSo, Map<String, Map<String, String>> typeMap, Map<String, String> fieldMap, StringBuffer filterSql) {
        String field = filterSo.getField();
        String value = filterSo.getValue();
        Map<String, String> transVar = translateDpRuleVariables();
        String leftValue = transVar.get(field);
        //没有取到翻译的变量 则取字段名称
        if (StrUtil.isEmpty(leftValue)) {
            leftValue = field;
        }
        String rightValue = transVar.get(value);
        if (StrUtil.isEmpty(rightValue)) {
            rightValue = value;
        }
        if(filterSo.getField().startsWith(DP_RULE_VAR_PREFIX) ){
            if(field.equals(DP_RULE_VAR_CURRENT_ROLE_ID)){
                String[] split = value.split(StrUtil.COMMA);
                Arrays.sort(split);
                rightValue = StrUtil.join(",", split);
            }

            Boolean calcResult = false;

            switch (filterSo.getType()) {
                case "eq":

                    if(DP_RULE_VAR_CURRENT_ROLE_ID.equals(field)){
                        calcResult=  StrUtil.contains(leftValue,rightValue);
                    }else {
                        if (leftValue.equals(value)) {
                            calcResult = true;
                        }
                    }

                    break;

                case "contain":
                    if (StrUtil.contains(rightValue,leftValue)) {
                        calcResult = true;
                    }

                    break;

                case "ne":
                    if (!StrUtil.equals(rightValue,leftValue)) {
                        calcResult = true;
                    }
                    break;

                case "notContain":
                    if (!StrUtil.contains(rightValue,leftValue)) {
                        calcResult = true;
                    }
                    break;

                default:break;
            }

            if(calcResult){
                filterSql.append(" 1=1");
            }else {
                filterSql.append(" 1=2");
            }


        }else{

            filterSql.append(" ");
            filterSql.append(leftValue);
            appendCondition(filterSo, filterSql, rightValue);

        }


    }

    private void appendCondition(FilterSo filterSo, StringBuffer filterSql, String rightValue) {
        switch (filterSo.getType()) {
            case "eq":
                filterSql.append(" = '").append(rightValue).append("'");
                break;
            case "ne":
                filterSql.append(" != '").append(rightValue).append("'");
                break;
            case "gt":
                filterSql.append(" > '").append(rightValue).append("'");
                break;
            case "ge":
                filterSql.append(" >= '").append(rightValue).append("'");
                break;
            case "lt":
                filterSql.append(" < '").append(rightValue).append("'");
                break;
            case "le":
                filterSql.append(" <= '").append(rightValue).append("'");
                break;
            case "contain":
                filterSql.append(" like '%").append(rightValue).append("%'");
                break;
            case "notContain":
                filterSql.append(" not like '%").append(rightValue).append("%'");
                break;
            case "start":
                filterSql.append(" like '").append(rightValue).append("%'");
                break;
            case "end":
                filterSql.append(" like '%").append(rightValue).append("'");
                break;
            case "null":
                filterSql.append(" is null");
                break;
            case "notNull":
                filterSql.append(" is not null");
                break;
            default:break;
        }
    }

    /****
     *
     * 翻译数据权限规则变量
     * @return
     */
    public static Map<String,String> translateDpRuleVariables(){
        SessionUser currentSessionUser = TenantContextHolder.getCurrentSessionUser();
        return  new HashMap<String,String>(){{
            put(DP_RULE_VAR_CURRENT_COMPANY_ID.getVariable(), currentSessionUser.getStaffInfo().getCompanyId()+"");
            put(DP_RULE_VAR_CURRENT_DEPT_ID.getVariable(),currentSessionUser.getDeptId()+"" );
            Integer[] roleIds = currentSessionUser.getRoleIds();
            if(roleIds!=null&&roleIds.length>0){
                Arrays.sort(roleIds);
            }
            put(DP_RULE_VAR_CURRENT_ROLE_ID.getVariable(),StrUtil.join(",",roleIds));
            put(DP_RULE_VAR_CURRENT_STAFF_ID.getVariable(),currentSessionUser.getStaffInfo().getId());
            put(DP_RULE_VAR_CURRENT_USER_ID.getVariable(),currentSessionUser.getId()+"");
            put(DP_RULE_VAR_CURRENT_JOB_ID.getVariable(),currentSessionUser.getStaffInfo().getJobId()+"");
        }};
    }
    public static void main(String[] args) {
    }
}

