package com.lg.financecloud.admin.api.feign;
import com.lg.financecloud.admin.api.entity.PmProjectMember;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020-12-18
 * <p>
 * 远程FinanceService调用接口
 */
@FeignClient(contextId = "pmService", value = ServiceNameConstants.OA_SERVICE)
public interface ProjectManangeService {
	/**
	 * 获取指定项目的成员信息
	 * */
	@GetMapping("/pmProjectMember/getMemberListByProjectId")
	R<List<PmProjectMember>> getMemberListByProjectId(@RequestParam("projectId") String projectId,
													   @RequestHeader(SecurityConstants.FROM) String from);


}
