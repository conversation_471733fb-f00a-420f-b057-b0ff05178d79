package com.lg.dao.core.batch;

import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.exception.DaoException;
import com.lg.dao.core.fill.MetaObjectHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EnumType;
import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

/**
 * 批量操作服务
 */
@Slf4j
@RequiredArgsConstructor
public class BatchOperationService {
    
    private final JdbcTemplate jdbcTemplate;
    private final MetaObjectHandler metaObjectHandler;
    private static final int DEFAULT_BATCH_SIZE = 1000;
    
    /**
     * 真正的批量插入
     */
    public <T> int[] batchInsert(List<T> entities, EntityInfo entityInfo) {
        return batchInsert(entities, entityInfo, DEFAULT_BATCH_SIZE);
    }
    
    /**
     * 分批批量插入
     */
    public <T> int[] batchInsert(List<T> entities, EntityInfo entityInfo, int batchSize) {
        if (entities == null || entities.isEmpty()) {
            return new int[0];
        }
        
        // 自动填充
        if (metaObjectHandler != null) {
            entities.forEach(metaObjectHandler::insertFill);
        }
        
        // 构建SQL模板
        BatchSqlTemplate template = buildInsertTemplate(entities.get(0), entityInfo);
        
        List<int[]> results = new ArrayList<>();
        
        // 分批处理
        for (int i = 0; i < entities.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, entities.size());
            List<T> batch = entities.subList(i, endIndex);
            
            int[] batchResult = executeBatchInsert(batch, template, entityInfo);
            results.add(batchResult);
        }
        
        // 合并结果 - 修复编译错误
        int totalSize = results.stream().mapToInt(arr -> arr.length).sum();
        int[] mergedResult = new int[totalSize];
        int index = 0;
        for (int[] result : results) {
            System.arraycopy(result, 0, mergedResult, index, result.length);
            index += result.length;
        }
        return mergedResult;
    }
    
    /**
     * 批量更新
     */
    @Transactional
    public <T> int[] batchUpdate(List<T> entities, EntityInfo entityInfo) {
        return batchUpdate(entities, entityInfo, DEFAULT_BATCH_SIZE);
    }
    
    /**
     * 分批批量更新
     */
    @Transactional
    public <T> int[] batchUpdate(List<T> entities, EntityInfo entityInfo, int batchSize) {
        if (entities == null || entities.isEmpty()) {
            return new int[0];
        }
        
        // 自动填充
        if (metaObjectHandler != null) {
            entities.forEach(metaObjectHandler::updateFill);
        }
        
        // 构建SQL模板
        BatchSqlTemplate template = buildUpdateTemplate(entities.get(0), entityInfo);
        
        List<int[]> results = new ArrayList<>();
        
        // 分批处理
        for (int i = 0; i < entities.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, entities.size());
            List<T> batch = entities.subList(i, endIndex);
            
            int[] batchResult = executeBatchUpdate(batch, template, entityInfo);
            results.add(batchResult);
        }
        
        // 合并结果 - 修复编译错误
        int totalSize = results.stream().mapToInt(arr -> arr.length).sum();
        int[] mergedResult = new int[totalSize];
        int index = 0;
        for (int[] result : results) {
            System.arraycopy(result, 0, mergedResult, index, result.length);
            index += result.length;
        }
        return mergedResult;
    }
    
    private <T> BatchSqlTemplate buildInsertTemplate(T entity, EntityInfo entityInfo) {
        List<EntityInfo.FieldInfo> insertableFields = new ArrayList<>();
        
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            try {
                Field field = fieldInfo.getField();
                field.setAccessible(true);
                Object value = field.get(entity);
                
                if (shouldIncludeInInsert(fieldInfo, value)) {
                    insertableFields.add(fieldInfo);
                }
            } catch (IllegalAccessException e) {
                throw new DaoException("FIELD_ACCESS_ERROR", 
                    "Failed to access field: " + fieldInfo.getField().getName(), e);
            }
        }
        
        if (insertableFields.isEmpty()) {
            throw new DaoException("NO_INSERTABLE_FIELDS", "No fields to insert found in entity");
        }
        
        // 构建SQL
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(entityInfo.getTableName()).append(" (");
        
        List<String> columns = insertableFields.stream()
                .map(EntityInfo.FieldInfo::getColumn)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        sql.append(String.join(", ", columns));
        sql.append(") VALUES (");
        sql.append(String.join(", ", Collections.nCopies(columns.size(), "?")));
        sql.append(")");
        
        return new BatchSqlTemplate(sql.toString(), insertableFields);
    }
    
    private <T> BatchSqlTemplate buildUpdateTemplate(T entity, EntityInfo entityInfo) {
        if (entityInfo.getIdFields().isEmpty()) {
            throw new DaoException("NO_ID_FIELD", 
                "No @Id field found in entity: " + entity.getClass().getName());
        }
        
        List<EntityInfo.FieldInfo> updatableFields = new ArrayList<>();
        
        for (EntityInfo.FieldInfo fieldInfo : entityInfo.getFields()) {
            if (!fieldInfo.isId() && fieldInfo.isUpdatable()) {
                updatableFields.add(fieldInfo);
            }
        }
        
        if (updatableFields.isEmpty()) {
            throw new DaoException("NO_UPDATABLE_FIELDS", "No updatable fields found in entity");
        }
        
        // 构建SQL
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(entityInfo.getTableName()).append(" SET ");
        
        List<String> updates = updatableFields.stream()
                .map(field -> field.getColumn() + " = ?")
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        sql.append(String.join(", ", updates));
        sql.append(" WHERE ");
        
        List<String> whereConditions = entityInfo.getIdFields().stream()
                .map(field -> field.getColumn() + " = ?")
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        sql.append(String.join(" AND ", whereConditions));
        
        // 合并字段列表（更新字段 + ID字段）
        List<EntityInfo.FieldInfo> allFields = new ArrayList<>(updatableFields);
        allFields.addAll(entityInfo.getIdFields());
        
        return new BatchSqlTemplate(sql.toString(), allFields);
    }
    
    private <T> int[] executeBatchInsert(List<T> entities, BatchSqlTemplate template, EntityInfo entityInfo) {
        // 如果实体数量小于等于1，使用原始的batchUpdate方法
        if (entities.size() <= 1) {
            int[][] results = jdbcTemplate.batchUpdate(template.getSql(), entities, entities.size(),
                (PreparedStatement ps, T entity) -> {
                    try {
                        setParameters(ps, entity, template.getFields());
                    } catch (Exception e) {
                        throw new DaoException("BATCH_INSERT_ERROR", "Failed to set parameters for batch insert", e);
                    }
                });
            
            // 将int[][]转换为int[]
            int totalSize = Arrays.stream(results).mapToInt(arr -> arr.length).sum();
            int[] mergedResult = new int[totalSize];
            int index = 0;
            for (int[] result : results) {
                System.arraycopy(result, 0, mergedResult, index, result.length);
                index += result.length;
            }
            return mergedResult;
        }
        
        // 对于多个实体，使用VALUES()()语法进行批量插入
        try {
            // 解析原始SQL，分离INSERT和VALUES部分
            String originalSql = template.getSql();
            int valuesIndex = originalSql.toUpperCase().indexOf("VALUES");
            if (valuesIndex == -1) {
                throw new DaoException("INVALID_SQL", "Cannot parse VALUES clause from SQL: " + originalSql);
            }
            
            String insertPart = originalSql.substring(0, valuesIndex + 6); // 包含"VALUES"
            String valuesPart = originalSql.substring(valuesIndex + 6);
            
            // 构建批量插入SQL
            StringBuilder sqlBuilder = new StringBuilder(insertPart);
            List<Object> allParams = new ArrayList<>();
            
            for (int i = 0; i < entities.size(); i++) {
                if (i > 0) {
                    sqlBuilder.append(",");
                }
                sqlBuilder.append(valuesPart);
                
                // 收集参数
                T entity = entities.get(i);
                for (EntityInfo.FieldInfo fieldInfo : template.getFields()) {
                    Field field = fieldInfo.getField();
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    
                    // 处理枚举字段
                    if (value != null && value.getClass().isEnum()) {
                        if (fieldInfo.getEnumType() == EnumType.STRING) {
                            value = ((Enum<?>) value).name();
                        } else {
                            value = ((Enum<?>) value).ordinal();
                        }
                    }
                    
                    allParams.add(value);
                }
            }
            
            // 执行批量插入
            int[] result = new int[entities.size()];
            int updateCount = jdbcTemplate.update(sqlBuilder.toString(), allParams.toArray());
            
            // 如果更新计数与实体数量相同，则每个实体都成功插入了一行
            if (updateCount == entities.size()) {
                Arrays.fill(result, 1);
            } else if (updateCount > 0) {
                // 部分成功，但无法确定具体哪些成功了
                Arrays.fill(result, 0, updateCount, 1);
                Arrays.fill(result, updateCount, result.length, 0);
            }
            
            return result;
        } catch (Exception e) {
            throw new DaoException("BATCH_INSERT_ERROR", "Failed to execute batch insert with VALUES syntax", e);
        }
    }
    
    private <T> int[] executeBatchUpdate(List<T> entities, BatchSqlTemplate template, EntityInfo entityInfo) {
        int[][] results = jdbcTemplate.batchUpdate(template.getSql(), entities, entities.size(),
            (PreparedStatement ps, T entity) -> {
                try {
                    setParameters(ps, entity, template.getFields());
                } catch (Exception e) {
                    throw new DaoException("BATCH_UPDATE_ERROR", "Failed to set parameters for batch update", e);
                }
            });
        
        // 将int[][]转换为int[]
        int totalSize = Arrays.stream(results).mapToInt(arr -> arr.length).sum();
        int[] mergedResult = new int[totalSize];
        int index = 0;
        for (int[] result : results) {
            System.arraycopy(result, 0, mergedResult, index, result.length);
            index += result.length;
        }
        return mergedResult;
    }
    
    private <T> void setParameters(PreparedStatement ps, T entity, List<EntityInfo.FieldInfo> fields) 
            throws SQLException, IllegalAccessException {
        
        for (int i = 0; i < fields.size(); i++) {
            EntityInfo.FieldInfo fieldInfo = fields.get(i);
            Field field = fieldInfo.getField();
            field.setAccessible(true);
            Object value = field.get(entity);
            
            // 处理枚举字段
            if (value != null && value.getClass().isEnum()) {
                if (fieldInfo.getEnumType() == EnumType.STRING) {
                    value = ((Enum<?>) value).name();
                } else {
                    value = ((Enum<?>) value).ordinal();
                }
            }
            
            ps.setObject(i + 1, value);
        }
    }
    
    private boolean shouldIncludeInInsert(EntityInfo.FieldInfo fieldInfo, Object value) {
        if (fieldInfo.isId()) {
            if (fieldInfo.getIdType() == null || 
                fieldInfo.getIdType() == com.baomidou.mybatisplus.annotation.IdType.INPUT) {
                return value != null;
            } else if (fieldInfo.getIdType() == com.baomidou.mybatisplus.annotation.IdType.AUTO) {
                return false;
            } else {
                return value != null;
            }
        }
        return value != null && fieldInfo.isInsertable();
    }
    
    /**
     * 批量SQL模板
     */
    private static class BatchSqlTemplate {
        private final String sql;
        private final List<EntityInfo.FieldInfo> fields;
        
        public BatchSqlTemplate(String sql, List<EntityInfo.FieldInfo> fields) {
            this.sql = sql;
            this.fields = fields;
        }
        
        public String getSql() {
            return sql;
        }
        
        public List<EntityInfo.FieldInfo> getFields() {
            return fields;
        }
    }
}