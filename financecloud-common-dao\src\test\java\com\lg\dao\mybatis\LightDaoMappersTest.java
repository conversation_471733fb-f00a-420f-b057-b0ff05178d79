package com.lg.dao.mybatis;

import cn.hutool.json.JSONUtil;
import com.lg.dao.config.LightORMAutoConfiguration;
import com.lg.dao.core.basic.GenericDaoSpringTest;
import com.lg.dao.mybatis.example.User;
import com.lg.dao.mybatis.example.UserMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType;
import org.springframework.test.context.jdbc.Sql;

import javax.sql.DataSource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LightDao Mapper测试类
 * 使用延迟加载和特定Mapper接口替换
 */
@SpringBootTest(classes = {LightDaoMappersTest.TestConfig.class})
@Sql(scripts = {
        "classpath:schema.sql",
        "classpath:data.sql"
})
public class LightDaoMappersTest {

    @Autowired
    private UserMapper userMapper;

    @Test
    public void testLazyMapper() {
        // 测试查询，这时才会创建代理
        List<User> allUsers = userMapper.getAllUsers();

        System.err.println(JSONUtil.toJsonStr(allUsers));
    }

    /**
     * 测试配置类
     * 使用特定Mapper接口替换和延迟加载
     */
    @EnableLightDaoMappers(
        mapperClasses = {UserMapper.class},
        lazyInit = true
    )
    @TestConfiguration
    @Import(LightORMAutoConfiguration.class)
    @SpringBootApplication  // 启用SpringBootApplication注解
    static class TestConfig {

        // 添加数据源Bean
        @Bean
        public DataSource dataSource() {
            return new EmbeddedDatabaseBuilder()
                    .setType(EmbeddedDatabaseType.H2)
                    .setName("testdb-" + System.currentTimeMillis()) // 使用时间戳避免名称冲突
                    .build();
        }
    }
} 