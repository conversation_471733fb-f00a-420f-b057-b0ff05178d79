package excel.export;

/**
 * Excel组件增强功能演示
 * 这是一个完整的演示程序，展示了新增的主从表导出、PDF导出和模板替换功能
 */
public class ExcelEnhancementDemo {

    public static void main(String[] args) {
        System.out.println("===========================================");
        System.out.println("Excel组件增强功能演示程序");
        System.out.println("===========================================");
        
        MasterDetailExportTest test = new MasterDetailExportTest();
        
        try {
            System.out.println("\n1. 测试基本导出功能");
            System.out.println("-------------------------------------------");
            test.testBasicExport();
            
            System.out.println("\n2. 测试主从表导出功能");
            System.out.println("-------------------------------------------");
            test.testMasterDetailExport();
            
            System.out.println("\n3. 测试模板替换功能");
            System.out.println("-------------------------------------------");
            test.testTemplateReplace();
            
            System.out.println("\n4. 测试PDF导出功能");
            System.out.println("-------------------------------------------");
            test.testPdfExport();
            
            System.out.println("\n===========================================");
            System.out.println("所有测试完成！");
            System.out.println("===========================================");
            System.out.println("生成的文件位置:");
            System.out.println("- 基本导出: src/test/java/excel/export/excel/basicExport.xlsx");
            System.out.println("- 主从表导出: src/test/java/excel/export/excel/masterDetailExport.xlsx");
            System.out.println("- 模板替换: src/test/java/excel/export/excel/templateResult.xlsx");
            System.out.println("- PDF导出: src/test/java/excel/export/excel/orderExport.pdf");
            System.out.println("\n请检查生成的文件以查看效果！");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
