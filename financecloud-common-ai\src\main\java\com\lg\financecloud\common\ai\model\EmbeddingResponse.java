package com.lg.financecloud.common.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 向量嵌入响应模型
 * 文本向量化的标准响应结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingResponse {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 向量数据
     * 可能是单个向量或多个向量的列表
     */
    private List<List<Double>> embeddings;
    
    /**
     * 模型类型
     */
    private String modelType;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 向量维度
     */
    private Integer dimensions;
    
    /**
     * 是否已归一化
     */
    private Boolean normalized;
    
    /**
     * token使用统计
     */
    private TokenUsage tokenUsage;
}