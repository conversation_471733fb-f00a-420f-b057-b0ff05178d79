package com.github.stupdit1t.excel.pdf;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * PDF模板操作类
 * 提供基于模板的PDF生成功能
 */
public class OpsPdfTemplate {

    private PdfTemplateConfig config;

    /**
     * 构造函数
     */
    public OpsPdfTemplate() {
        this.config = new PdfTemplateConfig();
    }

    /**
     * 构造函数
     * @param templatePath 模板文件路径
     */
    public OpsPdfTemplate(String templatePath) {
        this.config = new PdfTemplateConfig(templatePath);
    }

    /**
     * 构造函数
     * @param templateInputStream 模板输入流
     */
    public OpsPdfTemplate(InputStream templateInputStream) {
        this.config = new PdfTemplateConfig(templateInputStream);
    }

    /**
     * 设置模板路径
     * @param templatePath 模板文件路径
     * @return 操作对象
     */
    public OpsPdfTemplate from(String templatePath) {
        this.config.setTemplatePath(templatePath);
        return this;
    }

    /**
     * 设置模板输入流
     * @param templateInputStream 模板输入流
     * @return 操作对象
     */
    public OpsPdfTemplate from(InputStream templateInputStream) {
        this.config.setTemplateInputStream(templateInputStream);
        return this;
    }

    /**
     * 添加变量
     * @param key 变量名
     * @param value 变量值
     * @return 操作对象
     */
    public OpsPdfTemplate var(String key, Object value) {
        this.config.addVariable(key, value);
        return this;
    }

    /**
     * 批量添加变量
     * @param variables 变量映射
     * @return 操作对象
     */
    public OpsPdfTemplate vars(Map<String, Object> variables) {
        this.config.addVariables(variables);
        return this;
    }

    /**
     * 添加循环数据
     * @param key 循环变量名
     * @param data 循环数据列表
     * @return 操作对象
     */
    public OpsPdfTemplate loop(String key, List<?> data) {
        this.config.addLoop(key, data);
        return this;
    }

    /**
     * 设置模板类型
     * @param templateType 模板类型
     * @return 操作对象
     */
    public OpsPdfTemplate templateType(PdfTemplateConfig.TemplateType templateType) {
        this.config.setTemplateType(templateType);
        return this;
    }

    /**
     * 设置字体大小
     * @param fontSize 字体大小
     * @return 操作对象
     */
    public OpsPdfTemplate fontSize(float fontSize) {
        this.config.setFontSize(fontSize);
        return this;
    }

    /**
     * 设置页面方向
     * @param orientation 页面方向
     * @return 操作对象
     */
    public OpsPdfTemplate orientation(PdfTemplateConfig.PageOrientation orientation) {
        this.config.setOrientation(orientation);
        return this;
    }

    /**
     * 设置页面大小
     * @param pageSize 页面大小
     * @return 操作对象
     */
    public OpsPdfTemplate pageSize(PdfTemplateConfig.PageSize pageSize) {
        this.config.setPageSize(pageSize);
        return this;
    }

    /**
     * 设置页边距
     * @param margin 页边距
     * @return 操作对象
     */
    public OpsPdfTemplate margins(float margin) {
        this.config.setMargins(margin);
        return this;
    }

    /**
     * 设置页边距
     * @param top 上边距
     * @param right 右边距
     * @param bottom 下边距
     * @param left 左边距
     * @return 操作对象
     */
    public OpsPdfTemplate margins(float top, float right, float bottom, float left) {
        this.config.setMargins(top, right, bottom, left);
        return this;
    }

    /**
     * 设置是否显示网格线
     * @param showGridLines 是否显示网格线
     * @return 操作对象
     */
    public OpsPdfTemplate showGridLines(boolean showGridLines) {
        this.config.setShowGridLines(showGridLines);
        return this;
    }

    /**
     * 启用中文字体
     * @param enableChineseFont 是否启用中文字体
     * @return 操作对象
     */
    public OpsPdfTemplate enableChineseFont(boolean enableChineseFont) {
        this.config.setEnableChineseFont(enableChineseFont);
        return this;
    }

    /**
     * 设置自定义字体路径
     * @param customFontPath 自定义字体路径
     * @return 操作对象
     */
    public OpsPdfTemplate customFont(String customFontPath) {
        this.config.setCustomFontPath(customFontPath);
        return this;
    }

    /**
     * 设置表头背景色
     * @param headerBackgroundColor 表头背景色
     * @return 操作对象
     */
    public OpsPdfTemplate headerBackgroundColor(int headerBackgroundColor) {
        this.config.setHeaderBackgroundColor(headerBackgroundColor);
        return this;
    }

    /**
     * 设置表头字体颜色
     * @param headerFontColor 表头字体颜色
     * @return 操作对象
     */
    public OpsPdfTemplate headerFontColor(int headerFontColor) {
        this.config.setHeaderFontColor(headerFontColor);
        return this;
    }

    /**
     * 设置数据字体颜色
     * @param dataFontColor 数据字体颜色
     * @return 操作对象
     */
    public OpsPdfTemplate dataFontColor(int dataFontColor) {
        this.config.setDataFontColor(dataFontColor);
        return this;
    }

    /**
     * 设置是否保留模板格式
     * @param preserveTemplateFormat 是否保留模板格式
     * @return 操作对象
     */
    public OpsPdfTemplate preserveTemplateFormat(boolean preserveTemplateFormat) {
        this.config.setPreserveTemplateFormat(preserveTemplateFormat);
        return this;
    }

    /**
     * 导出到文件
     * @param filePath 文件路径
     * @throws Exception 导出异常
     */
    public void exportTo(String filePath) throws Exception {
        PdfTemplateProcessor processor = new PdfTemplateProcessor(config);
        processor.processTemplate(filePath);
    }

    /**
     * 导出到输出流
     * @param outputStream 输出流
     * @throws Exception 导出异常
     */
    public void exportTo(OutputStream outputStream) throws Exception {
        PdfTemplateProcessor processor = new PdfTemplateProcessor(config);
        processor.processTemplate(outputStream);
    }

    /**
     * 导出到HTTP响应
     * @param response HTTP响应
     * @param fileName 文件名
     * @throws Exception 导出异常
     */
    public void exportTo(HttpServletResponse response, String fileName) throws Exception {
        PdfTemplateProcessor processor = new PdfTemplateProcessor(config);
        processor.processTemplate(response, fileName);
    }

    /**
     * 获取配置对象（用于高级配置）
     * @return 配置对象
     */
    public PdfTemplateConfig getConfig() {
        return config;
    }

    /**
     * 设置配置对象
     * @param config 配置对象
     * @return 操作对象
     */
    public OpsPdfTemplate config(PdfTemplateConfig config) {
        this.config = config;
        return this;
    }
}
