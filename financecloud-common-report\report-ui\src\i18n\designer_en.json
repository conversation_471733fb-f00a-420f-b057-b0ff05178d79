{"selectTargetCellFirst": "Please select target cell first!", "selectMultiTargetCellFirst": "Please select multiple cells first!", "mergeSplitCells": "Merge/Split Cells", "image": "Image", "importExcel": "Import Excel", "italic": "Italic", "openFile": "Open File...", "setting": "Setting", "underline": "Underline", "panel": {"property": "Property", "datasource": "Data Source", "tip": "Click display/hide Panel"}, "tools": {"searchFormSwitch": "Switch to search form designer", "alignLeft": {"upDownAlign": "Align Top and Bottom", "leftRightAlign": "Align Left and Right", "changeMenu": "Change Menu", "leftAlign": "Align Left", "centerAlign": "Align Center", "rightAlign": "Align Right"}, "alignTop": {"upDownAlign": "Align Top and Bottom", "changeMenu": "Change Menu", "topAlign": "Align Top", "middleAlign": "Align Middle", "bottomAlign": "Align Bottom"}, "bgColor": {"bgColor": "Background Color", "changeMenu": "Change Menu"}, "bold": {"bold": "Bold"}, "border": {"borderLine": "Border Line", "allLine": "All Border Line", "noBorder": "No Border", "leftBorder": "Left Border Line", "rightBorder": "Right Border Line", "topBorder": "Top Border Line", "bottomBorder": "Bottom Border Line", "customBorder": "Custom Border Line", "up": "Up", "down": "Down", "left": "Left", "right": "Right", "customBorderLine": "Custom Border Line", "lineStyle": "Line Style", "solidLine": "Solid", "dashed": "Dashed", "none": "None", "size": "Size", "color": "Color"}, "chart": {"chart": "Chart", "pie": "Pie", "doughnut": "Doughnut", "line": "Line", "bar": "Bar", "horizontalBar": "Horizontal Bar", "area": "Area", "radar": "Radar", "polar": "Polar", "scatter": "<PERSON><PERSON><PERSON>", "bubble": "Bubble"}, "crosstab": {"title": "Slash Line"}, "font": {"font": "Font", "changeMenu": "Change Menu"}, "fontSize": {"size": "Font Size", "changeMenu": "Change Menu"}, "foreColor": {"color": "Fill Color", "changeMenu": "Change Menu"}, "preview": {"preview": "Report Preview", "view": "Preview Report", "pagingPreview": "Preview Report with Paging", "previewFail": "Report Preview Fail."}, "redo": {"noRedo": "Nothing to redo.", "redo": "Redo"}, "undo": {"noUndo": "Nothing to undo.", "redo": "Undo"}, "save": {"save": "Save", "successSave": "Save Successfully", "failSave": "Save Failed", "saveAs": "Save as..."}, "zxing": {"title": "Barcode/QRCode", "qrcode": "QRCode", "barcode": "Barcode"}}, "dialog": {"bean": {"beanDatasetConfig": "Spring Bean Dataset Config", "datasetName": "Dataset Name：", "methodName": "Method Name：", "selectMethod": "Select Method", "methodParameters": "Method must be contain three Parameters：", "returnObject": "Return Object：", "className": "Class full name for generate fields,if not specified,you need add field manually", "ok": "OK", "dataset": "Dataset", "datasetExist": "already exist,please change dataset name."}, "buildin": {"selectDatasource": "Select Buildin Datasource", "datasourceName": "Datasource Name", "select": "Select", "datasource": "Datasource", "datasourceExist": "already exist,please other datasource", "loadFail": "Load buildin datasource fail."}, "condition": {"config": "Condition Config", "relationship": "Relation with the previous condition：", "and": "And", "or": "Or", "propertyName": "Property Name：", "op": "Operator：", "greatThen": "Greater Then", "greatEquals": "Greater Then or Equals", "lessThen": "Less Then", "lessEquals": "Less Then or Equals", "equals": "Equals", "notEquals": "Not Equals", "in": "In", "like": "Like", "valueExpr": "Value Expr：", "exprError": "Syntax error！", "ok": "OK", "selectProperty": "Please select Property!", "selectOp": "Please select Operator!", "inputExpr": "Please input value Expr!"}, "crosstab": {"title": "Custom Slash Line", "crosstab": "Slash Line", "tip": "Multiple slash lines split with '|'，keep blank will generate all slash lines", "ok": "OK"}, "customGroup": {"title": "Custom Group Config", "deleteTip": "Please select group item for delete!", "deleteConfirm": "Are you sure delete the group item?", "modTip": "Please select group item for modify!", "groupCondition": "Group Condition", "addCondition": "Add Condition", "selectTip": "Please select group item first！", "editTip": "Edit Selected Condition", "editConditionTip": "Please select condition for modify first！", "delTitle": "Delete Selected Condition", "delConditionTip": "Please select condition for delete first！"}, "datasource": {"title": "Datasource Config", "name": "Datasource Name:", "username": "Username：", "password": "Password：", "driver": "Driver：", "url": "URL：", "test": "Test Connection", "save": "Save", "nameTip": "Datasource name can not be null.", "usernameTip": "Username can not be null.", "driverTip": "Driver can not be null", "urlTip": "URL can not be null", "datasource": "Datasource", "existTip": "already exist，please change the datasource name.", "testSuccess": "Test connection successfully!", "testFail": "Test connection fail:", "failTip": "Test connection fail,please confirm connection info is right.", "failTip1": "Test connection fail."}, "editPropCondition": {"title": "Condition Config", "relation": "Relation with Prev Condition:", "and": "And", "or": "Or", "leftValue": "Left Value:", "currentValue": "Current Value", "property": "Property", "expression": "Expression", "propName": "Property Name:", "expr": "Expression:", "syntaxError": "exist syntax error!", "operator": "Operator:", "greater": "Greater Then", "greaterEquals": "Greater Then or Equals", "less": "Less Then", "lessEquals": "Less Then or Equals", "equals": "Equals", "notEquals": "Not Equals", "in": "in", "like": "like", "valueExpr": "Value Expression:", "ok": "OK", "selectProp": "Please select property!", "leftValueExpr": "Please input left value expression", "selectOperator": "Please select operator", "inputExpr": "Please input expression value"}, "fontSetting": {"title": "Font Style Setting", "font": "Font:", "color": "Color:", "size": "Size", "bold": "Bold:", "yes": "Yes", "no": "No", "italic": "Italic", "underline": "Underline:", "ok": "OK"}, "groupItem": {"title": "Custom Group Item", "name": "Group Name:", "ok": "OK", "nameTip": "Please input group item name!", "addItem": "Add custom group item", "editItem": "Modify custom group item"}, "import": {"title": "Import Excel File", "desc": "This operation will import the first sheet content of Excel,if you choice the xlsx format of Excel file,if the background color could not parse,you can save the file as xls format,and upload it again.", "file": "Select the excel file to import:", "upload": "Upload", "fail": "Import fail"}, "mapping": {"title": "Add Data Mapping", "key": "Actual Value", "value": "Display Value", "save": "Save", "tip": "Please input all mapping item!", "add": "Add data mapping", "edit": "Edit data mapping"}, "methodSelect": {"title": "SpringBean Dataset config", "methodName": "Method Name", "select": "Select", "load": "Load <PERSON>", "fail": "avaliable method fail!"}, "open": {"title": "Open Report File", "source": "Report File Source：", "fileName": "File Name", "modDate": "Modify Date", "open": "Open", "del": "Delete", "openConfirm": "Are you sure open file ", "delConfirm": "Are you sure delete file ", "delFail": "File delete fail", "loadFail": "Load report file fail."}, "sqlParam": {"title": "SQL Parameters", "name": "Parameter Name：", "datatype": "Datatype：", "defaultValue": "Default Value:", "tip": "Date type data default value pattern is \"yyyy-MM-dd HH:mm:ss\"", "ok": "OK", "nameTip": "Parameter name can not be null!", "datatypeTip": "Datatype can not be null!", "param": "Parameter ", "exist": " already exist!"}, "paramTable": {"addParam": "Add Parameter", "paramName": "Parameter Name", "paramDatatype": "Datatype", "defaultValue": "Default Value", "operator": "Operator", "delParam": "Delete Parameter", "editParam": "Edit Parameter"}, "preview": {"title": "Data Preview", "load": "Data loading...", "ok": "Ok", "total": "Total ", "totalMid": ",preview first ", "item": ""}, "propCondition": {"title": "Condition Property Config", "config": "Condition Config", "addItem": "Add New", "editItem": "Edit Selected", "editTip": "Please select a item first!", "delItem": "Delete Item", "delTip": "Please select a item first!", "delConfirm": "Are you sure delete item ", "conditionConfig": "Condition Config", "propConfig": "Property Config", "currentValue": "Current Value", "addValue": "Add Condition", "selectItem": "Please select a item first!", "editConditionItem": "Edit selection Condition", "editConditionTip": "Please select a item first!", "selectConditionItem": "Please select a item first!", "delCondition": "Delete selection Condition", "delConditionTip": "Please select a item first!", "selectDelCondition": "Please select a condition item first!", "forecolor": "Forecolor", "scope": "Scope:", "currentCell": "Current Cell", "currentRow": "Current Row", "currentCol": "Current Column", "bgcolor": "BGColor", "font": "Font", "fontSize": "Font Size", "bold": "Bold", "yes": "Yes", "no": "No", "italic": "Italic", "underline": "Underline", "align": "Align", "left": "Left", "center": "Center", "right": "Right", "valign": "VAlign", "top": "Top", "mid": "Middle", "bottom": "Bottom", "border": "Border", "borderConfig": "Border Config", "newValue": "New Value", "format": "Format", "rowHeight": "Row Height", "colWidth": "<PERSON>", "paging": "Paging", "rowBefore": "Current Row Before", "rowAfter": "Current Row After", "link": "Link", "target": "Target:", "newWindow": "New Window", "currentWindow": "Current Window", "parentWindow": "Parent Window", "topWindow": "Top Window", "urlParameter": "URL Parameters Config", "linkUrl": "Please config url first!"}, "conditionItem": {"title": "Add Condition Item", "itemName": "Condition Name:", "ok": "OK", "nameTip": "Please input Condition Item Name!", "add": "Add Condition Item", "edit": "Edit Condition Item"}, "rowColWidthHeight": {"title": "Row height or Column width setting", "tip": "Please enter a number greater than 0", "numValidate": "Please enter a valid number! ", "colWidth": "<PERSON><PERSON><PERSON>", "ok": "OK", "rowHeight": "Row Height"}, "save": {"title": "Save Report File", "source": "Store Destination:", "fileName": "File Name", "modDate": "Modify Date", "del": "Delete", "delConfirm": "Are you sure delete the file ：", "delFail": "File delete failed.", "save": "Save", "nameTip": "File name can not be null.", "locationTip": "Please select destination to store file.", "file": "File", "exist": " already exist", "success": "Save successfully", "fail": "Save failed", "loadFail": "Load file failed."}, "setting": {"title": "Report Setting", "pageSetting": "Page Setting", "headerFooterSetting": "<PERSON><PERSON>", "pagingSetting": "Paging", "columnSetting": "Column <PERSON>", "sheetExport": "Export Excel by Sheet：", "disable": "Disable", "enable": "Enable", "recordCountPerPage": "The number of records of sheet ：", "recordCountTip": "Each sheet records not less than 1!", "paperType": "Paper Type：", "custom": "Custom", "paperWidth": "Paper Width(mm)：", "numberTip": "Please enter a number! ", "paperHeight": "Paper Height(mm)：", "leftMargin": "Left Margin(mm)：", "rightMargin": "Right Margin(mm)：", "topMargin": "Top Margin(mm)：", "bottomMargin": "Bottom Margin(mm)：", "orientation": "Orientation：", "portrait": "Portrait", "landscape": "Landscape", "htmlAlign": "Align for Html Report:", "left": "Left", "center": "Center", "right": "Right", "refreshSecond": "Html Report timing refresh(s)：", "tip1": "0 means no refresh", "tip2": "0 means no refresh", "secondTip": "Please enter a number greater than or equal to 0", "bg": "Background Image for Print：", "bgTip": "Please enter background image url for print,image resolution needs to be set to 96dpi", "hfdesc": "Headers/footers defined by expressions,like:\"Page\"+page()+\",total\"+pages()", "header": "Header", "fontStyleSetting": "Font Style Setting", "headerMargin": "Header Top <PERSON>(mm):", "footer": "Footer", "footerMargin": "Footer Bottom Margin(mm):", "hfLeft": "Left", "hfCenter": "Center", "hfRight": "Right", "pagingType": "Paging Type:", "auto": "Auto", "fixRows": "Fix Rows", "rowsPerPage": "Rows per Page：", "fixRowsTip": "Fixed rows cannot be less than one", "colDesc": "The column effect is shown in paging,and print,exporting report.", "column": "Column:", "columnCount": "Column count:", "columnUnit": " columns", "columnTip": "Please enter column number", "columnMargin": "Column Margin(mm):", "numTip": "Please enter a number!", "syntaxError": "Syntax error：", "syntaxCheckFail": "Syntax check failed!"}, "springDS": {"title": "Spring Bean Datasource Config", "name": "Datasource Name:", "bean": "Bean ID：", "save": "Save", "nameTip": "Datasource name cannot be null!", "beanTip": "Bean ID cannot be null!", "ds": "Datasource", "exist": " already exist!"}, "sql": {"title": "SQL Dataset Config", "search": "Table Search", "tableName": "Table Name", "type": "Type", "datasetName": "Dataset Name:", "desc": "SQL support expression，like：${expr...}</span>):", "syntaxCheckError": "Syntax check failed!", "fiterParam": "Search Parameters", "paramDesc": "(Please define parameters in the below table)", "preview": "Preview Data", "previewFail": "Preview data fail!", "ok": "OK", "nameTip": "Dataset name cannot be null!", "sqlTip": "SQL cannot be null!", "ds": "Dataset", "exist": " already exist.", "addSql": "Double click to add sql", "table": "Table", "view": "View", "loadFail": "Load failed!"}, "urlParam": {"title": "URL Parameters Config", "add": "Add Parameter", "delTip": "Are you sure delete it？", "name": "Parameter Name", "expr": "Value Expr", "op": "Operate"}, "paramItem": {"title": "Add Parameter", "name": "Parameter Name", "expr": "Value Expr:", "save": "Save", "tip": "Parameter item cannot be null!", "add": "Add Parameter", "edit": "Edit Parameter"}}, "property": {"datasource": {"title": "Add Database Connection", "addBean": "Add SpringBean Connection", "addBuildin": "Add Buildin Connection"}, "prop": {"linkConfig": "Link Config", "target": "Target:", "newWindow": "New Window", "currentWindow": "Current Window", "urlParameterConfig": "URL Parameters", "parentWindow": "Parent Window", "topWindow": "Top Window", "urlTip": "Please config link first!", "cellType": "Cell Type:", "text": "Text", "expr": "Expression", "dataset": "Dataset", "image": "Image", "slash": "Slash", "qrcode": "QRCode", "barcode": "Barcode", "chart": "Chart", "leftParent": "Left Parent:", "topParent": "Top Parent:", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "renderBean": "Render Bean：", "selectBean": "Select Bean", "prop": "Property ", "none": "None"}, "base": {"fillBlank": "Fill Rows：", "open": "On", "close": "Off", "rowTimes": "Times：", "newLineCompute": "New Line Compute:", "newLineComputeTip": "New Line compute will consuming more computing time!", "format": "Format:", "formatTip": "Format date or number，like: #.##", "conditionProp": "Condition Property:", "configCondition": "Config Condition Property", "syntaxError": "Syntax check failed."}, "dataset": {"datasetConfig": "Dataset Config", "filterCondition": "Filter", "mapping": "Mapping", "dataset": "Dataset:", "property": "Property:", "aggregateType": "Aggregate Type", "select": "Select", "group": "Group", "customGroup": "Custom Group", "sum": "Sum", "count": "Count", "max": "Max", "min": "Min", "avg": "Avg", "configCustomGroup": "Custom Group Config", "sortType": "Sort Type:", "notSort": "None", "asc": "Asc", "desc": "Desc", "expand": "Expand:", "down": "Down", "right": "Right", "noneExpand": "None", "lineHeight": "Line Height:", "lineHeightTip": "Please enter a number!", "addFilterCondition": "Add Condition", "editFilterCondition": "Edit Condition", "selectFilterConditionTip": "Please select condition for edit!", "delFilterCondition": "Delete Condition", "delFilterConditionTip": "Please select condition for delete!", "mappingType": "Mapping Type:", "simple": "Simple", "ds": "Dataset", "addMapping": "Add Mapping", "delConfirm": "Are you sure delete?", "realValue": "Key", "displayValue": "Value", "op": "Operate", "realValueProp": "Key Property:", "displayValueProp": "Value Property", "bindDatasetTip": "Please select dataset first!"}, "expr": {"expr": "Expression", "expand": "Expand:", "down": "Down", "right": "Right", "noneExpand": "None"}, "image": {"source": "Image Source：", "path": "Path", "expr": "Expression", "p": "Path:", "tip": "The default supports image starting with \"classpath:\" or \"/\"", "expand": "Expand:", "down": "Down", "right": "Right", "noneExpand": "None"}, "simple": {"content": "Text Content", "lineHeight": "Line Height", "tip": "Please enter a number!"}, "slash": {"content": "Slash Content", "refresh": "Refresh", "name": "Name:", "angle": "Angle:"}, "zxing": {"width": "Width:", "numberTip": "Please enter a number", "height": "Height:", "format": "Format:", "displayText": "Display Text:", "yes": "Yes", "no": "No", "source": "Data source:", "text": "Text", "expr": "Expression", "text1": "Text:", "expand": "Expand:", "down": "Down", "right": "Right", "noneExpand": "None"}}, "chart": {"datasetBind": "Dataset Bind", "option": "Option", "axisConfig": "Axis Config", "propBindConfig": "Property Bind Config", "dataset": "Dataset:", "categoryProperty": "Category Property:", "xProperty": "X Propverty:", "yProperty": "Y Property:", "rProperty": "R Property:", "valueProperty": "Value Property:", "seriesProperty": "Series Property:", "property": "Property", "static": "Static Value", "prop": "Property:", "staticValue": "Static Value:", "aggregate": "Aggregate Type:", "select": "Select", "sum": "Sum", "count": "Count", "max": "Max", "min": "Min", "avg": "Avg", "xAxis": "X Axis Config", "titleRotation": "Title Angle:", "angleScope": "Angle scope is 0~90", "titleFormat": "Title Format", "displayAxisTitle": "Display Axis Title:", "yes": "Yes", "no": "No", "axisTitle": "Axis Title:", "yAxisConfig": "Y Axis Config", "motionConfig": "Motion Config", "motionDelay": "Motion Delay:", "effect": "Effect", "margin": "<PERSON><PERSON>", "up": "Up", "down": "Down", "left": "Left", "right": "Right", "legendConfig": "Legend Config", "display": "Display", "position": "Position:", "titleConfig": "Title Config", "titleContent": "Title Content:"}, "table": {"render": {"image": "Image:", "slash": "Slash", "qrcode": "QRcode", "barcode": "Barcode", "chart": "Chart"}, "contextMenu": {"insertRowUp": "Insert Row(Up)", "insertRowDown": "Insert Row(Down)", "insertColBefore": "Insert Column(Before)", "insertColAfter": "Insert Column(After)", "delRow": "Delete Row", "delCol": "Delete Column", "rowHeight": "Set Row Height", "colWidth": "<PERSON>", "title": "Title Row", "repeatHeader": "<PERSON><PERSON> <PERSON><PERSON>", "repeatFooter": "<PERSON><PERSON> <PERSON><PERSON>", "summary": "Summary Row", "cancel": "Cancel", "copy": "Copy Style", "paste": "Paste Style", "clearContent": "Empty Content", "clearStyle": "Empty Style", "clearAll": "Empty All", "cancelConetntFail": "Cancel empty cell content failed!", "cancelStyleFail": "Cancel empty cell style failed!", "cancelClearFail": "Cancel empty cell failed!"}, "header": {"hr": "", "fr": "", "t": "", "s": ""}, "report": {"tip": "", "load": "Load report file ", "fail": " failed!"}, "colTip": "Please select a column!", "rowTip": "Please select a row!"}, "tree": {"cellTip": "Please select a cell!", "delConfirm": "Are you sure delete the datasource ", "addDataset": "Add Dataset", "delete": "Delete", "inputTip": "Please enter field name:", "addField": "Add Field", "fieldExist": "Field Exist!", "delDatasetConfirm": "Are you sure delete the dataset ", "add": "Edit", "edit": "Edit", "del": "Delete", "refresh": "Refresh", "loadFieldFail": "Load field failed!", "doubleClick": "Double click to add field", "delFieldConfirm": "Are sure delete field "}}