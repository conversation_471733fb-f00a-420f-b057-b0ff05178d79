package com.github.stupdit1t.excel.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强版PDF导出器
 * 专门用于模板导出，支持完整的样式继承
 */
public class EnhancedPdfExporter {

    private PdfTemplateConfig config;
    private ExcelToPdfStyleMapper styleMapper;

    public EnhancedPdfExporter(PdfTemplateConfig config) {
        this.config = config;
        this.styleMapper = new ExcelToPdfStyleMapper(config);
    }

    /**
     * 从Excel工作簿导出PDF
     */
    public void exportFromWorkbook(Workbook workbook, OutputStream outputStream) throws Exception {
        Document document = createDocument();
        PdfWriter writer = PdfWriter.getInstance(document, outputStream);
        document.open();

        // 遍历所有Sheet
        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            if (sheetIndex > 0) {
                document.newPage(); // 新页面
            }
            exportSheetWithStyles(document, sheet);
        }

        document.close();
        writer.close();
    }

    /**
     * 导出到文件路径
     */
    public void exportFromWorkbook(Workbook workbook, String filePath) throws Exception {
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath)) {
            exportFromWorkbook(workbook, fos);
        }
    }

    /**
     * 导出到HTTP响应
     */
    public void exportFromWorkbook(Workbook workbook, HttpServletResponse response, String fileName) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        exportFromWorkbook(workbook, response.getOutputStream());
    }

    /**
     * 创建PDF文档
     */
    private Document createDocument() {
        Rectangle pageSize = getPageSize();
        if (config.getOrientation() == PdfTemplateConfig.PageOrientation.LANDSCAPE) {
            pageSize = pageSize.rotate();
        }

        return new Document(pageSize, 
            config.getMarginLeft(), 
            config.getMarginRight(), 
            config.getMarginTop(), 
            config.getMarginBottom());
    }

    /**
     * 获取页面大小
     */
    private Rectangle getPageSize() {
        switch (config.getPageSize()) {
            case A3: return PageSize.A3;
            case A5: return PageSize.A5;
            case LETTER: return PageSize.LETTER;
            case LEGAL: return PageSize.LEGAL;
            default: return PageSize.A4;
        }
    }

    /**
     * 导出单个Sheet（带样式）
     */
    private void exportSheetWithStyles(Document document, Sheet sheet) throws DocumentException {
        if (sheet.getPhysicalNumberOfRows() == 0) {
            return;
        }

        // 添加Sheet标题（如果需要）
        if (sheet.getSheetName() != null && !sheet.getSheetName().isEmpty() && 
            !sheet.getSheetName().startsWith("Sheet")) {
            
            com.itextpdf.text.Font titleFont = styleMapper.convertToFont(null, sheet.getWorkbook());
            Paragraph title = new Paragraph(sheet.getSheetName(), titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(10f);
            document.add(title);
        }

        // 分析表格结构
        EnhancedTableStructure tableStructure = analyzeEnhancedTableStructure(sheet);
        
        // 创建PDF表格
        PdfPTable table = new PdfPTable(tableStructure.getColumnCount());
        table.setWidthPercentage(100);
        
        // 设置列宽
        if (tableStructure.getColumnWidths() != null) {
            try {
                table.setWidths(tableStructure.getColumnWidths());
            } catch (DocumentException e) {
                // 如果设置列宽失败，使用默认宽度
            }
        }

        // 添加表格内容（带样式）
        addTableContentWithStyles(table, sheet, tableStructure);
        
        document.add(table);
    }

    /**
     * 分析增强表格结构
     */
    private EnhancedTableStructure analyzeEnhancedTableStructure(Sheet sheet) {
        int maxColumns = 0;
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        
        // 找到最大列数
        for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row != null) {
                maxColumns = Math.max(maxColumns, row.getLastCellNum());
            }
        }

        // 计算列宽
        float[] columnWidths = new float[maxColumns];
        for (int col = 0; col < maxColumns; col++) {
            int columnWidth = sheet.getColumnWidth(col);
            // 将POI的列宽单位转换为相对宽度
            columnWidths[col] = Math.max(columnWidth / 256f, 20f); // 最小宽度20
        }

        // 获取合并单元格信息
        List<CellRangeAddress> mergedRegions = new ArrayList<>();
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            mergedRegions.add(sheet.getMergedRegion(i));
        }

        return new EnhancedTableStructure(maxColumns, columnWidths, mergedRegions);
    }

    /**
     * 添加表格内容（带样式）
     */
    private void addTableContentWithStyles(PdfPTable table, Sheet sheet, EnhancedTableStructure structure) {
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        
        // 用于跟踪已处理的合并单元格
        Map<String, Boolean> processedMergedCells = new HashMap<>();

        for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                // 添加空行
                for (int col = 0; col < structure.getColumnCount(); col++) {
                    PdfPCell emptyCell = new PdfPCell(new Phrase(""));
                    emptyCell.setBorder(Rectangle.NO_BORDER);
                    table.addCell(emptyCell);
                }
                continue;
            }

            // 设置行高（如果指定了）
            float rowHeight = row.getHeightInPoints();
            
            for (int col = 0; col < structure.getColumnCount(); col++) {
                Cell cell = row.getCell(col);
                String cellKey = rowNum + "," + col;
                
                // 检查是否是合并单元格的一部分
                CellRangeAddress mergedRegion = findMergedRegion(structure.getMergedRegions(), rowNum, col);
                
                if (mergedRegion != null) {
                    // 如果是合并单元格的左上角，创建合并的PDF单元格
                    if (mergedRegion.getFirstRow() == rowNum && mergedRegion.getFirstColumn() == col) {
                        if (!processedMergedCells.containsKey(cellKey)) {
                            PdfPCell pdfCell = createStyledCell(cell, sheet.getWorkbook());
                            
                            // 设置合并
                            int rowSpan = mergedRegion.getLastRow() - mergedRegion.getFirstRow() + 1;
                            int colSpan = mergedRegion.getLastColumn() - mergedRegion.getFirstColumn() + 1;
                            
                            pdfCell.setRowspan(rowSpan);
                            pdfCell.setColspan(colSpan);
                            
                            // 设置行高
                            if (rowHeight > 0) {
                                pdfCell.setMinimumHeight(rowHeight);
                            }
                            
                            table.addCell(pdfCell);
                            
                            // 标记所有相关单元格为已处理
                            for (int r = mergedRegion.getFirstRow(); r <= mergedRegion.getLastRow(); r++) {
                                for (int c = mergedRegion.getFirstColumn(); c <= mergedRegion.getLastColumn(); c++) {
                                    processedMergedCells.put(r + "," + c, true);
                                }
                            }
                        }
                    }
                    // 如果不是左上角，跳过（已经在左上角处理了）
                } else if (!processedMergedCells.containsKey(cellKey)) {
                    // 普通单元格
                    PdfPCell pdfCell = createStyledCell(cell, sheet.getWorkbook());
                    
                    // 设置行高
                    if (rowHeight > 0) {
                        pdfCell.setMinimumHeight(rowHeight);
                    }
                    
                    table.addCell(pdfCell);
                }
            }
        }
    }

    /**
     * 创建带样式的PDF单元格
     */
    private PdfPCell createStyledCell(Cell cell, Workbook workbook) {
        String cellValue = getCellValue(cell);
        
        // 获取单元格样式
        CellStyle cellStyle = cell != null ? cell.getCellStyle() : null;
        
        // 转换字体
        com.itextpdf.text.Font font = styleMapper.convertToFont(cellStyle, workbook);
        
        // 创建PDF单元格
        PdfPCell pdfCell = new PdfPCell(new Phrase(cellValue, font));
        
        // 应用样式
        if (cellStyle != null) {
            styleMapper.applyCellStyle(pdfCell, cellStyle, workbook);
        } else {
            // 默认样式
            pdfCell.setPadding(4f);
            if (config.isShowGridLines()) {
                pdfCell.setBorder(Rectangle.BOX);
                pdfCell.setBorderWidth(0.25f);
                pdfCell.setBorderColor(BaseColor.LIGHT_GRAY);
            } else {
                pdfCell.setBorder(Rectangle.NO_BORDER);
            }
        }
        
        return pdfCell;
    }

    /**
     * 查找合并单元格区域
     */
    private CellRangeAddress findMergedRegion(List<CellRangeAddress> mergedRegions, int row, int col) {
        for (CellRangeAddress region : mergedRegions) {
            if (region.isInRange(row, col)) {
                return region;
            }
        }
        return null;
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，不显示小数点
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf(cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getCellFormula();
                }
            default:
                return "";
        }
    }

    /**
     * 增强表格结构信息
     */
    private static class EnhancedTableStructure {
        private int columnCount;
        private float[] columnWidths;
        private List<CellRangeAddress> mergedRegions;

        public EnhancedTableStructure(int columnCount, float[] columnWidths, List<CellRangeAddress> mergedRegions) {
            this.columnCount = columnCount;
            this.columnWidths = columnWidths;
            this.mergedRegions = mergedRegions;
        }

        public int getColumnCount() {
            return columnCount;
        }

        public float[] getColumnWidths() {
            return columnWidths;
        }

        public List<CellRangeAddress> getMergedRegions() {
            return mergedRegions;
        }
    }
}
