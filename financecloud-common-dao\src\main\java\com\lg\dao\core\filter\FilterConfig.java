package com.lg.dao.core.filter;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 筛选条件配置
 *
 * <AUTHOR>
 */
@Data
public class FilterConfig {
    /**
     * 筛选条件列表
     */
    private List<Filter> filters = new ArrayList<>();
    
    /**
     * 排序条件列表
     */
    private List<SortCondition> sorts = new ArrayList<>();
    
    /**
     * 分页信息
     */
    private PageInfo page;
    
    /**
     * 添加筛选条件
     *
     * @param field 字段名
     * @param operator 操作符
     * @param value 值
     * @return 当前对象
     */
    public FilterConfig addFilter(String field, String operator, Object value) {
        Filter filter = new Filter();
        filter.setField(field);
        filter.setOperator(operator);
        filter.setValue(value);
        filters.add(filter);
        return this;
    }
    
    /**
     * 添加排序条件
     *
     * @param field 字段名
     * @param direction 排序方向
     * @return 当前对象
     */
    public FilterConfig addSort(String field, String direction) {
        SortCondition condition = new SortCondition();
        condition.setField(field);
        condition.setDirection(direction);
        sorts.add(condition);
        return this;
    }
    
    /**
     * 设置分页信息
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 当前对象
     */
    public FilterConfig setPage(int pageNum, int pageSize) {
        this.page = new PageInfo();
        this.page.setPageNum(pageNum);
        this.page.setPageSize(pageSize);
        return this;
    }
    
    /**
     * 筛选条件
     */
    @Data
    public static class Filter {
        /**
         * 字段名
         */
        private String field;
        
        /**
         * 操作符
         */
        private String operator;
        
        /**
         * 值
         */
        private Object value;
    }
    
    /**
     * 排序条件
     */
    @Data
    public static class SortCondition {
        /**
         * 字段名
         */
        private String field;
        
        /**
         * 排序方向
         */
        private String direction;
    }
    
    /**
     * 分页信息
     */
    @Data
    public static class PageInfo {
        /**
         * 页码
         */
        private int pageNum = 1;
        
        /**
         * 每页大小
         */
        private int pageSize = 10;
    }
} 