package com.lg.financecloud.common.redis.lock;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 分布式锁配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "light.lock")
public class DistributedLockProperties {
    
    /**
     * 是否启用分布式锁功能
     */
    private boolean enabled = true;
    
    /**
     * 锁的前缀
     */
    private String prefix = "distributed_lock:";
    
    /**
     * 默认等待时间（毫秒）
     */
    private long defaultWaitTime = 3000L;
    
    /**
     * 默认租约时间（毫秒）
     */
    private long defaultLeaseTime = 30000L;
    
    /**
     * 是否启用锁监控
     */
    private boolean enableMetrics = false;
    
    /**
     * 锁超时告警阈值（毫秒）
     */
    private long timeoutAlertThreshold = 10000L;
}
