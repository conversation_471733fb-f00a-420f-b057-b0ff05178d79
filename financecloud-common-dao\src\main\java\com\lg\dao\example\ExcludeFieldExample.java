package com.lg.dao.example;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lg.dao.core.EntityInfo;
import com.lg.dao.core.EntityInfoManager;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 排除字段注解使用示例
 * 演示如何使用JPA @Transient和MyBatis-Plus @TableField(exist=false)排除非数据库字段
 */
public class ExcludeFieldExample {

    /**
     * 示例实体类 - 演示字段排除注解的使用
     */
    @Data
    @Entity
    @Table(name = "user_profile")
    @TableName("user_profile")
    public static class UserProfile {
        
        @Id
        @TableId
        private Long id;
        
        @Column(name = "username")
        private String username;
        
        @Column(name = "email")
        private String email;
        
        @Column(name = "phone")
        private String phone;
        
        @Column(name = "created_time")
        private LocalDateTime createdTime;
        
        // ==================== 排除字段示例 ====================
        
        /**
         * 使用JPA @Transient注解排除字段
         * 该字段不会被映射到数据库
         */
        @Transient
        private String temporaryData;
        
        /**
         * 使用MyBatis-Plus @TableField(exist=false)排除字段
         * 该字段不会被映射到数据库
         */
        @TableField(exist = false)
        private String calculatedField;
        
        /**
         * 组合使用两种注解（推荐使用其中一种即可）
         */
        @Transient
        @TableField(exist = false)
        private List<String> relatedData;
        
        /**
         * 业务逻辑字段 - 用于存储计算结果
         */
        @TableField(exist = false)
        private String fullName;
        
        /**
         * 临时状态字段 - 不持久化到数据库
         */
        @Transient
        private boolean isOnline;
        
        /**
         * 缓存字段 - 用于提高性能，不存储到数据库
         */
        @TableField(exist = false)
        private String cachedValue;
        
        // ==================== 业务方法 ====================
        
        /**
         * 计算全名（业务逻辑字段的使用示例）
         */
        public String getFullName() {
            if (fullName == null) {
                fullName = username + "(" + email + ")";
            }
            return fullName;
        }
        
        /**
         * 设置临时数据（临时字段的使用示例）
         */
        public void setTemporaryData(String data) {
            this.temporaryData = data;
        }
    }
    
    /**
     * 测试排除字段功能
     */
    public static void testExcludeFields() {
        System.out.println("=== 排除字段注解测试 ===");
        
        // 创建实体信息（使用统一管理器）
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(UserProfile.class);
        
        System.out.println("表名: " + entityInfo.getTableName());
        System.out.println("映射到数据库的字段数量: " + entityInfo.getFields().size());
        
        System.out.println("\n数据库字段列表:");
        entityInfo.getFields().forEach(field -> {
            System.out.println("- Java字段: " + field.getField().getName() + 
                             " -> 数据库列: " + field.getColumn());
        });
        
        System.out.println("\n验证排除字段:");
        
        // 验证被排除的字段
        String[] excludedFields = {"temporaryData", "calculatedField", "relatedData", 
                                 "fullName", "isOnline", "cachedValue"};
        
        for (String fieldName : excludedFields) {
            String columnName = entityInfo.getColumnName(fieldName);
            if (columnName == null) {
                System.out.println("✓ 字段 '" + fieldName + "' 已被正确排除");
            } else {
                System.out.println("✗ 字段 '" + fieldName + "' 未被排除，映射到列: " + columnName);
            }
        }
        
        System.out.println("\n预期结果:");
        System.out.println("- 应该有5个数据库字段: id, username, email, phone, created_time");
        System.out.println("- 应该排除6个字段: temporaryData, calculatedField, relatedData, fullName, isOnline, cachedValue");
        
        // 生成的SQL示例
        System.out.println("\n生成的SQL示例:");
        System.out.println("SELECT id, username, email, phone, created_time FROM user_profile");
        System.out.println("INSERT INTO user_profile (username, email, phone, created_time) VALUES (?, ?, ?, ?)");
    }
    
    /**
     * 主方法 - 运行测试
     */
    public static void main(String[] args) {
        testExcludeFields();
    }
}