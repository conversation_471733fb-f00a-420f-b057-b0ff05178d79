package com.lg.dao.core;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EntityInfo调试测试
 * 用于调试EntityInfo的映射表构建过程
 */
@Slf4j
public class EntityInfoDebugTest {

    @Data
    @Table(name = "simple_user")
    public static class SimpleUser {
        @Id
        @Column(name = "user_id")
        private Long userId;
        
        @Column(name = "user_name")
        private String userName;
        
        private String firstName;
        private String lastName;
    }

    @Test
    void testEntityInfoCreation() {
        log.info("=== Testing EntityInfo creation ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SimpleUser.class);
        assertNotNull(entityInfo);
        
        log.info("EntityInfo created successfully for SimpleUser");
        log.info("Total fields: {}", entityInfo.getFields().size());
        
        // 打印所有字段信息
        log.info("All fields:");
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("  Field: {} -> Column: {} (Type: {})", 
                field.getPropertyName(), 
                field.getColumn(),
                field.getPropertyType().getSimpleName());
        }
    }

    @Test
    void testColumnMapping() {
        log.info("=== Testing column mapping ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SimpleUser.class);
        
        // 测试各种列名查找
        String[] testColumns = {
            "user_id", "USER_ID", "userId",
            "user_name", "USER_NAME", "userName", 
            "first_name", "firstName", "FIRST_NAME",
            "last_name", "lastName", "LAST_NAME"
        };
        
        for (String column : testColumns) {
            EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn(column);
            log.info("Column '{}' -> {}", column, 
                fieldInfo != null ? fieldInfo.getPropertyName() : "NOT FOUND");
        }
    }

    @Test
    void testFieldInfoFunctionality() throws Exception {
        log.info("=== Testing FieldInfo functionality ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SimpleUser.class);
        EntityInfo.FieldInfo userNameField = entityInfo.findFieldInfoByColumn("user_name");
        
        assertNotNull(userNameField, "Should find user_name field");
        
        SimpleUser user = new SimpleUser();
        
        // 测试属性设置
        userNameField.setPropertyValue(user, "Test User");
        assertEquals("Test User", user.getUserName());
        
        // 测试属性获取
        Object value = userNameField.getPropertyValue(user);
        assertEquals("Test User", value);
        
        log.info("FieldInfo functionality test passed");
    }

    @Test
    void testPropertyDescriptorCaching() throws Exception {
        log.info("=== Testing PropertyDescriptor caching ===");
        
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(SimpleUser.class);
        EntityInfo.FieldInfo userNameField = entityInfo.findFieldInfoByColumn("user_name");
        
        assertNotNull(userNameField);
        
        // 第一次获取
        long start1 = System.nanoTime();
        java.beans.PropertyDescriptor pd1 = userNameField.getPropertyDescriptor();
        long time1 = System.nanoTime() - start1;
        
        // 第二次获取（应该从缓存）
        long start2 = System.nanoTime();
        java.beans.PropertyDescriptor pd2 = userNameField.getPropertyDescriptor();
        long time2 = System.nanoTime() - start2;
        
        assertSame(pd1, pd2, "Should return same PropertyDescriptor instance");
        assertTrue(time2 < time1 / 2, "Second call should be much faster");
        
        log.info("PropertyDescriptor caching test passed. First: {}ns, Second: {}ns", time1, time2);
    }
}
