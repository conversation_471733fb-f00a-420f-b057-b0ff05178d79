package com.lg.financecloud.common.data.datascope;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

@Slf4j
public class DeletePhysical extends AbstractMethod {

	protected DeletePhysical() {
		super("deletePhysical");
	}

	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
		// 强制使用物理删除，忽略逻辑删除
		final String sql = "<script>DELETE FROM " + tableInfo.getTableName() + 
			"<if test=\"ew != null and ew.sqlSegment != null and ew.sqlSegment != ''\">" +
			" WHERE ${ew.sqlSegment}" +
			"</if></script>";
		SqlSource sqlSource = super.createSqlSource(configuration, sql, modelClass);
		return this.addDeleteMappedStatement(mapperClass, methodName, sqlSource);
	}
}
