package com.lg.dao.core.transaction;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;

import java.util.function.Supplier;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 增强的事务辅助类
 */
@Slf4j
public class TransactionHelper {
    private final PlatformTransactionManager transactionManager;
    private final TransactionTemplate transactionTemplate;

    public TransactionHelper(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    /**
     * 在事务中执行操作
     */
    public <T> T inTransaction(Supplier<T> action) {
        return inTransaction(action, Propagation.REQUIRED, Isolation.DEFAULT, false);
    }

    /**
     * 在事务中执行操作（无返回值）
     */
    public void inTransaction(Runnable action) {
        inTransaction(() -> {
            action.run();
            return null;
        });
    }

    /**
     * 在新事务中执行操作
     */
    public <T> T inNewTransaction(Supplier<T> action) {
        return inTransaction(action, Propagation.REQUIRES_NEW, Isolation.DEFAULT, false);
    }

    /**
     * 在只读事务中执行操作
     */
    public <T> T inReadOnlyTransaction(Supplier<T> action) {
        return inTransaction(action, Propagation.REQUIRED, Isolation.DEFAULT, true);
    }

    /**
     * 在指定传播行为和隔离级别的事务中执行操作
     */
    public <T> T inTransaction(Supplier<T> action, Propagation propagation, Isolation isolation, boolean readOnly) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(propagation.value());
        def.setIsolationLevel(isolation.value());
        def.setReadOnly(readOnly);
        
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            T result = action.get();
            commit(status);
            return result;
        } catch (Exception e) {
            rollback(status);
            throw e;
        }
    }

    /**
     * 批量事务操作
     */
    public <T> void batchInTransaction(Iterable<T> items, java.util.function.Consumer<T> action, int batchSize) {
        inTransaction(() -> {
            int count = 0;
            for (T item : items) {
                action.accept(item);
                if (++count % batchSize == 0) {
                    // 可以在这里添加批量提交逻辑

                }
            }
        });
    }

    private void commit(TransactionStatus status) {
        try {
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("Error committing transaction", e);
            throw e;
        }
    }

    private void rollback(TransactionStatus status) {
        try {
            transactionManager.rollback(status);
        } catch (Exception e) {
            log.error("Error rolling back transaction", e);
        }
    }

    /**
     * 异步事务执行
     */
    public <T> CompletableFuture<T> inTransactionAsync(Supplier<T> action, Executor executor) {
        return CompletableFuture.supplyAsync(() -> inTransaction(action), executor);
    }

    /**
     * 嵌套事务执行
     */
    public <T> T inNestedTransaction(Supplier<T> action) {
        return inTransaction(action, Propagation.NESTED, Isolation.DEFAULT, false);
    }

    /**
     * 使用TransactionTemplate执行事务
     */
    public <T> T executeInTransaction(Supplier<T> action) {
        return transactionTemplate.execute(status -> action.get());
    }

    /**
     * 使用TransactionTemplate执行事务（无返回值）
     */
    public void executeInTransaction(Runnable action) {
        transactionTemplate.executeWithoutResult(status -> action.run());
    }

    /**
     * 条件事务执行
     */
    public <T> T inTransactionIf(boolean condition, Supplier<T> transactionalAction, Supplier<T> nonTransactionalAction) {
        if (condition) {
            return inTransaction(transactionalAction);
        } else {
            return nonTransactionalAction.get();
        }
    }

    /**
     * 重试事务执行
     */
    public <T> T inTransactionWithRetry(Supplier<T> action, int maxRetries) {
        Exception lastException = null;
        for (int i = 0; i <= maxRetries; i++) {
            try {
                return inTransaction(action);
            } catch (Exception e) {
                lastException = e;
                if (i < maxRetries) {
                    log.warn("Transaction failed, retrying... attempt {}/{}", i + 1, maxRetries + 1, e);
                    try {
                        Thread.sleep(100 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Transaction retry interrupted", ie);
                    }
                }
            }
        }
        throw new RuntimeException("Transaction failed after " + (maxRetries + 1) + " attempts", lastException);
    }

    /**
     * 获取当前事务状态
     */
    public boolean isTransactionActive() {
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_SUPPORTS);
            TransactionStatus status = transactionManager.getTransaction(def);
            boolean isActive = status != null && !status.isCompleted();
            // 如果开启了新事务，需要回滚以释放资源
            if (status != null && status.isNewTransaction()) {
                transactionManager.rollback(status);
            }
            return isActive;
        } catch (Exception e) {
            log.warn("Error checking transaction status", e);
            return false;
        }
    }
}