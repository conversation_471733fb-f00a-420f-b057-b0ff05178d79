package com.lg.financecloud.common.data.datafilter;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 封装table查询数据
 *
 * <AUTHOR>
 * @date 2018/7/17 18:42
 * @return
 */
public class SoulPage<T> {

    private static final String[] SQL_SPECIAL_CHARS = {"'"};
    private static final String ESCAPE_CHAR = "''";

    public static String escapeSql(String input) {
        if (StrUtil.isBlank(input)) {
            return input;
        }

        for (String specialChar : SQL_SPECIAL_CHARS) {
            input = input.replace(specialChar, ESCAPE_CHAR );
        }

        return input;
    }


    @JsonIgnore
    private Map<String, String> fieldMapping = new HashMap<>();

    /**
     * 请求参数⬇⬇⬇⬇⬇⬇
     */



    /**
     * 表格列类型
     */
    @JsonIgnore
    private String tableFilterType;
    /**
     * 筛选信息
     */
    @JsonIgnore
    private String filterSos;

    @Getter
    @Setter
    private String tableCode;



    /**
     * 排序信息
     */
    @JsonIgnore
    private String field;
    @JsonIgnore
    private String order = "";
    private List<FilterSo> filterSoList;

    public SoulPage () {

    }

    public List<FilterSo> getFilterSos() {
        if(this.filterSoList==null){
            this.filterSoList = JSONUtil.toList(filterSos, FilterSo.class);
            if(this.filterSoList!=null&& this.filterSoList.size()>0){
                this.filterSoList.forEach(filterSo -> {
                    filterSo.setValue(SoulPage.escapeSql(filterSo.getValue()));
                    if(filterSo.getValues()!=null&& filterSo.getValues().size()>0){
                        filterSo.getValues().forEach(value -> {
                            value = SoulPage.escapeSql(value);
                        });
                    }
                });
            }
        }
        return this.filterSoList;
    }

    public void setFilterSos(String filterSos) {
        this.filterSos = filterSos;
    }






    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }



    public String getTableFilterType() {
        return tableFilterType;
    }

    public void setTableFilterType(String tableFilterType) {
        this.tableFilterType = tableFilterType;
    }


    /**
     * 结构化 Filter type
     * @return
     */
    @JsonIgnore
    public Map<String, Map<String, String>> getTypeMap() {
        Map<String, Map<String, String>> typeMap = new HashMap<>();
        if (StrUtil.isNotEmpty(tableFilterType)) {
            Map<String, String> filterType = JSONUtil.toBean(tableFilterType, Map.class);
            filterType.forEach((k,v)->{
                Map<String, String> map = new HashMap<>();
                map.put("type", v.substring(0, v.indexOf("[")));
                map.put("value",v.substring(v.indexOf("[")+1, v.indexOf("]")) );
                typeMap.put(k, map);
            });
        }
        return typeMap;
    }

    /**
     * 根据类型转换最终的值
     * @param typeMap
     * @param column
     * @param columnObject
     * @return
     */
    public String getFormatValue (Map<String, Map<String, String>> typeMap, String column, Object columnObject) {
        String columnValue;
        if (typeMap.containsKey(column)) {
            if ("date".equalsIgnoreCase(typeMap.get(column).get("type")) && columnObject instanceof Date) {
                columnValue = dateFormat((Date) columnObject, typeMap.get(column).get("value"));
            } else {
                columnValue = (String) columnObject;
            }
        } else {
            if (columnObject instanceof Date) {
                columnValue = dateFormat((Date) columnObject, null);
            } else {
                columnValue = String.valueOf(columnObject) ;
            }
        }
        return columnValue;
    }

    /**
     * 是否是列查询
     * @return
     */
    public boolean isColumn () {
        return false;
    }



    private String dateFormat(Date date, String format) {
        if (StrUtil.isBlank(format)) {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        } else {
            return new SimpleDateFormat(format).format(date);
        }
    }


    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }
}