package com.lg.financecloud.common.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI响应模型
 * 标准化的AI响应结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIResponse {
    
    /**
     * 响应状态码
     */
    private Integer code;


    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应内容
     */
    private String content;
    
    /**
     * 模型类型
     */
    private String modelType;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 是否为流式响应的一部分
     */
    @Builder.Default
    private Boolean isPartial = false;
    
    /**
     * 流式响应的序列号
     * 用于客户端排序和组装完整响应
     */
    private Integer sequenceNumber;
    
    /**
     * 是否为流式响应的最后一部分
     */
    @Builder.Default
    private Boolean isLast = false;
    private long createdAt;

    /**
     * 创建成功响应
     * 
     * @param content 响应内容
     * @param modelType 模型类型
     * @param modelName 模型名称
     * @return 成功的响应对象
     */
    public static AIResponse success(String content, String modelType, String modelName) {
        return AIResponse.builder()
                .code(200)
                .message("成功")
                .content(content)
                .modelType(modelType)
                .modelName(modelName)
                .build();
    }
    
    /**
     * 创建流式部分响应
     * 
     * @param content 部分响应内容
     * @param modelType 模型类型
     * @param modelName 模型名称
     * @param sequenceNumber 序列号
     * @param isLast 是否为最后一部分
     * @return 流式部分响应对象
     */
    public static AIResponse streamPart(String content, String modelType, String modelName, 
                                        Integer sequenceNumber, Boolean isLast) {
        return AIResponse.builder()
                .code(200)
                .message("成功")
                .content(content)
                .modelType(modelType)
                .modelName(modelName)
                .isPartial(true)
                .sequenceNumber(sequenceNumber)
                .isLast(isLast)
                .build();
    }
    
    /**
     * 创建错误响应
     * 
     * @param code 错误代码
     * @param message 错误消息
     * @return 错误的响应对象
     */
    public static AIResponse error(Integer code, String message) {
        return AIResponse.builder()
                .code(code)
                .message(message)
                .build();
    }

    String requestId;
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestId() {
        return requestId;
    }
    TokenUsage usage;
    public void setUsage(TokenUsage usage) {
        this.usage = usage;
    }

    public TokenUsage getUsage() {
        return usage;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getCreatedAt() {
        return createdAt;
    }




}