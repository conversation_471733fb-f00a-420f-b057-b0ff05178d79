package com.lg.dao.config;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.cache.CacheMonitor;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 缓存自动配置类
 * 统一管理框架中的所有缓存
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableScheduling
@EnableConfigurationProperties(CacheProperties.class)
@ConditionalOnProperty(prefix = "light.orm.cache", name = "enable", havingValue = "true", matchIfMissing = true)
public class CacheAutoConfiguration {

    /**
     * 配置统一缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public UnifiedCacheManager unifiedCacheManager() {
        log.info("配置统一缓存管理器");
        return new UnifiedCacheManager();
    }
    
    /**
     * 配置缓存监控
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheMonitor cacheMonitor(UnifiedCacheManager unifiedCacheManager) {
        log.info("配置缓存监控");
        return new CacheMonitor();
    }

    /**
     * 缓存清理任务
     * 每小时执行一次缓存清理，移除过期条目
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cacheCleanupTask() {
        try {
            UnifiedCacheManager cacheManager = unifiedCacheManager();
            cacheManager.cleanup();
            log.debug("执行缓存清理任务完成");
        } catch (Exception e) {
            log.error("缓存清理任务执行失败", e);
        }
    }

    /**
     * 缓存统计任务
     * 每6小时打印一次缓存统计信息
     */
    @Scheduled(fixedRate = 21600000) // 6小时
    public void cacheStatsTask() {
        try {
            UnifiedCacheManager cacheManager = unifiedCacheManager();
            cacheManager.printStats();
        } catch (Exception e) {
            log.error("缓存统计任务执行失败", e);
        }
    }
}
