package com.lg.dao.core;

import com.lg.dao.config.properties.CacheProperties;
import com.lg.dao.core.cache.UnifiedCacheManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.jdbc.support.JdbcUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * EntityRowMapper性能测试
 * 验证预构建映射表优化的效果
 */
@Slf4j
public class EntityRowMapperPerformanceTest {

    private EntityRowMapper<TestUser> rowMapper;
    private UnifiedCacheManager cacheManager;

    private CacheProperties cacheProperties;

    @BeforeEach
    void setUp() throws Exception {
        cacheProperties = new CacheProperties();
        cacheProperties.setEnable(true);

        cacheManager = new UnifiedCacheManager(cacheProperties);
        cacheManager.afterPropertiesSet();

        // 初始化 EntityInfoManager 的缓存管理器
        EntityInfoManager.initializeCacheManager(cacheManager);
        rowMapper = new EntityRowMapper<>(TestUser.class, cacheManager);
    }

    /**
     * 测试实体类
     */
    @Data
    @Table(name = "test_user")
    public static class TestUser {
        @Id
        @Column(name = "user_id")
        private Long userId;
        
        @Column(name = "user_name")
        private String userName;
        
        @Column(name = "email_address")
        private String emailAddress;
        
        @Column(name = "phone_number")
        private String phoneNumber;
        
        @Column(name = "create_time")
        private java.util.Date createTime;
        
        @Column(name = "is_active")
        private Boolean isActive;
        
        // 测试驼峰命名
        private String firstName;
        private String lastName;
        private Integer age;
    }

    @Test
    void testBasicMappingWithoutCache() throws SQLException {
        // 创建不使用缓存的EntityRowMapper
        EntityRowMapper<TestUser> simpleRowMapper = new EntityRowMapper<>(TestUser.class);

        // 先测试EntityInfo的映射表构建
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestUser.class);
        log.info("EntityInfo created for TestUser");

        // 打印所有字段信息用于调试
        log.info("All fields in TestUser:");
        for (EntityInfo.FieldInfo field : entityInfo.getFields()) {
            log.info("  Field: {} -> Column: {}", field.getPropertyName(), field.getColumn());
        }

        // 测试字段映射
        EntityInfo.FieldInfo userIdField = entityInfo.findFieldInfoByColumn("user_id");
        log.info("user_id field mapping: {}", userIdField != null ? userIdField.getPropertyName() : "null");

        // 创建模拟的ResultSet
        ResultSet rs = createMockResultSet();

        // 执行映射
        TestUser user = simpleRowMapper.mapRow(rs, 1);

        // 验证结果
        assertNotNull(user);
        log.info("Mapped user: userId={}, userName={}", user.getUserId(), user.getUserName());

        assertEquals(1L, user.getUserId());
        assertEquals("testuser", user.getUserName());

        log.info("Basic mapping without cache test passed");
    }

    @Test
    void testBasicMapping() throws SQLException {
        // 先测试EntityInfo的映射表构建
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestUser.class);
        log.info("EntityInfo created for TestUser");

        // 测试字段映射
        EntityInfo.FieldInfo userIdField = entityInfo.findFieldInfoByColumn("user_id");
        log.info("user_id field mapping: {}", userIdField != null ? userIdField.getPropertyName() : "null");

        EntityInfo.FieldInfo userNameField = entityInfo.findFieldInfoByColumn("user_name");
        log.info("user_name field mapping: {}", userNameField != null ? userNameField.getPropertyName() : "null");

        // 创建模拟的ResultSet
        ResultSet rs = createMockResultSet();

        // 执行映射
        TestUser user = rowMapper.mapRow(rs, 1);

        // 验证结果
        assertNotNull(user);
        log.info("Mapped user: userId={}, userName={}", user.getUserId(), user.getUserName());

        assertEquals(1L, user.getUserId());
        assertEquals("testuser", user.getUserName());
        assertEquals("<EMAIL>", user.getEmailAddress());
        assertEquals("1234567890", user.getPhoneNumber());
        assertTrue(user.getIsActive());
        assertEquals("John", user.getFirstName());
        assertEquals("Doe", user.getLastName());
        assertEquals(25, user.getAge());

        log.info("Basic mapping test passed");
    }

    @Test
    void testPerformanceComparison() throws SQLException {
        // 创建一次Mock对象，避免重复创建开销
        ResultSet rs = createMockResultSet();

        // 预热
        for (int i = 0; i < 100; i++) {
            rowMapper.mapRow(rs, i);
        }

        // 性能测试
        int iterations = 10000;
        long startTime = System.nanoTime();

        for (int i = 0; i < iterations; i++) {
            TestUser user = rowMapper.mapRow(rs, i);
            assertNotNull(user);
        }

        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        double avgTimePerMapping = duration / (double) iterations / 1_000_000; // 转换为毫秒

        log.info("Performance test completed:");
        log.info("Total iterations: {}", iterations);
        log.info("Total time: {} ms", duration / 1_000_000);
        log.info("Average time per mapping: {:.4f} ms", avgTimePerMapping);

        // 验证性能（考虑到包含9个字段的映射，0.3ms是合理的）
        assertTrue(avgTimePerMapping < 0.3, "Average mapping time should be less than 0.3ms for 9-field entity");
    }

    @Test
    void testPerformanceBenchmark() throws SQLException {
        log.info("=== Performance Benchmark Analysis ===");

        ResultSet rs = createMockResultSet();
        int iterations = 10000;

        // 1. 测试纯对象创建开销
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            TestUser user = new TestUser();
        }
        long objectCreationTime = System.nanoTime() - startTime;
        double avgObjectCreation = objectCreationTime / (double) iterations / 1_000_000;
        log.info("Pure object creation: {} ms/op", String.format("%.4f", avgObjectCreation));

        // 2. 测试ResultSet访问开销
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            rs.getObject(1);
            rs.getObject(2);
            rs.getObject(3);
            rs.getObject(4);
            rs.getObject(5);
            rs.getObject(6);
            rs.getObject(7);
            rs.getObject(8);
            rs.getObject(9);
        }
        long resultSetAccessTime = System.nanoTime() - startTime;
        double avgResultSetAccess = resultSetAccessTime / (double) iterations / 1_000_000;
        log.info("ResultSet access (9 fields): {} ms/op", String.format("%.4f", avgResultSetAccess));

        // 3. 测试完整映射开销
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            TestUser user = rowMapper.mapRow(rs, i);
        }
        long fullMappingTime = System.nanoTime() - startTime;
        double avgFullMapping = fullMappingTime / (double) iterations / 1_000_000;
        log.info("Full mapping (9 fields): {} ms/op", String.format("%.4f", avgFullMapping));

        // 4. 计算映射逻辑的纯开销
        double mappingLogicOverhead = avgFullMapping - avgObjectCreation - avgResultSetAccess;
        log.info("Mapping logic overhead: {} ms/op", String.format("%.4f", mappingLogicOverhead));

        log.info("=== Benchmark Summary ===");
        log.info("Object creation: {}%", String.format("%.1f", (avgObjectCreation / avgFullMapping) * 100));
        log.info("ResultSet access: {}%", String.format("%.1f", (avgResultSetAccess / avgFullMapping) * 100));
        log.info("Mapping logic: {}%", String.format("%.1f", (mappingLogicOverhead / avgFullMapping) * 100));
    }

    @Test
    void testEntityInfoMappingTable() {
        // 测试EntityInfo的映射表构建
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestUser.class);

        // 验证映射表已构建
        assertNotNull(entityInfo);

        // 测试列名到字段信息的映射
        EntityInfo.FieldInfo fieldInfo1 = entityInfo.findFieldInfoByColumn("user_id");
        assertNotNull(fieldInfo1);
        assertEquals("userId", fieldInfo1.getPropertyName());
        assertEquals("user_id", fieldInfo1.getColumn());
        assertTrue(fieldInfo1.isId());

        // 测试驼峰命名映射
        EntityInfo.FieldInfo fieldInfo2 = entityInfo.findFieldInfoByColumn("first_name");
        assertNotNull(fieldInfo2);
        assertEquals("firstName", fieldInfo2.getPropertyName());

        // 测试大小写不敏感
        EntityInfo.FieldInfo fieldInfo3 = entityInfo.findFieldInfoByColumn("USER_NAME");
        assertNotNull(fieldInfo3);
        assertEquals("userName", fieldInfo3.getPropertyName());

        // 测试去前缀匹配
        EntityInfo.FieldInfo fieldInfo4 = entityInfo.findFieldInfoByColumn("test_user_id");
        // 这个可能找不到，取决于具体的去前缀逻辑

        log.info("EntityInfo mapping table test passed");
    }

    @Test
    void testFieldInfoPerformance() throws Exception {
        EntityInfo entityInfo = EntityInfoManager.getInstance().getEntityInfo(TestUser.class);
        EntityInfo.FieldInfo fieldInfo = entityInfo.findFieldInfoByColumn("user_name");

        assertNotNull(fieldInfo);

        TestUser user = new TestUser();

        // 测试属性设置性能
        long startTime = System.nanoTime();

        for (int i = 0; i < 10000; i++) {
            fieldInfo.setPropertyValue(user, "testuser" + i);
        }

        long endTime = System.nanoTime();
        double avgTime = (endTime - startTime) / 10000.0 / 1_000_000; // 毫秒

        log.info("Property setting performance: {:.6f} ms per operation", avgTime);

        // 验证最终值
        assertEquals("testuser9999", user.getUserName());

        // 性能应该很好
        assertTrue(avgTime < 0.1, "Property setting should be very fast");
    }

    private ResultSet createMockResultSet() throws SQLException {
        ResultSet rs = mock(ResultSet.class);
        ResultSetMetaData rsmd = mock(ResultSetMetaData.class);
        
        // 设置列数
        when(rsmd.getColumnCount()).thenReturn(9);
        
        // 设置列名
        when(rsmd.getColumnName(1)).thenReturn("user_id");
        when(rsmd.getColumnName(2)).thenReturn("user_name");
        when(rsmd.getColumnName(3)).thenReturn("email_address");
        when(rsmd.getColumnName(4)).thenReturn("phone_number");
        when(rsmd.getColumnName(5)).thenReturn("is_active");
        when(rsmd.getColumnName(6)).thenReturn("first_name");
        when(rsmd.getColumnName(7)).thenReturn("last_name");
        when(rsmd.getColumnName(8)).thenReturn("age");
        when(rsmd.getColumnName(9)).thenReturn("create_time");
        
        // 设置列标签（JdbcUtils.lookupColumnName会使用这个）
        when(rsmd.getColumnLabel(1)).thenReturn("user_id");
        when(rsmd.getColumnLabel(2)).thenReturn("user_name");
        when(rsmd.getColumnLabel(3)).thenReturn("email_address");
        when(rsmd.getColumnLabel(4)).thenReturn("phone_number");
        when(rsmd.getColumnLabel(5)).thenReturn("is_active");
        when(rsmd.getColumnLabel(6)).thenReturn("first_name");
        when(rsmd.getColumnLabel(7)).thenReturn("last_name");
        when(rsmd.getColumnLabel(8)).thenReturn("age");
        when(rsmd.getColumnLabel(9)).thenReturn("create_time");
        
        when(rs.getMetaData()).thenReturn(rsmd);
        
        // 设置值
        when(rs.getObject(1)).thenReturn(1L);
        when(rs.getObject(2)).thenReturn("testuser");
        when(rs.getObject(3)).thenReturn("<EMAIL>");
        when(rs.getObject(4)).thenReturn("1234567890");
        when(rs.getObject(5)).thenReturn(true);
        when(rs.getObject(6)).thenReturn("John");
        when(rs.getObject(7)).thenReturn("Doe");
        when(rs.getObject(8)).thenReturn(25);
        when(rs.getObject(9)).thenReturn(new java.util.Date());
        
        return rs;
    }
}
