<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lg</groupId>
        <artifactId>financecloud-common</artifactId>
        <version>3.10.1</version>
    </parent>

    <artifactId>financecloud-common-log</artifactId>
    <packaging>jar</packaging>
    <description>financecloud 日志服务</description>

    <dependencies>
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.lg</groupId>
            <artifactId>financecloud-common-dao</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
