# Magic API 集成指南

## 📋 概述

本文档介绍如何在低代码平台中集成Magic API，实现动态接口管理能力。Magic API提供了完整的可视化接口开发环境，可以通过简单的配置快速创建和管理API接口。

## 🚀 快速开始

### 1. 启动应用

```bash
# 启动低代码平台
cd financecloud-lowcode-platform
mvn spring-boot:run
```

### 2. 访问Magic API管理界面

启动成功后，可以通过以下方式访问Magic API：

- **直接访问**: http://localhost:8080/magic/web
- **通过系统菜单**: 登录系统 → 低代码平台 → 动态接口管理 → 接口管理

### 3. 默认登录信息

- **用户名**: admin
- **密码**: 123456

## 🎯 功能特性

### 1. 可视化接口开发
- 在线SQL编辑器，支持语法高亮和自动补全
- 实时调试和测试功能
- 参数化查询支持
- 多数据源管理

### 2. 完整的管理功能
- 接口分组管理
- 版本控制和历史记录
- 接口文档自动生成
- 权限控制和访问限制

### 3. 与低代码平台集成
- 数据看板数据源
- 报表组件数据提供
- 统一的API调用接口
- 菜单系统集成

## 📊 使用示例

### 1. 创建简单查询接口

1. 访问Magic API管理界面
2. 点击"新建接口"
3. 配置接口信息：
   ```
   接口名称: 用户列表查询
   请求路径: /users
   请求方法: GET
   ```
4. 编写SQL脚本：
   ```javascript
   var pageNum = request.getParameter('pageNum') || 1;
   var pageSize = request.getParameter('pageSize') || 10;
   var keyword = request.getParameter('keyword') || '';
   
   var sql = 'SELECT id, username, email, create_time FROM sys_user WHERE del_flag = 0';
   var params = [];
   
   if(keyword) {
       sql += ' AND (username LIKE ? OR email LIKE ?)';
       params.push('%' + keyword + '%');
       params.push('%' + keyword + '%');
   }
   
   sql += ' ORDER BY create_time DESC LIMIT ?, ?';
   params.push((pageNum - 1) * pageSize);
   params.push(pageSize);
   
   var users = db.select(sql, params);
   var total = db.selectInt('SELECT COUNT(*) FROM sys_user WHERE del_flag = 0');
   
   return {
       'records': users,
       'total': total,
       'pageNum': pageNum,
       'pageSize': pageSize
   };
   ```
5. 保存并测试接口

### 2. 创建数据看板统计接口

```javascript
var startDate = request.getParameter('startDate') || '';
var endDate = request.getParameter('endDate') || '';

// 构建日期条件
var dateCondition = '';
var params = [];
if(startDate && endDate) {
    dateCondition = ' AND DATE(create_time) BETWEEN ? AND ?';
    params.push(startDate);
    params.push(endDate);
}

// 查询看板总数
var totalDashboards = db.selectInt('SELECT COUNT(*) FROM lc_dashboard WHERE del_flag = 0');

// 查询已发布看板数
var publishedDashboards = db.selectInt('SELECT COUNT(*) FROM lc_dashboard WHERE status = "PUBLISHED" AND del_flag = 0');

// 查询访问量统计
var accessStats = db.select('SELECT DATE(access_time) as date, COUNT(*) as count FROM lc_dashboard_access_log WHERE 1=1' + dateCondition + ' GROUP BY DATE(access_time) ORDER BY date', params);

return {
    'summary': {
        'totalDashboards': totalDashboards,
        'publishedDashboards': publishedDashboards,
        'publishRate': totalDashboards > 0 ? Math.round(publishedDashboards * 100 / totalDashboards) : 0
    },
    'accessTrend': accessStats
};
```

## 🔧 配置说明

### 1. 应用配置 (application.yml)

```yaml
# Magic API 配置
magic-api:
  # 接口路径前缀
  prefix: /magic/api
  # 页面入口路径
  web: /magic/web
  # 数据库配置
  datasource: 
    default: default
  # 安全配置
  security-config:
    username: admin
    password: 123456
  # 缓存配置
  cache-config:
    capacity: 10000
    ttl: 30
  # 存储配置
  resource:
    type: database
    database:
      table-prefix: magic_api_
      auto-create-table: true
```

### 2. 数据源配置

Magic API会自动使用应用的默认数据源，也可以配置多个数据源：

```yaml
magic-api:
  datasource:
    # 默认数据源
    default: default
    # 其他数据源
    report: report_datasource
    analytics: analytics_datasource
```

## 🔗 与低代码平台集成

### 1. 数据看板集成

在数据看板组件中使用Magic API作为数据源：

```javascript
// 组件配置
{
  "queryConfig": {
    "queryType": "MAGIC_API",
    "apiPath": "/dashboard/stats",
    "params": {
      "startDate": "2024-01-01",
      "endDate": "2024-12-31"
    }
  }
}
```

### 2. 报表组件集成

```java
// 在报表组件中调用Magic API
@Autowired
private MagicApiIntegrationService magicApiService;

public List<Map<String, Object>> getReportData(String reportId, Map<String, Object> params) {
    String apiPath = "/report/" + reportId;
    Object result = magicApiService.getReportData(apiPath, params);
    return (List<Map<String, Object>>) result;
}
```

### 3. 自定义API调用

```java
// 通过集成服务调用Magic API
@RestController
public class CustomController {
    
    @Autowired
    private MagicApiIntegrationService magicApiService;
    
    @GetMapping("/custom/data")
    public Object getCustomData(@RequestParam Map<String, Object> params) {
        return magicApiService.executeApi("/custom/data", params);
    }
}
```

## 📚 API文档

### 1. 自动生成文档

Magic API会自动为创建的接口生成文档，访问路径：
- http://localhost:8080/magic/web#/doc

### 2. 接口调用示例

```bash
# GET请求示例
curl "http://localhost:8080/magic/api/users?pageNum=1&pageSize=10&keyword=admin"

# POST请求示例
curl -X POST "http://localhost:8080/magic/api/dashboard/stats" \
  -H "Content-Type: application/json" \
  -d '{"startDate":"2024-01-01","endDate":"2024-12-31"}'
```

## 🛠️ 高级功能

### 1. 自定义函数

可以创建自定义函数供多个接口复用：

```javascript
// 创建日期格式化函数
function formatDate(date, pattern) {
    // 实现日期格式化逻辑
    return formatted;
}

// 在接口中使用
var formattedDate = formatDate(new Date(), 'yyyy-MM-dd');
```

### 2. 拦截器配置

```java
@Bean
public RequestInterceptor requestInterceptor() {
    return new RequestInterceptor() {
        @Override
        public Object preHandle(ApiInfo info, MagicHttpServletRequest request, MagicHttpServletResponse response) {
            // 权限验证
            String token = request.getHeader("Authorization");
            if (!validateToken(token)) {
                throw new RuntimeException("无效的访问令牌");
            }
            return null;
        }
    };
}
```

### 3. 缓存配置

```javascript
// 在接口中使用缓存
var cacheKey = 'user_list_' + pageNum + '_' + pageSize;
var cachedResult = cache.get(cacheKey);

if (cachedResult) {
    return cachedResult;
}

var result = db.select(sql, params);
cache.put(cacheKey, result, 300); // 缓存5分钟

return result;
```

## 🔒 安全配置

### 1. 访问控制

```yaml
magic-api:
  security:
    enable: true
    type: basic
    basic:
      username: admin
      password: 123456
```

### 2. IP白名单

```java
@Bean
public RequestInterceptor ipWhitelistInterceptor() {
    return new RequestInterceptor() {
        @Override
        public Object preHandle(ApiInfo info, MagicHttpServletRequest request, MagicHttpServletResponse response) {
            String clientIp = getClientIp(request);
            if (!isAllowedIp(clientIp)) {
                throw new RuntimeException("IP地址不在白名单中");
            }
            return null;
        }
    };
}
```

## 📈 监控和日志

### 1. 执行日志

Magic API会自动记录接口执行日志，包括：
- 请求参数
- 执行时间
- 响应结果
- 错误信息

### 2. 性能监控

```java
@Bean
public RequestInterceptor performanceInterceptor() {
    return new RequestInterceptor() {
        @Override
        public Object postHandle(ApiInfo info, MagicHttpServletRequest request, MagicHttpServletResponse response, Object result) {
            long executionTime = System.currentTimeMillis() - request.getStartTime();
            if (executionTime > 1000) {
                log.warn("接口执行时间过长: {} - {}ms", info.getName(), executionTime);
            }
            return result;
        }
    };
}
```

## 🚨 注意事项

### 1. 安全注意事项
- 生产环境必须修改默认密码
- 建议配置IP白名单
- 敏感操作需要添加权限验证

### 2. 性能注意事项
- 合理使用缓存机制
- 避免在接口中执行耗时操作
- 大数据量查询建议分页处理

### 3. 开发建议
- 接口命名要规范，便于管理
- 添加详细的接口描述和参数说明
- 定期备份接口配置

## 🔄 升级和维护

### 1. 版本升级

```xml
<dependency>
    <groupId>org.ssssssss</groupId>
    <artifactId>magic-api-spring-boot-starter</artifactId>
    <version>最新版本</version>
</dependency>
```

### 2. 数据备份

Magic API支持导出接口配置，建议定期备份：
- 在管理界面点击"导出"功能
- 备份数据库中的magic_api_*表

### 3. 故障排查

常见问题及解决方案：
- 接口无法访问：检查路径配置和权限设置
- SQL执行错误：检查数据源配置和SQL语法
- 性能问题：检查缓存配置和查询优化

通过以上配置，Magic API已经完全集成到低代码平台中，可以为数据看板、报表组件以及其他业务模块提供强大的动态接口能力！
