# financecloud-common-dao 配置示例

# Light ORM 配置
light:
  orm:
    # 缓存配置
    cache:
      enable: true                    # 启用缓存
      type: CAFFEINE                  # 缓存类型：CAFFEINE 或 REDIS
      caffeine:
        initialCapacity: 100          # 初始容量
        maximumSize: 10000           # 最大容量
        expireAfterWrite: 3600       # 写入后过期时间（秒）
        expireAfterAccess: 3600      # 访问后过期时间（秒）
        keyPrefix: "light:orm:"      # 缓存key前缀
      redis:
        expireTime: 3600             # Redis过期时间（秒）
        keyPrefix: "light:orm:cache:" # Redis缓存key前缀

    # 数据过滤和权限配置
    filter:
      enable: true                    # 启用数据过滤功能
      enableDataPermission: true      # 启用数据权限拦截器

    # SQL模板配置
    sqlTemplate:
      enable: true                    # 启用SQL模板功能
      enableCache: true               # 启用模板缓存
      cacheSize: 1000                # 模板缓存大小

    # 调试配置（仅开发环境）
    debug:
      enable: false                   # 启用调试功能（生产环境应设为false）

    # 基础配置
    basic:
      enableSqlPrint: false          # 启用SQL打印
      enablePerformanceMonitor: false # 启用性能监控

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

light:
  orm:
    cache:
      enable: true
      type: CAFFEINE
      caffeine:
        maximumSize: 1000
        expireAfterWrite: 300        # 开发环境缓存时间短一些
    filter:
      enable: true
      enableDataPermission: true
    debug:
      enable: true                   # 开发环境启用调试
    basic:
      enableSqlPrint: true          # 开发环境打印SQL
      enablePerformanceMonitor: true

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test

light:
  orm:
    cache:
      enable: false                  # 测试环境可以禁用缓存
    filter:
      enable: true
      enableDataPermission: false    # 测试环境可能不需要数据权限
    debug:
      enable: true
    basic:
      enableSqlPrint: true

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

light:
  orm:
    cache:
      enable: true
      type: CAFFEINE                 # 生产环境推荐使用Caffeine
      caffeine:
        maximumSize: 50000          # 生产环境可以设置更大的缓存
        expireAfterWrite: 7200      # 2小时过期
        expireAfterAccess: 3600     # 1小时无访问过期
    filter:
      enable: true
      enableDataPermission: true
    debug:
      enable: false                  # 生产环境禁用调试
    basic:
      enableSqlPrint: false         # 生产环境不打印SQL
      enablePerformanceMonitor: true # 生产环境启用性能监控

# 日志配置示例
logging:
  level:
    com.lg.dao: INFO                 # DAO框架日志级别
    com.lg.dao.core.cache: DEBUG    # 缓存相关日志
    com.lg.dao.core.interceptor: WARN # 拦截器日志
    com.lg.dao.core.filter: INFO    # 数据过滤日志

# Spring Boot Actuator 配置（用于监控）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,caches
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      simple:
        enabled: true
