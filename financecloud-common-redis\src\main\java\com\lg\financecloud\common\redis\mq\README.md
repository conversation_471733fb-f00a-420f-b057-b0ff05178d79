# Redis MQ 简易消息队列

基于 Redisson 的分布式任务队列实现，支持即时任务、延时任务和定时任务。

## 核心功能

### 基础功能
- **即时任务**: 立即执行的任务
- **延时任务**: 延迟指定时间后执行的任务
- **定时任务**: 按 Cron 表达式定时执行的任务
- **任务状态管理**: 完整的任务生命周期状态追踪
- **数据库持久化**: 任务信息持久化存储，支持查询和统计

### 增强功能
- **任务恢复机制**: 定时检查并恢复长时间处理中的任务
- **失败重试**: 自动重试失败的任务（支持最大重试次数限制）
- **分布式锁**: 多实例环境下确保只有一个实例执行恢复
- **REST API**: 提供完整的任务管理和监控接口
- **监听器增强**: 自动提取监听器信息，支持任务恢复时重新提交

## 核心组件

### 1. RedisJob - 任务提交入口
```java
// 提交即时任务
String taskId = RedisJob.submitJob(task, listener);

// 提交延时任务
String taskId = RedisJob.submitJob(task, listener, Duration.ofMinutes(5));

// 提交定时任务
String taskId = RedisJob.submitScheduleJob(task, listener, "0 0 12 * * ?");

// 查询任务状态
String status = RedisJob.getTaskStatus(taskId);

// 取消任务
boolean success = RedisJob.cancelTask(taskId);
```

### 2. BaseTask - 任务基类
```java
public class MyTask extends BaseTask {
    // 自定义任务属性
    private String customData;

    @Override
    public String queueCode() {
        return "my_task_queue";
    }

    public MyTask() {
        setJobDesc("我的任务");
        setModuleName("my_module");
    }
}
```

### 3. RedisAmqListener - 任务监听器
```java
@Component
public class MyTaskListener extends RedisAmqListener<MyTask> {
    @Override
    protected void execute(MyTask task) {
        // 任务处理逻辑
        log.info("处理任务: {}", task.getTaskIdentity());
        // 业务逻辑处理
    }
}
```

### 4. TaskRecoveryService - 任务恢复服务
```java
@Autowired(required = false)
private TaskRecoveryService taskRecoveryService;

// 手动触发恢复
if (taskRecoveryService != null) {
    taskRecoveryService.manualRecovery();
}
```

## 配置说明

### 基础配置
```yaml
light:
  mq:
    # 是否启用 MQ 功能 (默认: true)
    enabled: true

    # 任务恢复配置
    recovery:
      enabled: true                    # 是否启用恢复功能 (默认: true)
      interval-minutes: 5              # 恢复执行间隔，单位分钟 (默认: 5)
      batch-size: 100                  # 每次恢复处理的任务数量 (默认: 100)
      max-processing-hours: 2          # 最大处理时间，单位小时 (默认: 2)
      waiting-timeout-minutes: 30      # 等待任务超时时间，单位分钟 (默认: 30)
      max-retry-attempts: 3            # 最大重试次数 (默认: 3)
      lock-timeout-minutes: 10         # 分布式锁超时时间，单位分钟 (默认: 10)

      # 恢复 API 配置
      api:
        enabled: true                  # 是否启用恢复 API (默认: true)

    # 任务管理 API 配置
    api:
      enabled: true                    # 是否启用任务管理 API (默认: true)

    # 管理员 API 配置
    admin:
      enabled: false                   # 是否启用管理员 API (默认: false)
```

### 环境配置示例
```yaml
# 开发环境
spring:
  config:
    activate:
      on-profile: dev

light:
  mq:
    recovery:
      enabled: true
      interval-minutes: 3  # 开发环境更频繁的检查
      batch-size: 50       # 开发环境较小的批次

---
# 生产环境
spring:
  config:
    activate:
      on-profile: prod

light:
  mq:
    recovery:
      enabled: true
      interval-minutes: 5
      batch-size: 200      # 生产环境较大的批次
      max-processing-hours: 4  # 生产环境更长的处理时间
      max-retry-attempts: 5    # 生产环境更多的重试次数

---
# 测试环境
spring:
  config:
    activate:
      on-profile: test

light:
  mq:
    recovery:
      enabled: false  # 测试环境可禁用恢复功能
```

## 任务状态说明

| 状态 | 常量 | 说明 |
|------|------|------|
| 等待处理 | `TASK_STATE_WAIT` | 任务已提交，等待执行 |
| 处理中 | `TASK_STATE_PROCESSING` | 任务正在执行 |
| 执行成功 | `TASK_STATE_SUCCESS` | 任务执行完成 |
| 执行失败 | `TASK_STATE_FAIL` | 任务执行失败 |
| 部分成功 | `TASK_STATE_PARTIAL_SUCCESS` | 任务部分成功 |

## 恢复机制说明

### 自动恢复策略
1. **长时间处理任务**: 超过配置时间仍在处理中的任务将被标记为失败
2. **失败任务重试**: 失败次数小于最大重试次数的任务会被自动重试
3. **等待超时任务**: 等待超过配置时间的任务将被标记为失败
4. **定时执行**: 按配置间隔执行恢复检查（默认5分钟）
5. **分布式安全**: 使用分布式锁确保多实例环境下的安全执行

### 监听器增强机制
在提交任务时，系统会自动增强任务信息以便恢复时使用：

```java
// 提交任务时自动添加监听器信息
String taskId = RedisJob.submitJob(task, listener);

// 系统会自动：
// 1. 在 taskData 中添加 _internal_listenerClass 字段（前端不可见）
// 2. 尝试通过反射设置 listenerClass 字段
// 注意：不会修改 jobDesc，避免暴露给前端用户
```

// 批量停止任务
{
  "msgType": "batchStopTask",
  "taskList": [SysTask对象列表]
}

// 手动调度任务
{
  "msgType": "scheduleTask",
  "task": SysTask对象,
  "taskDataJson": "任务参数JSON字符串"
}
## REST API 接口

### 任务管理 API
```bash
# 查询任务详情（已清理敏感信息）
GET /api/mq/task/{taskId}

# 查询任务状态
GET /api/mq/task/{taskId}/status

# 分页查询任务列表
GET /api/mq/task/list?page=1&size=20&status=1&queueCode=xxx

# 取消任务
POST /api/mq/task/{taskId}/cancel

# 重试任务
POST /api/mq/task/{taskId}/retry

# 获取任务统计
GET /api/mq/task/stats
```

### 管理员 API
```bash
# 获取任务完整详情（包含监听器信息）
GET /api/mq/admin/task/{taskId}/detail

# 提取任务监听器信息
GET /api/mq/admin/task/{taskId}/listener

# 强制重试指定任务
POST /api/mq/admin/task/{taskId}/force-retry

# 批量重试失败任务
POST /api/mq/admin/task/batch-retry?limit=10

# 获取系统监控信息
GET /api/mq/admin/monitor
```

### 恢复服务 API
```bash
# 获取恢复服务状态
GET /api/mq/recovery/status

# 手动触发恢复
POST /api/mq/recovery/trigger

# 获取统计信息
GET /api/mq/recovery/stats

# 重置统计信息
POST /api/mq/recovery/stats/reset

# 获取配置信息
GET /api/mq/recovery/config
```

## 使用示例

### 1. 自定义任务类
```java
public class MyBusinessTask extends BaseTask {
    private String businessData;

    @Override
    public String queueCode() {
        return "my_business_queue";
    }

    public MyBusinessTask() {
        setJobDesc("业务处理任务");
        setModuleName("business_module");
    }

    // getter/setter
    public String getBusinessData() { return businessData; }
    public void setBusinessData(String businessData) { this.businessData = businessData; }
}
```

### 2. 自定义监听器
```java
@Component
public class MyBusinessTaskListener extends RedisAmqListener<MyBusinessTask> {

    @Autowired
    private BusinessService businessService;

    @Override
    protected void execute(MyBusinessTask task) {
        log.info("开始处理业务任务: {}", task.getTaskIdentity());

        try {
            // 业务处理逻辑
            businessService.processData(task.getBusinessData());
            log.info("业务任务处理完成: {}", task.getTaskIdentity());

        } catch (Exception e) {
            log.error("业务任务处理失败: {}", task.getTaskIdentity(), e);
            throw e; // 重新抛出异常，让框架标记任务为失败
        }
    }
}
```

### 3. 任务提交示例
```java
@Service
public class TaskSubmitService {

    @Autowired
    private MyBusinessTaskListener taskListener;

    public String submitImmediateTask(String businessData) {
        // 创建任务
        MyBusinessTask task = new MyBusinessTask();
        task.setBusinessData(businessData);

        // 提交即时任务
        return RedisJob.submitJob(task, taskListener);
    }

    public String submitDelayedTask(String businessData, int delayMinutes) {
        // 创建任务
        MyBusinessTask task = new MyBusinessTask();
        task.setBusinessData(businessData);

        // 提交延时任务
        return RedisJob.submitJob(task, taskListener, Duration.ofMinutes(delayMinutes));
    }

    public String submitScheduledTask(String businessData, String cronExpression) {
        // 创建任务
        MyBusinessTask task = new MyBusinessTask();
        task.setBusinessData(businessData);

        // 提交定时任务
        return RedisJob.submitScheduleJob(task, taskListener, cronExpression);
    }
}
```

### 4. 任务状态查询
```java
@RestController
@RequestMapping("/api/business")
public class BusinessTaskController {

    @GetMapping("/task/{taskId}/status")
    public ResponseEntity<String> getTaskStatus(@PathVariable String taskId) {
        String status = RedisJob.getTaskStatus(taskId);
        return ResponseEntity.ok(status);
    }

    @PostMapping("/task/{taskId}/cancel")
    public ResponseEntity<Boolean> cancelTask(@PathVariable String taskId) {
        boolean success = RedisJob.cancelTask(taskId);
        return ResponseEntity.ok(success);
    }
}
```

## 数据库表结构

### job_matemation 任务日志表
```sql
-- 任务执行日志表
CREATE TABLE job_matemation (
    job_id VARCHAR(64) PRIMARY KEY COMMENT '任务ID',
    title VARCHAR(255) COMMENT '任务标题',
    job_type VARCHAR(100) COMMENT '任务类型/队列代码',
    big_type VARCHAR(10) COMMENT '大类型',
    type VARCHAR(10) COMMENT '类型',
    request_body TEXT COMMENT '请求体/任务数据',
    response_body TEXT COMMENT '响应体/错误信息',
    status VARCHAR(10) COMMENT '状态：1-等待,2-处理中,3-失败,4-成功,5-部分成功',
    create_id VARCHAR(64) COMMENT '创建人ID',
    create_ip VARCHAR(50) COMMENT '创建IP',
    create_time VARCHAR(20) COMMENT '创建时间',
    complate_time DATETIME COMMENT '完成时间',
    tenant_id INT COMMENT '租户ID',
    retrys INT DEFAULT 0 COMMENT '重试次数'
);
```

## 安全性说明

### 数据隐私保护
1. **前端API安全**: 用户API自动清理敏感信息，不暴露监听器类名
2. **内部字段隔离**: 监听器信息存储在 `_internal_*` 字段中，前端不可见
3. **权限控制**: 管理员API需要额外的权限验证

### 配置安全
```yaml
# 生产环境推荐配置
light:
  mq:
    recovery:
      enabled: true   # 启用恢复功能
      interval-minutes: 5
      max-retry-attempts: 3
```

## 注意事项

1. **API兼容性**: 所有原有API保持不变，只是增加了新功能
2. **配置可选**: 恢复功能默认启用，可通过配置禁用
3. **性能影响**: 恢复服务每5分钟执行一次，对性能影响很小
4. **数据安全**: 所有操作都有完整的日志记录和异常处理
5. **JDK兼容**: 保持JDK 1.8兼容性
6. **监听器增强**: 任务提交时自动增强监听器信息，支持恢复时重新提交

## 故障排查

### 常见问题
1. **恢复服务未启动**: 检查配置 `light.mq.recovery.enabled`
2. **任务长时间等待**: 检查Redis连接和Redisson配置
3. **重试次数过多**: 检查业务逻辑是否有问题
4. **任务丢失**: 检查数据库连接和事务配置
5. **监听器找不到**: 检查监听器类是否正确注册为Spring Bean

### 日志级别
```yaml
logging:
  level:
    com.lg.financecloud.common.redis.mq: DEBUG
```

### 监控建议
- 定期检查任务统计信息
- 监控失败任务的重试情况
- 关注恢复服务的执行日志
- 设置任务执行时间告警
