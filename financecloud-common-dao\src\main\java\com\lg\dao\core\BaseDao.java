package com.lg.dao.core;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

import com.lg.dao.core.batch.BatchOperationService;
import com.lg.dao.core.builder.SqlBuilderService;
import com.lg.dao.core.cache.UnifiedCacheManager;

import com.lg.dao.core.executor.LightOrmSqlExecutor;
import com.lg.dao.core.query.JoinQueryBuilder;
import com.lg.dao.core.sequence.SequenceManager;
import com.lg.dao.core.sql.SqlBuilder;
import com.lg.dao.core.template.MybatisTemplateEngine;
import com.lg.dao.core.transaction.TransactionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 重构后的基础DAO类 - 职责分离
 */
@Slf4j
public class BaseDao {
    

    
    @Autowired
    protected LightOrmSqlExecutor lightOrmSqlExecutor;
    
    @Autowired
    protected SqlBuilderService sqlBuilderService;
    
    @Autowired
    protected BatchOperationService batchOperationService;
    
    @Autowired(required = false)
    protected TransactionHelper transactionHelper;
    
    @Autowired
    protected SqlTemplateManager sqlTemplateManager;

    @Autowired(required = false)
    protected UnifiedCacheManager unifiedCacheManager;

    public LightOrmSqlExecutor getSqlExecutor() {
        return lightOrmSqlExecutor;
    }


    @Autowired
    @Lazy
    private SequenceManager sequenceManager;

    private DDLHelper ddlHelper;

    public SequenceManager getSequenceManager() {
        return sequenceManager;
    }

    public DDLHelper getDDLHelper() {
        if (ddlHelper == null) {
            ddlHelper = new DDLHelper(lightOrmSqlExecutor.getJdbcTemplate());
        }
        return ddlHelper;
    }

    public SqlTemplateManager getSqlTemplateManager() {
        return sqlTemplateManager;
    }


    /**
     * 在事务中执行操作
     */
    public <T> T executeInTransaction(Supplier<T> action) {
        if (transactionHelper != null) {
            return transactionHelper.inTransaction(action);
        } else {
            // 如果没有事务辅助类，直接执行
            return action.get();
        }
    }
    
    /**
     * 在事务中执行操作（无返回值）
     */
    public void executeInTransaction(Runnable action) {
        if (transactionHelper != null) {
            transactionHelper.inTransaction(action);
        } else {
            action.run();
        }
    }
    
    /**
     * 批量操作（事务支持）
     */
    public <T> int[] batchInsertInTransaction(List<T> entities) {
        return executeInTransaction(() -> {
            if (entities == null || entities.isEmpty()) {
                return new int[0];
            }
            EntityInfo entityInfo = getEntityInfo(entities.get(0).getClass());
            return batchOperationService.batchInsert(entities, entityInfo);
        });
    }
    
    /**
     * 批量更新（事务支持）
     */
    public <T> int[] batchUpdateInTransaction(List<T> entities) {
        return executeInTransaction(() -> {
            if (entities == null || entities.isEmpty()) {
                return new int[0];
            }
            EntityInfo entityInfo = getEntityInfo(entities.get(0).getClass());
            return batchOperationService.batchUpdate(entities, entityInfo);
        });
    }
    
    /**
     * 插入数据
     */
    public <T> int insert(T entity) {
        EntityInfo entityInfo = getEntityInfo(entity.getClass());
        SqlBuilder builder = sqlBuilderService.buildInsertSql(entity, entityInfo);
        return lightOrmSqlExecutor.executeUpdate(builder.getSql(), builder.getParams());
    }
    
    /**
     * 更新数据
     */
    public <T> int update(T entity) {
        EntityInfo entityInfo = getEntityInfo(entity.getClass());
        SqlBuilder builder = sqlBuilderService.buildUpdateSql(entity, entityInfo);
        return lightOrmSqlExecutor.executeUpdate(builder.getSql(), builder.getParams());
    }
    
    /**
     * 删除数据
     */
    public <T> int delete(T entity) {
        EntityInfo entityInfo = getEntityInfo(entity.getClass());
        SqlBuilder builder = sqlBuilderService.buildDeleteSql(entity, entityInfo);
        return lightOrmSqlExecutor.executeUpdate(builder.getSql(), builder.getParams());
    }
    
    /**
     * 查询单条数据
     */
    public <T> T selectOne(Class<T> entityClass, SqlBuilder builder) {
        List<T> list = lightOrmSqlExecutor.executeQuery(builder.getSql(), builder.getParams(), entityClass);
        return list.isEmpty() ? null : list.get(0);
    }
    public Integer queryInteger(String sql, Object... params) {
        List<Integer> integers = lightOrmSqlExecutor.executeQuery(sql, ListUtil.of(params), Integer.class);
        return integers.isEmpty() ? null : integers.get(0);
    }
    public String queryString(String sql, Object... params) {
        List<String> values = lightOrmSqlExecutor.executeQuery(sql, ListUtil.of(params), String.class);
        return values.isEmpty() ? null : values.get(0);
    }

    public List<String> queryStringList(String sql, Object... params) {
        return lightOrmSqlExecutor.executeQuery(sql, ListUtil.of(params), String.class);
    }
    public List<Integer> queryIntegerList(String sql, Object... params) {
        return lightOrmSqlExecutor.executeQuery(sql, ListUtil.of(params), Integer.class);
    }

    /**
     * 查询多条数据
     */
    public <T> List<T> selectList(Class<T> entityClass, SqlBuilder builder) {
        return lightOrmSqlExecutor.executeQuery(builder.getSql(), builder.getParams(), entityClass);
    }
    
    /**
     * 真正的批量插入
     */
    public <T> int[] batchInsert(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return new int[0];
        }
        
        EntityInfo entityInfo = getEntityInfo(entities.get(0).getClass());
        return batchOperationService.batchInsert(entities, entityInfo);
    }
    
    /**
     * 真正的批量更新
     */
    public <T> int[] batchUpdate(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return new int[0];
        }
        
        EntityInfo entityInfo = getEntityInfo(entities.get(0).getClass());
        return batchOperationService.batchUpdate(entities, entityInfo);
    }
    
    /**
     * 分页查询
     */
    public <T> Page<T> selectPage(Class<T> entityClass, SqlBuilder builder, int pageNum, int pageSize) {
        // 查询总数
        String countSql = "SELECT COUNT(*) FROM (" + builder.getSql() + ") t";
        List<Long> countResult = lightOrmSqlExecutor.executeQuery(countSql, builder.getParams(), Long.class);
        Long total = countResult.isEmpty() ? 0L : countResult.get(0);
        
        // 构建分页SQL
        String pageSql = builder.getSql() + " LIMIT " + (pageNum - 1) * pageSize + "," + pageSize;
        List<T> records = lightOrmSqlExecutor.executeQuery(pageSql, builder.getParams(), entityClass);
        
        // 构建分页对象
        Page<T> page = new Page<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(total);
        page.setRecords(records);
        return page;
    }
    
    /**
     * 使用SQL模板查询单条记录
     */
    public <T> T selectOneByTemplate(Class<T> entityClass, String templateName, Map<String, Object> params) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        List<T> list = lightOrmSqlExecutor.executeQuery(result.getSql(), result.getParams(), entityClass);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 使用SQL模板查询列表
     */
    public <T> List<T> selectListByTemplate(Class<T> entityClass, String templateName, Map<String, Object> params) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        return lightOrmSqlExecutor.executeQuery(result.getSql(), result.getParams(), entityClass);
    }

    /**
     * 使用SQL模板查询分页数据
     */
    public <T> Page<T> selectPageByTemplate(Class<T> entityClass, String templateName,
            Map<String, Object> params, int pageNum, int pageSize) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        String sql = result.getSql();
        List<Object> orderedParams = result.getParams();

        // 查询总数
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        List<Long> countResult = lightOrmSqlExecutor.executeQuery(countSql, orderedParams, Long.class);
        Long total = countResult.isEmpty() ? 0L : countResult.get(0);

        // 构建分页SQL
        String pageSql = sql + " LIMIT " + (pageNum - 1) * pageSize + "," + pageSize;
        List<T> records = lightOrmSqlExecutor.executeQuery(pageSql, orderedParams, entityClass);

        // 构建分页对象
        Page<T> page = new Page<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(total);
        page.setRecords(records);
        return page;
    }

    /**
     * 使用SQL模板执行更新操作
     */
    public int executeUpdateByTemplate(String templateName, Map<String, Object> params) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        return lightOrmSqlExecutor.executeUpdate(result.getSql(), result.getParams());
    }

    /**
     * 使用SQL模板执行批量更新操作
     */
    public int[] batchUpdateByTemplate(String templateName, List<Map<String, Object>> paramsList) {
        if (paramsList == null || paramsList.isEmpty()) {
            return new int[0];
        }

        // 使用第一个参数集合来渲染SQL模板
        MybatisTemplateEngine.RenderResult firstResult = sqlTemplateManager.getSqlWithParams(templateName, paramsList.get(0));
        String sql = firstResult.getSql();

        List<List<Object>> batchParams = new ArrayList<>();
        for (Map<String, Object> params : paramsList) {
            // 为每个参数集合获取正确的参数顺序
            MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
            batchParams.add(result.getParams());
        }

        return lightOrmSqlExecutor.executeBatch(sql, batchParams);
    }

    /**
     * 带缓存的模板查询单个对象
     */
    public <T> T selectOneByTemplateWithCache(Class<T> entityClass, String templateName,
            Map<String, Object> params, Object... cacheKeyParts) {
        if (unifiedCacheManager != null) {
            MybatisTemplateEngine.RenderResult sqlWithParams = sqlTemplateManager.getSqlWithParams(templateName, params);
            String cacheKey = "query:" + sqlWithParams.getSql().hashCode();
            if (cacheKeyParts != null && cacheKeyParts.length > 0) {
                for (Object part : cacheKeyParts) {
                    cacheKey += ":" + (part != null ? part.hashCode() : "null");
                }
            }
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey, 
                () -> selectOneByTemplate(entityClass, templateName, params));
        }
        
        return selectOneByTemplate(entityClass, templateName, params);
    }

    /**
     * 带缓存的模板查询列表
     */
    public <T> List<T> selectListByTemplateWithCache(Class<T> entityClass, String templateName,
            Map<String, Object> params, Object... cacheKeyParts) {
        if (unifiedCacheManager != null) {
            MybatisTemplateEngine.RenderResult sqlWithParams = sqlTemplateManager.getSqlWithParams(templateName, params);
            String cacheKey = "query:" + sqlWithParams.getSql().hashCode();
            if (cacheKeyParts != null && cacheKeyParts.length > 0) {
                for (Object part : cacheKeyParts) {
                    cacheKey += ":" + (part != null ? part.hashCode() : "null");
                }
            }
            return unifiedCacheManager.get(UnifiedCacheManager.CacheType.DAO_CACHE, cacheKey, 
                () -> selectListByTemplate(entityClass, templateName, params));
        }
        
        return selectListByTemplate(entityClass, templateName, params);
    }
    
    /**
     * 无实体查询返回Map列表（直接SQL）
     */
    public List<Map<String, Object>> selectMapList(String sql, Object... params) {
        List<Object> paramList = new ArrayList<>();
        if (params != null) {
            for (Object param : params) {
                paramList.add(param);
            }
        }
        return lightOrmSqlExecutor.executeQueryForMap(sql, paramList);
    }

    /**
     * 无实体查询返回单个Map（直接SQL）
     */
    public Map<String, Object> selectOneMap(String sql, Object... params) {
        List<Map<String, Object>> list = selectMapList(sql, params);
        return list.isEmpty() ? null : list.get(0);
    }
    
    /**
     * 查询指定类型的对象列表（直接SQL）
     * 
     * @param sql SQL语句
     * @param clazz 目标类型
     * @param params 参数数组
     * @return 对象列表
     */
    public <T> List<T> selectList(String sql, Class<T> clazz, Object[] params) {
        List<Object> paramList = new ArrayList<>();
        if (params != null) {
            for (Object param : params) {
                paramList.add(param);
            }
        }
        return lightOrmSqlExecutor.executeQuery(sql, paramList, clazz);
    }
    
    /**
     * 查询指定类型的单个对象（直接SQL）
     * 
     * @param sql SQL语句
     * @param clazz 目标类型
     * @param params 参数数组
     * @return 单个对象
     */
    public <T> T selectOne(String sql, Class<T> clazz, Object[] params) {
        List<T> list = selectList(sql, clazz, params);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 使用SQL模板无实体查询返回Map列表
     */
    public List<Map<String, Object>> selectMapListByTemplate(String templateName, Map<String, Object> params) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        return lightOrmSqlExecutor.executeQueryForMap(result.getSql(), result.getParams());
    }

    /**
     * 使用SQL模板无实体查询返回单个Map
     */
    public Map<String, Object> selectOneMapByTemplate(String templateName, Map<String, Object> params) {
        List<Map<String, Object>> list = selectMapListByTemplate(templateName, params);
        return list.isEmpty() ? null : list.get(0);
    }
    
    // ==================== JSON查询方法 ====================
    
    /**
     * 执行SQL查询返回JSONArray（支持驼峰命名转换）
     *
     * @param sql SQL语句
     * @param params 参数数组
     * @param toCamelCase 是否转换为驼峰命名
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArray(String sql, Object[] params, boolean toCamelCase) {
        List<Object> paramList = params != null ? ListUtil.of(params) :  ListUtil.of();
        return lightOrmSqlExecutor.executeQueryForJson(sql, paramList, toCamelCase);
    }
    
    /**
     * 执行SQL查询返回JSONArray（默认驼峰命名转换）
     *
     * @param sql SQL语句
     * @param params 参数数组
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArray(String sql, Object... params) {
        return selectJsonArray(sql, params, true);
    }
    public List<JSONObject> selectJsonList(String sql, Object... params) {
        JSONArray jsonArray = selectJsonArray(sql, params, true);
        if(jsonArray == null|| jsonArray.isEmpty()){
            return ListUtil.empty();
        }
        return jsonArray.toList(JSONObject.class);
    }

    /**
     * 执行SQL查询返回JSONArray（支持驼峰命名转换）
     *
     * @param sql SQL语句
     * @param paramList 参数列表
     * @param toCamelCase 是否转换为驼峰命名
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArray(String sql, List<Object> paramList, boolean toCamelCase) {
        return lightOrmSqlExecutor.executeQueryForJson(sql, paramList, toCamelCase);
    }
    public List<JSONObject> selectJsonList(String sql, List<Object> paramList, boolean toCamelCase) {
        JSONArray jsonArray = lightOrmSqlExecutor.executeQueryForJson(sql, paramList, toCamelCase);
        if(jsonArray == null|| jsonArray.isEmpty()){
            return ListUtil.empty();
        }
        return jsonArray.toList(JSONObject.class);
    }

    /**
     * 执行SQL查询返回JSONArray（默认驼峰命名转换）
     *
     * @param sql SQL语句
     * @param paramList 参数列表
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArray(String sql, List<Object> paramList) {
        return selectJsonArray(sql, paramList, true);
    }
    
    /**
     * 使用模板执行查询返回JSONArray（支持驼峰命名转换）
     *
     * @param templateName 模板名称
     * @param params 参数Map
     * @param toCamelCase 是否转换为驼峰命名
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArrayByTemplate(String templateName, Map<String, Object> params, boolean toCamelCase) {
        MybatisTemplateEngine.RenderResult result = sqlTemplateManager.getSqlWithParams(templateName, params);
        return lightOrmSqlExecutor.executeQueryForJson(result.getSql(), result.getParams(), toCamelCase);
    }
    
    /**
     * 使用模板执行查询返回JSONArray（默认驼峰命名转换）
     *
     * @param templateName 模板名称
     * @param params 参数Map
     * @return 查询结果JSONArray
     */
    public JSONArray selectJsonArrayByTemplate(String templateName, Map<String, Object> params) {
        return selectJsonArrayByTemplate(templateName, params, true);
    }
    
    /**
     * 获取实体信息（统一管理）
     */
    public EntityInfo getEntityInfo(Class<?> entityClass) {
        return EntityInfoManager.getInstance().getEntityInfo(entityClass);
    }
}



