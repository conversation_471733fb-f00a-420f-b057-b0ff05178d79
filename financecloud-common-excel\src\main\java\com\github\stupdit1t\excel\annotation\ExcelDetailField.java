package com.github.stupdit1t.excel.annotation;

import java.lang.annotation.*;

/**
 * 明细表字段注解
 * 用于标记明细表字段的导出配置
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelDetailField {

    /**
     * 列标题
     */
    String title();

    /**
     * 列顺序
     */
    int order() default 0;

    /**
     * 列宽度
     */
    int width() default -1;

    /**
     * 数据格式
     */
    String pattern() default "";

    /**
     * 是否合并相同值的单元格
     */
    boolean mergeRepeat() default false;

    /**
     * 水平对齐方式
     * LEFT, CENTER, RIGHT
     */
    String align() default "CENTER";

    /**
     * 垂直对齐方式
     * TOP, CENTER, BOTTOM
     */
    String valign() default "CENTER";

    /**
     * 是否显示合计
     */
    boolean showSum() default false;

    /**
     * 合计标签
     */
    String sumLabel() default "合计";
}
