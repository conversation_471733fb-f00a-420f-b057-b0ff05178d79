package com.lg.financecloud.common.redis.cache.twolevel;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.lang.Nullable;

import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 二级缓存实现，使用本地内存作为一级缓存(L1)和Redis作为二级缓存(L2)
 * 
 * <AUTHOR>
 */
@Slf4j
public class TwoLevelCache implements Cache {
    
    @Getter
    private final String name;
    
    private final Cache memoryCache;
    private final RedisCache redisCache;
    
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    // 统计信息
    private final AtomicLong l1Hits = new AtomicLong(0);
    private final AtomicLong l2Hits = new AtomicLong(0);
    private final AtomicLong misses = new AtomicLong(0);
    private final AtomicLong puts = new AtomicLong(0);
    
    /**
     * 创建二级缓存
     *
     * @param name 缓存名称
     * @param memoryCache 内存缓存(L1)
     * @param redisCache Redis缓存(L2)
     */
    public TwoLevelCache(String name, Cache memoryCache, RedisCache redisCache) {
        this.name = name;
        this.memoryCache = memoryCache;
        this.redisCache = redisCache;
        log.info("初始化二级缓存[{}]: L1=Caffeine, L2=Redis", name);
    }
    
    @Override
    public Object getNativeCache() {
        // 返回包含两个缓存的数组，用于内省
        return new Object[] { memoryCache.getNativeCache(), redisCache.getNativeCache() };
    }
    
    @Override
    public ValueWrapper get(Object key) {
        // 首先尝试从L1(内存)获取
        ValueWrapper wrapper = memoryCache.get(key);
        if (wrapper != null) {
            l1Hits.incrementAndGet();
            log.debug("缓存[{}]L1命中: {}", name, key);
            return wrapper;
        }
        
        // 如果L1未找到，尝试从L2(Redis)获取
        wrapper = redisCache.get(key);
        if (wrapper != null) {
            l2Hits.incrementAndGet();
            log.debug("缓存[{}]L2命中: {}", name, key);
            Object value = wrapper.get();
            // 使用从L2获取的值填充L1缓存
            memoryCache.put(key, value);
            return wrapper;
        }
        
        misses.incrementAndGet();
        log.debug("缓存[{}]未命中: {}", name, key);
        return null;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T> T get(Object key, @Nullable Class<T> type) {
        // 首先尝试从L1(内存)获取
        T value = memoryCache.get(key, type);
        if (value != null || memoryCache.get(key) != null) {
            l1Hits.incrementAndGet();
            return value;
        }
        
        // 如果L1未找到，尝试从L2(Redis)获取
        value = redisCache.get(key, type);
        if (value != null) {
            l2Hits.incrementAndGet();
            // 使用从L2获取的值填充L1缓存
            memoryCache.put(key, value);
        } else {
            misses.incrementAndGet();
        }
        
        return value;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        // 首先尝试从L1(内存)获取
        ValueWrapper wrapper = memoryCache.get(key);
        if (wrapper != null) {
            l1Hits.incrementAndGet();
            return (T) wrapper.get();
        }
        
        // 尝试从L2(Redis)获取
        wrapper = redisCache.get(key);
        if (wrapper != null) {
            l2Hits.incrementAndGet();
            Object value = wrapper.get();
            // 使用从L2获取的值填充L1缓存
            memoryCache.put(key, value);
            return (T) value;
        }
        
        // 如果两级缓存中都未找到值，锁定并计算
        misses.incrementAndGet();
        lock.writeLock().lock();
        try {
            // 获取锁后再次检查
            wrapper = memoryCache.get(key);
            if (wrapper != null) {
                return (T) wrapper.get();
            }
            
            wrapper = redisCache.get(key);
            if (wrapper != null) {
                Object value = wrapper.get();
                memoryCache.put(key, value);
                return (T) value;
            }
            
            // 加载值
            T value;
            try {
                value = valueLoader.call();
                log.debug("缓存[{}]加载值: key={}", name, key);
            }
            catch (Exception ex) {
                throw new ValueRetrievalException(key, valueLoader, ex);
            }
            
            // 存储到两级缓存
            put(key, value);
            return value;
        }
        finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public void put(Object key, @Nullable Object value) {
        puts.incrementAndGet();
        // 存储到L1(内存)
        memoryCache.put(key, value);
        
        // 存储到L2(Redis)
        redisCache.put(key, value);
        
        log.debug("缓存[{}]存储: key={}", name, key);
    }
    
    @Override
    public void evict(Object key) {
        // 从两个缓存中都删除
        memoryCache.evict(key);
        redisCache.evict(key);
        
        log.debug("缓存[{}]删除: key={}", name, key);
    }
    
    @Override
    public void clear() {
        // 清除两个缓存
        memoryCache.clear();
        redisCache.clear();
        
        log.info("缓存[{}]清除", name);
        
        // 重置统计信息
        l1Hits.set(0);
        l2Hits.set(0);
        misses.set(0);
        puts.set(0);
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public CacheStats stats() {
        return new CacheStats(
                name,
                l1Hits.get(),
                l2Hits.get(),
                misses.get(),
                puts.get()
        );
    }
    
    /**
     * 二级缓存统计信息
     */
    public static class CacheStats {
        private final String name;
        private final long l1Hits;
        private final long l2Hits;
        private final long misses;
        private final long puts;
        
        public CacheStats(String name, long l1Hits, long l2Hits, long misses, long puts) {
            this.name = name;
            this.l1Hits = l1Hits;
            this.l2Hits = l2Hits;
            this.misses = misses;
            this.puts = puts;
        }
        
        public String getName() {
            return name;
        }
        
        public long getL1Hits() {
            return l1Hits;
        }
        
        public long getL2Hits() {
            return l2Hits;
        }
        
        public long getTotalHits() {
            return l1Hits + l2Hits;
        }
        
        public long getMisses() {
            return misses;
        }
        
        public long getPuts() {
            return puts;
        }
        
        public long getRequestCount() {
            return getTotalHits() + misses;
        }
        
        public double getHitRate() {
            long requestCount = getRequestCount();
            return requestCount == 0 ? 0 : (double) getTotalHits() / requestCount;
        }
        
        public double getL1HitRate() {
            long totalHits = getTotalHits();
            return totalHits == 0 ? 0 : (double) l1Hits / totalHits;
        }
        
        @Override
        public String toString() {
            return String.format("CacheStats{name='%s', hits=%d (L1=%d, L2=%d), misses=%d, hitRate=%.2f%%}", 
                    name, getTotalHits(), l1Hits, l2Hits, misses, getHitRate() * 100);
        }
    }
} 