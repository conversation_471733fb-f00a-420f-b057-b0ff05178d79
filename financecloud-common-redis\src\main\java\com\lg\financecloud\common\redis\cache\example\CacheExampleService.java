package com.lg.financecloud.common.redis.cache.example;

import com.lg.financecloud.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 示例服务，演示如何在不同缓存模式下使用缓存注解
 * 仅供参考，不用于实际使用
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CacheExampleService {
    
    /**
     * 租户特定缓存示例（使用默认TTL）
     * 
     * @param id 用户ID
     * @return 数据对象
     */
    @Cacheable(value = "userDetails", key = "#id")
    public Object getUserById(String id) {
        log.info("缓存未命中，加载用户: {}", id);
        return loadUserFromDatabase(id);
    }
    
    /**
     * 租户特定缓存示例（使用自定义TTL：30秒）
     * 
     * @param id 用户ID
     * @return 数据对象
     */
    @Cacheable(value = "userDetails#30", key = "#id")
    public Object getUserByIdShortTTL(String id) {
        log.info("缓存未命中，加载用户(短TTL): {}", id);
        return loadUserFromDatabase(id);
    }
    
    /**
     * 全局缓存示例（非租户特定，使用默认TTL）
     * 
     * @param id 用户ID
     * @return 数据对象
     */
    @Cacheable(value = CacheConstants.GLOBALLY + "globalUserDetails", key = "#id")
    public Object getGlobalUserById(String id) {
        log.info("全局缓存未命中，加载用户: {}", id);
        return loadUserFromDatabase(id);
    }
    
    /**
     * 全局缓存示例（非租户特定，使用自定义TTL：60秒）
     * 
     * @param id 用户ID
     * @return 数据对象
     */
    @Cacheable(value = CacheConstants.GLOBALLY + "globalUserDetails#60", key = "#id")
    public Object getGlobalUserByIdCustomTTL(String id) {
        log.info("全局缓存未命中，加载用户(自定义TTL): {}", id);
        return loadUserFromDatabase(id);
    }
    
    /**
     * 模拟从数据库加载用户数据
     */
    private Object loadUserFromDatabase(String id) {
        // 模拟数据库访问
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return new Object() {
            private final String userId = id;
            private final String name = "用户 " + id;
            
            @Override
            public String toString() {
                return "User{id='" + userId + "', name='" + name + "'}";
            }
        };
    }
} 