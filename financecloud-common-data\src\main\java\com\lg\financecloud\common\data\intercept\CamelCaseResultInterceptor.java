package com.lg.financecloud.common.data.intercept;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.util.*;

@Intercepts({@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})})
public class CamelCaseResultInterceptor implements Interceptor {


    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];

        BoundSql boundSql = mappedStatement.getBoundSql(args[1]);
        List<Object> list = (List<Object>) invocation.proceed();

        if (list != null && !list.isEmpty() && list.get(0) instanceof Map) {
            List<Object> convertedList = new ArrayList<>(list.size());
            for (Object item : list) {
                if (item instanceof Map) {
                    convertedList.add(convertMapKeysToCamelCase((Map<String, Object>) item));
                } else {
                    convertedList.add(item);
                }
            }
            return convertedList;
        }
        return list;
    }

    private Map<String, Object> convertMapKeysToCamelCase(Map<String, Object> originalMap) {
        Map<String, Object> camelCaseMap = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
            String key = entry.getKey();
            // Check if the key contains an underscore before converting
            if (key.contains("_")) {
                key = convertToCamelCase(key);
            }
            camelCaseMap.put(key, entry.getValue());
        }
        return camelCaseMap;
    }

    private String convertToCamelCase(String underscoreString) {
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (char ch : underscoreString.toCharArray()) {
            if (ch == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(ch));
                    nextUpperCase = false;
                } else {
                    result.append(result.length() == 0 ? Character.toLowerCase(ch) : ch);
                }
            }
        }
        return result.toString();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}
