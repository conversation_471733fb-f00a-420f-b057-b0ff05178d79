package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.pdf.PdfTemplateConfig;

import java.util.*;

/**
 * PDF模板导出演示程序
 */
public class PdfTemplateDemo {

    public static void main(String[] args) {
        System.out.println("=== PDF模板导出功能演示 ===");
        
        PdfTemplateDemo demo = new PdfTemplateDemo();
        
        try {
            // 演示基本PDF模板导出
            demo.demonstrateBasicPdfTemplate();
            
            System.out.println("所有演示完成！");
            
        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 演示基本PDF模板导出
     */
    public void demonstrateBasicPdfTemplate() {
        System.out.println("\n--- 基本PDF模板导出演示 ---");
        
        try {
            // 1. 创建Excel模板
            String templatePath = "src/test/java/excel/export/excel/demo_template.xlsx";
            createDemoTemplate(templatePath);
            System.out.println("✓ Excel模板创建成功: " + templatePath);
            
            // 2. 准备数据
            Map<String, Object> variables = new HashMap<>();
            variables.put("companyName", "金融云科技有限公司");
            variables.put("reportTitle", "员工信息报表");
            variables.put("reportDate", "2025年08月21日");
            variables.put("reportPerson", "人事部");
            
            List<Map<String, Object>> employees = createEmployeeData();
            System.out.println("✓ 数据准备完成，员工数量: " + employees.size());
            
            // 3. 检查PDF导出依赖
            try {
                Class.forName("com.itextpdf.text.Document");
                System.out.println("✓ PDF导出依赖检查通过");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠ 警告: 未找到iText依赖，PDF导出功能可能不可用");
                System.out.println("请在pom.xml中添加以下依赖:");
                System.out.println("<dependency>");
                System.out.println("    <groupId>com.itextpdf</groupId>");
                System.out.println("    <artifactId>itextpdf</artifactId>");
                System.out.println("    <version>5.5.13.3</version>");
                System.out.println("</dependency>");
                return;
            }
            
            // 4. 执行PDF模板导出
            String outputPath = "src/test/java/excel/export/excel/demo_result.pdf";
            
            ExcelHelper.opsPdfTemplate(templatePath)
                    .vars(variables)
                    .loop("employees", employees)
                    .fontSize(12f)
                    .enableChineseFont(true)
                    .orientation(PdfTemplateConfig.PageOrientation.PORTRAIT)
                    .pageSize(PdfTemplateConfig.PageSize.A4)
                    .margins(20f)
                    .showGridLines(true)
                    .exportTo(outputPath);
            
            System.out.println("✓ PDF模板导出成功: " + outputPath);
            
            // 5. 验证输出文件
            java.io.File outputFile = new java.io.File(outputPath);
            if (outputFile.exists() && outputFile.length() > 0) {
                System.out.println("✓ 输出文件验证成功，文件大小: " + outputFile.length() + " 字节");
            } else {
                System.out.println("⚠ 警告: 输出文件不存在或为空");
            }
            
        } catch (Exception e) {
            System.err.println("✗ PDF模板导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建演示用的Excel模板
     */
    private void createDemoTemplate(String templatePath) {
        try {
            // 确保目录存在
            java.io.File file = new java.io.File(templatePath);
            file.getParentFile().mkdirs();
            
            // 创建模板数据
            List<Map<String, Object>> templateData = new ArrayList<>();
            
            // 公司名称行
            Map<String, Object> row1 = new HashMap<>();
            row1.put("col1", "${companyName}");
            row1.put("col2", "");
            row1.put("col3", "");
            templateData.add(row1);
            
            // 报表标题行
            Map<String, Object> row2 = new HashMap<>();
            row2.put("col1", "${reportTitle}");
            row2.put("col2", "");
            row2.put("col3", "日期: ${reportDate}");
            templateData.add(row2);
            
            // 制表人行
            Map<String, Object> row3 = new HashMap<>();
            row3.put("col1", "制表人: ${reportPerson}");
            row3.put("col2", "");
            row3.put("col3", "");
            templateData.add(row3);
            
            // 空行
            Map<String, Object> row4 = new HashMap<>();
            row4.put("col1", "");
            row4.put("col2", "");
            row4.put("col3", "");
            templateData.add(row4);
            
            // 表头行
            Map<String, Object> row5 = new HashMap<>();
            row5.put("col1", "员工姓名");
            row5.put("col2", "部门");
            row5.put("col3", "职位");
            templateData.add(row5);
            
            // 循环开始标记
            Map<String, Object> row6 = new HashMap<>();
            row6.put("col1", "${#foreach employees}");
            row6.put("col2", "");
            row6.put("col3", "");
            templateData.add(row6);
            
            // 数据行模板
            Map<String, Object> row7 = new HashMap<>();
            row7.put("col1", "${name}");
            row7.put("col2", "${department}");
            row7.put("col3", "${position}");
            templateData.add(row7);
            
            // 循环结束标记
            Map<String, Object> row8 = new HashMap<>();
            row8.put("col1", "${/foreach}");
            row8.put("col2", "");
            row8.put("col3", "");
            templateData.add(row8);
            
            // 导出Excel模板
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("列1", "列2", "列3")
                        .done()
                    .opsColumn()
                        .fields("col1", "col2", "col3")
                        .done()
                    .done()
                    .export(templatePath);
            
        } catch (Exception e) {
            throw new RuntimeException("创建Excel模板失败", e);
        }
    }

    /**
     * 创建员工数据
     */
    private List<Map<String, Object>> createEmployeeData() {
        List<Map<String, Object>> employees = new ArrayList<>();
        
        String[] names = {"张三", "李四", "王五", "赵六", "钱七"};
        String[] departments = {"技术部", "销售部", "财务部", "人事部", "市场部"};
        String[] positions = {"高级工程师", "销售经理", "财务专员", "HR专员", "市场专员"};
        
        for (int i = 0; i < names.length; i++) {
            Map<String, Object> employee = new HashMap<>();
            employee.put("name", names[i]);
            employee.put("department", departments[i]);
            employee.put("position", positions[i]);
            employees.add(employee);
        }
        
        return employees;
    }
}
