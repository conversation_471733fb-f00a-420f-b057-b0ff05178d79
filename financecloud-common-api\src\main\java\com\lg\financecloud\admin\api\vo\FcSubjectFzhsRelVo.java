package com.lg.financecloud.admin.api.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 辅助核算操作类
 */
@Data
public class FcSubjectFzhsRelVo {

    /**
     * id
     */
    @TableId
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     *
     */
    @ApiModelProperty(value = "", hidden = true)
    private Integer tenantId;
    /**
     * 账套
     */
    @ApiModelProperty(value = "账套")
    private Long accountId;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String spec;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     *
     */
    @ApiModelProperty(value = "是否启用默认0 ,1启用")
    private String enableFlag;
    /**
     *
     */
    @ApiModelProperty(value = "是否删除默认0,1删除 ")
    private String delFlag;
    /**
     * 核算类型:1客户 2供应商 3 员工 4存货 5 部门 6项目
     */
    @ApiModelProperty(value = "核算类型:1客户 2供应商 3 员工 4存货 5 部门 6项目")
    private String type;
    /**
    * 部门计提工资科目id
    * */
    private Long salaryAccrualSubjectId;
    /**
    * 部门领料费用科目id
    * */
    private Long depotLldFeeSubjectId;


    /**
     * 核算项id
     */
    @ApiModelProperty(value = "核算项id")
    private Long fzhsTypeId;

    /**
     * 存货分类
     */
    private String category;

    private String subjectName;

    /**
     * 预设辅助核算id
     * */
    private Long preFzhsId;
    /**
     * erp那边的id
     */
    private String erpId;
}
