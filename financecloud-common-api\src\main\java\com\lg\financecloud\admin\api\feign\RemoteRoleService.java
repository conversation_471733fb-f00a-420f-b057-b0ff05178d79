package com.lg.financecloud.admin.api.feign;


import com.lg.financecloud.admin.api.entity.SysRole;
import com.lg.financecloud.common.core.constant.SecurityConstants;
import com.lg.financecloud.common.core.constant.ServiceNameConstants;
import com.lg.financecloud.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RemoteRoleService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteRoleService {

    @GetMapping("/role/list4Oa")
    R<List<SysRole>> listRoles4Oa(@RequestHeader(SecurityConstants.FROM) String from,@RequestParam("tenantId") Integer tenantId);
}
